# 门店回本分析导出功能说明

## 功能概述

为门店回本分析页面添加了 Excel 和 PDF 导出功能，支持将当前筛选条件下的数据导出为文件。

## 已实现功能

### 1. Excel 导出
- ✅ 导出当前筛选条件下的门店回本详情表格
- ✅ 包含筛选条件信息（门店范围、时间范围、导出时间）
- ✅ 自动设置列宽和样式
- ✅ 文件名包含日期（格式：`门店回本分析_YYYY-MM-DD.xlsx`）

**Excel 文件包含内容：**
- 报告标题
- 筛选条件详情
- 完整的门店回本数据表格（10列数据）

### 2. PDF 导出
- ✅ 导出完整报告，包含筛选条件、表格和图表
- ✅ 自动截图表格和图表元素
- ✅ 支持多页面布局
- ✅ 文件名包含日期（格式：`门店回本分析_YYYY-MM-DD.pdf`）

**PDF 文件包含内容：**
- 报告标题
- 筛选条件详情
- 门店回本详情表格（截图）
- 净资产 vs 总投入对比图表
- 净现金流趋势图表

### 3. 用户体验改进
- ✅ 导出过程中显示加载状态
- ✅ 按钮禁用防止重复点击
- ✅ 错误处理和用户提示
- ✅ 空数据状态下禁用导出功能

## 技术实现

### 依赖库
- `xlsx` - Excel 文件生成
- `jspdf` - PDF 文件生成
- `html2canvas` - HTML 元素截图

### 文件结构
```
lib/
  export-utils.ts     # 导出功能工具函数
app/pages/report-center/store-roi-analysis/
  StoreROIAnalysis.tsx # 主组件（已更新）
```

### 关键功能
1. **exportToExcel()** - Excel 导出功能
2. **exportToPDF()** - PDF 导出功能
3. **错误处理** - 完善的异常处理机制
4. **UI 状态管理** - 导出状态和加载提示

## 使用方法

1. 在门店回本分析页面设置筛选条件
2. 点击表格右上角的 "Excel" 或 "PDF" 按钮
3. 等待处理完成，文件将自动下载到本地

## 注意事项

1. **PDF 中文显示**: 已修复中文乱码问题！筛选条件部分现在使用HTML截图的方式生成，完美支持中文显示。如果截图失败，会自动回退到英文文本显示。

2. **图表截图**: PDF 导出依赖 html2canvas 进行图表截图，确保页面完全加载后再进行导出。

3. **数据格式**: 金额数据统一格式化为"XX.X万"的形式显示。

4. **错误处理**: 如果导出失败，会显示错误提示，用户可以重试。

## 最新更新

### v1.1 - 修复PDF中文乱码问题
- ✅ 使用HTML元素截图的方式生成标题部分，完美支持中文显示
- ✅ 使用HTML元素截图的方式生成筛选条件部分，完美支持中文显示
- ✅ 添加了回退机制，确保在截图失败时仍能正常导出
- ✅ 优化了标题和筛选条件的样式和布局
- ✅ 提升了PDF导出的视觉效果

### v1.2 - 修复标题乱码问题
- ✅ 标题"门店回本分析报告"现在也使用HTML截图方式生成
- ✅ 彻底解决了所有中文文本的乱码问题
- ✅ 标题样式优化，字体更大更醒目

## 文件位置

导出的文件将下载到用户的默认下载目录，文件名格式：
- Excel: `门店回本分析_2024-12-10.xlsx`
- PDF: `门店回本分析_2024-12-10.pdf` 