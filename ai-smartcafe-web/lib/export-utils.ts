import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// 门店回本分析数据类型
interface ROIData {
  id: string;
  name: string;
  totalInvestment: number;
  cumulativeCashFlow: number;
  inventoryValue: number;
  netAssets: number;
  remainingPayback: number;
  paybackStatus: string;
  predictedPaybackTime: string;
  actualPaybackTime: string;
  pendingReimbursement: number;
}

// 盈利分析数据类型
interface ProfitData {
  id: string;
  name: string;
  revenue: number;
  expenses: number;
  basicNetProfit: number;
  adjustedNetProfit: number;
  basicProfitMargin: number;
  adjustedProfitMargin: number;
  pendingReimbursement: number;
}

interface FilterConditions {
  selectedStores: string[];
  selectedPeriod: string;
  endYearMonth?: string;
  startYearMonth?: string;
  includeInventoryAdjustment?: boolean;
  storeNames: Record<string, string>;
}

// 格式化金额
const formatCurrency = (amount: number) => {
  return (amount / 10000).toFixed(1) + "万";
};

// 格式化百分比
const formatPercentage = (percentage: number) => {
  return percentage.toFixed(1) + "%";
};

// 获取筛选条件文本
const getFilterText = (conditions: FilterConditions) => {
  const { selectedStores, selectedPeriod, endYearMonth, startYearMonth, includeInventoryAdjustment, storeNames } = conditions;
  
  let storeText = "All Stores";
  let storeTextCN = "所有门店";
  
  if (!selectedStores.includes("all") && selectedStores.length > 0) {
    if (selectedStores.length === 1) {
      const storeName = storeNames[selectedStores[0]] || "Unknown Store";
      storeText = storeName;
      storeTextCN = storeNames[selectedStores[0]] || "未知门店";
    } else {
      storeText = `${selectedStores.length} stores selected`;
      storeTextCN = `已选择 ${selectedStores.length} 个门店`;
    }
  }
  
  let periodText = "";
  let periodTextCN = "";
  switch (selectedPeriod) {
    case "current-month-summary":
      periodText = "Current Month Summary";
      periodTextCN = "截至目前汇总";
      break;
    case "current-year":
      periodText = "Current Year";
      periodTextCN = "本年";
      break;
    case "by-month":
      periodText = `${startYearMonth} to ${endYearMonth}`;
      periodTextCN = `${startYearMonth} 至 ${endYearMonth}`;
      break;
    case "until-specified-month":
      periodText = `Until ${endYearMonth}`;
      periodTextCN = `截至 ${endYearMonth}`;
      break;
    default:
      periodText = selectedPeriod;
      periodTextCN = selectedPeriod;
  }
  
  return {
    stores: storeText,
    period: periodText,
    storesCN: storeTextCN,
    periodCN: periodTextCN,
    includeInventoryAdjustment: includeInventoryAdjustment || false
  };
};

// 导出盈利分析 Excel
export const exportProfitAnalysisToExcel = (
  profitData: ProfitData[],
  filterConditions: FilterConditions,
  filename?: string
) => {
  const filterText = getFilterText(filterConditions);
  const timestamp = new Date().toLocaleString('zh-CN');
  
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  
  // 准备筛选条件数据
  const filterData = [
    ['门店盈利分析报告'],
    [''],
    ['筛选条件:'],
    ['门店范围', filterText.storesCN],
    ['时间范围', filterText.periodCN],
    ['包含库存调整', filterText.includeInventoryAdjustment ? '是' : '否'],
    ['导出时间', timestamp],
    [''],
    ['门店盈利详情:']
  ];
  
  // 准备表格数据
  let tableHeaders: string[];
  let tableData: any[][];
  
  if (filterText.includeInventoryAdjustment) {
    tableHeaders = [
      '门店',
      '收入',
      '支出',
      '净利（基础）',
      '净利（调整后）',
      '利润率（基础）',
      '利润率（调整后）',
      '待报销款'
    ];
    
    tableData = profitData.map(store => [
      store.name,
      formatCurrency(store.revenue),
      formatCurrency(store.expenses),
      formatCurrency(store.basicNetProfit),
      formatCurrency(store.adjustedNetProfit),
      formatPercentage(store.basicProfitMargin),
      formatPercentage(store.adjustedProfitMargin),
      formatCurrency(store.pendingReimbursement)
    ]);
  } else {
    tableHeaders = [
      '门店',
      '收入',
      '支出',
      '净利',
      '利润率',
      '待报销款'
    ];
    
    tableData = profitData.map(store => [
      store.name,
      formatCurrency(store.revenue),
      formatCurrency(store.expenses),
      formatCurrency(store.basicNetProfit),
      formatPercentage(store.basicProfitMargin),
      formatCurrency(store.pendingReimbursement)
    ]);
  }
  
  // 合并所有数据
  const allData = [
    ...filterData,
    tableHeaders,
    ...tableData
  ];
  
  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(allData);
  
  // 设置列宽
  const colWidths = filterText.includeInventoryAdjustment ? [
    { wch: 15 }, // 门店
    { wch: 12 }, // 收入
    { wch: 12 }, // 支出
    { wch: 15 }, // 净利（基础）
    { wch: 15 }, // 净利（调整后）
    { wch: 15 }, // 利润率（基础）
    { wch: 15 }, // 利润率（调整后）
    { wch: 12 }  // 待报销款
  ] : [
    { wch: 15 }, // 门店
    { wch: 12 }, // 收入
    { wch: 12 }, // 支出
    { wch: 15 }, // 净利
    { wch: 12 }, // 利润率
    { wch: 12 }  // 待报销款
  ];
  ws['!cols'] = colWidths;
  
  // 设置样式
  const headerRowIndex = filterData.length;
  for (let col = 0; col < tableHeaders.length; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
    if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
    ws[cellAddress].s = {
      font: { bold: true },
      fill: { fgColor: { rgb: "F3F4F6" } },
      alignment: { horizontal: "center" }
    };
  }
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '门店盈利分析');
  
  // 导出文件
  const defaultFilename = `门店盈利分析_${new Date().toISOString().slice(0, 10)}.xlsx`;
  XLSX.writeFile(wb, filename || defaultFilename);
};

// 导出盈利分析 PDF
export const exportProfitAnalysisToPDF = async (
  profitData: ProfitData[],
  filterConditions: FilterConditions,
  tableElementId: string,
  chartsElementIds: string[],
  filename?: string
) => {
  const filterText = getFilterText(filterConditions);
  const timestamp = new Date().toLocaleString('zh-CN');
  
  // 创建 PDF 文档
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  let currentY = margin;
  
  // 创建临时的标题HTML元素并截图
  const titleElement = document.createElement('div');
  titleElement.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 600px;
    padding: 20px;
    background: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    color: #1f2937;
  `;
  
  titleElement.innerHTML = '门店盈利分析报告';
  document.body.appendChild(titleElement);
  
  try {
    // 截图标题
    const titleCanvas = await html2canvas(titleElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });
    
    const titleImgData = titleCanvas.toDataURL('image/png');
    const titleImgWidth = pageWidth - 2 * margin;
    const titleImgHeight = (titleCanvas.height * titleImgWidth) / titleCanvas.width;
    
    pdf.addImage(titleImgData, 'PNG', margin, currentY, titleImgWidth, titleImgHeight);
    currentY += titleImgHeight + 15;
    
    // 移除临时元素
    document.body.removeChild(titleElement);
    
  } catch (error) {
    console.error('标题截图失败:', error);
    // 移除临时元素
    if (document.body.contains(titleElement)) {
      document.body.removeChild(titleElement);
    }
    
    // 如果截图失败，使用英文标题作为备选方案
    pdf.setFontSize(18);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Store Profit Analysis Report', pageWidth / 2, currentY, { align: 'center' });
    currentY += 15;
  }
  
  // 创建临时的筛选条件HTML元素并截图
  const filterInfoElement = document.createElement('div');
  filterInfoElement.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 600px;
    padding: 20px;
    background: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
  `;
  
  filterInfoElement.innerHTML = `
    <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #1f2937;">筛选条件</div>
    <div style="margin-bottom: 8px;"><strong>门店范围:</strong> ${filterText.storesCN}</div>
    <div style="margin-bottom: 8px;"><strong>时间范围:</strong> ${filterText.periodCN}</div>
    <div style="margin-bottom: 8px;"><strong>包含库存调整:</strong> ${filterText.includeInventoryAdjustment ? '是' : '否'}</div>
    <div style="margin-bottom: 8px;"><strong>导出时间:</strong> ${timestamp}</div>
  `;
  
  document.body.appendChild(filterInfoElement);
  
  try {
    // 截图筛选条件
    const filterCanvas = await html2canvas(filterInfoElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });
    
    const filterImgData = filterCanvas.toDataURL('image/png');
    const filterImgWidth = pageWidth - 2 * margin;
    const filterImgHeight = (filterCanvas.height * filterImgWidth) / filterCanvas.width;
    
    pdf.addImage(filterImgData, 'PNG', margin, currentY, filterImgWidth, filterImgHeight);
    currentY += filterImgHeight + 15;
    
    // 移除临时元素
    document.body.removeChild(filterInfoElement);
    
  } catch (error) {
    console.error('筛选条件截图失败:', error);
    // 移除临时元素
    if (document.body.contains(filterInfoElement)) {
      document.body.removeChild(filterInfoElement);
    }
    
    // 如果截图失败，使用英文文本作为备选方案
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Filter Conditions:', margin, currentY);
    currentY += 8;
    
    pdf.setFontSize(10);
    pdf.text(`Stores: ${filterText.stores}`, margin + 10, currentY);
    currentY += 6;
    pdf.text(`Period: ${filterText.period}`, margin + 10, currentY);
    currentY += 6;
    pdf.text(`Include Inventory Adjustment: ${filterText.includeInventoryAdjustment ? 'Yes' : 'No'}`, margin + 10, currentY);
    currentY += 6;
    pdf.text(`Export Time: ${timestamp}`, margin + 10, currentY);
    currentY += 15;
  }
  
  // 获取表格元素并转换为canvas
  const tableElement = document.getElementById(tableElementId);
  if (tableElement) {
    try {
      const tableCanvas = await html2canvas(tableElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      const tableImgData = tableCanvas.toDataURL('image/png');
      const tableImgWidth = pageWidth - 2 * margin;
      const tableImgHeight = (tableCanvas.height * tableImgWidth) / tableCanvas.width;
      
      // 检查是否需要新页面
      if (currentY + tableImgHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }
      
      pdf.addImage(tableImgData, 'PNG', margin, currentY, tableImgWidth, tableImgHeight);
      currentY += tableImgHeight + 10;
      
    } catch (error) {
      console.error('表格截图失败:', error);
      // 如果截图失败，添加文本表格
      pdf.setFontSize(12);
      pdf.text('Store Profit Details', margin, currentY);
      currentY += 10;
      
      // 简化的文本表格
      profitData.slice(0, 10).forEach((store, index) => {
        if (currentY > pageHeight - 30) {
          pdf.addPage();
          currentY = margin;
        }
        
        pdf.setFontSize(9);
        const line = `${store.name} | Revenue: ${formatCurrency(store.revenue)} | Net Profit: ${formatCurrency(store.basicNetProfit)}`;
        pdf.text(line, margin, currentY);
        currentY += 5;
      });
      
      currentY += 10;
    }
  }
  
  // 添加图表
  for (const chartId of chartsElementIds) {
    const chartElement = document.getElementById(chartId);
    if (chartElement) {
      try {
        // 检查是否需要新页面
        if (currentY > pageHeight - 100) {
          pdf.addPage();
          currentY = margin;
        }
        
        const chartCanvas = await html2canvas(chartElement, {
          scale: 1.5,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        });
        
        const chartImgData = chartCanvas.toDataURL('image/png');
        const chartImgWidth = pageWidth - 2 * margin;
        const chartImgHeight = (chartCanvas.height * chartImgWidth) / chartCanvas.width;
        
        // 限制图表高度
        const maxChartHeight = (pageHeight - 2 * margin) * 0.4;
        const finalChartHeight = Math.min(chartImgHeight, maxChartHeight);
        const finalChartWidth = (chartCanvas.width * finalChartHeight) / chartCanvas.height;
        
        pdf.addImage(chartImgData, 'PNG', margin, currentY, finalChartWidth, finalChartHeight);
        currentY += finalChartHeight + 10;
        
      } catch (error) {
        console.error(`图表 ${chartId} 截图失败:`, error);
      }
    }
  }
  
  // 保存 PDF
  const defaultFilename = `门店盈利分析_${new Date().toISOString().slice(0, 10)}.pdf`;
  pdf.save(filename || defaultFilename);
};

// 原有的回本分析导出函数保持不变
// 导出 Excel
export const exportToExcel = (
  roiData: ROIData[],
  filterConditions: FilterConditions,
  filename?: string
) => {
  const filterText = getFilterText(filterConditions);
  const timestamp = new Date().toLocaleString('zh-CN');
  
  // 创建工作簿
  const wb = XLSX.utils.book_new();
  
  // 准备筛选条件数据
  const filterData = [
    ['门店回本分析报告'],
    [''],
    ['筛选条件:'],
    ['门店范围', filterText.stores],
    ['时间范围', filterText.period],
    ['导出时间', timestamp],
    [''],
    ['门店回本详情:']
  ];
  
  // 准备表格数据
  const tableHeaders = [
    '门店',
    '总投入成本',
    '累计净现金流', 
    '库存价值',
    '净资产',
    '剩余回本金额',
    '回本状态',
    '预测回本时间',
    '实际回本时间',
    '待报销款'
  ];
  
  const tableData = roiData.map(store => [
    store.name,
    formatCurrency(store.totalInvestment),
    formatCurrency(store.cumulativeCashFlow),
    formatCurrency(store.inventoryValue),
    formatCurrency(store.netAssets),
    formatCurrency(store.remainingPayback),
    store.paybackStatus,
    store.predictedPaybackTime,
    store.actualPaybackTime,
    formatCurrency(store.pendingReimbursement)
  ]);
  
  // 合并所有数据
  const allData = [
    ...filterData,
    tableHeaders,
    ...tableData
  ];
  
  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(allData);
  
  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 门店
    { wch: 12 }, // 总投入成本
    { wch: 15 }, // 累计净现金流
    { wch: 12 }, // 库存价值
    { wch: 12 }, // 净资产
    { wch: 15 }, // 剩余回本金额
    { wch: 12 }, // 回本状态
    { wch: 15 }, // 预测回本时间
    { wch: 15 }, // 实际回本时间
    { wch: 12 }  // 待报销款
  ];
  ws['!cols'] = colWidths;
  
  // 设置样式
  const headerRowIndex = filterData.length;
  for (let col = 0; col < tableHeaders.length; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
    if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
    ws[cellAddress].s = {
      font: { bold: true },
      fill: { fgColor: { rgb: "F3F4F6" } },
      alignment: { horizontal: "center" }
    };
  }
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '门店回本分析');
  
  // 导出文件
  const defaultFilename = `门店回本分析_${new Date().toISOString().slice(0, 10)}.xlsx`;
  XLSX.writeFile(wb, filename || defaultFilename);
};

// 导出 PDF
export const exportToPDF = async (
  roiData: ROIData[],
  filterConditions: FilterConditions,
  tableElementId: string,
  chartsElementIds: string[],
  filename?: string
) => {
  const filterText = getFilterText(filterConditions);
  const timestamp = new Date().toLocaleString('zh-CN');
  
  // 创建 PDF 文档
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  let currentY = margin;
  
  // 创建临时的标题HTML元素并截图
  const titleElement = document.createElement('div');
  titleElement.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 600px;
    padding: 20px;
    background: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    color: #1f2937;
  `;
  
  titleElement.innerHTML = '门店回本分析报告';
  document.body.appendChild(titleElement);
  
  try {
    // 截图标题
    const titleCanvas = await html2canvas(titleElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });
    
    const titleImgData = titleCanvas.toDataURL('image/png');
    const titleImgWidth = pageWidth - 2 * margin;
    const titleImgHeight = (titleCanvas.height * titleImgWidth) / titleCanvas.width;
    
    pdf.addImage(titleImgData, 'PNG', margin, currentY, titleImgWidth, titleImgHeight);
    currentY += titleImgHeight + 15;
    
    // 移除临时元素
    document.body.removeChild(titleElement);
    
  } catch (error) {
    console.error('标题截图失败:', error);
    // 移除临时元素
    if (document.body.contains(titleElement)) {
      document.body.removeChild(titleElement);
    }
    
    // 如果截图失败，使用英文标题作为备选方案
    pdf.setFontSize(18);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Store ROI Analysis Report', pageWidth / 2, currentY, { align: 'center' });
    currentY += 15;
  }
  
  // 创建临时的筛选条件HTML元素并截图
  const filterInfoElement = document.createElement('div');
  filterInfoElement.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 600px;
    padding: 20px;
    background: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
  `;
  
  filterInfoElement.innerHTML = `
    <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #1f2937;">筛选条件</div>
    <div style="margin-bottom: 8px;"><strong>门店范围:</strong> ${filterText.storesCN}</div>
    <div style="margin-bottom: 8px;"><strong>时间范围:</strong> ${filterText.periodCN}</div>
    <div style="margin-bottom: 8px;"><strong>导出时间:</strong> ${timestamp}</div>
  `;
  
  document.body.appendChild(filterInfoElement);
  
  try {
    // 截图筛选条件
    const filterCanvas = await html2canvas(filterInfoElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });
    
    const filterImgData = filterCanvas.toDataURL('image/png');
    const filterImgWidth = pageWidth - 2 * margin;
    const filterImgHeight = (filterCanvas.height * filterImgWidth) / filterCanvas.width;
    
    pdf.addImage(filterImgData, 'PNG', margin, currentY, filterImgWidth, filterImgHeight);
    currentY += filterImgHeight + 15;
    
    // 移除临时元素
    document.body.removeChild(filterInfoElement);
    
  } catch (error) {
    console.error('筛选条件截图失败:', error);
    // 移除临时元素
    if (document.body.contains(filterInfoElement)) {
      document.body.removeChild(filterInfoElement);
    }
    
    // 如果截图失败，使用英文文本作为备选方案
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Filter Conditions:', margin, currentY);
    currentY += 8;
    
    pdf.setFontSize(10);
    pdf.text(`Stores: ${filterText.stores}`, margin + 10, currentY);
    currentY += 6;
    pdf.text(`Period: ${filterText.period}`, margin + 10, currentY);
    currentY += 6;
    pdf.text(`Export Time: ${timestamp}`, margin + 10, currentY);
    currentY += 15;
  }
  
  // 获取表格元素并转换为canvas
  const tableElement = document.getElementById(tableElementId);
  if (tableElement) {
    try {
      const tableCanvas = await html2canvas(tableElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      const tableImgData = tableCanvas.toDataURL('image/png');
      const tableImgWidth = pageWidth - 2 * margin;
      const tableImgHeight = (tableCanvas.height * tableImgWidth) / tableCanvas.width;
      
      // 检查是否需要新页面
      if (currentY + tableImgHeight > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }
      
      pdf.addImage(tableImgData, 'PNG', margin, currentY, tableImgWidth, tableImgHeight);
      currentY += tableImgHeight + 10;
      
    } catch (error) {
      console.error('表格截图失败:', error);
      // 如果截图失败，添加文本表格
      pdf.setFontSize(12);
      pdf.text('Store ROI Details', margin, currentY);
      currentY += 10;
      
      // 简化的文本表格
      roiData.slice(0, 10).forEach((store, index) => {
        if (currentY > pageHeight - 30) {
          pdf.addPage();
          currentY = margin;
        }
        
        pdf.setFontSize(9);
        const line = `${store.name} | Net Assets: ${formatCurrency(store.netAssets)} | Status: ${store.paybackStatus}`;
        pdf.text(line, margin, currentY);
        currentY += 5;
      });
      
      currentY += 10;
    }
  }
  
  // 添加图表
  for (const chartId of chartsElementIds) {
    const chartElement = document.getElementById(chartId);
    if (chartElement) {
      try {
        // 检查是否需要新页面
        if (currentY > pageHeight - 100) {
          pdf.addPage();
          currentY = margin;
        }
        
        const chartCanvas = await html2canvas(chartElement, {
          scale: 1.5,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        });
        
        const chartImgData = chartCanvas.toDataURL('image/png');
        const chartImgWidth = pageWidth - 2 * margin;
        const chartImgHeight = (chartCanvas.height * chartImgWidth) / chartCanvas.width;
        
        // 限制图表高度
        const maxChartHeight = (pageHeight - 2 * margin) * 0.4;
        const finalChartHeight = Math.min(chartImgHeight, maxChartHeight);
        const finalChartWidth = (chartCanvas.width * finalChartHeight) / chartCanvas.height;
        
        pdf.addImage(chartImgData, 'PNG', margin, currentY, finalChartWidth, finalChartHeight);
        currentY += finalChartHeight + 10;
        
      } catch (error) {
        console.error(`图表 ${chartId} 截图失败:`, error);
      }
    }
  }
  
  // 保存 PDF
  const defaultFilename = `门店回本分析_${new Date().toISOString().slice(0, 10)}.pdf`;
  pdf.save(filename || defaultFilename);
}; 