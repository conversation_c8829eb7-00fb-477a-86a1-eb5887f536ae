import { arrayOutputType, z } from 'zod';

export const ReimbursementDetailSchema = z.object({
    expenseType: z.string().describe('费用类型'),
    expenseDate: z.string().describe('发生日期，格式为YYYY-MM-DD'),
    amount: z.number().describe('费用金额'),
    costCenter: z.string().describe('成本中心'),
    attachmentName: z.string().optional().describe('支持件（附件名称）'),
});

export const ReimbursementMainSchema = z.object({
    approvalNo: z.string().describe('审批编号'),
    applicantName: z.string().describe('申请人姓名'),
    department: z.string().describe('所在部门'),
    type: z.string().describe('报销类型'),
    submitTime: z.string().describe('提交时间，格式为YYYY-MM-DD HH:mm:ss'),
    totalAmount: z.number().describe('总金额'),
    status: z.string().describe('状态'),
    actualPayer: z.string().describe('实际付款人'),
    remarks: z.string().optional().describe('备注，报销事由，团建成员，费用说明'),
    details: z.array(ReimbursementDetailSchema),
});

// 报销单据列表流式响应
export const ReimbursementMainSchemaResponse = z.object({
    message: z.string(),
    data: z.array(ReimbursementMainSchema).optional()
});

export interface Message {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    data?: Array<any>;
    timestamp: string;
    files?: FileUploadResponse[];
    operationType?: string;  // 用户选择的操作类型ID
}

export interface FileUploadResponse {
    pathname: string;
    contentType: string;
    contentDisposition: string;
    url: string;
    downloadUrl: string;
    size?: string;
    fileName: string;
    fileSize: number;
    fileExtension: string;
    fileId: string;
    isUploading?: boolean;
}

export interface CoreOperationsProps {
    operations: Array<{
        id: string;
        label: string;
    }>;
}

// 定义上传文件的类型接口
export interface UploadedFile {
    url: string;               // 文件URL
    fileName?: string;         // 文件名称
    fileType?: string;         // 文件类型
    fileSize?: number;         // 文件大小
    uploadTime?: string;       // 上传时间
}

// 定义识别结果的类型接口
export interface RecognitionResult {
    fileName: string;          // 文件名称
    content?: string;          // 识别的内容
    error?: Error | string;    // 错误信息
    success: boolean;          // 是否成功
    processingTime?: number;   // 处理时间(毫秒)
}

// 处理OCR结果的接口
export interface OCRProcessingResult {
    success: boolean;
    ocrResults?: string;
    error?: {
        message: string;
        details: string[];
    };
}

export const ExcelDataDetail = z.object({
    id: z.union([z.number(), z.string()]).optional(),
    bill_type: z.string(),
    company_name: z.string(),
    store_name: z.string(),
    income_type: z.string(),
    code: z.string(),
    type_name: z.string(),
    bill_amount: z.number().optional(),
    transaction_time: z.string(),
    opposing_unit: z.string(),
    is_split_name: z.string(),
    is_split_record: z.boolean().optional(),  // 是否为均摊记录
    original_bill_id: z.number().optional(),  // 原始账单ID（针对均摊记录）
});

// ==========上传合同============
export const ContractPaymentRecordSchema = z.object({
    id: z.number().optional().describe('主键ID，新增不用返回该字段'),
    store_name: z.string().min(1, "门店名称不能为空").describe('门店名称'),
    store_address: z.string().min(1, "地址不能为空").describe('门店详细地址'),
    payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "日期格式应为YYYY-MM-DD").describe('约定支付日期'),
    expense_type: z.enum(['房租', '物业费']).describe('支出类型'),
    amount: z.number().positive("金额必须为正数").describe('金额(精确到分)'),
});

// 合同支付记录流式响应
export const ContractPaymentRecordResponse = z.object({
    message: z.string(),
    data: z.array(ContractPaymentRecordSchema).optional()
});
