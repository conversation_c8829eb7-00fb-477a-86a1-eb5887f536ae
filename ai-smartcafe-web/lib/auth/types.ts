/**
 * JWT用户载荷
 */
export interface UserJWTPayload {
  /** 用户ID */
  id: number;
  /** 公司ID */
  companyId: number;
  /** 用户角色 */
  role: string;
  /** 用户名 */
  username: string;
  /** 是否为超级管理员 */
  isSuperAdmin: boolean;
  /** 令牌过期时间 (Unix时间戳) */
  exp?: number;
  /** 令牌颁发时间 (Unix时间戳) */
  iat?: number;
}

/**
 * 登录请求
 */
export interface LoginRequest {
  /** 登录标识符 (邮箱/手机号/用户名) */
  identifier: string;
  /** 密码 */
  password: string;
  /** 登录类型 */
  loginType: 'email' | 'phone' | 'username';
  /** 是否记住我 */
  rememberMe?: boolean;
}

/**
 * 登录响应
 */
export interface LoginResponse {
  /** 访问令牌 */
  accessToken: string;
  /** 刷新令牌 */
  refreshToken: string;
  /** 过期时间 (秒) */
  expiresIn: number;
  /** 用户信息 */
  user: {
    /** 用户ID */
    id: number;
    /** 用户名 */
    username: string;
    /** 用户角色 */
    role: string;
    /** 是否为超级管理员 */
    isSuperAdmin: boolean;
  };
}

/**
 * 刷新令牌请求
 */
export interface RefreshTokenRequest {
  /** 刷新令牌 */
  refreshToken: string;
}

/**
 * 刷新令牌响应
 */
export interface RefreshTokenResponse {
  /** 新的访问令牌 */
  accessToken: string;
  /** 过期时间 (秒) */
  expiresIn: number;
}

/**
 * 改密请求
 */
export interface ChangePasswordRequest {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  newPassword: string;
  /** 用户ID (可选，用于在JWT验证失败时直接指定用户) */
  userId?: number;
}

/**
 * 错误响应
 */
export interface ErrorResponse {
  /** 错误消息 */
  message: string;
  /** 错误代码 */
  error?: string;
  /** 错误详情 */
  details?: any;
} 