import bcrypt from 'bcryptjs';
import { NextRequest, NextResponse } from 'next/server';
import { RateLimiterMemory } from 'rate-limiter-flexible';
import { ErrorResponse } from './types';

// 密码盐值轮次
const SALT_ROUNDS = 10;

/**
 * 哈希密码
 * @param password 明文密码
 * @returns 哈希后的密码
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

/**
 * 验证密码
 * @param plainPassword 明文密码
 * @param hashedPassword 哈希后的密码
 * @returns 是否匹配
 */
export async function verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(plainPassword, hashedPassword);
}

/**
 * 转换MD5哈希到bcrypt哈希
 * @param md5Hash MD5哈希值
 * @returns Bcrypt哈希值
 */
export async function convertMD5ToBcrypt(md5Hash: string): Promise<string> {
  return bcrypt.hash(md5Hash, SALT_ROUNDS);
}

/**
 * 验证MD5密码
 * @param plainPassword 明文密码
 * @param md5Hash MD5哈希
 * @returns 是否匹配
 */
export function verifyMD5Password(plainPassword: string, md5Hash: string): boolean {
  const crypto = require('crypto');
  const inputHash = crypto.createHash('md5').update(plainPassword).digest('hex');
  return inputHash === md5Hash;
}

// 创建API限流器实例
const loginRateLimiter = new RateLimiterMemory({
  points: 5, // 5次尝试
  duration: 60 * 15, // 15分钟
  keyPrefix: 'login', // 键前缀
});

/**
 * API限流中间件
 * @param request 请求对象
 * @param identifier 用户标识符 (IP/用户名)
 * @returns 错误响应或null
 */
export async function rateLimiterMiddleware(
  request: NextRequest,
  identifier?: string
): Promise<NextResponse<ErrorResponse> | null> {
  try {
    // 使用IP地址或提供的标识符作为限流键
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    const key = identifier ? `${ip}:${identifier}` : ip;
    
    // 消耗一个点数
    await loginRateLimiter.consume(key);
    return null; // 未达到限制
  } catch (rateLimiterRes: any) {
    // 达到限制，返回429错误
    console.warn(`请求被限制，IP: ${request.headers.get('x-forwarded-for') || 'unknown'}`);
    
    // 计算剩余锁定时间
    const remainingTimeSeconds = Math.ceil(rateLimiterRes.msBeforeNext / 1000) || 60 * 15;
    
    return NextResponse.json(
      {
        message: `登录尝试次数过多，请在${Math.ceil(remainingTimeSeconds / 60)}分钟后再试`,
        error: 'TOO_MANY_REQUESTS',
      },
      {
        status: 429,
        headers: {
          'Retry-After': String(remainingTimeSeconds),
          'X-RateLimit-Limit': '5',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': String(Math.ceil(Date.now() / 1000) + remainingTimeSeconds),
        },
      }
    );
  }
}

/**
 * 清理输入，防止XSS攻击
 * @param input 输入字符串
 * @returns 清理后的输入
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim();
}

/**
 * 验证登录参数
 * @param identifier 标识符
 * @param password 密码
 * @param loginType 登录类型
 * @returns 错误消息或null
 */
export function validateLoginParams(
  identifier: string, 
  password: string, 
  loginType: string
): string | null {
  // 检查必填字段
  if (!identifier || !password) {
    return '用户名和密码不能为空';
  }
  
  // 检查登录类型
  if (!['email', 'phone', 'username'].includes(loginType)) {
    return '无效的登录类型';
  }
  
  // 检查标识符格式
  if (loginType === 'email') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(identifier)) {
      return '无效的邮箱格式';
    }
  } else if (loginType === 'phone') {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(identifier)) {
      return '无效的手机号格式';
    }
  }
  
  return null; // 验证通过
} 