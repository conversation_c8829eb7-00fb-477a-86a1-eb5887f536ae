import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

/**
 * 创建数据库连接
 * @returns MySQL连接对象
 */
export async function createDbConnection(): Promise<mysql.Connection> {
  try {
    return await mysql.createConnection(dbConfig);
  } catch (error) {
    console.error('数据库连接失败:', error);
    throw new Error('无法连接到数据库');
  }
}

/**
 * 根据登录类型获取查询字段名
 * @param loginType 登录类型
 * @returns 对应的数据库字段名
 */
export function getFieldNameByLoginType(loginType: string): string {
  switch (loginType) {
    case 'email':
      return 'email';
    case 'phone':
      return 'contact_info';
    case 'username':
    default:
      return 'username';
  }
}

/**
 * 记录登录日志
 * @param userId 用户ID
 * @param ip IP地址
 * @param userAgent 用户代理
 * @param success 是否成功
 * @param message 消息
 */
export async function logLoginAttempt(
  userId: number | null,
  ip: string,
  userAgent: string | null,
  success: boolean,
  message: string = ''
): Promise<void> {
  try {
    const connection = await createDbConnection();
    await connection.execute(
      `INSERT INTO login_logs (user_id, ip_address, user_agent, success, message, login_time) 
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [userId, ip, userAgent, success ? 1 : 0, message]
    );
    await connection.end();
  } catch (error) {
    // 记录日志失败不应影响主流程
    console.error('记录登录日志失败:', error);
  }
}

/**
 * 根据用户ID获取用户角色和权限
 * @param userId 用户ID
 * @returns 角色和权限
 */
export async function getUserRolesAndPermissions(userId: number): Promise<{
  role: string;
  permissions: string[];
}> {
  try {
    const connection = await createDbConnection();
    const [rows] = await connection.execute(
      'SELECT role FROM account WHERE id = ? AND is_active = TRUE AND deleted_at IS NULL',
      [userId]
    ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];
    
    await connection.end();
    
    if (rows.length === 0) {
      throw new Error('用户不存在或已被禁用');
    }
    
    const role = rows[0].role;
    
    // 根据角色获取权限
    // 这里只是示例，实际应该从权限表中查询
    const permissions = [];
    
    return {
      role,
      permissions
    };
  } catch (error) {
    console.error('获取用户角色和权限失败:', error);
    throw new Error('获取用户角色和权限失败');
  }
}

/**
 * 获取锁定的账户
 * @returns 锁定的账户ID列表
 */
export async function getLockedAccounts(): Promise<number[]> {
  try {
    const connection = await createDbConnection();
    const [rows] = await connection.execute(
      'SELECT id FROM account WHERE is_active = FALSE AND deleted_at IS NULL'
    ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];
    
    await connection.end();
    
    return rows.map(row => row.id);
  } catch (error) {
    console.error('获取锁定账户失败:', error);
    return [];
  }
} 