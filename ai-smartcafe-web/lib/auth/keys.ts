import forge from 'node-forge';

// 尝试从环境变量中获取密钥
let privateKey: string | null = process.env.JWT_PRIVATE_KEY || null;
let publicKey: string | null = process.env.JWT_PUBLIC_KEY || null;

// 生成RSA密钥对
export function generateRSAKeyPair() {
  try {
    // 只在密钥尚未生成时生成
    if (!privateKey || !publicKey) {
      console.log('未找到环境变量中的JWT密钥，生成临时密钥对');
      const pki = forge.pki;
      const keyPair = pki.rsa.generateKeyPair({ bits: 2048 });
      
      // 转换为PKCS#8格式以兼容'jose'库
      const privateKeyInfo = pki.wrapRsaPrivateKey(pki.privateKeyToAsn1(keyPair.privateKey));
      privateKey = pki.privateKeyInfoToPem(privateKeyInfo);

      publicKey = forge.pki.publicKeyToPem(keyPair.publicKey);
    }
    return { privateKey, publicKey };
  } catch (error) {
    console.error('生成RSA密钥对时出错:', error);
    throw new Error('无法生成RSA密钥对');
  }
}

// 获取RSA密钥对 - 如果还没有生成，则生成一对
export function getRSAKeyPair() {
  if (!privateKey || !publicKey) {
    console.log('初始化RSA密钥对');
    if (process.env.JWT_PRIVATE_KEY && process.env.JWT_PUBLIC_KEY) {
      console.log('使用环境变量中的RSA密钥对');
      privateKey = process.env.JWT_PRIVATE_KEY;
      publicKey = process.env.JWT_PUBLIC_KEY;
      return { privateKey, publicKey };
    } else {
      console.log('环境变量中未找到RSA密钥对，生成临时密钥');
      return generateRSAKeyPair();
    }
  }
  return { privateKey, publicKey };
}

/**
 * 生成适合.env文件使用的RSA密钥对
 * 调用此函数并将输出复制到.env文件中
 */
export function generateEnvKeyPair() {
  const pki = forge.pki;
  // 生成新的密钥对
  const keyPair = pki.rsa.generateKeyPair({ bits: 2048 });

  // 转换为PKCS#8格式
  const privateKeyInfo = pki.wrapRsaPrivateKey(pki.privateKeyToAsn1(keyPair.privateKey));
  const privKey = pki.privateKeyInfoToPem(privateKeyInfo);
  const pubKey = pki.publicKeyToPem(keyPair.publicKey);
  
  // 转换为适合环境变量的格式 (将换行符替换为\n)
  const formattedPrivKey = privKey.replace(/\r?\n/g, '\\n');
  const formattedPubKey = pubKey.replace(/\r?\n/g, '\\n');
  
  console.log('=== 复制以下内容到.env文件中 ===');
  console.log(`JWT_PRIVATE_KEY="${formattedPrivKey}"`);
  console.log(`JWT_PUBLIC_KEY="${formattedPubKey}"`);
  console.log('=== 复制结束 ===');
  
  return { privateKey: formattedPrivKey, publicKey: formattedPubKey };
}

// 在生产环境中，应该使用以下方式从环境变量加载密钥：
// export function loadKeysFromEnv() {
//   privateKey = process.env.JWT_PRIVATE_KEY;
//   publicKey = process.env.JWT_PUBLIC_KEY;
//   if (!privateKey || !publicKey) {
//     throw new Error('JWT密钥未配置');
//   }
// }

/**
 * 检查环境变量中的JWT密钥是否正确加载
 * 此函数仅用于开发/调试
 */
export function checkJWTKeys() {
  console.log('=== JWT密钥检查 ===');
  
  const privateKeyB64 = process.env.JWT_PRIVATE_KEY;
  const publicKeyB64 = process.env.JWT_PUBLIC_KEY;

  if (privateKeyB64 && publicKeyB64) {
    const privKeyLength = privateKeyB64.length;
    const pubKeyLength = publicKeyB64.length;
    
    console.log(`✓ JWT_PRIVATE_KEY 已配置 (长度: ${privKeyLength}字符)`);
    console.log(`✓ JWT_PUBLIC_KEY 已配置 (长度: ${pubKeyLength}字符)`);
    
    try {
      // 从Base64解码密钥以检查其内容
      const privateKey = Buffer.from(privateKeyB64.replace(/"/g, ''), 'base64').toString('utf-8');
      const publicKey = Buffer.from(publicKeyB64.replace(/"/g, ''), 'base64').toString('utf-8');

      // 检查密钥格式
      const hasPrivKeyHeader = privateKey.includes('BEGIN PRIVATE KEY');
      const hasPubKeyHeader = publicKey.includes('BEGIN PUBLIC KEY');
      
      const keysValid = hasPrivKeyHeader && hasPubKeyHeader;

      if (keysValid) {
        console.log('✓ 密钥在Base64解码后格式正确');
        console.log('√ JWT密钥配置有效！');
      } else {
        console.log('⚠ 警告: 密钥在Base64解码后格式不正确');
        if (!hasPrivKeyHeader) console.log('  - JWT_PRIVATE_KEY 缺少正确的头部 (需要 "BEGIN PRIVATE KEY")');
        if (!hasPubKeyHeader) console.log('  - JWT_PUBLIC_KEY 缺少正确的头部');
      }

      return {
        privateKeyConfigured: true,
        publicKeyConfigured: true,
        keysValid,
        message: keysValid 
          ? 'JWT密钥配置有效，登录后的令牌将正常工作' 
          : 'JWT密钥配置无效或不完整，可能导致令牌验证失败'
      };

    } catch (e: any) {
      console.log('⚠ 错误: 无法对密钥进行Base64解码. 请确保它们是有效的Base64字符串。', e.message);
      return {
        privateKeyConfigured: true,
        publicKeyConfigured: true,
        keysValid: false,
        message: 'JWT密钥无法被Base64解码，请从 /api/generate-keys 重新生成。'
      };
    }
  } else {
    console.log('⚠ 警告: 环境变量中未找到JWT密钥');
    if (!privateKeyB64) console.log('  - JWT_PRIVATE_KEY 未配置');
    if (!publicKeyB64) console.log('  - JWT_PUBLIC_KEY 未配置');
    
    return {
      privateKeyConfigured: !!privateKeyB64,
      publicKeyConfigured: !!publicKeyB64,
      keysValid: false,
      message: "一个或多个JWT密钥未配置。"
    };
  }
} 