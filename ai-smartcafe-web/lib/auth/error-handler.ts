/**
 * 处理401未授权错误的辅助函数
 * 用于触发全局自定义事件，通知TokenRefreshHandler刷新令牌
 * 
 * 使用方法:
 * ```ts
 * try {
 *   const response = await fetch('/api/protected-endpoint');
 *   if (!response.ok) {
 *     if (response.status === 401) {
 *       // 尝试处理未授权错误
 *       const retryCallback = () => fetchData(); // 定义重试函数
 *       const handled = handleUnauthorized(response, retryCallback);
 *       if (handled) return; // 如果正在处理，则退出当前函数
 *     }
 *     throw new Error(`请求失败: ${response.status}`);
 *   }
 *   // 处理成功响应...
 * } catch (error) {
 *   console.error('请求出错:', error);
 * }
 * ```
 * 
 * @param response 包含401状态码的响应对象
 * @param retryCallback 可选的重试回调函数
 * @returns 布尔值，表示是否正在处理未授权错误
 */
export function handleUnauthorized(response: Response, retryCallback?: () => void): boolean {
  if (response.status === 401) {
    // 在窗口对象上触发自定义事件
    if (typeof window !== 'undefined') {
      const unauthorizedEvent = new CustomEvent('unauthorized-error', {
        detail: {
          status: 401,
          message: '未授权，尝试刷新令牌',
          retry: retryCallback
        }
      });
      
      window.dispatchEvent(unauthorizedEvent);
      return true;
    }
  }
  
  return false;
}

/**
 * HTTP请求封装，自动处理401错误
 * @param url 请求URL
 * @param options 请求选项
 * @returns 响应Promise
 */
export async function fetchWithTokenRefresh<T = any>(
  url: string, 
  options?: RequestInit
): Promise<T> {
  // 获取访问令牌
  const accessToken = typeof window !== 'undefined' 
    ? sessionStorage.getItem('accessToken') 
    : null;
  
  // 合并请求选项
  const requestOptions: RequestInit = {
    ...options,
    headers: {
      ...options?.headers,
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    }
  };
  
  // 定义重试函数
  const retryFetch = async (): Promise<T> => {
    // 获取最新的访问令牌
    const newToken = sessionStorage.getItem('accessToken');
    
    const newOptions: RequestInit = {
      ...requestOptions,
      headers: {
        ...requestOptions.headers,
        'Authorization': `Bearer ${newToken}`
      }
    };
    
    const retryResponse = await fetch(url, newOptions);
    if (!retryResponse.ok) {
      throw new Error(`请求失败: ${retryResponse.status}`);
    }
    
    return await retryResponse.json();
  };
  
  try {
    // 发送请求
    const response = await fetch(url, requestOptions);
    
    // 处理响应
    if (!response.ok) {
      // 处理401错误
      if (response.status === 401) {
        const handled = handleUnauthorized(response, retryFetch);
        if (handled) {
          // 返回一个Promise，等待令牌刷新和重试
          return new Promise((resolve, reject) => {
            // 使用一次性事件监听器，等待令牌刷新成功
            const handleRefreshSuccess = () => {
              retryFetch()
                .then(resolve)
                .catch(reject);
              
              // 移除事件监听器
              window.removeEventListener('token-refreshed', handleRefreshSuccess);
            };
            
            // 使用一次性事件监听器，处理令牌刷新失败
            const handleRefreshFailure = () => {
              reject(new Error('令牌刷新失败'));
              
              // 移除事件监听器
              window.removeEventListener('token-refresh-failed', handleRefreshFailure);
            };
            
            window.addEventListener('token-refreshed', handleRefreshSuccess);
            window.addEventListener('token-refresh-failed', handleRefreshFailure);
            
            // 超时处理，避免无限等待
            setTimeout(() => {
              window.removeEventListener('token-refreshed', handleRefreshSuccess);
              window.removeEventListener('token-refresh-failed', handleRefreshFailure);
              reject(new Error('令牌刷新超时'));
            }, 10000);
          });
        }
      }
      
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('请求出错:', error);
    throw error;
  }
} 