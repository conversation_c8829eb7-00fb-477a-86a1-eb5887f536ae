import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyJwt } from './lib/auth';

interface UserJwtPayload {
    userId: number;
    companyId: number;
    username: string;
    role: string;
}

const publicPaths = [
    '/login',
    '/register',
    '/',
    '/api/login',
    '/api/company-profile/register',
    '/api/auth/logout',
    '/api/auth/refresh',
    '/api/generate-keys', // for dev
    '/api/check-jwt-keys' // for dev/debug
];

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Check if the path is public
  const isPublicPath = publicPaths.some(publicPath => {
    if (publicPath.endsWith('/**')) {
      return path.startsWith(publicPath.slice(0, -3));
    }
    return path === publicPath;
  });

  if (isPublicPath) {
    return NextResponse.next();
  }

  // All other paths are protected, verify JWT
  const token = request.cookies.get('access_token')?.value;

  if (!token) {
    if (path.startsWith('/api/')) {
        return new NextResponse(JSON.stringify({ message: 'Authentication required' }), { status: 401, headers: { 'Content-Type': 'application/json' } });
    }
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  try {
    const decoded = await verifyJwt<UserJwtPayload>(token);
    if (!decoded) {
        throw new Error("Invalid token payload");
    }
    
    // Token is valid, add payload to headers and continue
    const requestHeaders = new Headers(request.headers);
    // Encode the payload to Base64 to handle non-ASCII characters
    const encodedPayload = Buffer.from(JSON.stringify(decoded)).toString('base64');
    requestHeaders.set('X-User-Payload', encodedPayload);

    // Continue to the requested path with new headers
    return NextResponse.next({
        request: {
            headers: requestHeaders,
        },
    });

  } catch (error) {
    console.error(`JWT Verification failed for path: ${path}`, error);
    
    if (path.startsWith('/api/')) {
        return new NextResponse(JSON.stringify({ message: 'Invalid or expired token' }), { status: 401, headers: { 'Content-Type': 'application/json' } });
    }

    // For page requests, redirect to home. 
    // This handles cases where a token exists but is invalid/expired.
    const response = NextResponse.redirect(new URL('/', request.url));
    // It's good practice to clear the invalid cookie
    response.cookies.delete('access_token');
    response.cookies.delete('refresh_token');
    return response;
  }
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    '/pages/:path*',
    '/api/:path*',
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
} 