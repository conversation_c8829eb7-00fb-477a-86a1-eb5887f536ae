{"name": "excel-online", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.0", "@ai-sdk/google": "^1.0.0", "@ai-sdk/openai": "^1.0.0", "@ai-sdk/react": "^1.0.0", "@ai-sdk/xai": "^1.0.0", "@alicloud/credentials": "^2.4.0", "@alicloud/darabonba-stream": "^0.0.2", "@alicloud/ocr-api20210707": "^3.1.2", "@alicloud/openapi-client": "^0.4.13", "@alicloud/pop-core": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@ant-design/icons": "^5.2.6", "@google/generative-ai": "^0.2.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.10.7", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.7", "@uploadthing/react": "^5.7.0", "@vercel/blob": "^0.16.1", "ai": "^2.2.31", "ali-oss": "^6.18.1", "antd": "^5.12.8", "date-fns": "^3.0.6", "dayjs": "^1.11.10", "echarts": "^5.4.3", "framer-motion": "^10.18.0", "mysql2": "^3.6.5", "next": "^13.4.19", "next-themes": "^0.2.1", "openai": "^4.23.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.21.1", "recharts": "^2.10.3", "remark-gfm": "^4.0.0", "sonner": "^1.2.4", "uploadthing": "^5.7.4", "uuid": "^9.0.1", "xlsx": "^0.18.5", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^2.1.3", "@types/ali-oss": "^6.16.11", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "eslint": "^8.56.0", "eslint-config-next": "13.4.19", "lucide-react": "^0.302.0", "postcss": "^8.4.32", "tailwind-merge": "^2.2.0", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}