{"name": "excel-online", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.0", "@ai-sdk/google": "^1.2.1", "@ai-sdk/openai": "^1.3.0", "@ai-sdk/react": "^1.2.0", "@ai-sdk/xai": "^1.2.1", "@alicloud/credentials": "^2.4.0", "@alicloud/darabonba-stream": "^0.0.2", "@alicloud/ocr-api20210707": "^3.1.2", "@alicloud/openapi-client": "^0.4.13", "@alicloud/pop-core": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@ant-design/icons": "^6.0.0", "@google/generative-ai": "^0.24.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.75.1", "@tanstack/react-table": "^8.21.2", "@types/axios": "^0.14.4", "@types/bcryptjs": "^3.0.0", "@types/cookie": "^1.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node-forge": "^1.3.11", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "@uploadthing/react": "^7.3.0", "@vercel/blob": "^0.27.3", "ai": "^4.2.8", "ali-oss": "^6.22.0", "antd": "^5.24.6", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.4.7", "graceful-fs": "^4.2.11", "html2canvas": "^1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mysql2": "^3.14.0", "next": "^15.2.1", "next-themes": "^0.4.6", "node-forge": "^1.3.1", "openai": "^4.85.4", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.1.1", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.4.0", "react-scroll": "^1.9.3", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "uploadthing": "^7.5.2", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/ali-oss": "^6.16.11", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-scroll": "^1.8.10", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.1.7", "lucide-react": "^0.475.0", "postcss": "^8.5.3", "tailwind-merge": "^3.0.2", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}