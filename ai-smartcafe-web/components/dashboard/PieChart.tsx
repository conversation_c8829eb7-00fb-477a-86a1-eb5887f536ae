
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

interface PieChartProps {
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  height?: number;
}

export function PieChart({ data, height = 200 }: PieChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsPieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
          label={({ name, percent }) => (
            <text
              x={0}
              y={0}
              textAnchor="middle"
              className="text-xs"
            >
              {`${name}: ${(percent * 100).toFixed(0)}%`}
            </text>
          )}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip 
          formatter={(value) => `¥${value.toLocaleString()}`}
          contentStyle={{ 
            backgroundColor: 'white', 
            border: 'none', 
            borderRadius: '8px', 
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            fontSize: '12px'
          }}
        />
        <Legend 
          wrapperStyle={{
            fontSize: '12px'
          }}
        />
      </RechartsPieChart>
    </ResponsiveContainer>
  );
}
