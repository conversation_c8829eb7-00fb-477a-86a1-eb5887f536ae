
import { Card } from "@/components/ui/card";
import { ReactNode } from "react";

interface ChartCardProps {
  title?: string;
  children?: ReactNode; // Changed from required to optional
  description?: string;
  className?: string;
  footer?: ReactNode;
  headerContent?: ReactNode;
}

export function ChartCard({ 
  title, 
  children, 
  description, 
  className = "", 
  footer,
  headerContent
}: ChartCardProps) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="p-6">
        {headerContent ? (
          <div>{headerContent}</div>
        ) : (
          title && (
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold">{title}</h3>
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
          )
        )}
        {children && <div className={headerContent || !title ? "mt-0" : "mt-3"}>{children}</div>}
      </div>
      {footer && (
        <div className="bg-muted/30 px-6 py-3 border-t">{footer}</div>
      )}
    </Card>
  );
}
