import { TooltipProvider, <PERSON><PERSON><PERSON>, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";

interface AIAssistantButtonProps {
  showAIChat: boolean;
  setShowAIChat: (value: boolean) => void;
}

export const AIAssistantButton = ({ showAIChat, setShowAIChat }: AIAssistantButtonProps) => {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button 
              onClick={() => router.push("/pages/inventory")}
              className="group relative flex items-center justify-center transition-all"
            >
              <div className="absolute -inset-0.5 rounded-xl bg-gradient-to-r from-violet-500 to-purple-500 opacity-30 blur transition-all group-hover:opacity-70" />
              <img 
                src="/image/robot.png"
                alt="AI Assistant" 
                className="relative w-14 h-14 rounded-xl cursor-pointer transition-transform group-hover:scale-105" 
              />
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>AI助手 - 点击与系统对话</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <div className="flex items-center mt-2 text-xs font-medium text-violet-600">
        <div className="h-2 w-2 rounded-full bg-violet-500 mr-1.5 animate-pulse" />
        <span>智能AI助手</span>
      </div>
    </div>
  );
};
