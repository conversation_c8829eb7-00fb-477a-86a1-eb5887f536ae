import { Building2, CalendarDays } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useEffect, useMemo, useState } from "react";

interface Store {
  id: number;
  company_name: string;
  store_name: string;
  brand: string;
  remarks: string;
}

interface EntitySelectorProps {
  selectedEntity: string;
  setSelectedEntity: (value: string) => void;
  selectedMonth: string;
  setSelectedMonth: (value: string) => void;
  monthlyData: Record<string, any>;
}

export const EntitySelector = ({
  selectedEntity,
  setSelectedEntity,
  selectedMonth,
  setSelectedMonth,
  monthlyData
}: EntitySelectorProps) => {
  const [stores, setStores] = useState<Store[]>([]);

  // 获取门店数据
  useEffect(() => {
    const fetchStores = async () => {
      try {
        const response = await fetch('/api/stores');
        const result = await response.json();
        if (result.success) {
          setStores(result.data);
        }
      } catch (error) {
        console.error('获取门店数据失败:', error);
      }
    };

    fetchStores();
  }, []);

  // 聚合门店数据
  const groupedStores = useMemo(() => {
    const groups: Record<string, Store[]> = {};
    
    // 按品牌分组
    stores.forEach(store => {
      if (!groups[store.brand]) {
        groups[store.brand] = [];
      }
      groups[store.brand].push(store);
    });

    return groups;
  }, [stores]);

  // 转换月份数据为"年-月"格式
  const formattedMonthlyData = useMemo(() => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // 当前月份（1-12）
    
    // 创建过去24个月的数据（包括当前月）
    const months: Record<string, any> = {};
    for (let i = 0; i < 24; i++) {
      let monthValue = currentMonth - i;
      let yearValue = currentYear;
      
      // 处理月份为负数的情况
      while (monthValue <= 0) {
        monthValue += 12;
        yearValue -= 1;
      }
      
      // 创建"年-月"格式的键
      const formattedKey = `${yearValue}-${String(monthValue).padStart(2, '0')}`;
      
      // 找到对应的原始数据
      const originalKey = `${monthValue}月`;
      if (originalKey in monthlyData) {
        months[formattedKey] = monthlyData[originalKey];
      } else {
        // 如果原始数据中没有这个月份，创建一个空数据
        months[formattedKey] = {
          cashBalance: 0,
          totalExpenses: 0,
          totalIncome: 0,
          pendingReimbursements: 0
        };
      }
    }
    
    return months;
  }, [monthlyData]);
  
  // 获取上个月的年-月格式
  const getPreviousMonth = useMemo(() => {
    const today = new Date();
    let prevMonth = today.getMonth(); // 获取上个月 (0-11)
    let prevYear = today.getFullYear();
    
    if (prevMonth === 0) {
      prevMonth = 12;
      prevYear -= 1;
    }
    
    return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
  }, []);
  
  // 判断月份是否是未来月份
  const isFutureMonth = (monthKey: string) => {
    const [year, month] = monthKey.split('-').map(Number);
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // 当前月份（1-12）
    
    // 修改判断逻辑，允许选择当前月份
    return (year > currentYear) || (year === currentYear && month > currentMonth);
  };
  
  // 初始化选择上个月
  useEffect(() => {
    if (!selectedMonth) {
      setSelectedMonth(getPreviousMonth);
    }
  }, [getPreviousMonth, selectedMonth, setSelectedMonth]);

  return (
    <div className="flex-1 flex flex-col">
      <div className="mb-2">
        <h2 className="text-2xl font-bold text-foreground tracking-tight">
          选择门店及统计时间
        </h2>
      </div>
      <div className="flex items-center justify-between bg-white/80 backdrop-blur-sm px-6 py-3 rounded-2xl border border-border/40 shadow-sm">
        <div className="flex items-center w-full space-x-4">
          <div className="flex-1 flex items-center gap-2">
            <Select value={selectedEntity} onValueChange={setSelectedEntity}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectTrigger className="w-full bg-white border-0 shadow-sm hover:bg-accent/5 transition-colors">
                      <Building2 className="mr-2 h-4 w-4 text-violet-500" />
                      <SelectValue placeholder="选择公司/门店" />
                    </SelectTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>选择要查看的公司或门店数据</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <SelectContent>
                {/* 总公司选项 */}
                <SelectItem value="总公司">
                  <div className="flex items-center">
                    <span>总公司</span>
                  </div>
                </SelectItem>

                {/* 按品牌分组展示门店 */}
                {Object.entries(groupedStores).map(([brand, brandStores]) => (
                  <div key={brand}>
                    {/* 品牌选项 */}
                    <SelectItem value={brand} className="pl-12">
                      <div className="flex items-center">
                        <span>{brand}</span>
                      </div>
                    </SelectItem>
                    
                    {/* 该品牌下的门店 */}
                    {brandStores.map(store => (
                      <SelectItem 
                        key={store.id} 
                        value={store.store_name}
                        className="pl-16"
                      >
                        <div className="flex items-center">
                          <span>{store.store_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="h-8 w-px bg-border/40" />

          <div className="flex-1 flex items-center gap-2">
            <Select 
              value={selectedMonth} 
              onValueChange={(value) => {
                if (!isFutureMonth(value)) {
                  setSelectedMonth(value);
                }
              }}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectTrigger className="w-full bg-white border-0 shadow-sm hover:bg-accent/5 transition-colors">
                      <CalendarDays className="mr-2 h-4 w-4 text-violet-500" />
                      <SelectValue placeholder="选择月份" />
                    </SelectTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>选择要查看的月份数据</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <SelectContent
                align="start"
                side="bottom"
                position="popper"
                sideOffset={4}
                className="max-h-[280px] overflow-y-auto"
                avoidCollisions={true}
              >
                {Object.keys(formattedMonthlyData)
                  .sort((a, b) => b.localeCompare(a)) // 修改排序顺序，最新的月份显示在前面
                  .map(month => (
                    <SelectItem 
                      key={month} 
                      value={month}
                      disabled={isFutureMonth(month)}
                    >
                      {month}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};
