
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface FeatureCardProps {
  title: string;
  icon: ReactNode;
  color?: string;
  onClick?: () => void;
  className?: string;
}

export function FeatureCard({ 
  title, 
  icon, 
  color = "bg-dashboard-blue", 
  onClick, 
  className 
}: FeatureCardProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center p-4 h-28 rounded-xl bg-card transition-all duration-300 border border-border/40 hover:border-primary/30 hover:shadow-md animate-fade-in",
        className
      )}
    >
      <div className={cn("flex items-center justify-center w-12 h-12 rounded-lg mb-2", color)}>
        {icon}
      </div>
      <span className="text-sm font-medium mt-1">{title}</span>
    </button>
  );
}
