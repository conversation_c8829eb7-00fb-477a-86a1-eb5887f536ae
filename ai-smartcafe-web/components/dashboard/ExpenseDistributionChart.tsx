
import React, { useState } from 'react';
import { Bar<PERSON><PERSON> } from "./BarChart";
import { But<PERSON> } from "@/components/ui/button";

// Sample data for expense types
const expenseTypeData = [
  { name: "采购", value: 17500 },
  { name: "人工", value: 10500 },
  { name: "运营", value: 7000 },
];

// Sample data for expense categories filtered by type
const expenseByPurchaseType = [
  { name: "原材料", value: 10000 },
  { name: "设备", value: 5000 },
  { name: "杂项", value: 2500 },
];

const expenseByLaborType = [
  { name: "工资", value: 8000 },
  { name: "福利", value: 1500 },
  { name: "培训", value: 1000 },
];

const expenseByOperationType = [
  { name: "租金", value: 3000 },
  { name: "水电", value: 2000 },
  { name: "市场", value: 2000 },
];

type FilterType = "全部" | "采购" | "人工" | "运营";

export function ExpenseDistributionChart() {
  const [filter, setFilter] = useState<FilterType>("全部");

  const getChartData = () => {
    switch (filter) {
      case "全部": return expenseTypeData;
      case "采购": return expenseByPurchaseType;
      case "人工": return expenseByLaborType;
      case "运营": return expenseByOperationType;
      default: return expenseTypeData;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-semibold">支出分布</h3>
          <p className="text-sm text-muted-foreground mt-1">本月支出的类型分布</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant={filter === "全部" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("全部")}
          >
            全部
          </Button>
          <Button 
            variant={filter === "采购" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("采购")}
          >
            采购
          </Button>
          <Button 
            variant={filter === "人工" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("人工")}
          >
            人工
          </Button>
          <Button 
            variant={filter === "运营" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("运营")}
          >
            运营
          </Button>
        </div>
      </div>
      <BarChart data={getChartData()} color="#4299e1" height={240} />
    </div>
  );
}
