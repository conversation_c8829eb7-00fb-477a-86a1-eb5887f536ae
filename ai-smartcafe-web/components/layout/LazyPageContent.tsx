"use client";

import { useState, useEffect, Suspense } from "react";

interface LazyPageContentProps {
  children: React.ReactNode;
}

function LoadingFallback() {
  return (
    <div className="w-full h-full min-h-[200px] flex items-center justify-center">
      <div className="h-8 w-8 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
    </div>
  );
}

export function LazyPageContent({ children }: LazyPageContentProps) {
  const [isClient, setIsClient] = useState(false);
  
  // 客户端渲染标记
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // 如果不是客户端渲染，显示加载状态
  if (!isClient) {
    return <LoadingFallback />;
  }
  
  return (
    <Suspense fallback={<LoadingFallback />}>
      <div className="h-full transition-opacity duration-300">
        {children}
      </div>
    </Suspense>
  );
}
