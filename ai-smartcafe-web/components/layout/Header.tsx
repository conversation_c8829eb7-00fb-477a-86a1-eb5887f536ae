"use client";
import { Bell, LogOut, Search, Settings } from "lucide-react";
import { useRouter } from "next/navigation";

export function Header() {
  const router = useRouter();
  const today = new Date().toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const handleLogout = async () => {
    try {
      // 清除客户端sessionStorage中的所有认证相关数据
      sessionStorage.removeItem('accessToken');
      sessionStorage.removeItem('refreshToken');
      sessionStorage.removeItem('userInfo');
      sessionStorage.removeItem('tokenData');
      
      // 如果存在定时器，清除它
      if (window.tokenRefreshTimerId) {
        clearTimeout(window.tokenRefreshTimerId);
      }
      
      // 调用服务器端登出API清除cookie
      const response = await fetch("/api/auth/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        // 重定向到首页
        router.push("/");
      } else {
        console.error("登出失败");
        // 即使API调用失败，也强制重定向到首页
        router.push("/");
      }
    } catch (error) {
      console.error("登出请求异常:", error);
      // 即使发生异常，也强制重定向到首页
      router.push("/");
    }
  };

  return (
    <header className="border-b border-border/40 bg-background/80 backdrop-blur-sm h-16 flex items-center px-6 sticky top-0 z-30">
      <div className="flex items-center gap-4 w-full">
        {/* <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search..."
            className="h-10 w-full rounded-full border border-input bg-background px-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          />
        </div> */}
        <div className="ml-auto flex items-center space-x-4">
          <span className="text-sm text-muted-foreground">{today}</span>
          <div className="flex items-center space-x-1">
            <button className="rounded-full p-2 hover:bg-accent/10">
              <Bell className="h-5 w-5 text-muted-foreground" />
            </button>
            <button className="rounded-full p-2 hover:bg-accent/10">
              <Settings className="h-5 w-5 text-muted-foreground" />
            </button>
            <button 
              className="rounded-full p-2 hover:bg-accent/10"
              onClick={handleLogout}
              title="退出登录"
            >
              <LogOut className="h-5 w-5 text-muted-foreground" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
