import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

/**
 * 全局令牌刷新组件
 * 1. 在应用启动时设置令牌刷新计时器
 * 2. 监听401未授权错误，自动刷新令牌
 */
export default function TokenRefreshHandler() {
  const router = useRouter();

  /**
   * 刷新访问令牌
   */
  const refreshToken = useCallback(async () => {
    try {
      // 从sessionStorage获取刷新令牌
      const refreshToken = sessionStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        console.warn('刷新令牌不存在');
        return false;
      }
      
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });
      
      if (!response.ok) {
               console.error('刷新令牌失败:', response.status);
       
       // 触发令牌刷新失败事件
       window.dispatchEvent(new CustomEvent('token-refresh-failed'));
       
       // 跳转到登录页
       router.push(`/login?redirect=${encodeURIComponent(window.location.pathname)}`);
       return false;
      }
      
      const data = await response.json();
      
             // 保存新的访问令牌
       sessionStorage.setItem('accessToken', data.accessToken);
       
       // 保存令牌过期信息
       if (data.expiresIn) {
         const expiresAt = Date.now() + (data.expiresIn * 1000);
         sessionStorage.setItem('tokenData', JSON.stringify({ 
           expiresAt,
           expiresIn: data.expiresIn
         }));
         setupRefreshTimer(data.expiresIn);
       }
       
       // 触发令牌刷新成功事件
       window.dispatchEvent(new CustomEvent('token-refreshed'));
       
       return true;
    } catch (error) {
      console.error('刷新令牌时发生错误:', error);
      return false;
    }
  }, [router]);

  /**
   * 设置令牌刷新计时器
   */
  const setupRefreshTimer = useCallback((expiresIn: number) => {
    // 提前1分钟刷新令牌
    const refreshTimeMs = (expiresIn - 60) * 1000;
    
    // 最少等待30秒，避免频繁刷新
    const timerMs = Math.max(refreshTimeMs, 30 * 1000);
    
    const timerId = setTimeout(() => {
      refreshToken();
    }, timerMs);
    
    // 保存定时器ID以便清除
    window.tokenRefreshTimerId = timerId;
  }, [refreshToken]);

  /**
   * 初始化组件
   */
  useEffect(() => {
    // 清除可能存在的旧定时器
    if (window.tokenRefreshTimerId) {
      clearTimeout(window.tokenRefreshTimerId);
    }
    
    // 从sessionStorage获取令牌过期时间
    const tokenData = sessionStorage.getItem('tokenData');
    if (tokenData) {
      try {
        const { expiresAt } = JSON.parse(tokenData);
        const now = Date.now();
        const expiresIn = Math.floor((expiresAt - now) / 1000);
        
        // 如果令牌还有效，设置刷新计时器
        if (expiresIn > 60) {
          setupRefreshTimer(expiresIn);
        } else {
          // 如果令牌已过期或即将过期，立即刷新
          refreshToken();
        }
      } catch (e) {
        console.error('解析令牌数据失败:', e);
      }
    }
    
    // 添加全局未授权错误监听器
    const handleUnauthorizedError = async (event: Event) => {
      const responseEvent = event as any;
      if (responseEvent.detail?.status === 401) {
        // 尝试刷新令牌
        const success = await refreshToken();
        if (success && responseEvent.detail?.retry) {
          // 如果刷新成功且提供了重试函数，则重试请求
          responseEvent.detail.retry();
        }
      }
    };
    
    // 使用自定义事件来通知401错误
    window.addEventListener('unauthorized-error', handleUnauthorizedError);
    
    return () => {
      // 清理定时器和事件监听器
      if (window.tokenRefreshTimerId) {
        clearTimeout(window.tokenRefreshTimerId);
      }
      window.removeEventListener('unauthorized-error', handleUnauthorizedError);
    };
  }, [refreshToken, setupRefreshTimer]);

  // 组件不渲染任何内容
  return null;
}

// 扩展Window接口以包含我们的全局变量
declare global {
  interface Window {
    tokenRefreshTimerId?: NodeJS.Timeout;
  }
} 