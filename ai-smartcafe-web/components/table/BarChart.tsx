
import React from 'react';

interface BarChartProps {
  data: { value: number; label?: string }[];
  height?: number;
  minValue?: number;
  maxValue?: number;
}

const BarChart: React.FC<BarChartProps> = ({ 
  data, 
  height = 120,
  minValue = 0,
  maxValue
}) => {
  const effectiveMax = maxValue || Math.max(...data.map(item => item.value));
  const effectiveMin = minValue;
  const range = effectiveMax - effectiveMin;

  return (
    <div className="flex items-end h-full gap-2 justify-between">
      {data.map((item, index) => {
        const normalizedHeight = ((item.value - effectiveMin) / range) * 100;
        
        return (
          <div 
            key={index} 
            className="flex flex-col items-center"
            style={{ height: `${height}px` }}
          >
            <div 
              className="w-6 bg-purple-600 rounded-sm transition-all duration-500 ease-out animate-slideUp"
              style={{ 
                height: `${normalizedHeight}%`,
                animationDelay: `${index * 0.05}s` 
              }} 
            />
            {item.label && (
              <span className="text-xs text-gray-500 mt-1">{item.label}</span>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default BarChart;
