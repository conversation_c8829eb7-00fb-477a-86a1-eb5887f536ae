
import React from 'react';
import { cn } from '@/lib/utils';

type TagVariant = 'blue' | 'purple' | 'orange' | 'green';

interface TagProps {
  children: React.ReactNode;
  variant?: TagVariant;
  className?: string;
}

const Tag: React.FC<TagProps> = ({ children, variant = 'blue', className }) => {
  return (
    <span className={cn(`tag tag-${variant}`, className)}>
      {children}
    </span>
  );
};

export default Tag;
