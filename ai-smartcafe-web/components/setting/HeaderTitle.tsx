
import React from 'react';
import { motion } from 'framer-motion';

interface HeaderProps {
  title: string;
  subtitle?: string;
}

const HeaderTitle = ({ title, subtitle }: HeaderProps) => {
  return (
    <motion.header 
      className="py-6 px-6 md:px-10 mb-8"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div>
        <motion.h1 
          className="text-2xl font-bold"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.4 }}
        >
          {title}
        </motion.h1>
        {subtitle && (
          <motion.p 
            className="text-gray-500 mt-0.5 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
          >
            {subtitle}
          </motion.p>
        )}
      </div>
    </motion.header>
  );
};

export default HeaderTitle;
