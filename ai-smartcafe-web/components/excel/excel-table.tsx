"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useToast } from "@/lib/use-toast";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
  getPaginationRowModel,
} from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Download,
  Save,
  FileDown,
  Search,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  FileEdit,
  Filter,
  Maximize,
} from "lucide-react";
import dynamic from "next/dynamic";
import * as XLSX from "xlsx";

interface ExcelTableProps {
  data: Record<string, string | number>[];
  fileName: string;
  onSave?: (data: Record<string, string | number>[]) => void;
  maxHeight?: string;
  onShowFilter?: () => void;
  onFullScreenChange?: (isFullScreen: boolean) => void;
  isFullScreen?: boolean;
}

// 创建客户端组件内容
const ExcelTableContent: React.FC<ExcelTableProps> = ({
  data,
  fileName,
  // onSave,
  maxHeight,
  onShowFilter,
  onFullScreenChange,
  isFullScreen = false,
}) => {
  const [tableData, setTableData] = useState<Record<string, string | number>[]>(
    []
  );
  const [sorting, setSorting] = useState<SortingState>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const [editingCell, setEditingCell] = useState<{
    row: number;
    column: string | number;
  } | null>(null);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 50,
  });

  // 根据数据生成列
  const columnHelper = createColumnHelper<Record<string, string | number>>();

  // 过滤数据
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) return tableData;

    return tableData.filter((row) => {
      return Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  }, [tableData, searchTerm]);

  // 初始化表格数据
  useEffect(() => {
    if (data && data.length > 0) {
      setTableData(data);
      // 重置分页状态到第一页，每页显示50条
      setPagination({
        pageIndex: 0,
        pageSize: 50
      });
    }
  }, [data]);

  const columns = useMemo(() => {
    if (!tableData.length) return [];

    // 获取所有列
    const keys = Object.keys(tableData[0] || {});

    return keys.map((key) =>
      columnHelper.accessor(key, {
        header: ({ column }) => {
          return (
            <div
              className="flex items-center justify-between hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer px-2 py-1 rounded"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
            >
              {key}
              <ArrowUpDown className="ml-2 h-4 w-4" />
            </div>
          );
        },
        cell: ({ row, column, getValue }) => {
          const rowIndex = row.index;
          const columnId = column.id;
          const isEditing =
            editingCell?.row === rowIndex && editingCell?.column === columnId;

          if (isEditing) {
            return (
              <Input
                value={getValue() !== undefined && getValue() !== null
                  ? (getValue() as string | number).toString()
                  : ""}
                onChange={(e) => {
                  const newData = [...tableData];
                  newData[rowIndex][columnId] = e.target.value;
                  setTableData(newData);
                }}
                autoFocus
                onBlur={() => setEditingCell(null)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    setEditingCell(null);
                  }
                }}
                className="min-w-[100px] p-1 h-8 focus:ring-1 focus:ring-green-500"
              />
            );
          }

          return (
            <div
              className="group p-2 cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 rounded transition-colors"
              onClick={() => {
                setEditingCell({
                  row: rowIndex,
                  column: columnId,
                });
              }}
            >
              <div className="flex items-center gap-1 justify-between">
                <span className="truncate">
                  {getValue() !== undefined && getValue() !== null
                    ? (getValue() as string | number).toString()
                    : ""}
                </span>
                <FileEdit className="h-3 w-3 text-green-500 opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </div>
          );
        },
      })
    );
  }, [tableData, editingCell, columnHelper]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
    state: {
      sorting,
      pagination,
    },
    manualPagination: false,
  });

  // 检查并修正页码超出范围的问题
  useEffect(() => {
    const pageCount = table.getPageCount();
    if (pageCount > 0 && pagination.pageIndex >= pageCount) {
      setPagination(prev => ({
        ...prev,
        pageIndex: Math.max(0, pageCount - 1)
      }));
    }
  }, [table, filteredData, pagination.pageIndex]);

  // 保存数据
  const handleSave = useCallback(() => {
    // onSave(tableData);
    toast({
      title: "保存成功",
      description: "Excel数据已更新",
    });
  }, [tableData, toast]);

  // 下载Excel
  const handleDownload = useCallback(() => {
    // 转换数据
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(tableData);

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    // 生成二进制数据并下载
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const data = new Blob([excelBuffer], { type: "application/octet-stream" });

    // 创建下载链接
    const downloadName = fileName.includes(".xlsx")
      ? fileName
      : `${fileName}.xlsx`;
    const url = window.URL.createObjectURL(data);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", downloadName);
    document.body.appendChild(link);
    link.click();

    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

    toast({
      title: "下载成功",
      description: `文件已下载: ${downloadName}`,
    });
  }, [tableData, fileName, toast]);

  // 切换全屏模式 - 根据当前状态决定切换方向
  const handleFullScreenToggle = useCallback(() => {
    console.log("触发全屏回调，当前状态:", isFullScreen ? "全屏" : "非全屏");
    if (onFullScreenChange) {
      // 如果当前是全屏状态，传递false表示退出全屏；如果非全屏状态，传递true表示进入全屏
      onFullScreenChange(!isFullScreen);
    }
  }, [onFullScreenChange, isFullScreen]);

  if (!tableData.length) {
    return <div className="p-4 text-center">没有数据可显示</div>;
  }

  const totalPages = table.getPageCount();
  const currentPage = pagination.pageIndex + 1;

  return (
    <div className="flex flex-col h-full">
      {/* 头部工具栏 */}
      <div className="flex-shrink-0 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pb-4">
        <h2 className="text-xl font-bold flex items-center gap-2 text-gray-800 dark:text-gray-100">
          <FileDown className="h-5 w-5 text-blue-500" />
          {fileName}
        </h2>

        <div className="flex items-center gap-3 w-full sm:w-auto">
          <div className="relative flex-1 sm:flex-initial">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              placeholder="搜索数据..."
              className="pl-9 bg-white dark:bg-gray-800"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            {/* <Button 
              onClick={handleSave} 
              variant="default" 
              size="sm" 
              className="flex-shrink-0 bg-green-500 hover:bg-green-600 text-white"
            >
              <Save className="mr-2 h-4 w-4" />
              保存
            </Button> */}
            <Button
              onClick={() => onShowFilter && onShowFilter()}
              variant="default"
              size="sm"
              className="flex-shrink-0 bg-green-500 hover:bg-green-600 text-white"
            >
              <Filter className="mr-2 h-4 w-4" />
              查询
            </Button>
            <Button
              onClick={handleDownload}
              variant="outline"
              size="sm"
              className="flex-shrink-0"
            >
              <Download className="mr-2 h-4 w-4" />
              下载
            </Button>
            <Button
              onClick={(e) => {
                // 使用DOM事件直接记录点击
                console.log("全屏按钮被点击，当前状态:", isFullScreen ? "全屏" : "非全屏");
                // 确保事件冒泡正常
                e.stopPropagation();
                handleFullScreenToggle();
              }}
              variant="outline"
              size="sm"
              className="flex-shrink-0"
              title={isFullScreen ? "退出全屏" : "全屏显示"}
            >
              <Maximize className="h-4 w-4" />
              <span className="ml-1 hidden sm:inline">{isFullScreen ? "退出全屏" : "全屏"}</span>
            </Button>
          </div>
        </div>
      </div>

      {/* 表格区域 - 使用flex布局确保分页始终可见 */}
      <div className="flex flex-col border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden flex-1 min-h-0">
        {/* 表格内容 - 可滚动区域，添加自定义滚动条样式 */}
        <div
          className="flex-1 overflow-auto min-h-0 scrollbar-hide scrollbar-hidden-until-hover scrollbar-green-thin"
          style={{ maxHeight: maxHeight }}
        >
          <table className="min-w-full border-collapse  m-0">
            <thead className="bg-gray-50 dark:bg-gray-900/50 sticky top-0 z-10">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header, index) => (
                    <th
                      key={header.id}
                      className={`text-left p-3 font-medium text-sm border-b border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap ${
                        index === 0
                          ? "first-cell"
                          : index === headerGroup.headers.length - 1
                          ? "last-cell"
                          : ""
                      }`}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors"
                  >
                    {row.getVisibleCells().map((cell, cellIndex) => (
                      <td
                        key={cell.id}
                        className={`p-0 text-sm ${
                          row.index === table.getRowModel().rows.length - 1
                            ? cellIndex === 0
                              ? "last-row-first-cell"
                              : cellIndex === row.getVisibleCells().length - 1
                              ? "last-row-last-cell"
                              : ""
                            : ""
                        }`}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="text-center py-8 text-gray-500 dark:text-gray-400"
                  >
                    未找到匹配的数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* 分页控件 - 固定在底部 */}
        <div className="flex-shrink-0 flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs">
          <div className="text-gray-500 dark:text-gray-400">
            共 {tableData.length} 行数据
          </div>

          <div className="flex items-center gap-1.5">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="h-6 w-6 p-0"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            <span className="text-gray-600 dark:text-gray-300">
              第 {currentPage} / {totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="h-6 w-6 p-0"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </div>

          <div className="flex items-center gap-1.5">
            <span className="text-gray-600 dark:text-gray-300">每页显示:</span>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
              className="p-0.5 rounded border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-xs"
            >
              {[50, 10, 25, 100].map((pageSize) => (
                <option key={pageSize} value={pageSize} className="text-xs">
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

// 在服务器端显示的加载组件
const ServerLoadingComponent: React.FC<{
  fileName: string;
  maxHeight?: string;
  onShowFilter?: () => void;
}> = ({ fileName, maxHeight }) => (
  <div className="space-y-6">
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pb-4">
      <h2 className="text-xl font-bold flex items-center gap-2 text-gray-800 dark:text-gray-100">
        <FileDown className="h-5 w-5 text-blue-500" />
        {fileName}
      </h2>
      <div className="h-10 bg-gray-100 dark:bg-gray-800 rounded w-64"></div>
    </div>
    <div
      className="border rounded-md p-8 flex justify-center items-center"
      style={{ maxHeight }}
    >
      <div className="text-gray-500 dark:text-gray-400">加载中...</div>
    </div>
  </div>
);

// 使用动态导入创建仅在客户端渲染的组件
const ExcelTableComponent = dynamic<ExcelTableProps>(
  () => Promise.resolve(ExcelTableContent),
  {
    ssr: false,
    loading: () => <ServerLoadingComponent fileName="数据表格" />,
  }
);

// 导出重命名的组件
export default ExcelTableComponent;
export { ExcelTableComponent as ExcelTable };
