/**
 * Get the current year as a string
 */
export function getCurrentYear(): string {
  return new Date().getFullYear().toString();
}

/**
 * Generate an array of years for the dropdown
 * (current year and 4 years back)
 */
export function generateYears(): string[] {
  const currentYear = new Date().getFullYear();
  const years = [];
  
  for (let i = 0; i < 5; i++) {
    years.push((currentYear - i).toString());
  }
  
  return years;
} 