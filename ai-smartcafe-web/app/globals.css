@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 224 76% 48%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 224 76% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .bg-dashboard-blue {
    @apply bg-blue-500;
  }
  
  .bg-dashboard-orange {
    @apply bg-orange-500;
  }
  
  .bg-dashboard-green {
    @apply bg-green-500;
  }
  
  .bg-dashboard-purple {
    @apply bg-purple-500;
  }
  
  .bg-dashboard-pink {
    @apply bg-pink-500;
  }
  
  .bg-dashboard-teal {
    @apply bg-teal-500;
  }

  .stat-card {
    @apply rounded-xl bg-card p-6 shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md;
  }
  
  .dashboard-icon {
    @apply flex items-center justify-center w-12 h-12 rounded-lg text-white shadow-sm;
  }

  .menu-item {
    @apply flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors text-[hsl(var(--sidebar-foreground))] hover:bg-sidebar-accent hover:text-[hsl(var(--sidebar-accent-foreground))];
  }

  .menu-item.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
  }

  .menu-icon {
    @apply w-5 h-5;
  }

  .animated-number {
    @apply tabular-nums tracking-tight;
  }

  .tag {
    @apply px-2.5 py-0.5 text-xs font-medium rounded-full;
  }
  
  .tag-blue {
    @apply bg-tag-blue text-tag-blueText;
  }
  
  .tag-purple {
    @apply bg-tag-purple text-tag-purpleText;
  }
  
  .tag-orange {
    @apply bg-tag-orange text-tag-orangeText;
  }
  
  .tag-green {
    @apply bg-tag-green text-tag-greenText;
  }
  
  .data-table {
    @apply w-full border-collapse;
  }
  
  .data-table th {
    @apply bg-gray-50 text-sm font-medium text-gray-600 px-4 py-3 text-left border-b;
  }
  
  .data-table td {
    @apply px-4 py-4 text-sm border-b text-gray-800;
  }
  
  .data-table tr:nth-child(even) td {
    @apply bg-gray-50;
  }
  
  .data-card {
    @apply bg-white rounded-lg border border-gray-200 overflow-hidden transition-all duration-300;
  }
  
  .data-card:hover {
    @apply shadow-md;
  }
  
  .progress-bar {
    @apply h-2 rounded-full overflow-hidden bg-purple-100;
  }
  
  .progress-value {
    @apply h-full bg-purple-600;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* 自定义样式 */
.glass-panel {
  @apply bg-white/80 backdrop-blur-md rounded-xl border border-gray-100 dark:bg-gray-800/90 dark:border-gray-700;
}

/* 动画效果 */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  background-size: 1000px 100%;
}

/* 表格样式增强 */
table {
  border-collapse: separate;
  border-spacing: 0;
}

.table-hover-effect tr:hover td {
  @apply bg-blue-50/50 dark:bg-blue-900/10;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent rounded;
}

::-webkit-scrollbar-thumb {
  @apply bg-green-400/70 dark:bg-green-500/70 rounded-full hover:bg-green-500 dark:hover:bg-green-400 transition-colors;
}

/* 隐藏滚动条但保持可滚动 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* 悬停时才显示的滚动条 */
.scrollbar-hidden-until-hover {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hidden-until-hover::-webkit-scrollbar {
  display: none;
}

.scrollbar-hidden-until-hover:hover::-webkit-scrollbar {
  display: block;
}

/* 绿色细滚动条样式 */
.scrollbar-green-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(74, 222, 128, 0.7) transparent;
}

.scrollbar-green-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-green-thin::-webkit-scrollbar-thumb {
  @apply bg-green-400/70 dark:bg-green-500/70 rounded-full;
}

/* 富文本样式 */
.rich-text h1 {
  @apply text-2xl font-bold mb-4;
}

.rich-text h2 {
  @apply text-xl font-bold mb-3;
}

.rich-text p {
  @apply mb-4;
}

.rich-text ul {
  @apply list-disc list-inside mb-4;
}

.rich-text ol {
  @apply list-decimal list-inside mb-4;
}

/* 响应式工具类 */
.container-responsive {
  @apply px-4 sm:px-6 md:px-8 mx-auto max-w-7xl;
}

/* Excel表格边框圆角修复 */
.rounded-md.overflow-hidden {
  border-radius: 0.375rem; /* 与rounded-md一致 */
}

/* 确保边框连续性 */
.rounded-md.overflow-hidden table {
  border-collapse: separate;
  border-spacing: 0;
}

/* 表头圆角 */
.first-cell,
.rounded-md.overflow-hidden th:first-child {
  border-top-left-radius: 0.375rem;
}

.last-cell,
.rounded-md.overflow-hidden th:last-child {
  border-top-right-radius: 0.375rem;
}

/* 最后一行圆角 */
.last-row-first-cell,
.rounded-md.overflow-hidden tr:last-child td:first-child {
  border-bottom-left-radius: 0.375rem;
}

.last-row-last-cell,
.rounded-md.overflow-hidden tr:last-child td:last-child {
  border-bottom-right-radius: 0.375rem;
}

/* 标题渐变效果 */
.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500 dark:from-blue-400 dark:to-cyan-300;
}

/* 卡片悬停效果 */
.card-hover {
  @apply transition-all duration-300 hover:-translate-y-1;
}

/* 自定义按钮样式 */
.btn-primary-gradient {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-md transition-all duration-300;
}

/* 移动设备优化 */
@media (max-width: 640px) {
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* 表格分页区域样式 */
.text-xs option {
  font-size: 0.75rem;
}

/* 暗色模式增强 */
.dark .dark-enhance {
  @apply bg-gray-800 text-white border-gray-700;
}

/* 装饰性样式 */
.deco-line {
  @apply h-px w-full bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent my-8;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.3) 0%, rgba(59, 130, 246, 0.1) 50%, rgba(59, 130, 246, 0) 100%);
}

/* Markdown内容样式 */
.markdown-content {
  @apply text-sm text-gray-700 dark:text-gray-200 leading-relaxed;
}

/* 用户消息中的Markdown内容白色样式 */
.user-message-content * {
  color: white !important;
}

.markdown-content h1 {
  @apply text-2xl font-bold mt-5 mb-3 text-green-600 dark:text-green-400 border-b border-green-100 dark:border-green-800 pb-2;
}

.markdown-content h2 {
  @apply text-xl font-bold mt-4 mb-3 text-green-600 dark:text-green-400;
}

.markdown-content h3 {
  @apply text-lg font-bold mt-3 mb-2 text-green-500 dark:text-green-400;
}

.markdown-content p {
  @apply my-2 text-gray-700 dark:text-gray-200;
}

.markdown-content ul, .markdown-content ol {
  @apply pl-5 my-3;
}

.markdown-content ul {
  @apply list-disc;
}

.markdown-content ol {
  @apply list-decimal;
}

.markdown-content li {
  @apply my-1.5;
}

.markdown-content a {
  @apply text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:underline transition-colors;
}

.markdown-content code {
  @apply px-1.5 py-0.5 bg-green-50 dark:bg-green-900/30 rounded text-sm text-green-700 dark:text-green-300 font-mono;
}

.markdown-content pre {
  @apply p-3 bg-gray-50 dark:bg-gray-800 rounded-md my-3 overflow-x-auto border border-green-100 dark:border-green-900/50;
}

.markdown-content blockquote {
  @apply pl-4 border-l-4 border-green-300 dark:border-green-700 italic my-3 text-gray-600 dark:text-gray-300 bg-green-50/50 dark:bg-green-900/20 py-2 rounded-r;
}

.markdown-content table {
  @apply w-full border-collapse my-3 border border-green-200 dark:border-green-800 rounded overflow-hidden;
}

.markdown-content th, .markdown-content td {
  @apply border border-green-200 dark:border-green-800 px-3 py-2;
}

.markdown-content th {
  @apply bg-green-50 dark:bg-green-900/40 font-medium text-green-700 dark:text-green-300;
}

.markdown-content tr:nth-child(even) {
  @apply bg-green-50/50 dark:bg-green-900/20;
}

.markdown-content hr {
  @apply my-4 border-green-200 dark:border-green-800;
}

.markdown-content img {
  @apply max-w-full my-3 rounded border border-green-100 dark:border-green-800;
}

.markdown-content strong {
  @apply font-bold text-green-700 dark:text-green-300;
}

.markdown-content em {
  @apply italic text-green-600 dark:text-green-400;
}

/* 确保所有样式定义使用标准的 CSS 语法 */
:root {
  color-scheme: light dark;
}

/* 其余样式保持不变 */

/* Markdown表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

th {
  background-color: #f3f4f6;
  font-weight: 600;
  text-align: left;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
}

td {
  /* padding: 0.5rem; */
  border: 1px solid #e5e7eb;
}

tr:nth-child(even) {
  background-color: #f9fafb;
}

/* 强调显示表头 */
th strong {
  font-weight: 700;
}
.common-table {
  @apply overflow-x-auto bg-white rounded-md shadow-sm;
}
.common-table table
{
  @apply w-full border-collapse m-0;
}
.common-table table thead{
  @apply sticky -top-[1px] z-10 bg-gray-50;
}
.common-table table thead th{
  @apply px-2 py-2 text-sm font-medium text-gray-600 border-0;
}
.common-table table tbody tr{
  @apply hover:bg-violet-100;
}
.common-table table tbody td{
  border: none;
}
.common-table table tbody td input{
  @apply w-full px-2 py-2 m-1 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white;
}
.common-table table tbody td textarea{
  @apply w-full px-2 py-2 m-1 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white;
}
.common-table .common-table-select-button{
  @apply w-full grow text-center m-1 bg-white text-base h-full focus:ring-0 focus:ring-offset-0;
}
.common-table-select-item{
  @apply px-[1rem] py-[0.75rem] text-base cursor-pointer;
}
.common-table-select-text-center{
  span{
    margin: 0 auto;
  }
}