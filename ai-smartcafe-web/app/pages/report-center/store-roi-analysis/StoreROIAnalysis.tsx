import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Calendar, Download, FileText, ChevronDown } from "lucide-react";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid } from "recharts";
import { exportToExcel, exportToPDF } from "@/lib/export-utils";
import dynamic from "next/dynamic";

// 动态导入 ECharts 以避免 SSR 问题
const ReactECharts = dynamic(() => import("echarts-for-react"), { ssr: false });

const StoreROIAnalysis = () => {
  const [selectedStores, setSelectedStores] = useState<string[]>(["all"]);
  const [selectedPeriod, setSelectedPeriod] = useState("current-month-summary");
  const [stores, setStores] = useState<any[]>([]);
  const [roiData, setRoiData] = useState<any[]>([]);
  const [trendData, setTrendData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [dataPending, setDataPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportLoading, setExportLoading] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string>("--");

  // Set default values - current year first month to current month
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  
  const [startYearMonth, setStartYearMonth] = useState<string>(`${currentYear}-01`);
  const [endYearMonth, setEndYearMonth] = useState<string>(`${currentYear}-${currentMonth}`);

  // 格式化时间为 yyyy-MM-dd HH:mm:ss
  const formatDateTime = (dateString: string | null): string => {
    if (!dateString) return "--";
    
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // 获取最新更新时间
  const fetchLastUpdated = async () => {
    try {
      const response = await fetch('/api/report-center/last-updated');
      const result = await response.json();
      
      if (result.success && result.data.last_updated) {
        setLastUpdated(formatDateTime(result.data.last_updated));
      } else {
        setLastUpdated("--");
      }
    } catch (error) {
      console.error('获取最新更新时间失败:', error);
      setLastUpdated("--");
    }
  };

  // 获取最新更新时间
  useEffect(() => {
    fetchLastUpdated();
  }, []);

  // 获取门店数据
  useEffect(() => {
    const fetchStores = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/report-center/store-roi-analysis');
        const result = await response.json();
        
        if (result.success) {
          // 添加"所有门店"选项
          const storesWithAll = [
            { id: "all", name: "所有门店" },
            ...result.data.stores
          ];
          setStores(storesWithAll);
        } else {
          setError(result.message || '获取门店数据失败');
        }
      } catch (err) {
        console.error('获取门店数据失败:', err);
        setError('获取门店数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, []);

  // Generate year-month options
  const generateYearMonthOptions = () => {
    const options = [];
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 2; // 从前两年开始
    const endYear = currentYear + 1; // 到明年结束
    
    for (let year = startYear; year <= endYear; year++) {
      for (let month = 1; month <= 12; month++) {
        const monthStr = month.toString().padStart(2, '0');
        options.push({
          value: `${year}-${monthStr}`,
          label: `${year}-${monthStr}`
        });
      }
    }
    return options;
  };

  const yearMonthOptions = generateYearMonthOptions();

  // 获取回本分析数据
  useEffect(() => {
    const fetchROIData = async () => {
      try {
        setDataPending(true);
        const storeIds = selectedStores.includes("all") ? [] : selectedStores;
        const response = await fetch('/api/report-center/store-roi-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            storeIds,
            period: selectedPeriod,
            endYearMonth: selectedPeriod === "until-specified-month" ? endYearMonth : undefined,
            includeTrend: true
          })
        });
        
        const result = await response.json();
        
        if (result.success) {
          setRoiData(result.data.roiData);
          setTrendData(result.data.trendData);
          setError(null);
        } else {
          setError(result.message || '获取回本分析数据失败');
        }
      } catch (err) {
        console.error('获取回本分析数据失败:', err);
        setError('获取回本分析数据失败');
      } finally {
        setDataPending(false);
      }
    };

    // 只有在门店数据加载完成后才获取回本数据
    if (stores.length > 0) {
      fetchROIData();
    }
  }, [selectedStores, selectedPeriod, startYearMonth, endYearMonth, stores]);

  const handleStoreSelection = (storeId: string) => {
    if (storeId === "all") {
      if (selectedStores.includes("all")) {
        setSelectedStores([]);
      } else {
        setSelectedStores(["all"]);
      }
    } else {
      setSelectedStores(prev => {
        const newSelection = prev.filter(id => id !== "all");
        if (newSelection.includes(storeId)) {
          return newSelection.filter(id => id !== storeId);
        } else {
          return [...newSelection, storeId];
        }
      });
    }
  };

  const getSelectedStoresText = () => {
    if (selectedStores.includes("all") || selectedStores.length === 0) {
      return "所有门店";
    }
    if (selectedStores.length === 1) {
      const store = stores.find(s => s.id === selectedStores[0]);
      return store?.name || "选择门店";
    }
    return `已选择 ${selectedStores.length} 个门店`;
  };

  // 使用真实数据生成图表数据
  const chartData = roiData.map(store => ({
    name: store.name,
    netAssets: store.netAssets,
    totalInvestment: store.totalInvestment
  }));

  // ECharts 柱状图配置
  const barChartOption = {
    title: {
      text: '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let result = `<strong>${params[0].name}</strong><br/>`;
        params.forEach((param: any) => {
          const value = (param.value / 10000).toFixed(1) + '万';
          result += `${param.marker} ${param.seriesName}: ${value}<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ['净资产', '总投入'],
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 0,
        fontSize: 10,
        color: '#666',
        margin: 8,
        formatter: function(value: string) {
          // 如果名称过长，截断并添加省略号
          return value.length > 6 ? value.substring(0, 6) + '...' : value;
        }
      },
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      scale: false,
      min: function(value: any) {
        // 确保包含0基线，如果所有数据都是正数，从0开始；如果有负数，适当扩展
        return Math.min(0, value.min);
      },
      axisLabel: {
        formatter: function(value: number) {
          return (value / 10000).toFixed(0) + '万';
        },
        fontSize: 11,
        color: '#666'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: function(value: number) {
            // 0基线使用更深的颜色
            return value === 0 ? '#999' : '#f0f0f0';
          },
          width: function(value: number) {
            // 0基线使用更粗的线条
            return value === 0 ? 2 : 1;
          }
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      // 添加0基线
      splitNumber: 5
    },
    series: [
      {
        name: '净资产',
        type: 'bar',
        data: chartData.map(item => item.netAssets),
        itemStyle: {
          color: '#10b981',
          borderRadius: function(params: any) {
            // 根据数值正负设置圆角：正值上方圆角，负值下方圆角
            return params.value >= 0 ? [4, 4, 0, 0] : [0, 0, 4, 4];
          }
        },
        emphasis: {
          itemStyle: {
            color: '#059669'
          }
        },
        barWidth: '35%'
      },
      {
        name: '总投入',
        type: 'bar',
        data: chartData.map(item => item.totalInvestment),
        itemStyle: {
          color: '#6366f1',
          borderRadius: function(params: any) {
            // 根据数值正负设置圆角：正值上方圆角，负值下方圆角
            return params.value >= 0 ? [4, 4, 0, 0] : [0, 0, 4, 4];
          }
        },
        emphasis: {
          itemStyle: {
            color: '#4f46e5'
          }
        },
        barWidth: '35%'
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'elasticOut'
  };

  // 生成预设的颜色数组，确保门店有不同的颜色
  const colors = [
    '#10b981', '#f59e0b', '#3b82f6', '#ef4444', '#8b5cf6', 
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ];

  // 生成净现金流趋势图配置
  const getCashFlowTrendOption = () => {
    if (!trendData || !trendData.months || !trendData.stores) {
      return null;
    }

    const { months, stores } = trendData;
    
    // 生成系列数据，每个门店一条线
    const series = stores.map((store: any, index: number) => {
      const isClosedStore = store.state === 0;
      const lineColor = colors[index % colors.length];
      
      return {
        name: store.name,
        type: 'line',
        data: store.cashFlowData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: lineColor,
          type: isClosedStore ? 'dashed' : 'solid' // 闭店门店使用虚线
        },
        itemStyle: {
          color: lineColor,
          opacity: isClosedStore ? 0.7 : 1 // 闭店门店透明度降低
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 4
          },
          itemStyle: {
            borderWidth: 2,
            borderColor: '#fff'
          }
        }
      };
    });

    return {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params: any) {
          let result = `<strong>${params[0].axisValue}</strong><br/>`;
          params.forEach((param: any) => {
            const value = (param.value / 10000).toFixed(1) + '万';
            result += `${param.marker} ${param.seriesName}: ${value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: stores.map((store: any) => store.name),
        top: 10,
        textStyle: {
          fontSize: 12
        },
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        top: stores.length > 5 ? '20%' : '15%', // 如果门店较多，给图例更多空间
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months,
        axisLabel: {
          fontSize: 11,
          color: '#666',
          interval: 0,
          rotate: months.length > 8 ? 45 : 0 // 如果月份较多，旋转标签
        },
        axisLine: {
          lineStyle: {
            color: '#e0e0e0'
          }
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisLabel: {
          formatter: function(value: number) {
            return (value / 10000).toFixed(0) + '万';
          },
          fontSize: 11,
          color: '#666'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: function(value: number) {
              return value === 0 ? '#999' : '#f0f0f0';
            },
            width: function(value: number) {
              return value === 0 ? 2 : 1;
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#e0e0e0'
          }
        }
      },
      series: series,
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    };
  };

  const cashFlowTrendOption = getCashFlowTrendOption();

  const chartConfig = {
    netAssets: {
      label: "净资产",
      color: "#10b981",
    },
    totalInvestment: {
      label: "总投入",
      color: "#6366f1",
    },
    cashFlow: {
      label: "净现金流",
      color: "#f59e0b",
    },
  };

  const formatCurrency = (amount: number) => {
    return (amount / 10000).toFixed(1) + "万";
  };

  // 获取门店名称映射
  const getStoreNamesMap = () => {
    const storeNamesMap: Record<string, string> = {};
    stores.forEach(store => {
      if (store.id !== "all") {
        storeNamesMap[store.id] = store.name;
      }
    });
    return storeNamesMap;
  };

  // 处理 Excel 导出
  const handleExcelExport = async () => {
    try {
      setExportLoading('excel');
      
      const filterConditions = {
        selectedStores,
        selectedPeriod,
        endYearMonth,
        storeNames: getStoreNamesMap()
      };
      
      exportToExcel(roiData, filterConditions);
      
      console.log('Excel 导出成功');
      
      // 简单的成功提示，可以后续改为 toast
      setTimeout(() => {
        setExportLoading(null);
      }, 1000);
      
    } catch (error) {
      console.error('Excel 导出失败:', error);
      alert('Excel 导出失败，请重试');
      setExportLoading(null);
    }
  };

  // 处理 PDF 导出
  const handlePDFExport = async () => {
    try {
      setExportLoading('pdf');
      
      const filterConditions = {
        selectedStores,
        selectedPeriod,
        endYearMonth,
        storeNames: getStoreNamesMap()
      };
      
      await exportToPDF(
        roiData, 
        filterConditions, 
        'roi-table-container',
        ['bar-chart-container', 'line-chart-container']
      );
      
      console.log('PDF 导出成功');
      setExportLoading(null);
      
    } catch (error) {
      console.error('PDF 导出失败:', error);
      alert('PDF 导出失败，请重试');
      setExportLoading(null);
    }
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex w-full">
        <div className="flex-1 flex flex-col">
          <div className="flex-1 p-6 md:p-8">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white rounded-2xl shadow-sm p-8 border border-gray-100">
                <div className="flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">加载门店数据中...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex w-full">
        <div className="flex-1 flex flex-col">
          <div className="flex-1 p-6 md:p-8">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white rounded-2xl shadow-sm p-8 border border-gray-100">
                <div className="text-center">
                  <div className="text-red-500 mb-4">⚠️ 数据加载失败</div>
                  <p className="text-gray-600">{error}</p>
                  <button 
                    onClick={() => window.location.reload()} 
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    重新加载
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex w-full">
      
      <div className="flex-1 flex flex-col">
        
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-7xl mx-auto space-y-8">
            {/* Page Header */}
            <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">门店回本分析</h1>
              <p className="text-gray-600">分析各门店投资回报情况和回本预测</p>
            </div>

            {/* Filter Section */}
            <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100">
              <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">筛选条件:</span>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3 flex-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full sm:w-48 justify-between">
                        {getSelectedStoresText()}
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-white border border-gray-200 shadow-md">
                      <div className="p-2 space-y-2">
                        {stores.map((store) => (
                          <div 
                            key={store.id} 
                            className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded cursor-pointer"
                            onClick={() => handleStoreSelection(store.id)}
                          >
                            <div className="flex items-center space-x-2">
                            <Checkbox 
                              checked={
                                store.id === "all" 
                                  ? selectedStores.includes("all") 
                                  : selectedStores.includes(store.id)
                              }
                              onChange={() => {}}
                            />
                            <span className="text-sm">{store.name}</span>
                            </div>
                            {/* 显示门店状态 */}
                            {store.id !== "all" && (
                              <span className={`text-xs px-2 py-1 rounded ${
                                store.state === 1 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {store.stateText}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="current-month-summary">截至目前汇总</SelectItem>
                      <SelectItem value="until-specified-month">截至指定月</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Date Picker - Show when "截至指定月" is selected */}
                  {selectedPeriod === "until-specified-month" && (
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600">截至</span>
                      <Select value={endYearMonth} onValueChange={setEndYearMonth}>
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="选择月份" />
                        </SelectTrigger>
                        <SelectContent className="max-h-40">
                          {yearMonthOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Data Table */}
            <div className="relative">
              <Card className="border-gray-100">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-xl text-gray-900">门店回本详情</CardTitle>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="hover:scale-105 transition-transform"
                      onClick={handleExcelExport}
                      disabled={dataPending || roiData.length === 0 || exportLoading !== null}
                    >
                      {exportLoading === 'excel' ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                      ) : (
                        <Download className="w-4 h-4 mr-2" />
                      )}
                      {exportLoading === 'excel' ? '导出中...' : 'Excel'}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="hover:scale-105 transition-transform"
                      onClick={handlePDFExport}
                      disabled={dataPending || roiData.length === 0 || exportLoading !== null}
                    >
                      {exportLoading === 'pdf' ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                      ) : (
                        <FileText className="w-4 h-4 mr-2" />
                      )}
                      {exportLoading === 'pdf' ? '生成中...' : 'PDF'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div id="roi-table-container" className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>门店</TableHead>
                          <TableHead className="text-right">总投入成本</TableHead>
                          <TableHead className="text-right">累计净现金流</TableHead>
                          <TableHead className="text-right">库存价值</TableHead>
                          <TableHead className="text-right">净资产</TableHead>
                          <TableHead className="text-right">剩余回本金额</TableHead>
                          <TableHead className="text-center">回本状态</TableHead>
                          <TableHead className="text-center">预测回本时间</TableHead>
                          <TableHead className="text-center">实际回本时间</TableHead>
                          <TableHead className="text-right">待报销款</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {roiData.map((store) => (
                          <TableRow key={store.id}>
                            <TableCell className="font-medium">{store.name}</TableCell>
                            <TableCell className="text-right">{formatCurrency(store.totalInvestment)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(store.cumulativeCashFlow)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(store.inventoryValue)}</TableCell>
                            <TableCell className="text-right font-semibold">{formatCurrency(store.netAssets)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(store.remainingPayback)}</TableCell>
                            <TableCell className="text-center">
                              <Badge 
                                variant={store.paybackStatus === "已回本" ? "default" : "secondary"}
                                className={store.paybackStatus === "已回本" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                              >
                                {store.paybackStatus}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-center">{store.predictedPaybackTime}</TableCell>
                            <TableCell className="text-center">{store.actualPaybackTime}</TableCell>
                            <TableCell className="text-right">{formatCurrency(store.pendingReimbursement)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
              
              {/* Pending Overlay for Table */}
              {dataPending && (
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
                  <div className="flex flex-col items-center gap-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="text-sm text-gray-600 font-medium">正在更新数据...</p>
                  </div>
                </div>
              )}
            </div>

            {/* Charts Section */}
            <div className="relative">
              <div className="grid lg:grid-cols-2 gap-6">
                {/* Bar Chart */}
                <Card className="border-gray-100">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-900">净资产 vs 总投入对比</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div id="bar-chart-container" className="h-96 w-full">
                      <ReactECharts 
                        option={barChartOption} 
                        style={{ height: '100%', width: '100%' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Line Chart */}
                <Card className="border-gray-100">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-900">净现金流趋势</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div id="line-chart-container" className="h-80 w-full">
                      {cashFlowTrendOption ? (
                        <ReactECharts 
                          option={cashFlowTrendOption} 
                          style={{ height: '100%', width: '100%' }}
                          opts={{ renderer: 'canvas' }}
                        />
                      ) : (
                        <div className="h-full flex items-center justify-center text-gray-500">
                          <div className="text-center">
                            <div className="mb-2">📈</div>
                            <div>暂无趋势数据</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Pending Overlay for Charts */}
              {dataPending && (
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
                  <div className="flex flex-col items-center gap-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="text-sm text-gray-600 font-medium">正在更新图表...</p>
                  </div>
                </div>
              )}
            </div>

            {/* Footer with Data Update Time */}
            <div className="bg-white rounded-2xl shadow-sm p-4 border border-gray-100">
              <div className="text-center text-sm text-gray-500">
                数据更新时间：{lastUpdated}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreROIAnalysis;
