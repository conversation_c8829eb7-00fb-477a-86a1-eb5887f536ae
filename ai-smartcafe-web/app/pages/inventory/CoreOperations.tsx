import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>clip, Camera, SendHorizonal, CircleStop, CheckCircle } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useToast } from "@/lib/use-toast";
import Mic<PERSON>hat from '@/components/mic/MicChat';
import { motion, AnimatePresence } from "framer-motion";
import { experimental_useObject as useObject } from '@ai-sdk/react';
import { cn } from "@/lib/utils";
import { JSONValue } from "ai";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Message, FileUploadResponse, ReimbursementMainSchemaResponse, CoreOperationsProps } from '@/lib/schema/inventory';

export function CoreOperations({ operations }: CoreOperationsProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [uploadedFileInfos, setUploadedFileInfos] = useState<FileUploadResponse[]>([]);
  const [selectedOperation, setSelectedOperation] = useState<string | null>(null);
  const { toast } = useToast();
  const [isListening, setIsListening] = useState(false);
  const [showHeader, setShowHeader] = useState(() => {
    if (typeof window !== 'undefined') {
      return !sessionStorage.getItem('chatMessages');
    }
    return true;
  });
  const [prompt, setPrompt] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  // 添加确认弹窗状态
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  // 添加操作类型未选择提示状态
  const [showOperationWarning, setShowOperationWarning] = useState(false);

  // todo ai调用
  const { object, submit, isLoading, stop, error } = useObject({
    api: '/api/openai',
    schema: ReimbursementMainSchemaResponse,
    onError: (error) => {
      toast({
        title: "接口错误",
        description: error.message || "服务器响应错误，请清空后重试",
        variant: "destructive"
      });
    }
  });

  // 监听错误状态
  useEffect(() => {
    if (error) {
      toast({
        title: "接口错误",
        description: error.message || "服务器响应错误，请清空后重试",
        variant: "destructive"
      });
    }
  }, [error, toast]);

  // 添加数据确认处理函数
  const handleDataConfirm = (e: React.MouseEvent) => {
    e.preventDefault();

    // 显示自定义确认弹窗
    setShowConfirmModal(true);
  };

  // 确认提交数据
  const confirmSubmitData = () => {
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: "数据已确认，请入库处理。",
      timestamp: new Date().toLocaleString(),
      operationType: selectedOperation || undefined
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    sessionStorage.setItem('chatMessages', JSON.stringify(updatedMessages));

    submit({
      type: selectedOperation,
      data: {
        message: "数据已确认，请入库处理。",
        uploadedFiles: [] as JSONValue,
        // 用户数据已确认，可操作入库
        state: 1
      },
      historyChat: messages
    });

    toast({
      title: "数据已确认",
      description: "数据已提交入库处理",
      variant: "default"
    });

    // 关闭弹窗
    setShowConfirmModal(false);
  };

  // 取消确认
  const cancelConfirm = () => {
    setShowConfirmModal(false);
  };

  // 从sessionStorage加载消息历史
  useEffect(() => {
    const storedMessages = sessionStorage.getItem('chatMessages');
    if (storedMessages) {
      try {
        setMessages(JSON.parse(storedMessages));
        setShowHeader(false);
      } catch (error) {
        console.error('解析存储的消息时出错:', error);
      }
    }
  }, []);

  // 滚动到底部函数
  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  // 当收到AI回复时更新消息列表
  useEffect(() => {
    if (object?.message && object.message.trim()) {
      const messageContent = object.message as string;
      const messageData = object.data;
      setMessages(prevMessages => {
        const lastMessage = prevMessages[prevMessages.length - 1];
        let updatedMessages;
        if (lastMessage && lastMessage.role === 'assistant') {
          updatedMessages = [...prevMessages.slice(0, -1), {
            ...lastMessage,
            content: messageContent,
            data: messageData
          }];
        } else {
          // 获取用户最后一条消息的操作类型
          const lastUserMessage = [...prevMessages].reverse().find(msg => msg.role === 'user');
          const operationType = lastUserMessage?.operationType;

          const newMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: messageContent,
            data: messageData,
            timestamp: new Date().toLocaleString(),
            operationType: operationType
          };
          updatedMessages = [...prevMessages, newMessage];
        }
        sessionStorage.setItem('chatMessages', JSON.stringify(updatedMessages));
        return updatedMessages;
      });
      setShowHeader(false);
      
      // 添加延时滚动到底部，确保DOM已更新
      setTimeout(scrollToBottom, 300);
    }
  }, [object?.message, object?.data]);

  // 在消息列表更新后自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handlerSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!prompt.trim() && uploadedFileInfos.length === 0) return;

    // 检查是否选择了操作类型
    if (!selectedOperation) {
      setShowOperationWarning(true);
      toast({
        title: "请选择操作类型",
        description: "发送消息前请先选择上方或下方的操作类型",
        variant: "destructive"
      });
      return;
    }

    // 隐藏警告提示
    setShowOperationWarning(false);

    // 添加用户消息到消息列表
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: prompt,
      timestamp: new Date().toLocaleString(),
      // 添加files属性保存上传的文件信息
      files: uploadedFileInfos.length > 0 ? [...uploadedFileInfos] : undefined,
      // 添加operationType记录当前选择的操作类型ID
      operationType: selectedOperation || undefined
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);

    // 保存到sessionStorage
    sessionStorage.setItem('chatMessages', JSON.stringify(updatedMessages));

    const fileInfosForSubmit = uploadedFileInfos.map(info => {
      const result: Record<string, JSONValue> = {
        pathname: info.pathname,
        contentType: info.contentType,
        contentDisposition: info.contentDisposition,
        url: info.url,
        downloadUrl: info.downloadUrl,
        fileName: info.fileName,
        fileSize: info.fileSize,
        fileExtension: info.fileExtension,
        fileId: info.fileId
      };
      if (info.size) {
        result.size = info.size;
      }
      return result;
    });
    submit({
      type: selectedOperation,
      data: {
        message: prompt,
        uploadedFiles: fileInfosForSubmit as JSONValue,
        state: 0 // 默认状态为0
      },
      historyChat: messages
    });
    setPrompt(''); // 清空输入框
    setUploadedFileInfos([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setShowHeader(false);
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + 'B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + 'KB';
    return (bytes / (1024 * 1024)).toFixed(1) + 'MB';
  };

  const getFileIcon = (type: string): React.ReactElement => {
    if (type.startsWith('image/')) {
      return <Camera className="h-5 w-5 text-gray-500" />;
    }
    return <Paperclip className="h-5 w-5 text-gray-500" />;
  };

  const getFileExtension = (filename: string): string => {
    return filename.split('.').pop()?.toUpperCase() || '';
  };

  const removeFile = async (file: FileUploadResponse, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    const fileInfo = uploadedFileInfos.find(info => info.fileId === file.fileId);
    if (!fileInfo) return;
    try {
      const response = await fetch(`/api/upload?url=${encodeURIComponent(fileInfo.url)}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        setUploadedFileInfos(prev => prev.filter(info => info.fileId !== fileInfo.fileId));
        toast({
          title: "文件已删除",
          description: `文件 ${fileInfo.fileName} 已成功删除`
        });
      } else {
        toast({
          title: "删除失败",
          description: "无法删除文件，请重试",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("删除文件时出错:", error);
      toast({
        title: "删除错误",
        description: "删除文件时发生错误",
        variant: "destructive"
      });
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files) return;
    const files = Array.from(event.target.files);
    const tempFileInfos: FileUploadResponse[] = files.map(file => ({
      pathname: '',
      contentType: file.type,
      contentDisposition: '',
      url: '',
      downloadUrl: '',
      fileName: file.name,
      fileSize: file.size,
      fileExtension: getFileExtension(file.name),
      fileId: Math.random().toString(36).substr(2, 9),
      // 标记为正在上传
      isUploading: true
    }));
    setUploadedFileInfos(prev => [...prev, ...tempFileInfos]);
    toast({
      title: "文件上传中",
      description: `正在上传 ${files.length} 个文件...`
    });
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const tempFileInfo = tempFileInfos[i];
      try {
        const formData = new FormData();
        formData.append('file', file);
        const response = await fetch('/api/upload', {
          method: 'PUT',
          body: formData,
        });
        if (!response.ok) {
          throw new Error(`上传文件 ${file.name} 失败`);
        }
        const apiResponse = await response.json();
        setUploadedFileInfos(prev => prev.map(info =>
          info.fileId === tempFileInfo.fileId
            ? {
              ...info,
              ...apiResponse,
              isUploading: false
            }
            : info
        ));
      } catch (error) {
        console.error("上传文件时出错:", error);
        setUploadedFileInfos(prev =>
          prev.filter(info => info.fileId !== tempFileInfo.fileId)
        );
        toast({
          title: "上传失败",
          description: `文件 ${file.name} 上传失败`,
          variant: "destructive"
        });
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return "早上好";
    if (hour >= 12 && hour < 14) return "中午好";
    if (hour >= 14 && hour < 18) return "下午好";
    if (hour >= 18 && hour < 23) return "晚上好";
    return "夜里好";
  };

  const handleOperationSelect = (operationId: string) => {
    setSelectedOperation(operationId);
    // 选择操作类型后隐藏警告
    setShowOperationWarning(false);
  };

  const handleClearChat = () => {
    sessionStorage.removeItem('chatMessages');
    setMessages([]);
    toast({
      title: "已清空对话",
      description: "所有对话记录已被清除"
    });
    setShowHeader(true);
  };

  const handleTranscript = (transcript: string) => {
    setPrompt(prompt + transcript);
  };

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* 确认弹窗 */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-lg p-6 w-[90%] max-w-[400px] shadow-xl"
          >
            <h3 className="text-xl font-bold mb-4">确认提交数据入库？</h3>
            <p className="text-gray-700 mb-6">提交后数据将直接入库，不可更改，请谨慎操作！</p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={cancelConfirm}
                className="px-4"
              >
                取消
              </Button>
              <Button
                onClick={confirmSubmitData}
                className="bg-green-600 hover:bg-green-700 px-4"
              >
                确认提交
              </Button>
            </div>
          </motion.div>
        </div>
      )}
      <Card className="flex-1 flex flex-col h-full overflow-hidden">
        <AnimatePresence mode="wait">
          {showHeader ? (
            <motion.div
              key="header"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="flex-1 flex items-center justify-center h-full"
            >
              <div className="flex flex-col items-center justify-center w-[70%]">
                <div className="text-center mb-6">
                  <img src="/image/robot.png" alt="logo" className="w-[70px] h-[70px] rounded-full" />
                </div>
                <div className="text-center mb-6">
                  <h1 className="text-4xl font-bold mb-10">{getGreeting()}，Lily Liu</h1>
                </div>

                <Card className="w-full">
                  <CardContent className="p-0">
                    <form onSubmit={handlerSubmit} className="relative bg-gray-50/70 rounded-[20px] shadow-sm">
                      {uploadedFileInfos.length > 0 && (
                        <div className="flex flex-wrap gap-3 pt-2 pl-2 pr-2 pb-0">
                          {uploadedFileInfos.map(file => (
                            <div
                              key={file.fileId}
                              className={cn(
                                "relative flex items-center gap-2 px-4 py-2 rounded-lg border group",
                                file.isUploading
                                  ? "bg-gray-200 border-gray-200"
                                  : "bg-white hover:border-gray-300"
                              )}
                            >
                              {getFileIcon(file.contentType)}
                              <div className="flex flex-col gap-0">
                                <span className={cn(
                                  "text-sm truncate max-w-[150px] font-medium leading-tight",
                                  file.isUploading ? "text-gray-400" : "text-gray-600"
                                )}>
                                  {file.fileName}
                                </span>
                                <div className="flex items-center gap-1 text-xs leading-tight">
                                  <span className={cn(
                                    "font-medium",
                                    file.isUploading ? "text-gray-400" : "text-blue-500"
                                  )}>
                                    {file.fileExtension}
                                  </span>
                                  <span className="text-gray-400">
                                    {formatFileSize(file.fileSize)}
                                  </span>
                                </div>
                              </div>
                              {!file.isUploading && (
                                <button
                                  type="button"
                                  onClick={(e) => removeFile(file, e)}
                                  className="absolute -top-2 -right-2 h-5 w-5 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  ×
                                </button>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      <textarea
                        onChange={(e) => setPrompt(e.target.value)}
                        value={prompt}
                        placeholder="请直接输入指令或上传文件..."
                        className="w-full rounded-[20px] bg-transparent px-5 py-4 pb-12 text-[15px] \
                          focus:outline-none focus:ring-0 placeholder:text-gray-400 resize-none\
                          overflow-y-auto max-h-[300px] min-h-[100px]"
                        rows={1}
                      />

                      <div className="absolute bottom-2 right-3 flex items-center gap-3">
                        <div className="group relative">
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            onChange={handleFileChange}
                            multiple
                            accept=".pdf,.txt,.csv,.xlsx,.xls,.png,.jpg"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-gray-200/70 rounded-lg"
                            aria-label="附件"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Paperclip className="h-6 w-6 text-gray-500" />
                          </Button>
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            <div className="text-[15px]">
                              • 文件数量：最多 5 个<br />
                              • 图片数量：最多 5 个<br />
                              • 文件类型：pdf、png、jpg
                            </div>
                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 border-transparent border-t-gray-800"></div>
                          </div>
                        </div>
                        <div className="group relative">
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-gray-200/70 rounded-lg"
                            aria-label="拍照"
                          >
                            <Camera className="h-6 w-6 text-gray-500" />
                          </Button>
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            拍照上传
                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 border-transparent border-t-gray-800"></div>
                          </div>
                        </div>
                        <div className="group relative">
                          <MicChat
                            onTranscript={handleTranscript}
                            isListening={isListening}
                            setIsListening={setIsListening}
                          />
                        </div>
                        <div className="group relative text-gray-300 px-3">|</div>
                        {isLoading ? (
                          <div className="group relative">
                            <CircleStop onClick={() => stop()} className="h-8 w-8 bg-white text-black rounded-full" />
                            <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                              停止生成
                            </div>
                          </div>
                        ) : (
                          <Button
                            size="icon"
                            className={cn(
                              "h-8 w-8 rounded-lg transition-colors",
                              prompt.trim() && selectedOperation
                                ? "bg-primary hover:bg-primary/90"
                                : "bg-gray-200 hover:bg-gray-300 cursor-not-allowed"
                            )}
                            disabled={!prompt.trim() || !selectedOperation}
                            type="submit"
                            aria-label="发送"
                          >
                            <SendHorizonal className={cn(
                              "h-6 w-6",
                              prompt.trim() && selectedOperation ? "text-white" : "text-gray-400"
                            )} />
                          </Button>
                        )}
                      </div>

                      {/* 添加操作类型警告提示 */}
                      {showOperationWarning && (
                        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 px-4 py-2 bg-red-50 text-red-600 rounded-md border border-red-200 text-sm whitespace-nowrap">
                          请先选择操作类型才能发送消息
                        </div>
                      )}
                    </form>
                  </CardContent>
                </Card>

                <div className="flex flex-wrap gap-3 mt-6">
                  {operations.map(op => (
                    <div key={op.id} className="flex flex-col items-center">
                      <Button
                        variant={selectedOperation === op.id ? "default" : "outline"}
                        onClick={() => handleOperationSelect(op.id)}
                        className={`h-9 px-5 py-2 rounded-md text-[15px] font-medium transition-colors
                          ${selectedOperation === op.id
                            ? 'bg-primary/10 text-primary border-primary hover:bg-primary/20'
                            : 'bg-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 border-gray-200'
                          }`}
                      >
                        {op.label}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ) : (
            // ===================================对话页面=========================================
            <motion.div
              key="chat"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="h-full flex flex-col"
            >
              <div className="flex-none">
                <CardContent className="p-6 border-b">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-bold text-black text-2xl mb-1">
                        Hi！请用语音、拍照或输入告诉我操作指令！AI 助手将实时响应
                      </p>
                      <p className="text-gray-500 mt-1">
                        例如&quot;进货安佳牛奶10箱&quot;或上传入库单
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={handleClearChat}
                      className="text-gray-500 hover:text-gray-700 hover:bg-transparent hover:font-bold"
                    >
                      清空对话
                    </Button>
                  </div>
                </CardContent>
              </div>

              {/* 对话历史====================================================== */}
              <div className="flex-1 min-h-0 overflow-y-auto" ref={messagesContainerRef}>
                <CardContent className="p-6 bg-muted/20 h-[300px]">
                  <div className="space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={message.id}
                        className={cn(
                          "flex flex-col gap-3",
                          message.role === "user" ? "items-end" : "items-start"
                        )}
                      >
                        {message.role === "user" && message.files && message.files.length > 0 && (
                          <div className="flex flex-wrap-reverse justify-end gap-2 max-w-[86%]">
                            {message.files.map((file, index) => (
                              <div
                                key={index}
                                className="relative w-[120px] h-[60px] overflow-hidden rounded-md bg-gray-50 border border-gray-200"
                              >
                                <img
                                  src={file.url}
                                  alt={file.fileName || '附件'}
                                  className="h-full w-full object-cover cursor-pointer"
                                  onClick={() => window.open(file.url, '_blank')}
                                />
                              </div>
                            )).reverse()}
                          </div>
                        )}
                        <div
                          className={cn(
                            "flex max-w-[86%] flex-col gap-2 rounded-lg px-4 py-3",
                            message.role === "user"
                              ? "bg-primary text-primary-foreground"
                              : "bg-secondary"
                          )}
                        >
                          {message.role === "assistant" ? (
                            <div className="prose prose-sm dark:prose-invert max-w-none break-words text-base">
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>{message.content}</ReactMarkdown>
                            </div>
                          ) : (
                            <div className="prose prose-sm dark:prose-invert max-w-none break-words text-white text-base">
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>{message.content}</ReactMarkdown>
                            </div>
                          )}
                          <div className="flex items-center justify-between gap-2 text-xs opacity-70">
                            <div className="flex items-center gap-2">
                              {message.operationType && (
                                <span className="bg-primary/10 px-1 py-1 rounded-md">
                                  {operations.find(op => op.id === message.operationType)?.label || message.operationType}
                                </span>
                              )}
                              {/* <span>{message.role === "user" ? "你" : "AI"}</span> */}
                              <span>·</span>
                              <span>{message.timestamp}</span>
                            </div>
                            {message.role === "assistant" &&
                              !isLoading &&
                              message.data &&
                              (Array.isArray(message.data) ? message.data.length > 0 : true) &&
                              message.id === messages[messages.length - 1]?.id && (
                                <Button
                                  size="sm"
                                  onClick={handleDataConfirm}
                                  className="bg-green-500 hover:bg-green-600 text-white rounded-md px-2 py-1 text-xs flex items-center gap-1"
                                >
                                  <CheckCircle className="h-3 w-3" />
                                  <span>确认数据</span>
                                </Button>
                              )}
                          </div>
                          {isLoading && 
                           message.role === "user" && 
                           index === messages.length - 1 && (
                            <div className="flex items-center gap-1">
                              {["努", "力", "响", "应", "中", ".", ".", "."].map((char, i) => (
                                <motion.span
                                  key={i}
                                  animate={{
                                    y: [0, -3, 0]
                                  }}
                                  transition={{
                                    duration: 0.5,
                                    ease: "easeInOut",
                                    repeat: Infinity,
                                    repeatType: "loop",
                                    delay: i * 0.08
                                  }}
                                  className="text-xs text-white"
                                >
                                  {char}
                                </motion.span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </div>
              <div className="flex-none">
                <div className="border-t bg-white">
                  <CardContent className="px-6 py-4">
                    <form onSubmit={handlerSubmit} className="relative bg-gray-50/70 rounded-[20px] shadow-sm">
                      {uploadedFileInfos.length > 0 && (
                        <div className="flex flex-wrap gap-3 pt-2 pl-2 pr-2 pb-0">
                          {uploadedFileInfos.map(file => (
                            <div
                              key={file.fileId}
                              className={cn(
                                "relative flex items-center gap-2 px-4 py-2 rounded-lg border group",
                                file.isUploading
                                  ? "bg-gray-200 border-gray-200"
                                  : "bg-white hover:border-gray-300"
                              )}
                            >
                              {getFileIcon(file.contentType)}
                              <div className="flex flex-col gap-0">
                                <span className={cn(
                                  "text-sm truncate max-w-[150px] font-medium leading-tight",
                                  file.isUploading ? "text-gray-400" : "text-gray-600"
                                )}>
                                  {file.fileName}
                                </span>
                                <div className="flex items-center gap-1 text-xs leading-tight">
                                  <span className={cn(
                                    "font-medium",
                                    file.isUploading ? "text-gray-400" : "text-blue-500"
                                  )}>
                                    {file.fileExtension}
                                  </span>
                                  <span className="text-gray-400">
                                    {formatFileSize(file.fileSize)}
                                  </span>
                                </div>
                              </div>
                              {!file.isUploading && (
                                <button
                                  type="button"
                                  onClick={(e) => removeFile(file, e)}
                                  className="absolute -top-2 -right-2 h-5 w-5 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  ×
                                </button>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      <textarea
                        onChange={(e) => setPrompt(e.target.value)}
                        value={prompt}
                        placeholder="请直接输入指令或上传文件..."
                        className="w-full rounded-[20px] border-0 bg-transparent px-5 py-4 pb-12 text-[15px] \
                          focus:outline-none focus:ring-0 placeholder:text-gray-400 resize-none\
                          overflow-y-auto max-h-[200px] min-h-[80px]"
                        rows={1}
                      />
                      <div className="absolute bottom-2 right-3 flex items-center gap-3">
                        <div className="group relative">
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            onChange={handleFileChange}
                            multiple
                            accept=".pdf,.txt,.csv,.xlsx,.xls,.png,.jpg"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-gray-200/70 rounded-lg"
                            aria-label="附件"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Paperclip className="h-6 w-6 text-gray-500" />
                          </Button>
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            <div className="text-[15px]">
                              • 文件数量：最多 5 个<br />
                              • 图片数量：最多 5 个<br />
                              • 文件类型：pdf、png、jpg
                            </div>
                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 border-4 border-transparent border-t-gray-800"></div>
                          </div>
                        </div>
                        <div className="group relative">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-gray-200/70 rounded-lg"
                            aria-label="拍照"
                          >
                            <Camera className="h-6 w-6 text-gray-500" />
                          </Button>
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            拍照上传
                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 border-4 border-transparent border-t-gray-800"></div>
                          </div>
                        </div>
                        <div className="group relative">
                          <MicChat
                            onTranscript={handleTranscript}
                            isListening={isListening}
                            setIsListening={setIsListening}
                          />
                        </div>
                        <div className="group relative text-gray-300 px-3">|</div>
                        {isLoading ? (
                          <div className="group relative">
                            <CircleStop onClick={() => stop()} className="h-8 w-8 bg-white text-black rounded-full" />
                            <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                              停止生成
                            </div>
                          </div>
                        ) : (
                          <Button
                            size="icon"
                            className={cn(
                              "h-8 w-8 rounded-lg transition-colors",
                              prompt.trim() && selectedOperation
                                ? "bg-primary hover:bg-primary/90"
                                : "bg-gray-200 hover:bg-gray-300 cursor-not-allowed"
                            )}
                            disabled={!prompt.trim() || !selectedOperation}
                            type="submit"
                            aria-label="发送"
                          >
                            <SendHorizonal className={cn(
                              "h-6 w-6",
                              prompt.trim() && selectedOperation ? "text-white" : "text-gray-400"
                            )} />
                          </Button>
                        )}
                      </div>

                      {/* 添加操作类型警告提示 */}
                      {showOperationWarning && (
                        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 px-4 py-2 bg-red-50 text-red-600 rounded-md border border-red-200 text-sm whitespace-nowrap">
                          请先选择操作类型才能发送消息
                        </div>
                      )}
                    </form>
                  </CardContent>
                </div>

                <CardContent className=" p-4 border-t px-6 pb-3 pt-0">
                  <div className="flex flex-wrap gap-3 mt-4">
                    {operations.map(op => (
                      <div key={op.id} className="flex flex-col items-center">
                        <Button
                          variant={selectedOperation === op.id ? "default" : "outline"}
                          onClick={() => handleOperationSelect(op.id)}
                          className={`h-9 px-5 py-2 rounded-md text-[15px] font-medium transition-colors
                            ${selectedOperation === op.id
                              ? 'bg-primary/10 text-primary border-primary hover:bg-primary/20'
                              : 'bg-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 border-gray-200'
                            }`}
                        >
                          {op.label}
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
}
