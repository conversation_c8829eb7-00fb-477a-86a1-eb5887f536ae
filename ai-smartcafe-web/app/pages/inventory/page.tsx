"use client";
import ClientLayout from "@/components/layout/client-layout";
import { LazyPageContent } from "@/components/layout/LazyPageContent";
import { CoreOperations } from "@/app/pages/inventory/CoreOperations";

// 添加operations常量
const INVENTORY_OPERATIONS = [{
  id: "upload_expense",
  label: "上传报销单"
}, {
  id: "upload_contract",
  label: "上传合同"
}];

export default function Inventory() {
  return (
    <ClientLayout>
      <LazyPageContent>
        <div className="h-full relative overflow-hidden">
          <CoreOperations operations={INVENTORY_OPERATIONS} />
        </div>
      </LazyPageContent>
    </ClientLayout>
  );
}
