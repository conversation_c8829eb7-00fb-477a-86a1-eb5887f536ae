import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Timeline } from "antd";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Check, ChevronDown, ChevronUp, ChevronsUp, ChevronsDown, Lock, AlertCircle } from "lucide-react";
import "@/app/styles/checkout.css"

// 缓动函数 - 实现更自然的动画效果
const easeOutCubic = (t: number): number => {
  return 1 - Math.pow(1 - t, 3);
};

// 自定义动画进度条组件
const AnimatedProgress = ({
  isActive,
  duration = 1500,
}: {
  isActive: boolean;
  duration?: number;
}) => {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (!isActive) {
      setProgress(0);
      setIsComplete(false);
      return;
    }

    // 重置状态
    setProgress(0);
    setIsComplete(false);

    // 使用 requestAnimationFrame 实现丝滑动画
    let startTime: number | null = null;
    let animationId: number;

    const animate = (currentTime: number) => {
      if (startTime === null) {
        startTime = currentTime;
      }

      const elapsed = currentTime - startTime;
      const normalizedTime = Math.min(elapsed / duration, 1);

      // 使用缓动函数创建更自然的动画效果
      const easedProgress = easeOutCubic(normalizedTime);
      const progressPercent = easedProgress * 100;

      setProgress(progressPercent);

      if (normalizedTime >= 1) {
        // 动画完成，设置完成状态并停止动画
        setIsComplete(true);
        return;
      }

      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isActive, duration]);

  if (!isActive) {
    return null;
  }

  return (
    <div className="flex-1">
      <div className="relative h-3 bg-gradient-to-r from-purple-50 via-purple-100 to-purple-50 rounded-full overflow-hidden border border-purple-200/50 shadow-inner">
        {/* 背景轨道纹理 */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-100/30 via-purple-200/20 to-purple-100/30 rounded-full" />

        {/* 主进度条 */}
        <div
          className="absolute top-0 left-0 h-full rounded-full shadow-md transition-none"
          style={{
            width: `${progress}%`,
            background:
              "linear-gradient(135deg, #8b5cf6 0%, #a855f7 30%, #c084fc 60%, #a855f7 90%, #8b5cf6 100%)",
            boxShadow:
              progress > 0
                ? "0 1px 8px rgba(147, 51, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)"
                : "none",
          }}
        >
          {/* 动态流光效果 */}
          {progress > 1 && progress < 99 && (
            <div
              className="absolute top-0 h-full w-16 bg-gradient-to-r from-transparent via-white/50 to-transparent transform skew-x-12"
              style={{
                left: `${Math.max(0, progress - 20)}%`,
                opacity: 0.8,
                filter: "blur(0.5px)",
              }}
            />
          )}

          {/* 顶部高光 */}
          <div className="absolute inset-0 bg-gradient-to-b from-white/40 via-white/10 to-transparent rounded-full" />

          {/* 边缘光晕 */}
          <div
            className="absolute top-0 right-0 h-full w-2 bg-gradient-to-l from-white/60 to-transparent rounded-r-full"
            style={{
              opacity: progress > 5 ? 0.8 : 0,
              transition: "opacity 0.2s ease-out",
            }}
          />
        </div>

        {/* 完成时的轻微闪光 - 只闪一次 */}
        {isComplete && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full animate-pulse"
            style={{
              animationDuration: "0.6s",
              animationIterationCount: "1",
            }}
          />
        )}
      </div>
    </div>
  );
};

interface HistoryLog {
  id: string;
  operationType: "结账" | "反结账";
  operatorUser: string;
  operationTime: string;
}
interface MonthItem {
  name: string,
  completed?: boolean,
  // current: isLastMonth,
  locked?: boolean,
  clickable: boolean,
}

const Checkout = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>("");
  const [isCheckedOut, setIsCheckedOut] = useState<boolean>(false);
  const [isLoadingStatus, setIsLoadingStatus] = useState<boolean>(false);
  const [historyLogs, setHistoryLogs] = useState<HistoryLog[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [months,setMonths] = useState<MonthItem[]>([])

  const steps = [
    "保存并分析收入明细",
    "保存并分析支出明细",
    "保存并分析报销数据",
    "计算各项业务指标--汇总报表",
  ];

  const handleGenerateMonths = (startYearMonth:string)=>{
    const [startYear,startMonth] = startYearMonth.split('-').map(item=>Number(item))
    const months = [];
    // 生成12个月的数据，从当前月开始向前推移
    for (let i = 11; i >= 0; i--) {
      const date = new Date(startYear, startMonth - i);
      const yearMonth = `${date.getFullYear()}-${String(
        date.getMonth()
      ).padStart(2, "0")}`;
      const isLocked = yearMonth<prevYearMonth // 除了当前月和上个月，其他都是锁定状态
      months.push({
        name: yearMonth,
        completed: isLocked,
        locked: isLocked,
        clickable: true,
      });
    }
    if(Number(months[0]['name'].split('-').join(''))>=199101){
      months.unshift({name:'prev',clickable:true})
    }
    
    return months
  }
  const prevYearMonth = `${new Date().getFullYear()}-${String(
    new Date().getMonth()
  ).padStart(2, "0")}`
  const currentYearMonth = `${new Date().getFullYear()}-${String(
    new Date().getMonth() + 1
  ).padStart(2, "0")}`


  useEffect(()=>{
    setMonths(handleGenerateMonths(currentYearMonth))
  },[prevYearMonth])

  // 初始化选中月份
  useEffect(() => {
    if (!selectedMonth) {
      setSelectedMonth(prevYearMonth);
    }
  }, [selectedMonth]);

  // 处理月份点击事件
  const handleMonthClick = (monthName: string, clickable: boolean) => {
    if (clickable) {
      setSelectedMonth(monthName);
    }
  };

  // 查询结账状态
  const fetchCheckoutStatus = async (yearMonth: string) => {
    if (!yearMonth) return;

    setIsLoadingStatus(true);
    try {
      const accountId = 1; // 暂时写死为1
      const response = await fetch(
        `/api/check-out/check-out-status?accountId=${accountId}&yearMonth=${yearMonth}`
      );
      const data = await response.json();

      if (data.success) {
        setIsCheckedOut(data.isCheckedOut);
        setIsCompleted(data.isCheckedOut); // 同步更新isCompleted状态

        // 如果已结账，将所有步骤设置为完成状态
        if (data.isCheckedOut) {
          setCompletedSteps(new Array(steps.length).fill(true));
        } else {
          setCompletedSteps(new Array(steps.length).fill(false));
        }
      } else {
        console.error("查询结账状态失败:", data.message);
        setIsCheckedOut(false);
        setIsCompleted(false);
        setCompletedSteps(new Array(steps.length).fill(false));
      }
    } catch (error) {
      console.error("查询结账状态时出错:", error);
      setIsCheckedOut(false);
      setIsCompleted(false);
      setCompletedSteps(new Array(steps.length).fill(false));
    } finally {
      setIsLoadingStatus(false);
    }
  };

  // 获取历史操作日志
  const fetchHistoryLogs = async (yearMonth: string) => {
    if (!yearMonth) return;

    setIsLoadingHistory(true);
    try {
      const accountId = 1; // 暂时写死为1
      const response = await fetch(
        `/api/check-out/check-out-records?accountId=${accountId}&yearMonth=${yearMonth}`
      );
      const data = await response.json();

      if (data.success) {
        setHistoryLogs(data.data || []);
      } else {
        console.error("获取历史操作记录失败:", data.message);
        setHistoryLogs([]);
      }
    } catch (error) {
      console.error("获取历史操作记录时出错:", error);
      setHistoryLogs([]);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // 监听selectedMonth变化，查询结账状态和历史记录
  useEffect(() => {
    if (selectedMonth) {
      setErrorMessage(""); // 清除错误信息
      fetchCheckoutStatus(selectedMonth);
      fetchHistoryLogs(selectedMonth);
    }
  }, [selectedMonth]);

  useEffect(() => {
    setCompletedSteps(new Array(steps.length).fill(false));
  }, []);

  const handleCheckout = async () => {
    setIsProcessing(true);
    setCurrentStepIndex(0);
    setErrorMessage(""); // 清除之前的错误信息

    try {
      // 先调用API检查是否可以结账
      const response = await fetch(
        "/api/check-out/check-out-operation/do-check-out",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            accountId: 1,
            subAccountId: 7,
            yearMonth: selectedMonth,
          }),
        }
      );

      const data = await response.json();

      if (!data.success) {
        // 如果API返回错误，停止处理并显示错误信息
        setIsProcessing(false);
        setCurrentStepIndex(-1);
        setErrorMessage(data.message || "结账操作失败");
        return;
      }

      // API调用成功，开始执行动画
      for (let i = 0; i < steps.length; i++) {
        setCurrentStepIndex(i);

        await new Promise((resolve) => setTimeout(resolve, 1500));

        setCompletedSteps((prev) => {
          const newSteps = [...prev];
          newSteps[i] = true;
          return newSteps;
        });
      }

      // 动画完成
      setIsProcessing(false);
      setIsCompleted(true);
      setIsCheckedOut(true);
      setCurrentStepIndex(-1);

      // 重新查询当前月份的结账状态和历史记录
      if (selectedMonth) {
        await Promise.all([
          fetchCheckoutStatus(selectedMonth),
          fetchHistoryLogs(selectedMonth),
        ]);
      }
    } catch (error) {
      console.error("结账操作出错:", error);
      setIsProcessing(false);
      setCurrentStepIndex(-1);
      setErrorMessage("结账操作失败，请稍后重试");
    }
  };

  const handleUndo = async () => {
    setIsProcessing(true);
    setErrorMessage(""); // 清除之前的错误信息

    try {
      const response = await fetch(
        "/api/check-out/check-out-operation/undo-check-out",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            accountId: 1,
            subAccountId: 7,
            yearMonth: selectedMonth,
          }),
        }
      );

      const data = await response.json();
      if (!data.success) {
        setErrorMessage(data.message || "反结账操作失败");
        setIsProcessing(false);
        return;
      }

      setIsCompleted(false);
      setIsCheckedOut(false);
      setIsProcessing(false);
      setCurrentStepIndex(-1);
      setCompletedSteps(new Array(steps.length).fill(false));

      // 重新查询当前月份的结账状态和历史记录
      if (selectedMonth) {
        await Promise.all([
          fetchCheckoutStatus(selectedMonth),
          fetchHistoryLogs(selectedMonth),
        ]);
      }
    } catch (error) {
      console.error("反结账API调用失败:", error);
      setErrorMessage("反结账操作失败，请稍后重试");
      setIsProcessing(false);
    }
  };

  const toggleHistory = () => {
    console.log("Toggle history clicked, current state:", showHistory);
    setShowHistory(!showHistory);
  };

  const getDotByMonth = (month: any) => {
    if(month.name === 'prev'){
      return (
        <div onClick={()=>handleCalcPrevMonthList()}>
          <ChevronsUp className="w-7.5 h-7.5 text-purple-500 cursor-pointer transition-transform duration-200 hover:-translate-y-1" />
        </div>
      )
    } else if(month.name === 'next'){
      return (
        <div onClick={()=>handleCalcNextMonthList()}>
          <ChevronsDown className="w-7.5 h-7.5 text-purple-500 cursor-pointer transition-transform duration-200 hover:translate-y-1" />
        </div>
      )
    }
    if (month.locked) {
      return (
        <div className="w-4 h-4 rounded-full border-2 flex items-center justify-center bg-gray-400 border-gray-400">
          <Lock className="w-2.5 h-2.5 text-white" />
        </div>
      );
    }
    if (selectedMonth === month.name || month.completed) {
      return (
        <div className="w-4 h-4 rounded-full border-2 flex items-center justify-center bg-purple-500 border-purple-500">
          {month.completed && <Check className="w-2.5 h-2.5 text-white" />}
        </div>
      );
    }
    return (
      <div className="w-4 h-4 rounded-full border-2 flex items-center justify-center bg-white border-gray-300"></div>
    );
  };
  const handleCalcTimeline = ()=>{

    return months.map((month, index) => {
      if(month.name === 'prev'){
        return ({
          children: (<div className="w-24 px-3 py-1 "></div>),
          dot: getDotByMonth(month),
        })
      } else if(month.name === 'next'){
        return ({
          children: (<div className="h-0"></div>),
          dot: getDotByMonth(month),
        })
      } else {
        return ({
          children: (
            <div
              className={`w-24 px-3 py-1 rounded text-center text-sm font-medium transition-colors cursor-pointer hover:bg-purple-100 hover:text-gray-900 ${
                selectedMonth === month.name
                  ? "bg-purple-500 text-white"
                  : "text-gray-400"
              }`}
              onClick={() =>
                handleMonthClick(month.name, month.clickable)
              }
            >
              <span className="font-normal">{month.name}</span>
            </div>
          ),
          dot: getDotByMonth(month),
        })
      }
    })
  }

  const handleCalcPrevMonthList = ()=>{
    const [year,month] = months[1]['name'].split('-')
    const startYearMonth = `${year}-${String(
      Number(month) - 1
    ).padStart(2, "0")}`
    const monthList = handleGenerateMonths(startYearMonth)
    monthList.push({
      name:'next',
      clickable:true
    })
    setMonths(monthList)
  }
  const handleCalcNextMonthList = ()=>{
    const [year,month] = months[months.length-2]['name'].split('-')
    const startYearMonth = `${Number(year)+1}-${String(
      Number(month)
    ).padStart(2, "0")}`
    const monthList = handleGenerateMonths(startYearMonth)
    if(monthList[monthList.length-1]['name']!==currentYearMonth){
      monthList.push({
        name:'next',
        clickable:true
      })
    }
    setMonths(monthList)
  }

  return (
    <>
      <div className="w-full h-full">
        <motion.div
          className="w-full h-full bg-white p-6"
          // initial={{ opacity: 0, y: 20 }}
          // animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h1
            className="text-2xl font-bold mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
          >
            结账
          </motion.h1>
          <div className="flex" style={{ height: "calc(100% - 5rem)" }}>
            <div className="w-44 pt-6 px-6 h-full overflow-y-auto">
              <div className="">
                <Timeline
                  className="check-out-timeline"
                  items={handleCalcTimeline()}
                ></Timeline>
              </div>
            </div>
            <div className="grow p-6 pr-0 h-full overflow-y-auto">
              <div className="flex-1">
                {/* {
                  (selectedMonth===currentYearMonth || selectedMonth===prevYearMonth) ?(
                    <> */}
                    <div className="flex justify-center mb-8">
                  <div className="relative">
                    <div className="absolute inset-0 w-64 h-64 rounded-full border border-purple-200 opacity-30"></div>
                    <div className="absolute inset-2 w-60 h-60 rounded-full border border-purple-300 opacity-40"></div>
                    <div className="absolute inset-4 w-56 h-56 rounded-full border border-purple-300 opacity-50"></div>

                    {isLoadingStatus ? (
                      <Button
                        disabled={true}
                        className="relative z-10 w-48 h-48 rounded-full text-xl font-bold transition-all duration-300 bg-gray-400"
                        style={{ margin: "32px" }}
                      >
                        <div className="flex flex-col items-center">
                          <span className="text-xl mb-2">查询中...</span>
                        </div>
                      </Button>
                    ) : isCheckedOut ? (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            disabled={isProcessing}
                            className="relative z-10 w-48 h-48 rounded-full text-xl font-bold transition-all duration-300 bg-orange-500 hover:bg-orange-600"
                            style={{ margin: "32px" }}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-2xl mb-2">反结账</span>
                              <span className="text-lg"></span>
                              {isProcessing && (
                                <span className="text-sm mt-2">处理中...</span>
                              )}
                            </div>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认反结账</AlertDialogTitle>
                            <AlertDialogDescription>
                              是否对 {selectedMonth}{" "}
                              进行反结账？此操作将撤销该月度的结账状态。
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction onClick={handleUndo}>
                              确认
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    ) : (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            disabled={isProcessing}
                            className="relative z-10 w-48 h-48 rounded-full text-xl font-bold transition-all duration-300 bg-purple-500 hover:bg-purple-600"
                            style={{ margin: "32px" }}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-2xl mb-2">月度结账</span>
                              <span className="text-lg">{selectedMonth}</span>
                              {isProcessing && (
                                <span className="text-sm mt-2">处理中...</span>
                              )}
                            </div>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认月度结账</AlertDialogTitle>
                            <AlertDialogDescription>
                              是否确认对 {selectedMonth}{" "}
                              进行月度结账？结账完成后该月份数据将被封存，如需修改请先进行反结账操作。
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction onClick={handleCheckout}>
                              确认结账
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </div>
                   {/*  </>
                  ):(
                    <></>
                  )
                } */}
                

                {/* 错误信息显示 */}
                {errorMessage && (
                  <div className="mb-6">
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{errorMessage}</AlertDescription>
                    </Alert>
                  </div>
                )}

                {/* 结账进度卡片和历史操作日志并排显示 */}
                <div className="flex gap-6">
                  {/* 结账进度卡片 - 占一半宽度 */}
                  <div className="flex-1">
                    <Card className=" shadow-sm  border-gray-100">
                      <CardHeader>
                        <CardTitle className="text-lg text-gray-900">
                          {isCheckedOut
                            ? `${selectedMonth} 结账完成`
                            : isProcessing
                            ? `${selectedMonth} 结账进度`
                            : `${selectedMonth} 结账状态`}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4 min-h-40">
                        {isProcessing || isCheckedOut ? (
                          steps.map((step, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50"
                            >
                              <div
                                className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                                  completedSteps[index]
                                    ? "bg-green-500 border-green-500"
                                    : currentStepIndex === index
                                    ? "border-purple-500 bg-purple-50"
                                    : "border-gray-300 bg-white"
                                }`}
                              >
                                {completedSteps[index] && (
                                  <Check className="w-4 h-4 text-white" />
                                )}
                                {currentStepIndex === index &&
                                  !completedSteps[index] && (
                                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                  )}
                              </div>

                              <span
                                className={`flex-1 ${
                                  completedSteps[index]
                                    ? "text-green-700 font-medium"
                                    : "text-gray-700"
                                }`}
                              >
                                {step}
                              </span>

                              <AnimatedProgress
                                isActive={
                                  currentStepIndex === index &&
                                  !completedSteps[index]
                                }
                                duration={1500}
                              />
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            {selectedMonth
                              ? `点击"月度结账"开始 ${selectedMonth} 的结账流程`
                              : "请先选择要结账的月份"}
                          </div>
                        )}

                        {isCheckedOut && (
                          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center">
                              <Check className="w-5 h-5 text-green-600 mr-2" />
                              <span className="text-green-700 font-medium">
                                全部完成
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-2">
                              当月如有特殊需求，可进行反结账，不支持跨月操作
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>

                  {/* 历史操作日志 - 占一半宽度 */}
                  <div className="flex-1">
                    <Card className=" shadow-sm  border-gray-100">
                      <CardHeader>
                        <CardTitle className="text-lg text-gray-900">
                          历史操作日志 {selectedMonth && `(${selectedMonth})`}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="common-table min-h-40">
                        {isLoadingHistory ? (
                          <div className="text-center py-8 text-gray-500">
                            加载中...
                          </div>
                        ) : (
                          <table>
                            <thead>
                              <tr>
                                <th className="w-1/3 text-center whitespace-nowrap">
                                  操作类型
                                </th>
                                <th className="w-1/3 text-center whitespace-nowrap">
                                  操作用户
                                </th>
                                <th className="w-1/3 text-center whitespace-nowrap">
                                  操作时间
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {historyLogs.length > 0 ? (
                                historyLogs.map((log) => (
                                  <tr key={log.id}>
                                    <td className="text-center px-2 py-2 whitespace-nowrap">
                                      <span
                                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                          log.operationType === "结账"
                                            ? "bg-green-100 text-green-800"
                                            : "bg-orange-100 text-orange-800"
                                        }`}
                                      >
                                        {log.operationType}
                                      </span>
                                    </td>
                                    <td className="text-gray-700 text-center px-2 py-2 whitespace-nowrap">
                                      {log.operatorUser}
                                    </td>
                                    <td className="text-gray-500 text-center px-2 py-2 whitespace-nowrap">
                                      {log.operationTime}
                                    </td>
                                  </tr>
                                ))
                              ) : (
                                <tr>
                                  <td
                                    colSpan={3}
                                    className="text-center py-8 text-gray-500"
                                  >
                                    暂无操作记录
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default Checkout;
