"use client";
import ClientLayout from "@/components/layout/client-layout";
import ExpenseType from "@/app/pages/settings/expense-type/ExpenseType";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

const ExpenseTypePage = () => {
  const router = useRouter();

  return (
    <ClientLayout>
      <div className="">
        <div className="px-6 pt-6">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 hover:bg-background hover:text-primary"
            onClick={() => router.push("/pages/settings")}
          >
            <ArrowLeft className="h-4 w-4" />
            <span>返回</span>
          </Button>
        </div>
        <ExpenseType />
      </div>
    </ClientLayout>
  );
};

export default ExpenseTypePage;
