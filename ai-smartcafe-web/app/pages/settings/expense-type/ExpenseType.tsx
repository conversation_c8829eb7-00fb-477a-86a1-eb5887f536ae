import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  PlusCircle,
  Trash2,
  Edit2,
  Edit,
  Save,
  ChevronRight,
  ChevronDown,
  ChevronsDownUp,
  ChevronsUpDown,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import HeaderTitle from "@/components/setting/HeaderTitle";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Confirm } from '@/components/ui/confirm';
// 修改接口以支持层级结构
interface ExpenseSubtype {
  id: string;
  name: string;
  notes: string;
  isEditing: boolean;
}

interface NewExpenseSubtype extends Omit<ExpenseSubtype, "id"> {
  id: string;
}

interface ExpenseType {
  id: string;
  name: string;
  expense_nature: string;
  notes: string;
  isEditing: boolean;
  subtypes: ExpenseSubtype[];
  isExpanded: boolean;
}

interface NewExpenseType extends Omit<ExpenseType, "id"> {
  id: string;
}

const ExpenseType = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAllExpanded, setIsAllExpanded] = useState(true);

  // 已保存的支出类型
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  // 新添加的支出类型
  const [newExpenseTypes, setNewExpenseTypes] = useState<NewExpenseType[]>([]);

  // 获取支出类型数据
  const refreshExpenseTypes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/expense-types");
      const result = await response.json();

      if (result.success) {
        const formattedExpenseTypes = result.data.expenseTypes.map(
          (type: any) => ({
            id: String(type.id),
            name: type.name,
            expense_nature: type.expense_nature || "经营支出",
            notes: type.notes,
            isEditing: false,
            isExpanded: true,
            subtypes: type.subtypes.map((subtype: any) => ({
              id: String(subtype.id),
              name: subtype.name,
              notes: subtype.notes,
              isEditing: false,
            })),
          })
        );
        setExpenseTypes(formattedExpenseTypes);
      } else {
        toast.error("获取支出类型数据失败");
      }
    } catch (error) {
      console.error("获取支出类型数据时出错:", error);
      toast.error("获取数据失败");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshExpenseTypes();
  }, []);

  const toggleAllExpand = () => {
    setExpenseTypes(
      expenseTypes.map((expenseType) => {
        return { ...expenseType, isExpanded: !expenseType.isExpanded };
      })
    );
    setIsAllExpanded(!isAllExpanded);
  };

  // 切换展开/折叠状态
  const toggleExpand = (id: string) => {
    setExpenseTypes(
      expenseTypes.map((expenseType) =>
        expenseType.id === id
          ? { ...expenseType, isExpanded: !expenseType.isExpanded }
          : expenseType
      )
    );
  };

  // 添加一级类型
  const addNewParentType = () => {
    const newEntry: NewExpenseType = {
      id: `temp-${Date.now()}`,
      name: "",
      expense_nature: "经营支出",
      notes: "",
      isEditing: true,
      isExpanded: true,
      subtypes: [],
    };
    setNewExpenseTypes([...newExpenseTypes, newEntry]);
  };

  // 添加二级类型
  const addNewSubtype = (parentId: string) => {
    const isNewParent = parentId.startsWith("temp-");
    if (isNewParent) {
      setNewExpenseTypes(
        newExpenseTypes.map((expenseType) => {
          if (expenseType.id === parentId) {
            const newSubtype: NewExpenseSubtype = {
              id: `${parentId}-sub-${Date.now()}`,
              name: "",
              notes: "",
              isEditing: true,
            };
            return {
              ...expenseType,
              subtypes: [...expenseType.subtypes, newSubtype],
              isExpanded: true,
            };
          }
          return expenseType;
        })
      );
    } else {
      setExpenseTypes(
        expenseTypes.map((expenseType) => {
          if (expenseType.id === parentId) {
            const newSubtype: NewExpenseSubtype = {
              id: `${parentId}-sub-${Date.now()}`,
              name: "",
              notes: "",
              isEditing: true,
            };
            return {
              ...expenseType,
              subtypes: [...expenseType.subtypes, newSubtype],
              isExpanded: true,
            };
          }
          return expenseType;
        })
      );
    }
  };

  // 删除一级类型
  const deleteParentType = async (id: string) => {
    let confirmFlag = await Confirm('确认要执行删除操作？','确认操作');
    if(!confirmFlag){
      return
    }
    const isNewParent = id.startsWith("temp-");
    if (isNewParent) {
      setNewExpenseTypes(newExpenseTypes.filter((type) => type.id !== id));
      toast.success("已删除新添加的一级类型");
    } else {
      try {
        const response = await fetch("/api/expense-types", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ id, type: "parent" }),
        });

        const result = await response.json();

        if (result.success) {
          setExpenseTypes(expenseTypes.filter((type) => type.id !== id));
          toast.success("已删除一级类型及其所有二级类型");
        } else {
          toast.error(result.message || "删除失败，请重试");
        }
      } catch (error) {
        console.error("删除支出类型时出错:", error);
        toast.error("删除失败，请重试");
      }
    }
  };

  // 删除二级类型
  const deleteSubtype = async (parentId: string, subtypeId: string) => {
    const isNewParent = parentId.startsWith("temp-");
    if (isNewParent) {
      setNewExpenseTypes(
        newExpenseTypes.map((expenseType) => {
          if (expenseType.id === parentId) {
            return {
              ...expenseType,
              subtypes: expenseType.subtypes.filter(
                (subtype) => subtype.id !== subtypeId
              ),
            };
          }
          return expenseType;
        })
      );
      toast.success("已删除新添加的二级类型");
    } else {
      let confirmFlag = await Confirm('确认要执行删除操作？','确认操作');
    if(!confirmFlag){
      return
    }
      // 检查subtypeId是否包含-sub-
      if (!subtypeId.includes("-sub-")) {
        try {
          const response = await fetch("/api/expense-types", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id: subtypeId, type: "subtype" }),
          });

          const result = await response.json();

          if (result.success) {
            setExpenseTypes(
              expenseTypes.map((expenseType) => {
                if (expenseType.id === parentId) {
                  return {
                    ...expenseType,
                    subtypes: expenseType.subtypes.filter(
                      (subtype) => subtype.id !== subtypeId
                    ),
                  };
                }
                return expenseType;
              })
            );
            toast.success("已删除二级类型");
          } else {
            toast.error(result.message || "删除失败，请重试");
          }
        } catch (error) {
          console.error("删除二级类型时出错:", error);
          toast.error("删除失败，请重试");
        }
      } else {
        // 如果包含-sub-，直接更新状态
        setExpenseTypes(
          expenseTypes.map((expenseType) => {
            if (expenseType.id === parentId) {
              return {
                ...expenseType,
                subtypes: expenseType.subtypes.filter(
                  (subtype) => subtype.id !== subtypeId
                ),
              };
            }
            return expenseType;
          })
        );
        toast.success("已删除二级类型");
      }
    }
  };

  // 切换一级类型编辑状态
  const toggleParentEdit = async (id: string) => {
    const type = expenseTypes.find((t) => t.id === id);
    if (!type) return;

    if (type.isEditing) {
      if (!type.name.trim()) {
        toast.error("请填写支出类型名称");
        return;
      }

      try {
        const typeToUpdate = {
          id: type.id,
          name: type.name,
          expense_nature: type.expense_nature,
          notes: type.notes,
        };

        const response = await fetch("/api/expense-types", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify([typeToUpdate]),
        });

        const result = await response.json();

        if (result.success) {
          // After successful save, reload the data
          const fetchResponse = await fetch("/api/expense-types");
          const fetchResult = await fetchResponse.json();

          if (fetchResult.success) {
            const formattedExpenseTypes = fetchResult.data.expenseTypes.map(
              (type: any) => ({
                id: String(type.id),
                name: type.name,
                expense_nature: type.expense_nature || "经营支出",
                notes: type.notes,
                isEditing: false,
                isExpanded: true,
                subtypes: type.subtypes.map((subtype: any) => ({
                  id: String(subtype.id),
                  name: subtype.name,
                  notes: subtype.notes,
                  isEditing: false,
                })),
              })
            );
            setExpenseTypes(formattedExpenseTypes);
            toast.success("保存成功");
          }
        } else {
          toast.error(result.message || "保存失败，请重试");
        }
      } catch (error) {
        console.error("保存数据时出错:", error);
        toast.error("保存失败，请重试");
      }
    } else {
      // 直接切换编辑状态，不需要从数据库获取数据
      setExpenseTypes(
        expenseTypes.map((t) => (t.id === id ? { ...t, isEditing: true } : t))
      );


      // toggleSubtypeEdit(parentType.id, subtype.id)
      // }
 
    }
  };

  // 切换二级类型编辑状态
  const toggleSubtypeEdit = async (parentId: string, subtypeId: string) => {
    const parentType = expenseTypes.find((t) => t.id === parentId);
    if (!parentType) {
      toast.error("请先保存一级类型后再编辑二级类型");
      return;
    }

    const subtype = parentType.subtypes.find((s) => s.id === subtypeId);
    if (!subtype) return;

    if (subtype.isEditing) {
      if (!subtype.name.trim()) {
        toast.error("请填写二级类型名称");
        return;
      }

      try {
        const response = await fetch("/api/expense-types", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parentId,
            subtype: {
              ...subtype,
              id: subtype.id.includes("-sub-") ? null : subtype.id,
            },
          }),
        });

        const result = await response.json();

        if (result.success) {
          // 重新获取数据
          await refreshExpenseTypes();
          toast.success("保存成功");
        } else {
          toast.error(result.message || "保存失败，请重试");
        }
      } catch (error) {
        console.error("保存数据时出错:", error);
        toast.error("保存失败，请重试");
      }
    } else {
      // 直接切换编辑状态
      setExpenseTypes(
        expenseTypes.map((expenseType) => {
          if (expenseType.id === parentId) {
            const updatedSubtypes = expenseType.subtypes.map((s) =>
              s.id === subtypeId ? { ...s, isEditing: true } : s
            );
            return { ...expenseType, subtypes: updatedSubtypes };
          }
          return expenseType;
        })
      );
    }
  };

  // 处理一级类型字段变更
  const handleParentChange = (
    id: string,
    field: keyof ExpenseType,
    value: string
  ) => {
    setExpenseTypes(
      expenseTypes.map((expenseType) =>
        expenseType.id === id
          ? { ...expenseType, [field]: value.trim() }
          : expenseType
      )
    );
  };

  // 处理二级类型字段变更
  const handleSubtypeChange = (
    parentId: string,
    subtypeId: string,
    field: keyof ExpenseSubtype,
    value: string
  ) => {
    setExpenseTypes(
      expenseTypes.map((expenseType) => {
        if (expenseType.id === parentId) {
          const updatedSubtypes = expenseType.subtypes.map((subtype) =>
            subtype.id === subtypeId
              ? { ...subtype, [field]: value.trim() }
              : subtype
          );
          return { ...expenseType, subtypes: updatedSubtypes };
        }
        return expenseType;
      })
    );
  };

  // 保存所有数据
  const saveData = async () => {
    // 验证现有支出类型
    const invalidExistingTypes = expenseTypes.some((type) => !type.name.trim());
    if (invalidExistingTypes) {
      toast.error("现有支出类型中，名称不能为空");
      return;
    }

    // 验证新支出类型
    const invalidNewTypes = newExpenseTypes.some((type) => !type.name.trim());
    if (invalidNewTypes) {
      toast.error("新支出类型中，名称不能为空");
      return;
    }

    // 验证所有二级类型
    const invalidSubtypes = [...expenseTypes, ...newExpenseTypes].some(
      (parent) => parent.subtypes.some((subtype) => !subtype.name.trim())
    );
    if (invalidSubtypes) {
      toast.error("所有二级类型名称不能为空");
      return;
    }

    try {
      // 保存新的一级类型
      const newParentTypes = newExpenseTypes.map((type) => ({
        name: type.name,
        expense_nature: type.expense_nature,
        notes: type.notes,
        subtypes: type.subtypes.map((subtype) => ({
          name: subtype.name,
          notes: subtype.notes,
        })),
      }));

      const response = await fetch("/api/expense-types", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newParentTypes),
      });

      const result = await response.json();

      if (result.success) {
        // 重新获取最新的支出类型数据
        await refreshExpenseTypes();
        setNewExpenseTypes([]); // 清空新添加的支出类型
        toast.success("所有数据已保存");
        router.push("/pages/settings"); // 返回设置页面
      } else {
        toast.error(result.message || "保存失败，请重试");
      }
    } catch (error) {
      console.error("保存支出类型数据时出错:", error);
      toast.error("保存失败，请重试");
    }
  };

  // 处理新支出类型字段变更
  const handleNewTypeChange = (
    id: string,
    field: keyof NewExpenseType,
    value: string
  ) => {
    setNewExpenseTypes(
      newExpenseTypes.map((type) =>
        type.id === id ? { ...type, [field]: value.trim() } : type
      )
    );
  };

  // 处理新二级类型字段变更
  const handleNewSubtypeChange = (
    parentId: string,
    subtypeId: string,
    field: keyof NewExpenseSubtype,
    value: string
  ) => {
    setNewExpenseTypes(
      newExpenseTypes.map((expenseType) => {
        if (expenseType.id === parentId) {
          return {
            ...expenseType,
            subtypes: expenseType.subtypes.map((subtype) =>
              subtype.id === subtypeId
                ? { ...subtype, [field]: value.trim() }
                : subtype
            ),
          };
        }
        return expenseType;
      })
    );
  };

  // 保存新支出类型
  const saveNewType = async (type: NewExpenseType) => {
    if (!type.name.trim()) {
      toast.error("请填写支出类型名称");
      return;
    }

    try {
      const typeToSave = {
        name: type.name,
        expense_nature: type.expense_nature,
        notes: type.notes,
      };

      const response = await fetch("/api/expense-types", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify([typeToSave]),
      });

      const result = await response.json();

      if (result.success) {
        await refreshExpenseTypes();
        setNewExpenseTypes(newExpenseTypes.filter((t) => t.id !== type.id));
        toast.success("保存成功");
      } else {
        toast.error(result.message || "保存失败，请重试");
      }
    } catch (error) {
      console.error("保存数据时出错:", error);
      toast.error("保存失败，请重试");
    }
  };

  // 取消新支出类型
  const cancelNewType = (id: string) => {
    setNewExpenseTypes(newExpenseTypes.filter((type) => type.id !== id));
  };

  // 保存新二级类型
  const saveNewSubtype = async (
    parentId: string,
    subtype: NewExpenseSubtype
  ) => {
    if (!subtype.name.trim()) {
      toast.error("请填写二级类型名称");
      return;
    }

    try {
      const subtypeToSave = {
        name: subtype.name,
        notes: subtype.notes,
        parentId: parentId,
      };

      const response = await fetch("/api/expense-subtypes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify([subtypeToSave]),
      });

      const result = await response.json();

      if (result.success) {
        await refreshExpenseTypes();
        setNewExpenseTypes(
          newExpenseTypes.map((expenseType) => {
            if (expenseType.id === parentId) {
              return {
                ...expenseType,
                subtypes: expenseType.subtypes.filter(
                  (s) => s.id !== subtype.id
                ),
              };
            }
            return expenseType;
          })
        );
        toast.success("保存成功");
      } else {
        toast.error(result.message || "保存失败，请重试");
      }
    } catch (error) {
      console.error("保存数据时出错:", error);
      toast.error("保存失败，请重试");
    }
  };

  // 取消新二级类型
  const cancelNewSubtype = (parentId: string, subtypeId: string) => {
    setNewExpenseTypes(
      newExpenseTypes.map((expenseType) => {
        if (expenseType.id === parentId) {
          return {
            ...expenseType,
            subtypes: expenseType.subtypes.filter(
              (subtype) => subtype.id !== subtypeId
            ),
          };
        }
        return expenseType;
      })
    );
  };

  return (
    <div className="mx-auto p-6">
      <div className="text-center pt-0 mb-6">
        <motion.h1
          className="text-2xl font-bold m-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          支出类型设置
        </motion.h1>
        <motion.p
          className="text-gray-500 mt-0 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.3 }}
        >
          请设置您的支出类型，以便系统能够正确分类您的支出。
        </motion.p>
      </div>
      <motion.div
        className="bg-white rounded-lg shadow-sm p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              支出类型设置
            </h2>
            <p className="text-gray-500 mt-0 text-sm">
            </p>
          </div>
          <div className="flex gap-3">
            <Button onClick={saveData} size="lg" className="gap-2">
              保存所有
            </Button>
          </div>
        </div>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mb-4"></div>
            <p className="text-gray-600">正在加载...</p>
          </div>
        ) : (
          <div className="rounded-lg overflow-hidden">
          <Table className="m-0">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/3 text-center">
                        一级类型
                      </TableHead>
                      <TableHead className="w-2/3 text-center">
                        二级类型
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <AnimatePresence>
                      {expenseTypes.map((parentType) => (
                        <tr
                          key={parentType.id}
                         
                          className="border-t border-gray-100"
                        >
                          {/* 一级类型列 */}
                          <TableCell className="align-top border-r border-gray-100 p-0">
                            <div className="p-4">
                              {parentType.isEditing ? (
                                <div className="space-y-3">
                                  <input
                                    type="text"
                                    value={parentType.name}
                                    onChange={(e) =>
                                      handleParentChange(
                                        parentType.id,
                                        "name",
                                        e.target.value
                                      )
                                    }
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                                    placeholder="请输入一级类型名称"
                                  />

                                  <div className="w-full">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                      支出性质
                                    </label>
                                    <Select
                                  value={parentType.expense_nature}
                                  onValueChange={(value) =>
                                    handleParentChange(
                                      parentType.id,
                                      "expense_nature",
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger className="w-full whitespace-nowrap bg-white rounded-lg px-2 [&>span]:px-[2px]">
                                    <SelectValue placeholder="选择类型" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="经营支出" className="">
                                      <span>经营支出</span>
                                    </SelectItem>
                                    <SelectItem value="非经营支出" className="">
                                      <span>非经营支出</span>
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                  </div>

                                  <input
                                    type="text"
                                    value={parentType.notes}
                                    onChange={(e) =>
                                      handleParentChange(
                                        parentType.id,
                                        "notes",
                                        e.target.value
                                      )
                                    }
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                                    placeholder="备注信息（选填）"
                                  />
                                  <div className="flex gap-2 justify-end mt-2">
                                    <motion.button
                                      onClick={() =>
                                        toggleParentEdit(parentType.id)
                                      }
                                      className="p-1.5 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                                      whileHover={{ scale: 1.1 }}
                                      whileTap={{ scale: 0.9 }}
                                    >
                                      <Save size={17} />
                                    </motion.button>
                                    <motion.button
                                      onClick={() =>
                                        deleteParentType(parentType.id)
                                      }
                                      className="p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                      whileHover={{ scale: 1.1 }}
                                      whileTap={{ scale: 0.9 }}
                                    >
                                      <Trash2 size={17} />
                                    </motion.button>
                                  </div>
                                </div>
                              ) : (
                                <div>
                                  <div className="flex items-center justify-between">
                                    <div
                                      className="cursor-pointer flex items-center gap-1 font-medium text-gray-800"
                                      onClick={() =>
                                        toggleExpand(parentType.id)
                                      }
                                    >
                                      {parentType.isExpanded ? (
                                        <ChevronDown
                                          size={18}
                                          className="text-gray-500"
                                        />
                                      ) : (
                                        <ChevronRight
                                          size={18}
                                          className="text-gray-500"
                                        />
                                      )}
                                      <span>{parentType.name}</span>
                                      <span
                                        className={`ml-2 text-xs px-2 py-1 rounded-full ${
                                          parentType.expense_nature ===
                                          "经营支出"
                                            ? "bg-green-100 text-green-800"
                                            : "bg-yellow-100 text-yellow-800"
                                        }`}
                                      >
                                        {parentType.expense_nature}
                                      </span>
                                    </div>
                                    <div className="flex gap-1">
                                      <motion.button
                                        onClick={() =>
                                          toggleParentEdit(parentType.id)
                                        }
                                        className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                      >
                                        <Edit2 size={16} />
                                      </motion.button>
                                      <motion.button
                                        onClick={() =>
                                          deleteParentType(parentType.id)
                                        }
                                        className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                      >
                                        <Trash2 size={16} />
                                      </motion.button>
                                    </div>
                                  </div>
                                  {parentType.notes && (
                                    <p className="text-sm text-gray-500 mt-1">
                                      {parentType.notes}
                                    </p>
                                  )}
                                </div>
                              )}
                            </div>
                          </TableCell>

                          {/* 二级类型列 */}
                          <TableCell className="p-0 align-top">
                            <div className="p-4">
                              {parentType.isExpanded && (
                                <div className="space-y-4">
                                  {parentType.subtypes.map((subtype) => (
                                    <div
                                      key={subtype.id}
                                      className="border-b border-gray-100 pb-3 last:border-0 last:pb-0"
                                    >
                                      {subtype.isEditing ? (
                                        <div className="space-y-3">
                                          <input
                                            type="text"
                                            value={subtype.name}
                                            onChange={(e) =>
                                              handleSubtypeChange(
                                                parentType.id,
                                                subtype.id,
                                                "name",
                                                e.target.value
                                              )
                                            }
                                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                                            placeholder="请输入二级类型名称"
                                          />
                                          <input
                                            type="text"
                                            value={subtype.notes}
                                            onChange={(e) =>
                                              handleSubtypeChange(
                                                parentType.id,
                                                subtype.id,
                                                "notes",
                                                e.target.value
                                              )
                                            }
                                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                                            placeholder="备注信息（选填）"
                                          />
                                          <div className="flex gap-2 justify-end mt-2">
                                            <motion.button
                                              onClick={() =>
                                                toggleSubtypeEdit(
                                                  parentType.id,
                                                  subtype.id
                                                )
                                              }
                                              className="p-1.5 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.9 }}
                                            >
                                              <Save size={17} />
                                            </motion.button>
                                            <motion.button
                                              onClick={() =>
                                                deleteSubtype(
                                                  parentType.id,
                                                  subtype.id
                                                )
                                              }
                                              className="p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.9 }}
                                            >
                                              <Trash2 size={17} />
                                            </motion.button>
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="flex items-center justify-between">
                                          <div>
                                            <span className="text-gray-800">
                                              {subtype.name}
                                            </span>
                                            {subtype.notes && (
                                              <p className="text-sm text-gray-500 mt-1">
                                                {subtype.notes}
                                              </p>
                                            )}
                                          </div>
                                          <div className="flex gap-1">
                                            <motion.button
                                              onClick={() =>
                                                toggleSubtypeEdit(
                                                  parentType.id,
                                                  subtype.id
                                                )
                                              }
                                              className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.9 }}
                                            >
                                              <Edit2 size={16} />
                                            </motion.button>
                                            <motion.button
                                              onClick={() =>
                                                deleteSubtype(
                                                  parentType.id,
                                                  subtype.id
                                                )
                                              }
                                              className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.9 }}
                                            >
                                              <Trash2 size={16} />
                                            </motion.button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}

                                  {/* 添加二级类型按钮 */}
                                  <motion.div
                                    className="flex justify-center mt-4"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.2 }}
                                  >
                                    <button
                                      onClick={() =>
                                        addNewSubtype(parentType.id)
                                      }
                                      className="px-3 py-1.5 text-blue-500 flex items-center gap-1.5 hover:text-blue-700 transition-colors focus:outline-none text-sm"
                                    >
                                      <PlusCircle size={16} />
                                      <span>添加二级类型</span>
                                    </button>
                                  </motion.div>
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </tr>
                      ))}
                      {newExpenseTypes.map((parentType) => (
                        <tr
                          key={parentType.id}
                         
                          className="border-t border-gray-100 bg-violet-50"
                        >
                          {/* 一级类型列 */}
                          <TableCell className="align-top border-r border-gray-100 p-0">
                            <div className="p-4">
                              <div className="space-y-3">
                                <input
                                  type="text"
                                  value={parentType.name}
                                  onChange={(e) =>
                                    handleNewTypeChange(
                                      parentType.id,
                                      "name",
                                      e.target.value
                                    )
                                  }
                                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white"
                                  placeholder="请输入一级类型名称"
                                />

                                <div className="w-full">
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    支出性质
                                  </label>
                                  <Select
                                  value={parentType.expense_nature}
                                  onValueChange={(value) =>
                                    handleNewTypeChange(
                                      parentType.id,
                                      "expense_nature",
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger className="w-full whitespace-nowrap bg-white rounded-lg px-2 [&>span]:px-[2px]">
                                    <SelectValue placeholder="选择类型" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="经营支出" className="">
                                      <span>经营支出</span>
                                    </SelectItem>
                                    <SelectItem value="非经营支出" className="">
                                      <span>非经营支出</span>
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                </div>

                                <input
                                  type="text"
                                  value={parentType.notes}
                                  onChange={(e) =>
                                    handleNewTypeChange(
                                      parentType.id,
                                      "notes",
                                      e.target.value
                                    )
                                  }
                                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white"
                                  placeholder="备注信息（选填）"
                                />
                                <div className="flex gap-2 justify-end mt-2">
                                  <motion.button
                                    onClick={() => saveNewType(parentType)}
                                    className="p-1.5 text-violet-500 hover:text-violet-700 hover:bg-violet-50 rounded-md transition-colors"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Save size={17} />
                                  </motion.button>
                                  <motion.button
                                    onClick={() => cancelNewType(parentType.id)}
                                    className="p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Trash2 size={17} />
                                  </motion.button>
                                </div>
                              </div>
                            </div>
                          </TableCell>

                          {/* 二级类型列 */}
                          <TableCell className="p-0 align-top">
                            <div className="p-4">
                              {parentType.isExpanded && (
                                <div className="space-y-4">
                                  {parentType.subtypes.map((subtype) => (
                                    <div
                                      key={subtype.id}
                                      className="border-b border-gray-100 pb-3 last:border-0 last:pb-0"
                                    >
                                      <div className="space-y-3">
                                        <input
                                          type="text"
                                          value={subtype.name}
                                          onChange={(e) =>
                                            handleNewSubtypeChange(
                                              parentType.id,
                                              subtype.id,
                                              "name",
                                              e.target.value
                                            )
                                          }
                                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white"
                                          placeholder="请输入二级类型名称"
                                        />
                                        <input
                                          type="text"
                                          value={subtype.notes}
                                          onChange={(e) =>
                                            handleNewSubtypeChange(
                                              parentType.id,
                                              subtype.id,
                                              "notes",
                                              e.target.value
                                            )
                                          }
                                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all bg-white"
                                          placeholder="备注信息（选填）"
                                        />
                                        <div className="flex gap-2 justify-end mt-2">
                                          <motion.button
                                            onClick={() =>
                                              saveNewSubtype(
                                                parentType.id,
                                                subtype
                                              )
                                            }
                                            className="p-1.5 text-violet-500 hover:text-violet-700 hover:bg-violet-50 rounded-md transition-colors"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                          >
                                            <Save size={17} />
                                          </motion.button>
                                          <motion.button
                                            onClick={() =>
                                              cancelNewSubtype(
                                                parentType.id,
                                                subtype.id
                                              )
                                            }
                                            className="p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                          >
                                            <Trash2 size={17} />
                                          </motion.button>
                                        </div>
                                      </div>
                                    </div>
                                  ))}

                                  {/* 添加二级类型按钮 */}
                                  <motion.div
                                    className="flex justify-center mt-4"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.2 }}
                                  >
                                    <button
                                      onClick={() =>
                                        addNewSubtype(parentType.id)
                                      }
                                      className="px-3 py-1.5 text-blue-500 flex items-center gap-1.5 hover:text-blue-700 transition-colors focus:outline-none text-sm"
                                    >
                                      <PlusCircle size={16} />
                                      <span>添加二级类型</span>
                                    </button>
                                  </motion.div>
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </tr>
                      ))}
                    </AnimatePresence>
                  </TableBody>
                </Table>
                <motion.div
              className="mt-6 flex justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <button
                onClick={addNewParentType}
                className="px-4 py-2 text-violet-500 flex items-center gap-1.5 hover:text-violet-700 transition-colors focus:outline-none"
              >
                <PlusCircle size={18} />
                <span>添加一级支出类型</span>
              </button>
            </motion.div>
                </div>
        )}
      </motion.div>
    </div>
  );
};

export default ExpenseType;
