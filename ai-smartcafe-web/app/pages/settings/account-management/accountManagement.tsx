"use client";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence, Variants } from "framer-motion";
import { Button } from "@/components/ui/button";
import { toast as sonnerToast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON>cOff,
  Edit,
  Trash2,
  Save,
  BookText,
  FileText,
  X,
  Check,
  PlusCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Confirm } from '@/components/ui/confirm';

export interface BankAccount {
  id: string;
  bankName: string;
  accountHolder: string;
  accountNumber: string;
}

export interface UserAccount {
  companyName: string;
  phoneNumber: string;
  email: string;
  isActive: boolean;
  maxUsersAllowed?: number; // Added based on API
  maxStores?: number; // Added based on API
}
interface BankAccountFormProps {
  account?: BankAccount;
  onSubmit: (account: Omit<BankAccount, "id">) => void;
  onCancel: () => void;
}

const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};



const BankAccountForm = ({
  account,
  onSubmit,
  onCancel,
}: BankAccountFormProps) => {
  const [formData, setFormData] = useState({
    bankName: "",
    accountHolder: "",
    accountNumber: "",
  });

  useEffect(() => {
    if (account) {
      setFormData({
        bankName: account.bankName,
        accountHolder: account.accountHolder,
        accountNumber: account.accountNumber,
      });
    } else {
      // Clear form if no account is provided (e.g., when switching from edit to add)
      setFormData({ bankName: "", accountHolder: "", accountNumber: "" });
    }
  }, [account]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="border rounded-md p-6 mb-6 bg-white">
      <h3 className="text-lg font-medium mb-4">
        {account ? "编辑银行账户" : "添加银行账户"}
      </h3>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label
              htmlFor="bankName"
              className="block text-sm text-gray-700 mb-1"
            >
              开户行
            </label>
            <input
              id="bankName"
              type="text"
              name="bankName"
              value={formData.bankName}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300"
              required
            />
          </div>

          <div>
            <label
              htmlFor="accountHolder"
              className="block text-sm text-gray-700 mb-1"
            >
              开户名
            </label>
            <input
              id="accountHolder"
              type="text"
              name="accountHolder"
              value={formData.accountHolder}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300"
              required
            />
          </div>

          <div>
            <label
              htmlFor="accountNumber"
              className="block text-sm text-gray-700 mb-1"
            >
              银行账号
            </label>
            <input
              id="accountNumber"
              type="text"
              name="accountNumber"
              value={formData.accountNumber}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300"
              required
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#9b87f5] text-white rounded-md hover:bg-[#8671e0] transition-colors"
          >
            保存
          </button>
        </div>
      </form>
    </div>
  );
};

const AccountManagementPage = () => {
  const { toast } = useToast();
  const [userAccount, setUserAccount] = useState<UserAccount>({
    companyName: "",
    phoneNumber: "",
    email: "",
    isActive: true,
    maxUsersAllowed: 5,
    maxStores: 0,
  });
  const [originalUserAccount, setOriginalUserAccount] =
    useState<UserAccount | null>(null);
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [isAddingAccount, setIsAddingAccount] = useState(false);
  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isSavingProfile, setIsSavingProfile] = useState(false);
  const [isCompanyProfileEstablished, setIsCompanyProfileEstablished] =
    useState(false);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [initialLoadError, setInitialLoadError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setInitialLoadError(null);
      try {
        const companyRes = await fetch("/api/company-profile/info");
        if (companyRes.ok) {
          const companyData = await companyRes.json();
          setUserAccount(companyData);
          setOriginalUserAccount(JSON.parse(JSON.stringify(companyData)));
          setIsCompanyProfileEstablished(true);
          setIsEditingProfile(false);

          const bankAccountsRes = await fetch(
            "/api/company-profile/bank-accounts"
          );
          if (bankAccountsRes.ok) {
            const bankData = await bankAccountsRes.json();
            setBankAccounts(bankData);
          } else {
            toast({ title: "获取银行账户列表失败", variant: "destructive" });
          }
        } else {
          if (companyRes.status === 404) {
            toast({
              title: "提示",
              description: "账套信息未设置，请填写并保存。",
            });
            setIsCompanyProfileEstablished(false);
            setIsEditingProfile(true);
            setOriginalUserAccount(JSON.parse(JSON.stringify(userAccount)));
          } else {
            const errorData = await companyRes
              .json()
              .catch(() => ({ message: "获取公司信息失败" }));
            toast({
              title: "获取公司信息失败",
              description: errorData.message,
              variant: "destructive",
            });
            setInitialLoadError(errorData.message || "获取公司信息失败");
            setIsCompanyProfileEstablished(false);
            setIsEditingProfile(true);
          }
        }
      } catch (error) {
        toast({
          title: "加载数据出错",
          description: String(error),
          variant: "destructive",
        });
        setInitialLoadError(String(error));
        setIsCompanyProfileEstablished(false);
        setIsEditingProfile(true);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [toast]);

  const handleAddAccount = async (accountData: Omit<BankAccount, "id">) => {
    if (!isCompanyProfileEstablished) {
      toast({
        title: "操作受限",
        description: "请先保存账套基本信息后再添加银行账户。",
        variant: "default",
      });
      return;
    }
    try {
      const response = await fetch("/api/company-profile/bank-accounts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(accountData),
      });
      const result = await response.json();
      if (response.ok) {
        setBankAccounts((prev) => [...prev, result]);
        setIsAddingAccount(false);
        toast({ title: "银行账户已添加", description: result.message });
      } else {
        toast({
          title: "添加失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "添加银行账户出错",
        description: String(error),
        variant: "destructive",
      });
    }
  };

  const handleEditAccount = async (accountData: Omit<BankAccount, "id">) => {
    if (!editingAccount) return;
    try {
      const response = await fetch(
        `/api/company-profile/bank-accounts/${editingAccount.id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(accountData),
        }
      );
      const result = await response.json();
      if (response.ok) {
        setBankAccounts((prevAccounts) =>
          prevAccounts.map((acc) =>
            acc.id === editingAccount.id
              ? { ...acc, ...accountData, id: editingAccount.id }
              : acc
          )
        );
        setEditingAccount(null);
        toast({ title: "银行账户已更新", description: result.message });
      } else {
        toast({
          title: "更新失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "更新银行账户出错",
        description: String(error),
        variant: "destructive",
      });
    }
  };

  const handleDeleteAccount = async (id: string) => {
    // if (!confirm("确定要删除此银行账户吗？")) return;
    let confirmFlag = await Confirm('确定要删除此银行账户吗？','确认操作');
    if(!confirmFlag){
      return
    }
    try {
      const response = await fetch(`/api/company-profile/bank-accounts/${id}`, {
        method: "DELETE",
      });
      const result = await response.json();
      if (response.ok) {
        setBankAccounts((prev) => prev.filter((acc) => acc.id !== id));
        toast({ title: "银行账户已删除", description: result.message });
      } else {
        toast({
          title: "删除失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "删除银行账户出错",
        description: String(error),
        variant: "destructive",
      });
    }
  };

  const handleUserAccountChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    let processedValue: string | boolean | number = value;

    if (type === "checkbox") {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (name === "maxUsersAllowed") {
      processedValue = parseInt(value, 5);
      if (isNaN(processedValue) || processedValue < 1) processedValue = 1; // Min 1
    }

    setUserAccount((prev) => ({
      ...prev,
      [name]: processedValue,
    }));
  };

  const handleSaveUserAccount = async () => {
    if (!userAccount.companyName || !userAccount.phoneNumber) {
      toast({
        title: "保存失败",
        description: "公司名称和手机号为必填项。",
        variant: "destructive",
      });
      return;
    }
    if (!/^1[3-9]\d{9}$/.test(userAccount.phoneNumber)) {
      toast({
        title: "保存失败",
        description: "请输入有效的11位手机号码。",
        variant: "destructive",
      });
      return;
    }

    setIsSavingProfile(true);
    try {
      const response = await fetch("/api/company-profile/info", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userAccount),
      });
      const result = await response.json();
      if (response.ok) {
        toast({ title: "账套信息已保存", description: result.message });
        setOriginalUserAccount(JSON.parse(JSON.stringify(userAccount)));
        setIsCompanyProfileEstablished(true);
        setIsEditingProfile(false);

        if (!bankAccounts.length && isCompanyProfileEstablished) {
          const bankAccountsRes = await fetch(
            "/api/company-profile/bank-accounts"
          );
          if (bankAccountsRes.ok) {
            const bankData = await bankAccountsRes.json();
            setBankAccounts(bankData);
          }
        }
      } else {
        toast({
          title: "保存失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "保存账套信息出错",
        description: String(error),
        variant: "destructive",
      });
    } finally {
      setIsSavingProfile(false);
    }
  };

  const handleEditProfileClick = () => {
    setOriginalUserAccount(JSON.parse(JSON.stringify(userAccount)));
    setIsEditingProfile(true);
  };

  const handleCancelEditProfile = () => {
    if (originalUserAccount) {
      setUserAccount(originalUserAccount);
    }
    setIsEditingProfile(false);
    if (!isCompanyProfileEstablished) {
      setIsEditingProfile(true);
      toast({ title: "提示", description: "账套信息尚未保存，请完成设置。" });
    }
  };

  const startEditAccount = (id: string) => {
    const accountToEdit = bankAccounts.find((acc) => acc.id === id);
    if (accountToEdit) {
      setEditingAccount(accountToEdit);
      setIsAddingAccount(false);
    }
  };

  const [inlineEditEntry, setInlineEditEntry] = useState<BankAccount>({
    id: "",
    bankName: "",
    accountHolder: "",
    accountNumber: "",
  });

  const [inlineEditingId, setInlineEditingId] = useState<string | null>(null);

  const handleInlineEdit = (id: string) => {
    const accountToEdit = bankAccounts.find((acc) => acc.id === id);
    if (accountToEdit) {
      setInlineEditEntry(accountToEdit);
      setInlineEditingId(id);
    }
  };

  const handleCancelInlineEdit = () => {
    setInlineEditingId(null);
    setInlineEditEntry({
      id: "",
      bankName: "",
      accountHolder: "",
      accountNumber: "",
    });
  };

  const handleSaveInlineEdit = async () => {
    if (!inlineEditingId) return;
    let accountData = inlineEditEntry;
    try {
      const response = await fetch(
        `/api/company-profile/bank-accounts/${accountData.id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(accountData),
        }
      );
      const result = await response.json();
      if (response.ok) {
        setBankAccounts((prevAccounts) =>
          prevAccounts.map((acc) =>
            acc.id === inlineEditEntry.id
              ? { ...acc, ...accountData, id: inlineEditEntry.id }
              : acc
          )
        );
        setInlineEditingId(null);
        setInlineEditEntry({
          id: "",
          bankName: "",
          accountHolder: "",
          accountNumber: "",
        });
        toast({ title: "银行账户已更新", description: result.message });
      } else {
        toast({
          title: "更新失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "更新银行账户出错",
        description: String(error),
        variant: "destructive",
      });
    }
  };

  const [newAccount, setNewAccount] = useState({
    bankName: "",
    accountHolder: "",
    accountNumber: "",
  });

  const handleAddAccountClick = () => {
    setIsAddingAccount(true);
    handleCancelInlineEdit();
    setNewAccount({
      bankName: "",
      accountHolder: "",
      accountNumber: "",
    });
  };

  const handleNewAccountInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof BankAccount
  ) => {
    setNewAccount({
      ...newAccount,
      [field]: e.target.value,
    });
  };

  const handleSaveNewAccount = async () => {
    if (!isCompanyProfileEstablished) {
      toast({
        title: "操作受限",
        description: "请先保存账套基本信息后再添加银行账户。",
        variant: "default",
      });
      return;
    }
    if (
      !newAccount.bankName ||
      !newAccount.accountHolder ||
      !newAccount.accountNumber
    ) {
      sonnerToast.error("请填写所有必填字段");
      return;
    }
    try {
      const response = await fetch("/api/company-profile/bank-accounts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newAccount),
      });
      const result = await response.json();
      if (response.ok) {
        setBankAccounts((prev) => [...prev, result]);
        setIsAddingAccount(false);
        toast({ title: "银行账户已添加", description: result.message });
      } else {
        toast({
          title: "添加失败",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "添加银行账户出错",
        description: String(error),
        variant: "destructive",
      });
    }
  };

  const handleCancelAddAccount = () => {
    setIsAddingAccount(false);
    setNewAccount({
      bankName: "",
      accountHolder: "",
      accountNumber: "",
    });
  };

  if (isLoading && !isCompanyProfileEstablished && initialLoadError === null) {
    // return <div className="h-full w-full p-6 text-center">加载中...</div>;
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mb-4"></div>
        <p className="text-gray-600">正在加载...</p>
      </div>
    );
  }

  return (
    <div className="h-full w-full p-6">
      {initialLoadError && !isCompanyProfileEstablished && (
        <Card className="mb-6 shadow-sm border-red-500 bg-red-50">
          <CardHeader>
            <CardTitle className="text-lg text-red-700">加载错误</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              加载初始账套信息失败：{initialLoadError}
            </p>
            <p className="text-red-600 mt-2">
              请检查网络连接或联系管理员。您仍然可以尝试填写并保存下方的账套基本信息。
            </p>
          </CardContent>
        </Card>
      )}
      <Card className="mb-6 shadow-sm border-0">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">账套基本信息</CardTitle>
          <div className="flex space-x-2">
            <div className="flex items-center mt-2">
              <label
                htmlFor="isActive"
                className={`flex items-center ${!isEditingProfile && isCompanyProfileEstablished
                    ? "cursor-not-allowed"
                    : "cursor-pointer"
                  }`}
              >
                <div className="relative">
                  <input
                    id="isActive"
                    type="checkbox"
                    name="isActive"
                    checked={userAccount.isActive}
                    onChange={handleUserAccountChange}
                    className="sr-only"
                    disabled={!isEditingProfile && isCompanyProfileEstablished}
                  />
                  <div
                    className={`block w-10 h-6 rounded-full transition-colors ${userAccount.isActive ? "bg-[#9b87f5]" : "bg-gray-300"
                      } ${!isEditingProfile && isCompanyProfileEstablished
                        ? "opacity-70"
                        : ""
                      }`}
                  ></div>
                  <div
                    className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${userAccount.isActive ? "translate-x-full" : ""
                      }`}
                  ></div>
                </div>
                <div
                  className={`ml-3 text-gray-700 font-medium ${!isEditingProfile && isCompanyProfileEstablished
                      ? "opacity-70"
                      : ""
                    }`}
                >
                  {userAccount.isActive ? "活跃" : "已停用"}
                </div>
              </label>
            </div>
            {/* {isCompanyProfileEstablished && !isEditingProfile && (
              <button
                onClick={handleEditProfileClick}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
                // disabled={isSavingProfile}
                disabled={true}
              >
                编辑
              </button>
            )}
            {(isEditingProfile || !isCompanyProfileEstablished) && (
              <>
                <button
                  onClick={handleSaveUserAccount}
                  className="px-4 py-2 bg-[#9b87f5] text-white rounded-md hover:bg-[#8671e0] transition-colors text-sm disabled:opacity-50"
                  disabled={isSavingProfile}
                >
                  {isSavingProfile ? "保存中..." : "保存账套信息"}
                </button>
                {isCompanyProfileEstablished && isEditingProfile && (
                  <button
                    onClick={handleCancelEditProfile}
                    className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors text-sm"
                    disabled={isSavingProfile}
                  >
                    取消
                  </button>
                )}
              </>
            )} */}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="companyName"
                className="block text-sm text-gray-700 mb-1"
              >
                公司名称
              </label>
              <input
                id="companyName"
                type="text"
                name="companyName"
                value={userAccount.companyName}
                onChange={handleUserAccountChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
                disabled={!isEditingProfile && isCompanyProfileEstablished}
              />
            </div>

            <div>
              <label
                htmlFor="phoneNumber"
                className="block text-sm text-gray-700 mb-1"
              >
                手机号（唯一标识）
              </label>
              <input
                id="phoneNumber"
                type="tel"
                name="phoneNumber"
                value={userAccount.phoneNumber}
                onChange={handleUserAccountChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
                pattern="^1[3-9]\d{9}$"
                title="请输入有效的11位手机号码"
                disabled={!isEditingProfile && isCompanyProfileEstablished}
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm text-gray-700 mb-1"
              >
                邮箱
              </label>
              <input
                id="email"
                type="email"
                name="email"
                value={userAccount.email}
                onChange={handleUserAccountChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 disabled:bg-gray-100 disabled:cursor-not-allowed"
                disabled={!isEditingProfile && isCompanyProfileEstablished}
              />
            </div>
            {/* <div>
              <label htmlFor="maxUsersAllowed" className="block text-sm text-gray-700 mb-1">最大门店数量</label>
              <input
                id="maxUsersAllowed"
                name="maxUsersAllowed"
                value={userAccount.maxUsersAllowed}
                onChange={handleUserAccountChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 disabled:bg-gray-100 disabled:cursor-not-allowed"
                min="1"
                required
                disabled={!isEditingProfile && isCompanyProfileEstablished}
              />
            </div> */}
            <div>
              <label
                htmlFor="maxStores"
                className="block text-sm text-gray-700 mb-1"
              >
                最大门店数量
              </label>
              <input
                id="maxStores"
                name="maxStores"
                value={userAccount.maxStores}
                onChange={handleUserAccountChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 disabled:bg-gray-100 disabled:cursor-not-allowed"
                min="1"
                required
                disabled={!isEditingProfile && isCompanyProfileEstablished}
              />
            </div>

            {/* <div>
              <p className="text-sm text-gray-500 mb-1">账套状态</p>
              <div className="flex items-center mt-2">
                <label htmlFor="isActive" className={`flex items-center ${(!isEditingProfile && isCompanyProfileEstablished) ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                  <div className="relative">
                    <input
                      id="isActive"
                      type="checkbox"
                      name="isActive"
                      checked={userAccount.isActive}
                      onChange={handleUserAccountChange}
                      className="sr-only"
                      disabled={!isEditingProfile && isCompanyProfileEstablished}
                    />
                    <div className={`block w-10 h-6 rounded-full transition-colors ${userAccount.isActive ? 'bg-[#9b87f5]' : 'bg-gray-300'} ${(!isEditingProfile && isCompanyProfileEstablished) ? 'opacity-70' : ''}`}></div>
                    <div className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${userAccount.isActive ? 'translate-x-full' : ''}`}></div>
                  </div>
                  <div className={`ml-3 text-gray-700 font-medium ${(!isEditingProfile && isCompanyProfileEstablished) ? 'opacity-70' : ''}`}>
                    {userAccount.isActive ? "活跃" : "已停用"}
                  </div>
                </label>
              </div>
            </div> */}
          </div>
        </CardContent>
      </Card>

      <Card
        className={`shadow-sm border-0 ${isLoading ? "opacity-50 cursor-not-allowed" : ""
          }`}
      >
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">银行账户</CardTitle>
          {/* {isCompanyProfileEstablished && (
            <Button
              className=""
              onClick={handleAddAccountClick}
              disabled={
                isLoading ||
                !isCompanyProfileEstablished ||
                isAddingAccount ||
                inlineEditingId != null
              }
            >
              添加银行账户
            </Button>
          )} */}
          {/* {isCompanyProfileEstablished &&
            !isAddingAccount &&
            !editingAccount && (
              <Button
                className=""
                onClick={handleAddAccountClick}
                disabled={isLoading || !isCompanyProfileEstablished}
              >
                添加银行账户
              </Button>
            )} */}
        </CardHeader>
        <CardContent>
          {!isCompanyProfileEstablished ? (
            <div className="text-center py-8 bg-gray-50 border rounded-md">
              <p className="text-gray-500">
                请先保存账套基本信息，然后才能管理银行账户。
              </p>
            </div>
          ) : initialLoadError && bankAccounts.length === 0 ? (
            <div className="text-center py-8 bg-yellow-50 border border-yellow-300 rounded-md">
              <p className="text-yellow-700">
                无法加载银行账户列表。请确保账套信息已正确保存并检查网络连接。
              </p>
            </div>
          ) : (
            <>
              <div className="common-table">
                <table className="">
                  <thead className="">
                    <tr className="text-left">
                      <th className="w-1/2 text-center whitespace-nowrap">
                        开户行
                      </th>
                      <th className="w-1/7 text-center whitespace-nowrap">
                        开户名
                      </th>
                      <th className="w-1/7 text-center whitespace-nowrap">
                        银行账号
                      </th>
                      <th className="w-1/12 text-center whitespace-nowrap">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <motion.tbody
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <AnimatePresence>

                      {bankAccounts.map((account: BankAccount, index) => (
                        <tr
                          key={account.id}
                          className={`hover:bg-violet-50 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"
                            }`}
                        >
                          {inlineEditingId === account.id ? (
                            <>
                              <td>
                                <input
                                  id="bankName"
                                  type="text"
                                  name="bankName"
                                  value={inlineEditEntry.bankName}
                                  onChange={(e) =>
                                    setInlineEditEntry({
                                      ...inlineEditEntry,
                                      bankName: e.target.value,
                                    })
                                  }
                                  className="text-center"
                                  required
                                />
                              </td>
                              <td>
                                <input
                                  id="accountHolder"
                                  type="text"
                                  name="accountHolder"
                                  value={inlineEditEntry.accountHolder}
                                  onChange={(e) =>
                                    setInlineEditEntry({
                                      ...inlineEditEntry,
                                      accountHolder: e.target.value,
                                    })
                                  }
                                  className="text-center"
                                  required
                                />
                              </td>
                              <td>
                                <input
                                  id="accountNumber"
                                  type="text"
                                  name="accountNumber"
                                  value={inlineEditEntry.accountNumber}
                                  onChange={(e) =>
                                    setInlineEditEntry({
                                      ...inlineEditEntry,
                                      accountNumber: e.target.value,
                                    })
                                  }
                                  className="text-center"
                                  required
                                />
                              </td>
                            </>
                          ) : (
                            <>
                              <td className="px-2 py-2 text-center">
                                {account.bankName}
                              </td>
                              <td className="px-2 py-2 text-center">
                                {account.accountHolder}
                              </td>
                              <td className="px-2 py-2 text-center">
                                {account.accountNumber}
                              </td>
                            </>
                          )}
                          <td className="px-2 py-2 text-center">
                            <div className="flex justify-center space-x-3">
                              {inlineEditingId === account.id ? (
                                <>
                                  <button
                                    onClick={handleSaveInlineEdit}
                                    className={`whitespace-nowrap text-violet-500 hover:text-violet-700 flex items-center text-sm`}
                                    title="保存"
                                  >
                                    <Save size={16} className="mr-1" />
                                    保存
                                  </button>
                                  <button
                                    onClick={handleCancelInlineEdit}
                                    className="whitespace-nowrap text-gray-500 hover:text-gray-700 flex items-center text-sm"
                                    title="取消编辑"
                                  >
                                    <X size={16} className="mr-1" />
                                    取消
                                  </button>
                                </>
                              ) : (
                                <>
                                  <button
                                    onClick={() => handleInlineEdit(account.id)}
                                    className={`whitespace-nowrap text-blue-500 hover:text-blue-700 flex items-center text-sm`}
                                    title="编辑"
                                  >
                                    <Edit size={16} className="mr-1" />
                                    编辑
                                  </button>
                                  <button
                                    onClick={() =>
                                      handleDeleteAccount(account.id)
                                    }
                                    className={`whitespace-nowrap flex items-center text-sm text-red-500 hover:text-red-700`}
                                    title="删除"
                                  >
                                    <Trash2 size={16} className="mr-1" />
                                    删除
                                  </button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                      {bankAccounts.length === 0 && (
                        <tr key="nodata">
                          <td
                            colSpan={5}
                            className="text-gray-500 px-4 py-3 text-center"
                          >
                            暂无银行账户信息，请添加一个。
                          </td>
                        </tr>
                      )}
                      {isAddingAccount && (
                        <tr className="border-t border-gray-100 bg-violet-50">
                          <td className="">
                            <input
                              type="text"
                              value={newAccount.bankName}
                              onChange={(e) =>
                                handleNewAccountInputChange(e, "bankName")
                              }
                              placeholder="请输入开户行"
                              className="text-center"
                            />
                          </td>
                          <td className="">
                            <input
                              type="text"
                              value={newAccount.accountHolder}
                              onChange={(e) =>
                                handleNewAccountInputChange(e, "accountHolder")
                              }
                              placeholder="请输入开户名"
                              className="text-center"
                            />
                          </td>
                          <td className="">
                            <input
                              type="text"
                              value={newAccount.accountNumber}
                              onChange={(e) =>
                                handleNewAccountInputChange(e, "accountNumber")
                              }
                              placeholder="请输入银行账号"
                              className="text-center"
                            />
                          </td>
                          <td className="px-2 py-2 text-center">
                            <div className="flex justify-center space-x-3">
                              <button
                                onClick={handleSaveNewAccount}
                                className="text-[#9b87f5] hover:text-[#8671e0] flex items-center text-sm"
                              >
                                <Save size={16} className="mr-1" />
                                保存
                              </button>
                              <button
                                onClick={handleCancelAddAccount}
                                className="text-gray-500 hover:text-gray-700 flex items-center text-sm"
                              >
                                <X size={16} className="mr-1" />
                                取消
                              </button>
                            </div>
                          </td>
                        </tr>
                      )}
                    </AnimatePresence>
                  </motion.tbody>
                </table>
              </div>
              {isCompanyProfileEstablished && (
                <motion.div
                  className="mt-6 flex justify-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <button
                    onClick={handleAddAccountClick}
                    disabled={
                      isLoading ||
                      !isCompanyProfileEstablished ||
                      isAddingAccount ||
                      inlineEditingId != null
                    }
                    className="px-4 py-2 text-violet-500 flex items-center gap-1.5 hover:text-violet-700 transition-colors focus:outline-none"
                  >
                    <PlusCircle size={18} />
                    <span>添加银行账户</span>
                  </button>
                </motion.div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AccountManagementPage;
