import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence, Variants } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Edit,
  Trash2,
  Save,
  BookText,
  FileText,
  X,
  Check,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

import { Card, CardContent } from "@/components/ui/card";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";

import { analyzeDescription } from "@/app/api/knowledge-base/KnowledgeBaseAnalyzer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Confirm } from '@/components/ui/confirm';
interface KnowledgeEntry {
  id: string;
  content: string;
  category: "business-rule" | "special-description";
  type: number; // 对应数据库 type: 1=业务规则，2=特殊描述
  account_id: number;
  sub_account_id: number;
  created_at: string;
  updated_at: string;
}

interface AnalyzedItem {
  id: string;
  content: string;
  category: "business-rule" | "special-description";
  type: number;
}

interface SuggestionResult {
  id: string;
  content: string;
  type: number;
}

const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};


const KnowledgeBase = () => {
  const { toast } = useToast();
  const [entries, setEntries] = useState<KnowledgeEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const [newEntry, setNewEntry] = useState<Partial<KnowledgeEntry>>({
    content: "",
    category: "business-rule",
    type: 1,
    account_id: 1,
    sub_account_id: 1,
  });

  const [editingId, setEditingId] = useState<string | null>(null);
  const [inlineEditingId, setInlineEditingId] = useState<string | null>(null);
  const [inlineEditEntry, setInlineEditEntry] = useState<
    Partial<KnowledgeEntry>
  >({
    content: "",
    category: "business-rule",
    type: 1,
  });

  const [isRecording, setIsRecording] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(
    null
  );

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [analyzedItems, setAnalyzedItems] = useState<AnalyzedItem[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // 添加模糊搜索相关状态
  const [searchSuggestions, setSearchSuggestions] = useState<
    SuggestionResult[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorPosition, setCursorPosition] = useState({ top: 0, left: 0 });
  const [searchingLine, setSearchingLine] = useState("");
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const suggestionsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  useEffect(() => {
    fetchKnowledgeEntries();

    return () => {
      // 组件卸载时清理
      if (suggestionsTimeoutRef.current) {
        clearTimeout(suggestionsTimeoutRef.current);
      }

      // 清理语音识别
      if (recognition) {
        recognition.stop();
      }
    };
  }, []);

  const fetchKnowledgeEntries = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/knowledge-base?accountId=1`);
      const result = await response.json();

      if (result.success) {
        const formattedEntries = result.data.map((item: any) => ({
          id: item.id.toString(),
          content: item.content,
          category: item.type === 1 ? "business-rule" : "special-description",
          type: item.type,
          account_id: item.account_id,
          sub_account_id: item.sub_account_id,
          created_at: item.created_at,
          updated_at: item.updated_at,
        }));

        const sortedEntries = formattedEntries.sort(
          (a: KnowledgeEntry, b: KnowledgeEntry) =>
            parseInt(b.id) - parseInt(a.id)
        );

        setEntries(sortedEntries);
      } else {
        toast({
          title: "错误",
          description: result.message || "获取知识库数据失败",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("获取知识库数据出错:", error);
      toast({
        title: "错误",
        description: "获取知识库数据失败",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateId = () => {
    return Math.random().toString(36).substring(2, 9);
  };

  const handleAddEntry = async () => {
    if (!newEntry.content) {
      toast({
        title: "错误",
        description: "内容不能为空",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);

    try {
      const result = await analyzeDescription(newEntry.content);

      const items: AnalyzedItem[] = [];

      if (result.业务规则 && result.业务规则.length > 0) {
        result.业务规则.forEach((rule) => {
          items.push({
            id: generateId(),
            content: rule,
            category: "business-rule",
            type: 1,
          });
        });
      }

      if (result.特殊描述 && result.特殊描述.length > 0) {
        result.特殊描述.forEach((desc) => {
          items.push({
            id: generateId(),
            content: desc,
            category: "special-description",
            type: 2,
          });
        });
      }

      if (items.length === 0) {
        items.push({
          id: generateId(),
          content: newEntry.content,
          category: "business-rule",
          type: 1,
        });
      }

      setAnalyzedItems(items);
      setIsConfirmDialogOpen(true);
    } catch (error) {
      console.error("分析知识内容出错:", error);
      toast({
        title: "错误",
        description: "分析知识内容失败",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleConfirmItems = async () => {
    const itemsToSave = analyzedItems.map((item) => ({
      accountId: 1,
      subAccountId: 1,
      content: item.content,
      type: item.type,
    }));

    try {
      const response = await fetch("/api/knowledge-base", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(itemsToSave),
      });

      const result = await response.json();

      if (result.success) {
        fetchKnowledgeEntries();
        setNewEntry({
          content: "",
          category: "business-rule",
          type: 1,
          account_id: 1,
          sub_account_id: 1,
        });

        setIsConfirmDialogOpen(false);

        toast({
          title: "成功",
          description: `已添加 ${itemsToSave.length} 条知识条目`,
        });
      } else {
        toast({
          title: "错误",
          description: result.message || "添加知识条目失败",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("添加知识条目出错:", error);
      toast({
        title: "错误",
        description: "添加知识条目失败",
        variant: "destructive",
      });
    }
  };

  const handleCancelDialog = () => {
    setIsConfirmDialogOpen(false);
    setAnalyzedItems([]);
  };

  const handleItemContentChange = (id: string, content: string) => {
    setAnalyzedItems(
      analyzedItems.map((item) =>
        item.id === id ? { ...item, content: content.trim() } : item
      )
    );
  };

  const handleItemCategoryChange = (
    id: string,
    category: "business-rule" | "special-description"
  ) => {
    setAnalyzedItems((prev) =>
      prev.map((item) => {
        if (item.id === id) {
          const type = category === "business-rule" ? 1 : 2;
          return { ...item, category, type };
        }
        return item;
      })
    );
  };

  const handleRemoveAnalyzedItem = (id: string) => {
    setAnalyzedItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleEdit = (id: string) => {
    const entryToEdit = entries.find((entry) => entry.id === id);
    if (entryToEdit) {
      setNewEntry({
        content: entryToEdit.content,
        category: entryToEdit.category,
        type: entryToEdit.type,
        account_id: entryToEdit.account_id,
        sub_account_id: entryToEdit.sub_account_id,
      });
      setEditingId(id);
    }
  };

  const handleInlineEdit = (id: string) => {
    const entryToEdit = entries.find((entry) => entry.id === id);
    if (entryToEdit) {
      setInlineEditEntry({
        content: entryToEdit.content,
        category: entryToEdit.category,
        type: entryToEdit.type,
      });
      setInlineEditingId(id);
    }
  };

  const handleCancelInlineEdit = () => {
    setInlineEditingId(null);
    setInlineEditEntry({
      content: "",
      category: "business-rule",
      type: 1,
    });
  };

  const handleSaveInlineEdit = async () => {
    if (!inlineEditingId) return;
    if (!inlineEditEntry.content) {
      toast({
        title: "错误",
        description: "内容不能为空",
        variant: "destructive",
      });
      return;
    }

    const type = inlineEditEntry.category === "business-rule" ? 1 : 2;

    try {
      const response = await fetch("/api/knowledge-base", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: inlineEditingId,
          accountId: 1,
          subAccountId: 1,
          content: inlineEditEntry.content,
          type: type,
        }),
      });

      const result = await response.json();

      if (result.success) {
        fetchKnowledgeEntries();
        setInlineEditingId(null);
        setInlineEditEntry({
          content: "",
          category: "business-rule",
          type: 1,
        });

        toast({
          title: "成功",
          description: "知识条目已更新",
        });
      } else {
        toast({
          title: "错误",
          description: result.message || "更新知识条目失败",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("更新知识条目出错:", error);
      toast({
        title: "错误",
        description: "更新知识条目失败",
        variant: "destructive",
      });
    }
  };

  const handleSaveEdit = async () => {
    if (!editingId) return;
    if (!newEntry.content) {
      toast({
        title: "错误",
        description: "内容不能为空",
        variant: "destructive",
      });
      return;
    }

    const type = newEntry.category === "business-rule" ? 1 : 2;

    try {
      const response = await fetch("/api/knowledge-base", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: editingId,
          accountId: 1,
          subAccountId: 1,
          content: newEntry.content,
          type: type,
        }),
      });

      const result = await response.json();

      if (result.success) {
        fetchKnowledgeEntries();
        setNewEntry({
          content: "",
          category: "business-rule",
          type: 1,
          account_id: 1,
          sub_account_id: 1,
        });
        setEditingId(null);

        toast({
          title: "成功",
          description: "知识条目已更新",
        });
      } else {
        toast({
          title: "错误",
          description: result.message || "更新知识条目失败",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("更新知识条目出错:", error);
      toast({
        title: "错误",
        description: "更新知识条目失败",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string) => {
    let confirmFlag = await Confirm('确认要执行删除操作？','确认操作');
    if(!confirmFlag){
      return
    }
    try {
      const response = await fetch("/api/knowledge-base", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id }),
      });

      const result = await response.json();

      if (result.success) {
        fetchKnowledgeEntries();

        toast({
          title: "成功",
          description: "知识条目已删除",
        });
      } else {
        toast({
          title: "错误",
          description: result.message || "删除知识条目失败",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("删除知识条目出错:", error);
      toast({
        title: "错误",
        description: "删除知识条目失败",
        variant: "destructive",
      });
    }
  };

  const startVoiceRecording = () => {
    if ("SpeechRecognition" in window || "webkitSpeechRecognition" in window) {
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.lang = "zh-CN";
      recognitionInstance.continuous = true;
      recognitionInstance.interimResults = true;

      recognitionInstance.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map((result) => result[0].transcript)
          .join("");

        setNewEntry((prev) => ({
          ...prev,
          content: transcript,
        }));
      };

      recognitionInstance.start();
      setRecognition(recognitionInstance);
      setIsRecording(true);
      toast({
        title: "语音录入已开始",
        description: "请开始说话",
      });
    } else {
      toast({
        title: "错误",
        description: "您的浏览器不支持语音识别",
        variant: "destructive",
      });
    }
  };

  const stopVoiceRecording = () => {
    if (recognition) {
      recognition.stop();
      setIsRecording(false);
      toast({
        title: "语音录入已停止",
        description: "内容已保存到表单中",
      });
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getCategoryIcon = (category: string) => {
    return category === "business-rule" ? (
      <BookText className="h-4 w-4 mr-1 text-blue-500" />
    ) : (
      <FileText className="h-4 w-4 mr-1 text-emerald-500" />
    );
  };

  const getCategoryText = (category: string) => {
    return category === "business-rule" ? "业务规则" : "特殊描述";
  };

  // 添加搜索建议相关函数
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value.trim();
    setNewEntry({ ...newEntry, content: newContent });

    // 获取光标位置所在行的文本
    const cursorPos = e.target.selectionStart;
    const contentBeforeCursor = newContent.substring(0, cursorPos);

    // 找到光标所在行
    const lineBeforeCursor = contentBeforeCursor.split("\n");
    const currentLine = lineBeforeCursor[lineBeforeCursor.length - 1];

    // 设置当前搜索行
    setSearchingLine(currentLine);

    // 清除之前的定时器
    if (suggestionsTimeoutRef.current) {
      clearTimeout(suggestionsTimeoutRef.current);
    }

    // 如果当前行有内容，则启动搜索
    if (currentLine.trim()) {
      // 设置一个短暂的延迟，避免频繁请求
      suggestionsTimeoutRef.current = setTimeout(() => {
        fetchSuggestions(currentLine);
      }, 300);

      // 计算准确的光标位置
      if (textareaRef.current) {
        const textArea = textareaRef.current;
        // 临时存储当前输入内容和光标位置
        const content = textArea.value;
        const cursorPosition = textArea.selectionStart;

        // 计算当前光标所在行号
        const contentBeforeCursor = content.substring(0, cursorPosition);
        const lines = contentBeforeCursor.split("\n");
        const currentLineIndex = lines.length - 1;

        // 获取文本区域的位置信息
        const textAreaRect = textArea.getBoundingClientRect();
        const style = window.getComputedStyle(textArea);
        const lineHeight =
          parseInt(style.lineHeight) || parseInt(style.fontSize) * 1.5;
        const paddingTop = parseInt(style.paddingTop) || 0;
        const paddingLeft = parseInt(style.paddingLeft) || 0;
        const borderTop = parseInt(style.borderTopWidth) || 0;

        // 计算当前行的顶部位置
        const lineTop =
          textAreaRect.top +
          borderTop +
          paddingTop +
          currentLineIndex * lineHeight;

        // 设置下拉框的显示位置
        setCursorPosition({
          top: lineTop + lineHeight + window.scrollY, // 定位在当前行正下方
          left: textAreaRect.left + paddingLeft, // 水平对齐到文本区域开头
        });
      }
    } else {
      // 如果当前行无内容，则清空建议并隐藏
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const fetchSuggestions = async (keyword: string) => {
    if (!keyword.trim()) return;

    try {
      const response = await fetch(
        `/api/knowledge-base?accountId=1&keyword=${encodeURIComponent(keyword)}`
      );
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        // 转换结果
        const suggestions = result.data.map((item: any) => ({
          id: item.id.toString(),
          content: item.content,
          type: item.type,
        }));

        // 根据相关性排序结果
        // 1. 开头匹配的排在前面
        // 2. 按照内容长度升序排序
        const sortedSuggestions = suggestions.sort(
          (a: SuggestionResult, b: SuggestionResult) => {
            const aStartsWithKeyword = a.content
              .toLowerCase()
              .startsWith(keyword.toLowerCase());
            const bStartsWithKeyword = b.content
              .toLowerCase()
              .startsWith(keyword.toLowerCase());

            if (aStartsWithKeyword && !bStartsWithKeyword) return -1;
            if (!aStartsWithKeyword && bStartsWithKeyword) return 1;

            return a.content.length - b.content.length;
          }
        );

        // 限制显示数量
        const limitedSuggestions = sortedSuggestions.slice(0, 10);

        setSearchSuggestions(limitedSuggestions);
        setShowSuggestions(true);
      } else {
        setSearchSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error("获取建议时出错:", error);
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const handleSelectSuggestion = (content: string) => {
    // 替换当前行的内容
    const lines = newEntry.content?.split("\n") || [];
    const cursorText = newEntry.content || "";

    // 找到当前光标位置
    const textArea = textareaRef.current;
    if (!textArea) return;

    const cursorPosition = textArea.selectionStart;
    const textBeforeCursor = cursorText.substring(0, cursorPosition);
    const textAfterCursor = cursorText.substring(cursorPosition);

    // 找到光标前的最后一个换行符和光标后的第一个换行符
    const lastNewLineBeforeCursor = textBeforeCursor.lastIndexOf("\n");
    const firstNewLineAfterCursor = textAfterCursor.indexOf("\n");

    const startPos =
      lastNewLineBeforeCursor === -1 ? 0 : lastNewLineBeforeCursor + 1;
    const endPos =
      firstNewLineAfterCursor === -1
        ? cursorText.length
        : cursorPosition + firstNewLineAfterCursor;

    // 组合新的文本
    const newContent =
      cursorText.substring(0, startPos) +
      content +
      (firstNewLineAfterCursor === -1 ? "" : cursorText.substring(endPos));

    setNewEntry({ ...newEntry, content: newContent });
    setShowSuggestions(false);
  };

  // 高亮匹配的关键词
  const highlightMatch = (text: string, keyword: string) => {
    if (!keyword || !keyword.trim()) return text;

    try {
      // 转义正则表达式中的特殊字符
      const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const regex = new RegExp(`(${escapedKeyword})`, "gi");
      const parts = text.split(regex);

      return (
        <>
          {parts.map((part, i) => {
            // 判断当前部分是否匹配关键词（不区分大小写）
            if (part.toLowerCase() === keyword.toLowerCase()) {
              return (
                <span key={i} className="text-red-500 font-bold">
                  {part}
                </span>
              );
            }
            return part;
          })}
        </>
      );
    } catch (error) {
      // 如果正则表达式有问题，直接返回原文本
      console.error("高亮处理出错:", error);
      return text;
    }
  };

  // 添加点击外部关闭建议下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 键盘导航处理函数
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions || searchSuggestions.length === 0) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedSuggestionIndex((prev) =>
        prev < searchSuggestions.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedSuggestionIndex((prev) =>
        prev > 0 ? prev - 1 : searchSuggestions.length - 1
      );
    } else if (e.key === "Enter" && selectedSuggestionIndex >= 0) {
      e.preventDefault();
      handleSelectSuggestion(
        searchSuggestions[selectedSuggestionIndex].content
      );
    } else if (e.key === "Escape") {
      e.preventDefault();
      setShowSuggestions(false);
    }
  };

  // 每次建议列表变更时，重置选中索引
  useEffect(() => {
    setSelectedSuggestionIndex(-1);
  }, [searchSuggestions]);

  return (
    <div className="mx-auto p-6">
      <div className="text-center pt-0 mb-6">
        <motion.h1
          className="text-2xl font-bold m-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          知识库管理
        </motion.h1>
        <motion.p
          className="text-gray-500 mt-0 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.3 }}
        >
          添加、编辑和管理知识库条目
        </motion.p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <motion.div
          className="md:col-span-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="h-full border-0">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex-grow">
                <h3 className="text-lg font-bold mb-4">
                  {editingId ? "编辑知识条目" : "添加知识条目"}
                </h3>
                <div className="space-y-4">
                  <div className="relative">
                    <Textarea
                      ref={textareaRef}
                      id="content"
                      placeholder="输入内容"
                      className="min-h-[150px]"
                      value={newEntry.content || ""}
                      onChange={handleContentChange}
                      onKeyDown={handleKeyDown}
                    />

                    {showSuggestions && searchSuggestions.length > 0 && (
                      <div
                        ref={popoverRef}
                        className="fixed z-50 bg-white border-2 border-blue-200 rounded-md shadow-lg max-h-[180px] overflow-y-auto"
                        style={{
                          top: `${cursorPosition.top}px`,
                          left: `${cursorPosition.left}px`,
                          width: "min(500px, calc(100vw - 4rem))",
                        }}
                      >
                        <div className="py-1">
                          {searchSuggestions.map((suggestion, index) => {
                            const isSelected =
                              selectedSuggestionIndex === index;
                            return (
                              <div
                                key={suggestion.id}
                                className={`px-4 py-2 text-sm cursor-pointer flex items-center transition-colors duration-150 ${
                                  isSelected
                                    ? "bg-blue-100"
                                    : "hover:bg-blue-50"
                                }`}
                                onClick={() =>
                                  handleSelectSuggestion(suggestion.content)
                                }
                              >
                                <div className="flex items-center w-full">
                                  {suggestion.type === 1 ? (
                                    <BookText className="h-4 w-4 mr-2 flex-shrink-0 text-blue-500" />
                                  ) : (
                                    <FileText className="h-4 w-4 mr-2 flex-shrink-0 text-emerald-500" />
                                  )}
                                  <span className="truncate">
                                    {highlightMatch(
                                      suggestion.content,
                                      searchingLine
                                    )}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-center mt-4">
                    {editingId ? (
                      <div className="space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setEditingId(null);
                            setNewEntry({
                              content: "",
                              category: "business-rule",
                              type: 1,
                              account_id: 1,
                              sub_account_id: 1,
                            });
                          }}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={handleSaveEdit}
                          className="flex items-center gap-1"
                        >
                          <Save className="w-4 h-4" />
                          保存更改
                        </Button>
                      </div>
                    ) : (
                      <div className="flex space-x-2">
                        {!isRecording ? (
                          <Button
                            variant="outline"
                            onClick={startVoiceRecording}
                            className="flex items-center gap-1"
                          >
                            <Mic className="w-4 h-4" />
                            语音输入
                          </Button>
                        ) : (
                          <Button
                            variant="destructive"
                            onClick={stopVoiceRecording}
                            className="flex items-center gap-1"
                          >
                            <MicOff className="w-4 h-4" />
                            停止录音
                          </Button>
                        )}
                        <Button
                          onClick={handleAddEntry}
                          disabled={!newEntry.content || isAnalyzing}
                        >
                          {isAnalyzing ? "分析中..." : "添加条目"}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          className="md:col-span-1"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="h-full border-0">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex-grow">
                <h3 className="text-lg font-bold mb-4">类型说明</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-2 p-3 border rounded-md">
                    <BookText className="h-5 w-5 mt-0.5 text-blue-500" />
                    <div>
                      <h4 className="font-medium">业务规则</h4>
                      <p className="text-sm text-muted-foreground">
                        帮助AI理解业务规则，比如如何根据备注说明匹配对应的收入或支出类型
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2 p-3 border rounded-md">
                    <FileText className="h-5 w-5 mt-0.5 text-emerald-500" />
                    <div>
                      <h4 className="font-medium">特殊描述</h4>
                      <p className="text-sm text-muted-foreground">
                        帮助AI理解文本的含义，比如人物的昵称、公司的简称、货品的品牌、常规的业务说法等
                      </p>
                    </div>
                  </div>

                  <p className="text-xs text-center italic text-muted-foreground mt-2">
                    *AI将根据输入的知识条目，自动识别内容类型
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="overflow-hidden rounded-lg shadow-sm bg-white"
      >
        {loading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mb-4"></div>
            <p className="text-gray-600">正在加载知识条目...</p>
          </div>
        ) : (
          <>
            <div
              className="common-table p-6"
            >
              <table className="overflow-hidden rounded-lg">
                <thead className="">
                  <tr className="text-left">
                    <th className="w-1/2 text-center whitespace-nowrap">
                      内容
                    </th>
                    <th className="w-1/7 text-center whitespace-nowrap">
                      类型
                    </th>
                    <th className="w-1/7 text-center whitespace-nowrap">
                      创建时间
                    </th>
                    <th className="w-1/7 text-center whitespace-nowrap">
                      更新时间
                    </th>
                    <th className="w-1/12 text-center whitespace-nowrap">
                      操作
                    </th>
                  </tr>
                </thead>
                <motion.tbody
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <AnimatePresence>
                    {entries.map((entry, index) => (
                      <tr
                        key={entry.id}
                        className={`hover:bg-violet-50 ${
                          index % 2 === 0 ? "bg-white" : "bg-gray-50"
                        }`}
                      >
                        {inlineEditingId === entry.id ? (
                          <>
                            <td>
                              <Textarea
                                value={inlineEditEntry.content}
                                onChange={(e) =>
                                  setInlineEditEntry({
                                    ...inlineEditEntry,
                                    content: e.target.value,
                                  })
                                }
                                minRows={3}
                                className="rounded-lg"
                              />
                            </td>
                            <td>
                              <Select
                                value={inlineEditEntry.category}
                                onValueChange={(
                                  value: "business-rule" | "special-description"
                                ) =>
                                  setInlineEditEntry({
                                    ...inlineEditEntry,
                                    category: value,
                                    type: value === "business-rule" ? 1 : 2,
                                  })
                                }
                              >
                                <SelectTrigger
                                  className="common-table-select-button common-table-select-text-center whitespace-nowrap"
                                  style={{ height: "60px" }}
                                >
                                  <SelectValue placeholder="选择类型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem
                                    value="business-rule"
                                    className="common-table-select-item common-table-select-text-center"
                                  >
                                    <div className="flex items-center">
                                      <BookText className="h-4 w-4 mr-1 text-blue-500" />
                                      <span>业务规则</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem
                                    value="special-description"
                                    className="common-table-select-item common-table-select-text-center"
                                  >
                                    <div className="flex items-center">
                                      <FileText className="h-4 w-4 mr-1 text-emerald-500" />
                                      <span>特殊描述</span>
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </td>
                          </>
                        ) : (
                          <>
                            <td className="px-2 py-2 text-center">
                              <span className="text-gray-800">
                                {entry.content}
                              </span>
                            </td>
                            <td className="px-2 py-2 text-center">
                              <div className="flex items-center justify-center whitespace-nowrap">
                                {getCategoryIcon(entry.category)}
                                <span>{getCategoryText(entry.category)}</span>
                              </div>
                            </td>
                          </>
                        )}
                        <td className="px-2 py-2 text-center">
                          <span className="text-gray-800">
                            {formatDate(new Date(entry.created_at))}
                          </span>
                        </td>
                        <td className="px-2 py-2 text-center">
                          <span className="text-gray-800">
                            {formatDate(new Date(entry.updated_at))}
                          </span>
                        </td>
                        <td className="px-2 py-2 text-center">
                          <div className="flex justify-center space-x-3">
                            {inlineEditingId === entry.id ? (
                              <>
                                <button
                                  onClick={handleSaveInlineEdit}
                                  className={`whitespace-nowrap text-violet-500 hover:text-violet-700 flex items-center text-sm`}
                                  title="保存"
                                >
                                  <Save size={16} className="mr-1" />
                                  保存
                                </button>
                                <button
                                  onClick={handleCancelInlineEdit}
                                  className="whitespace-nowrap text-gray-500 hover:text-gray-700 flex items-center text-sm"
                                  title="取消编辑"
                                >
                                  <X size={16} className="mr-1" />
                                  取消
                                </button>
                              </>
                            ) : (
                              <>
                                <button
                                  onClick={() => handleInlineEdit(entry.id)}
                                  className={`whitespace-nowrap text-blue-500 hover:text-blue-700 flex items-center text-sm`}
                                  title="编辑"
                                >
                                  <Edit size={16} className="mr-1" />
                                  编辑
                                </button>
                                <button
                                  onClick={() => handleDelete(entry.id)}
                                  className={`whitespace-nowrap flex items-center text-sm text-red-500 hover:text-red-700`}
                                  title="删除"
                                >
                                  <Trash2 size={16} className="mr-1" />
                                  删除
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                    {entries.length === 0 && (
                      <tr key="nodata">
                        <td
                          colSpan={5}
                          className="text-gray-500 px-4 py-3 text-center"
                        >
                          暂无知识条目
                        </td>
                      </tr>
                    )}
                  </AnimatePresence>
                </motion.tbody>
              </table>
            </div>
          </>
        )}
      </motion.div>

      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>确认知识条目</DialogTitle>
            <DialogDescription>
              大模型已分析您输入的内容，请确认以下知识条目是否准确。您可以编辑内容和类型，或移除不需要的条目。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 my-4">
            {analyzedItems.length === 0 ? (
              <p className="text-center text-muted-foreground py-4">
                未能识别任何有效的知识条目
              </p>
            ) : (
              analyzedItems.map((item) => (
                <div key={item.id} className="border rounded-md p-4 relative">
                  <div className="flex gap-3 items-center">
                    <div className="flex-1">
                      <Textarea
                        id={`content-${item.id}`}
                        value={item.content}
                        onChange={(e) =>
                          handleItemContentChange(item.id, e.target.value)
                        }
                        className="resize-none h-10 py-1.5 px-3 flex items-center"
                        style={{ overflow: "hidden" }}
                      />
                    </div>

                    <div className="w-[150px]">
                      <Select
                        value={item.category}
                        onValueChange={(
                          value: "business-rule" | "special-description"
                        ) => handleItemCategoryChange(item.id, value)}
                      >
                        <SelectTrigger className="w-full h-[60px]">
                          <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="business-rule">
                            <div className="flex items-center">
                              <BookText className="h-4 w-4 mr-2 text-blue-500" />
                              <span>业务规则</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="special-description">
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 mr-2 text-emerald-500" />
                              <span>特殊描述</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAnalyzedItem(item.id)}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">删除</span>
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDialog}>
              返回
            </Button>
            <Button
              onClick={handleConfirmItems}
              disabled={analyzedItems.length === 0}
            >
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KnowledgeBase;
