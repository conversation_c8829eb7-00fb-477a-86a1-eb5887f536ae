"use client";
import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { getCurrentYear, generateYears } from "@/app/utils/dateUtils";

// Interface for Store
interface Store {
  id: number;
  store_name: string;
  open_time?: string | null; // Optional, as it's not directly used in this file but good for consistency
  close_time?: string | null; // Added close_time
}

// Interface for HistoryDataTable
interface HistoryEntry {
  id: number;
  month: string;
  initialValue: string;
  purchaseValue: string;
  endValue: string;
  updateTime: string;
  isAutoGenerated?: boolean; // Added to match API if needed for display
}

// InventoryInputForm component logic
const InventoryInputFormInternal = ({ 
  onUpdateSuccess, 
  stores,
  selectedStoreId,
  onStoreChange,
}: {
  onUpdateSuccess: (message: string, details?: { beginning: string, purchase: string }) => void;
  stores: Store[];
  selectedStoreId: string;
  onStoreChange: (storeId: string) => void;
}) => {
  const [beginningValue, setBeginningValue] = useState("系统自动计算");
  const [purchaseExpense, setPurchaseExpense] = useState("系统自动计算");
  const [endingValue, setEndingValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const currentSystemDate = new Date();
  const dataEntryTargetMonth = currentSystemDate.getMonth() === 0 ? 12 : currentSystemDate.getMonth(); 
  const dataEntryTargetYear = currentSystemDate.getMonth() === 0 ? currentSystemDate.getFullYear() - 1 : currentSystemDate.getFullYear();
  const dataEntryMonthName = new Date(dataEntryTargetYear, dataEntryTargetMonth - 1).toLocaleString('zh-CN', { month: 'long' });

  useEffect(() => {
    setEndingValue(""); 
    setBeginningValue("系统自动计算");
    setPurchaseExpense("系统自动计算");
  }, [selectedStoreId, dataEntryTargetYear, dataEntryTargetMonth]);

  const handleConfirmUpdate = async () => {
    if (!selectedStoreId) {
      toast({ title: "错误", description: "请先选择一个门店。", variant: "destructive" });
      return;
    }
    if (endingValue.trim() === "" || isNaN(parseFloat(endingValue))) {
      toast({ title: "错误", description: "请输入有效的期末库存价值。", variant: "destructive" });
      return;
    }

    const today = new Date();
    const currentDayOfMonth = today.getDate();
    const currentMonth = today.getMonth() + 1; 
    const currentYear = today.getFullYear();

    let isValidEntryPeriod = false;
    if (dataEntryTargetYear === currentYear) { 
      if (currentMonth === dataEntryTargetMonth + 1 && (currentDayOfMonth >= 1 && currentDayOfMonth <= 15)) {
        isValidEntryPeriod = true;
      }
    } else if (dataEntryTargetYear === currentYear - 1) { 
      if (dataEntryTargetMonth === 12 && currentMonth === 1 && (currentDayOfMonth >= 1 && currentDayOfMonth <= 15)) {
        isValidEntryPeriod = true;
      }
    }

    if (!isValidEntryPeriod) {
        toast({ 
            title: "录入时间限制", 
            description: `您只能在 ${new Date(dataEntryTargetYear, dataEntryTargetMonth, 1).toLocaleString('zh-CN', {month: 'long'})} 1号至15号期间录入 ${dataEntryMonthName} (${dataEntryTargetYear}年) 的库存数据。`, 
            variant: "default",
            duration: 7000
        });
        return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/inventory-value/records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          storeId: parseInt(selectedStoreId),
          year: dataEntryTargetYear,
          month: dataEntryTargetMonth, 
          endingValue: parseFloat(endingValue),
        }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || result.error || '更新失败');
      }
      onUpdateSuccess(
        `成功为 ${dataEntryMonthName} (${dataEntryTargetYear}年) 更新门店库存。`, 
        { beginning: result.calculatedBeginningValue, purchase: result.calculatedPurchaseExpense }
      );
      setBeginningValue(String(result.calculatedBeginningValue));
      setPurchaseExpense(String(result.calculatedPurchaseExpense));
      setEndingValue("");
    } catch (error) {
      console.error("Update error:", error);
      const errorMessage = error instanceof Error ? error.message : "发生未知错误";
      toast({ title: "更新失败", description: errorMessage, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-end md:gap-3 space-y-3 md:space-y-0">
        <div className="flex-shrink-0 md:w-auto lg:w-1/5 space-y-1">
          <Label htmlFor="store-select" className="text-sm font-medium text-gray-700">选择门店 *</Label>
          <Select value={selectedStoreId} onValueChange={onStoreChange} disabled={isLoading || stores.length === 0}>
            <SelectTrigger id="store-select">
              <SelectValue placeholder={stores.length === 0 ? "加载中..." : "请选择门店"} />
            </SelectTrigger>
            <SelectContent>
              {stores.length === 0 && <SelectItem value="loading" disabled>正在加载门店...</SelectItem>}
              {stores
                .filter(store => store.close_time === null) // Filter out closed stores
                .map((store) => (
                <SelectItem key={store.id} value={String(store.id)}>
                  {store.store_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {!selectedStoreId && stores.length > 0 && <p className="text-xs text-red-500 pt-1">请选择门店。</p>}
        </div>

        <div className="flex-grow space-y-1">
          <Label htmlFor="beginning-value" className="text-sm font-medium text-gray-700">
            {dataEntryMonthName}期初(元)
          </Label>
          <Input id="beginning-value" type="text" value={beginningValue} readOnly className="bg-gray-100 cursor-not-allowed" />
        </div>

        <div className="flex-grow space-y-1">
          <Label htmlFor="purchase-expense" className="text-sm font-medium text-gray-700">
            {dataEntryMonthName}采购(元)
          </Label>
          <Input id="purchase-expense" type="text" value={purchaseExpense} readOnly className="bg-gray-100 cursor-not-allowed" />
        </div>

        <div className="flex-grow space-y-1">
          <Label htmlFor="ending-value" className="text-sm font-medium text-gray-700">
            {dataEntryMonthName}期末(元)*
          </Label>
          <Input
            id="ending-value"
            value={endingValue}
            onChange={(e) => setEndingValue(e.target.value)}
            placeholder="请输入期末价值"
            disabled={isLoading || !selectedStoreId}
            className={!selectedStoreId ? "bg-gray-100 cursor-not-allowed" : ""}
          />
        </div>

        <div className="flex-shrink-0 self-end space-y-1">
            <Label className="text-sm font-medium text-gray-700 invisible">操作</Label> 
            <AlertDialog>
                <AlertDialogTrigger asChild>
                <Button 
                    type="button" 
                    className="bg-purple-600 hover:bg-purple-700 text-white w-full md:w-auto whitespace-nowrap"
                    disabled={isLoading || !selectedStoreId || endingValue.trim() === ""}
                >
                    {isLoading ? "处理中..." : "确认更新"}
                </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                    <AlertDialogTitle>确认更新库存价值</AlertDialogTitle>
                    <AlertDialogDescription>
                        您将为 <strong>{dataEntryMonthName} ({dataEntryTargetYear}年)</strong> 更新期末库存价值为 <strong>{endingValue || '0'}</strong> 元。
                        <br />
                        期初库存将是 <strong>{beginningValue}</strong> 元，预估采购支出为 <strong>{purchaseExpense}</strong> 元。
                        <br />
                        （期初和采购为系统根据规则自动计算，实际以提交后为准）
                        <br />
                        <br />
                        您确定要提交吗？
                    </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                    <AlertDialogCancel disabled={isLoading}>取消</AlertDialogCancel>
                    <AlertDialogAction onClick={handleConfirmUpdate} disabled={isLoading}>
                        {isLoading ? "提交中..." : "确认提交"}
                    </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div> 
      </div>

      <div className="text-xs md:text-sm text-gray-600 leading-relaxed pt-1 md:pt-2">
        您正在为 <strong>{dataEntryMonthName} ({dataEntryTargetYear}年)</strong> 录入数据。期初库存价值和采购支出将根据您的期末录入和历史账单自动计算。
      </div>
    </form>
  );
};

// HistoryDataTable component logic
const HistoryDataTableInternal = ({ 
    year, 
    storeId,
    stores,
    onStoreChangeForHistory,
    onYearChangeForHistory,
}: { 
    year: string; 
    storeId: string;
    stores: Store[];
    onStoreChangeForHistory: (storeId: string) => void;
    onYearChangeForHistory: (year: string) => void;
}) => {
  const [historyData, setHistoryData] = useState<HistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const years = generateYears();

  const fetchHistoryData = useCallback(async () => {
    if (!year || !storeId) {
      setHistoryData([]);
      return;
    }
    setIsLoading(true);
    try {
      const response = await fetch(`/api/inventory-value/records?year=${year}&storeId=${storeId}`);
      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.message || errorResult.error || '获取历史数据失败');
      }
      const data: HistoryEntry[] = await response.json();
      const sortedData = data.map(item => ({...item, month: String(item.month)})).sort((a, b) => parseInt(b.month) - parseInt(a.month));
      setHistoryData(sortedData);
    } catch (error) {
      console.error("Fetch history error:", error);
      const errorMessage = error instanceof Error ? error.message : "发生未知错误";
      toast({ title: "获取历史数据失败", description: errorMessage, variant: "destructive" });
      setHistoryData([]);
    } finally {
      setIsLoading(false);
    }
  }, [year, storeId, toast]);

  useEffect(() => {
    fetchHistoryData();
  }, [fetchHistoryData]);

  return (
    <div className="space-y-4">
        <div className="flex flex-wrap items-center gap-4 p-1">
            <div className="flex items-center gap-2">
                <Label htmlFor="history-store-select">选择门店:</Label>
                <Select value={storeId} onValueChange={onStoreChangeForHistory} disabled={isLoading || stores.length === 0}>
                    <SelectTrigger id="history-store-select" className="w-40">
                    <SelectValue placeholder={stores.length === 0 ? "无可用门店" : "选择门店"} />
                    </SelectTrigger>
                    <SelectContent>
                    {stores.length === 0 && <SelectItem value="no-data" disabled>无门店数据</SelectItem>}
                    {stores
                        .filter(store => store.close_time === null) // Filter out closed stores
                        .map((s) => (
                        <SelectItem key={s.id} value={String(s.id)}>
                        {s.store_name}
                        </SelectItem>
                    ))}
                    </SelectContent>
                </Select>
            </div>
            <div className="flex items-center gap-2">
                <Label htmlFor="year-select">选择年份:</Label>
                <Select value={year} onValueChange={onYearChangeForHistory} disabled={isLoading}>
                    <SelectTrigger id="year-select" className="w-32">
                    <SelectValue placeholder="选择年份" />
                    </SelectTrigger>
                    <SelectContent>
                    {years.map((y) => (
                        <SelectItem key={y} value={y}>
                        {y}
                        </SelectItem>
                    ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
        <div className="rounded-md border">
        <Table>
            <TableHeader>
            <TableRow>
                <TableHead>月份</TableHead>
                <TableHead>期初（元）</TableHead>
                <TableHead>采购（元）</TableHead>
                <TableHead>期末（元）</TableHead>
                <TableHead>更新时间</TableHead>
            </TableRow>
            </TableHeader>
            <TableBody>
            {isLoading ? (
                <TableRow>
                    <TableCell colSpan={5} className="text-center py-10 text-gray-500">加载中...</TableCell>
                </TableRow>
            ) : historyData.length > 0 ? (
                historyData.map((entry) => (
                <TableRow key={entry.id}>
                    <TableCell>{parseInt(entry.month)}月</TableCell>
                    <TableCell>{entry.initialValue}</TableCell>
                    <TableCell>{entry.purchaseValue}</TableCell>
                    <TableCell>{entry.endValue}</TableCell>
                    <TableCell>{entry.updateTime}</TableCell>
                </TableRow>
                ))
            ) : (
                <TableRow>
                <TableCell colSpan={5} className="text-center py-10 text-gray-500">
                    {!storeId ? "请先选择门店" : "暂无数据或未选择年份"}
                </TableCell>
                </TableRow>
            )}
            </TableBody>
        </Table>
        </div>
    </div>
  );
};

const Index = () => {
  const { toast } = useToast();
  const [selectedYearForHistory, setSelectedYearForHistory] = useState<string>(getCurrentYear());
  const [stores, setStores] = useState<Store[]>([]);
  const [selectedStoreIdForForm, setSelectedStoreIdForForm] = useState<string>("");
  const [selectedStoreIdForHistory, setSelectedStoreIdForHistory] = useState<string>("");
  const [isStoresLoading, setIsStoresLoading] = useState(true);

  useEffect(() => {
    const fetchStores = async () => {
      setIsStoresLoading(true);
      try {
        const response = await fetch('/api/inventory-value/stores');
        if (!response.ok) {
            const errData = await response.json();
            throw new Error(errData.message || errData.error || '获取门店列表失败');
        }
        const data: Store[] = await response.json();
        setStores(data);
      } catch (error) {
        console.error("Fetch stores error:", error);
        toast({ title: "获取门店列表失败", description: error instanceof Error ? error.message : "未知错误", variant: "destructive" });
        setStores([]);
      } finally {
        setIsStoresLoading(false);
      }
    };
    fetchStores();
  }, [toast]);

  const currentSystemDate = new Date();
  const dataEntryTargetMonthForHistoryRefresh = currentSystemDate.getMonth() === 0 ? 12 : currentSystemDate.getMonth();
  const dataEntryTargetYearForHistoryRefresh = currentSystemDate.getMonth() === 0 ? currentSystemDate.getFullYear() - 1 : currentSystemDate.getFullYear();

  const handleUpdateSuccess = (message: string, details?: { beginning: string, purchase: string }) => {
    toast({ 
        title: "操作成功", 
        description: `${message} 期初: ${details?.beginning}, 采购: ${details?.purchase}`,
        duration: 5000,
    });
    
    if (selectedStoreIdForForm === selectedStoreIdForHistory && 
        String(dataEntryTargetYearForHistoryRefresh) === selectedYearForHistory
    ) {
        const currentSelection = selectedStoreIdForHistory;
        setSelectedStoreIdForHistory(""); 
        setTimeout(() => setSelectedStoreIdForHistory(currentSelection), 0);
    }
  };
 
  return (
    <div className="flex h-full bg-gray-50">
      <div className="flex-1 p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>库存价值更新 (录入上月数据)</CardTitle>
          </CardHeader>
          <CardContent>
            {isStoresLoading ? (
                <p className="text-center text-gray-500 py-4">正在加载门店信息...</p>
            ) : stores.length === 0 ? (
                <p className="text-center text-red-500 py-4">未能加载到门店信息或无可用门店。请检查系统配置或联系管理员。</p>
            ) : (
                <InventoryInputFormInternal 
                onUpdateSuccess={handleUpdateSuccess} 
                stores={stores}
                selectedStoreId={selectedStoreIdForForm}
                onStoreChange={setSelectedStoreIdForForm}
                />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>历史库存价值明细</CardTitle>
          </CardHeader>
          <CardContent>
          {isStoresLoading && stores.length === 0 ? (
             <p className="text-center text-gray-500 py-4">门店信息加载中...</p>
          ) : (
            <HistoryDataTableInternal 
                year={selectedYearForHistory} 
                storeId={selectedStoreIdForHistory}
                stores={stores}
                onStoreChangeForHistory={setSelectedStoreIdForHistory}
                onYearChangeForHistory={setSelectedYearForHistory}
            />
          )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;