"use client";

import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import UploadPage from "./UploadPage";
import PreviewPage from "./PreviewPage";
import DatabaseWritePage from "./DatabaseWritePage";
import ResultsPage from "./ResultsPage";
import NotFound from "@/app/NotFound";
import "@/app/styles/analytics.css";
import { fetchCompletedMenus } from "@/components/layout/SideNav";
import { useNewAnalyticsStore } from "@/lib/store/newAnalytics";

export type AnalyticsStep = "upload" | "preview" | "database-write" | "results";

export default function AnalyticsApp() {
    const [queryClient] = useState(() => new QueryClient());
    const [currentStep, setCurrentStep] = useState<AnalyticsStep>("upload");
    const [stepData, setStepData] = useState<any>({});
    
    // 获取已完成菜单数据
    const setNewAnalyticsSubItems = useNewAnalyticsStore((state) => state.setSubItems);
    
    useEffect(() => {
        // 页面加载时获取已完成菜单数据
        fetchCompletedMenus();
    }, []);

    const navigateToStep = (step: AnalyticsStep, data?: any) => {
        window.scrollTo(0, 0);
        setCurrentStep(step);
        if (data) {
            setStepData((prevData: any) => ({ ...prevData, ...data }));
        }
    };

    const renderCurrentStep = () => {
        switch (currentStep) {
            case "upload":
                return <UploadPage onNavigate={navigateToStep} />;
            case "preview":
                return <PreviewPage onNavigate={navigateToStep} data={stepData} />;
            case "database-write":
                return <DatabaseWritePage onNavigate={navigateToStep} data={stepData} />;
            case "results":
                return <ResultsPage onNavigate={navigateToStep} data={stepData} />;
            default:
                return <NotFound />;
        }
    };

    return (
        <QueryClientProvider client={queryClient}>
            <TooltipProvider>
                <div className="h-full flex flex-col overflow-y-auto">
                    <Toaster />
                    <Sonner />
                    <div className="flex-1">
                        {renderCurrentStep()}
                    </div>
                </div>
            </TooltipProvider>
        </QueryClientProvider>
    );
} 
