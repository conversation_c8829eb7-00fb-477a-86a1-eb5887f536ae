"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, FileText, ArrowLeft, ArrowRight } from "lucide-react";
import ProcessStatus from "./ProcessStatus";
import { StatementFile } from "../types";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";
import { AnalyticsStep } from "./AnalyticsApp";
import { useRouter } from "next/navigation";

interface ResultsPageProps {
  onNavigate: (step: AnalyticsStep, data?: any) => void;
  data?: {
    statements: StatementFile[];
    currentStatementId: string;
  };
}

const ResultsPage = ({ onNavigate, data }: ResultsPageProps) => {
  const [statements, setStatements] = useState<StatementFile[]>([]);
  const router = useRouter();

  useEffect(() => {
    // 从props获取对账单数据
    if (data?.statements) {
      setStatements(data.statements);
    } else {
      // 如果没有找到对账单，重定向到上传页面
      onNavigate("upload");
    }
  }, [data, onNavigate]);

  const handleMatchReimbursements = () => {
    // 跳转到报销页面
    router.push("/pages/reimburse");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "written":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "failed":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "uploaded":
        return "已上传";
      case "analyzed":
        return "已分析";
      case "confirmed":
        return "已确认";
      case "written":
        return "已写入数据库";
      case "failed":
        return "失败";
      default:
        return "未知";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "written":
        return "text-green-500";
      case "failed":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const handleReturnToUpload = () => {
    onNavigate("upload");
  };

  const handleProcessNext = () => {
    // 处理下一份对账单，导航到上传页面
    onNavigate("upload");
  };

  const getPendingCount = () => {
    return statements.filter(stmt => stmt.status !== "written" && stmt.status !== "failed").length;
  };

  return (
    <div className="h-full bg-[#F9FAFB] p-6">
      <div className="mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">结果确认</h1>
        </div>

        <ProcessStatus currentStep={4} />

        <div className="bg-white rounded-lg shadow overflow-hidden mt-6">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-medium">数据保存成功！您可以继续处理下一份对账单 😊</h2>
          </div>
          
          <ul className="divide-y divide-gray-200">
            {statements.map(statement => (
              <li key={statement.id} className="p-4 hover:bg-blue-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(statement.status)}
                    <div>
                      <p className="font-medium">{statement.name}</p>
                      <p className="text-sm text-gray-500">
                        账户: {statement.account} | 公司: {statement.company}
                      </p>
                    </div>
                  </div>
                  <div className={`font-medium ${getStatusColor(statement.status)}`}>
                    {getStatusText(statement.status)}
                    {statement.errorMessage && <p className="text-sm text-red-500">{statement.errorMessage}</p>}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>

        {/* 财务数据卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <Card className="overflow-hidden bg-gradient-to-br from-purple-100 to-blue-100 border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="font-medium text-gray-700 mb-2">数据总结</h3>
              <p className="text-sm text-gray-600">成功处理账单：{statements.filter(s => s.status === "written").length}份</p>
            </CardContent>
          </Card>
        </div>
        
        {/* 居中放置的按钮行 */}
        <div className="mt-8 flex justify-center space-x-6">
          <Button 
            variant="default" 
            className="flex items-center gap-1 bg-purple-500 hover:bg-purple-600 text-white" 
            onClick={handleMatchReimbursements}
          >
            匹配报销付款
            <ArrowRight className="h-4 w-4" />
          </Button>
          <Button 
            className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white" 
            onClick={handleProcessNext}
          >
            处理下一份对账单
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ResultsPage;
