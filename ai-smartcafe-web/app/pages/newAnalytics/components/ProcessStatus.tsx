"use client";

import { ProcessStatusProps } from "../types";

const ProcessStatus = ({ currentStep }: ProcessStatusProps) => {
  return (
    <div className="step-indicator w-full">
      <div className={`step ${currentStep >= 1 ? 'step-active' : 'step-pending'} ${currentStep > 1 ? 'step-completed' : ''}`}>
        <div className="step-number">1</div>
        <span className="ml-2 text-sm font-medium font-progress">上传</span>
      </div>
      <div className={`step-connector ${currentStep >= 2 ? 'bg-purple-500' : 'bg-gray-200'}`}></div>
      <div className={`step ${currentStep >= 2 ? 'step-active' : 'step-pending'} ${currentStep > 2 ? 'step-completed' : ''}`}>
        <div className="step-number">2</div>
        <span className="ml-2 text-sm font-medium font-progress">预览与编辑</span>
      </div>
      <div className={`step-connector ${currentStep >= 3 ? 'bg-purple-500' : 'bg-gray-200'}`}></div>
      <div className={`step ${currentStep >= 3 ? 'step-active' : 'step-pending'} ${currentStep > 3 ? 'step-completed' : ''}`}>
        <div className="step-number">3</div>
        <span className="ml-2 text-sm font-medium font-progress">写入数据库</span>
      </div>
      <div className={`step-connector ${currentStep >= 4 ? 'bg-purple-500' : 'bg-gray-200'}`}></div>
      <div className={`step ${currentStep >= 4 ? 'step-active' : 'step-pending'}`}>
        <div className="step-number">4</div>
        <span className="ml-2 text-sm font-medium font-progress">结果确认</span>
      </div>
    </div>
  );
};

export default ProcessStatus;
