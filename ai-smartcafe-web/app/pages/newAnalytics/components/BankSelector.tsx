import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import Image from "next/image";

export interface Bank {
  id: string;
  name: string;
  icon: string;
}

const banks: Bank[] = [
  {
    id: "boc",
    name: "中国银行",
    icon: "/banks/中国银行.png"
  },
  {
    id: "icbc",
    name: "工商银行",
    icon: "/banks/工商银行.png"
  },
  {
    id: "spdb",
    name: "浦发银行",
    icon: "/banks/浦发银行.png"
  }
];

interface BankSelectorProps {
  selectedBank: string | null;
  onBankChange: (bankId: string) => void;
  disabled?: boolean;
  compact?: boolean;
}

const BankSelector: React.FC<BankSelectorProps> = ({ 
  selectedBank, 
  onBankChange, 
  disabled = false,
  compact = false
}) => {
  const selectedBankInfo = banks.find(bank => bank.id === selectedBank);

  return (
    <div className={compact ? "" : "space-y-2"}>
      {!compact && (
        <Label htmlFor="bank-select" className="text-sm font-medium text-gray-700">
          选择银行
        </Label>
      )}
      <Select 
        value={selectedBank || ""} 
        onValueChange={onBankChange}
        disabled={disabled}
      >
        <SelectTrigger className={compact ? "w-full h-9 border-gray-300 focus:border-purple-500 transition-colors duration-200" : "w-full"}>
          <SelectValue placeholder={compact ? "选择银行" : "请选择银行"}>
            {selectedBankInfo && (
              <div className="flex items-center gap-2">
                <Image
                  src={selectedBankInfo.icon}
                  alt={selectedBankInfo.name}
                  width={16}
                  height={16}
                  className="rounded-sm"
                />
                <span className={compact ? "text-sm font-medium" : ""}>{selectedBankInfo.name}</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="min-w-[200px]">
          {banks.map((bank) => (
            <SelectItem key={bank.id} value={bank.id} className="cursor-pointer">
              <div className="flex items-center gap-2">
                <Image
                  src={bank.icon}
                  alt={bank.name}
                  width={16}
                  height={16}
                  className="rounded-sm"
                />
                <span className="text-sm">{bank.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default BankSelector;
export { banks }; 