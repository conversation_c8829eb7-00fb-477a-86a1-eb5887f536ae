'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';
import ClientLayout from '@/components/layout/client-layout';
import { ConfigProvider, Select, Tooltip, Modal, Pagination, Button as AntdButton } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

// 导入类型
import { TransactionRow, BankAccount, Company } from '../../newAnalytics/types';

interface MenuData {
  id: string;
  menuName: string;
}

// 交易记录数据结构
interface BillData {
  id: string;
  billType: 'income' | 'expense';
  storeId: string | null;
  incomeExpenseTypeId: string | null;
  incomeSourceId: string | null;
  isSplit: number | null;
  billAmount: number | null;
  billBalance: number | null;
  billDate: string;
  opposingUnit: string;
  originData: string;
  status: number | null;
  transactionTime?: string;
  splitRecords?: { storeId: string; splitAmount: number; menuId: string }[];
}

interface StoreData {
  id: string;
  company_name: string;
  store_name: string;
  brand: string;
}

interface IncomeType {
  id: string;
  income_type: string;
}

interface IncomeSource {
  id: string;
  income_type: string;
  code: string;
  store_id: string;
}

interface ExpenseSubtype {
  id: string;
  name: string;
}

interface ExpenseType {
  id: string;
  name: string;
  subtypes: ExpenseSubtype[];
}

export default function AnalyticsDetailPage() {
  const params = useParams();
  const menuId = params.id as string;
  
  // 状态定义
  const [menu, setMenu] = useState<MenuData | null>(null);
  const [bills, setBills] = useState<BillData[]>([]);
  const [stores, setStores] = useState<StoreData[]>([]);
  const [incomeTypes, setIncomeTypes] = useState<IncomeType[]>([]);
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>([]);
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [loading, setLoading] = useState(true);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  // 模态框状态
  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [formattedJson, setFormattedJson] = useState<string>("");
  const [isSplitModalVisible, setIsSplitModalVisible] = useState(false);
  const [currentBill, setCurrentBill] = useState<BillData | null>(null);
  
  // 获取当前页的数据
  const currentBills = bills.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  
  // 计算汇总数据
  const summaryData = React.useMemo(() => {
    if (!bills.length) return { balance: 0, totalIncome: 0, totalExpense: 0 };
    
    let totalIncome = 0;
    let totalExpense = 0;
    let balance = 0;
    let latestBill = bills[0];
    let latestTime = latestBill && latestBill.transactionTime ? 
      new Date(latestBill.transactionTime).getTime() : 0;
    
    bills.forEach(bill => {
      // 计算收入总和
      if (bill.billType === 'income' && bill.billAmount !== null && bill.billAmount !== undefined) {
        const amount = typeof bill.billAmount === 'number' ? 
          bill.billAmount : parseFloat(String(bill.billAmount));
        
        if (!isNaN(amount)) {
          totalIncome += amount;
        }
      } 
      // 计算支出总和
      else if (bill.billType === 'expense' && bill.billAmount !== null && bill.billAmount !== undefined) {
        const amount = typeof bill.billAmount === 'number' ? 
          bill.billAmount : parseFloat(String(bill.billAmount));
          
        if (!isNaN(amount)) {
          totalExpense += Math.abs(amount);
        }
      }
      
      // 查找交易时间最晚的记录
      if (bill.transactionTime) {
        try {
          const currentTime = new Date(bill.transactionTime).getTime();
          if (!isNaN(currentTime) && currentTime > latestTime) {
            latestTime = currentTime;
            latestBill = bill;
          }
        } catch (e) {
          console.error('处理交易时间时出错:', e);
        }
      }
    });
    
    // 使用最晚记录的账单余额
    if (latestBill && latestBill.billBalance !== null && latestBill.billBalance !== undefined) {
      const latestBalance = typeof latestBill.billBalance === 'number' ? 
        latestBill.billBalance : parseFloat(String(latestBill.billBalance));
        
      if (!isNaN(latestBalance)) {
        balance = latestBalance;
      }
    }
    
    return { balance, totalIncome, totalExpense };
  }, [bills]);

  // 加载数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [menuResponse, billsResponse, storesResponse, incomeTypesResponse, incomeSourcesResponse, expenseTypesResponse] = await Promise.all([
          fetch(`/api/menus/${menuId}`),
          fetch(`/api/bills/${menuId}`),
          fetch('/api/stores'),
          fetch('/api/income-types'),
          fetch('/api/income-sources'),
          fetch('/api/expense-types')
        ]);

        const menuResult = await menuResponse.json();
        const billsResult = await billsResponse.json();
        const storesResult = await storesResponse.json();
        const incomeTypesResult = await incomeTypesResponse.json();
        const incomeSourcesResult = await incomeSourcesResponse.json();
        const expenseTypesResult = await expenseTypesResponse.json();
        
        if (menuResult.success) {
          setMenu(menuResult.data);
        } else {
          toast.error(menuResult.message || '获取菜单信息失败');
        }

        if (storesResult.success) {
          setStores(storesResult.data);
          
          // 处理账单数据
          if (billsResult.success) {
            setBills(billsResult.data);
          } else {
            toast.error(billsResult.message || '获取账单数据失败');
          }
        } else {
          toast.error(storesResult.message || '获取门店数据失败');
        }

        if (incomeTypesResult.success) {
          setIncomeTypes(incomeTypesResult.data.incomeTypes);
        } else {
          toast.error(incomeTypesResult.message || '获取收入类型数据失败');
        }

        if (incomeSourcesResult.success) {
          setIncomeSources(incomeSourcesResult.data.incomeSources);
        } else {
          toast.error(incomeSourcesResult.message || '获取收入来源数据失败');
        }

        if (expenseTypesResult.success) {
          setExpenseTypes(expenseTypesResult.data.expenseTypes);
        } else {
          toast.error(expenseTypesResult.message || '获取支出类型数据失败');
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        toast.error('获取数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (menuId) {
      fetchData();
    }
  }, [menuId]);

  // 格式化日期时间
  const formatDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return '';
    try {
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) return dateTimeString;
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
      console.error('格式化日期时间失败:', e);
      return dateTimeString;
    }
  };

  // 查看JSON原始数据
  const handleShowJsonModal = (jsonString: string) => {
    try {
      let parsedJson: any;
      
      try {
        parsedJson = JSON.parse(jsonString);
      } catch (e) {
        if (jsonString.startsWith('"') && jsonString.endsWith('"')) {
          try {
            const unescapedJson = JSON.parse(jsonString);
            parsedJson = JSON.parse(unescapedJson);
          } catch (innerE) {
            const cleanedJson = jsonString
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              .replace(/'/g, '"')
              .replace(/,\s*}/g, '}')
              .replace(/,\s*]/g, ']');
            
            parsedJson = JSON.parse(cleanedJson);
          }
        } else {
          if (!jsonString.startsWith('{')) {
            parsedJson = JSON.parse(`{${jsonString}}`);
          } else {
            throw e;
          }
        }
      }
      
      const prettyJson = JSON.stringify(parsedJson, null, 2);
      setFormattedJson(prettyJson);
      setJsonModalVisible(true);
    } catch (error) {
      console.error('JSON格式化失败:', error);
      setFormattedJson(jsonString);
      setJsonModalVisible(true);
    }
  };

  // 查看均摊详情
  const handleViewSplitRecords = (bill: BillData) => {
    setCurrentBill(bill);
    setIsSplitModalVisible(true);
  };

  if (loading) {
    return (
      <ClientLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </ClientLayout>
    );
  }

  return (
    <ClientLayout>
      <div className="bg-[#F9FAFB] p-6">
        <div className="mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">
              交易明细 - {menu?.menuName || `菜单ID: ${menuId}`}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              此视图仅供查看，不允许修改数据
            </p>
          </div>

          {/* 汇总数据展示区 */}
          <div className="mb-6 grid grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-md shadow">
              <div className="text-sm text-gray-500 mb-1">本期对账单余额</div>
              <div className="text-xl font-bold text-black">
                ￥{isNaN(Number(summaryData.balance)) ? '0.00' : Number(summaryData.balance).toFixed(2)}
              </div>
            </div>
            <div className="bg-white p-4 rounded-md shadow">
              <div className="text-sm text-gray-500 mb-1">本期收入总和</div>
              <div className="text-xl font-bold text-red-600">
                ￥{isNaN(Number(summaryData.totalIncome)) ? '0.00' : Number(summaryData.totalIncome).toFixed(2)}
              </div>
            </div>
            <div className="bg-white p-4 rounded-md shadow">
              <div className="text-sm text-gray-500 mb-1">本期支出总和</div>
              <div className="text-xl font-bold text-green-600">
                ￥{isNaN(Number(summaryData.totalExpense)) ? '0.00' : Number(summaryData.totalExpense).toFixed(2)}
              </div>
            </div>
          </div>

          {/* 数据表格 */}
          <ConfigProvider locale={zhCN} theme={{
            components: {
              Select: {
                activeBorderColor:'#8b5cf6',
                hoverBorderColor:'#8b5cf6'
              }
            },
            token:{
              colorPrimary:'#8b5cf6'
            }
          }}>
            <div className="p-0 border overflow-hidden bg-white rounded-lg shadow-sm">
              <div className='overflow-x-auto'>
              {/* style={{ height: '75vh', overflowY: 'auto'}} */}
                <table className="w-full border-collapse mt-0 text-base">
                  <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b border-r">#</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap" style={{ minWidth: '180px' }}>交易时间</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b" style={{ minWidth: '100px' }}>账单类型</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap">归属门店</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap">收支类型</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap">收入来源</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap" style={{ minWidth: '100px' }}>是否均摊</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap" style={{ minWidth: '120px' }}>账单金额</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap" style={{ minWidth: '250px' }}>对方单位</th>
                      <th className="text-center py-1 px-1 font-bold text-gray-700 border-b whitespace-nowrap" style={{ minWidth: '120px' }}>原始数据</th>
                    </tr>
                  </thead>
                  <tbody className="text-base">
                    {currentBills.map((bill, index) => {
                      // 获取门店名称
                      const storeName = bill.storeId ? stores.find(s => s.id === bill.storeId)?.store_name || '' : '';
                      
                      // 获取收支类型名称
                      let categoryName = '';
                      if (bill.billType === 'income' && bill.incomeExpenseTypeId) {
                        categoryName = incomeTypes.find(type => String(type.id) === String(bill.incomeExpenseTypeId))?.income_type || '';
                      } else if (bill.billType === 'expense' && bill.incomeExpenseTypeId) {
                        const [typeId, subtypeId] = bill.incomeExpenseTypeId.split('-');
                        const type = expenseTypes.find(t => String(t.id) === typeId);
                        if (type) {
                          if (subtypeId) {
                            const subtype = type.subtypes.find(s => String(s.id) === subtypeId);
                            categoryName = subtype ? `${type.name}-${subtype.name}` : type.name;
                          } else {
                            categoryName = type.name;
                          }
                        }
                      }
                      
                      // 获取收入来源
                      const incomeSource = bill.incomeSourceId 
                        ? incomeSources.find(source => source.id === bill.incomeSourceId)?.income_type || '' 
                        : '';
                      
                      return (
                        <tr key={bill.id} className="hover:bg-gray-50 transition-colors border-b last:border-b-0">
                          <td className="py-1 px-1 text-center" style={{ width: '40px' }}>
                            {(currentPage - 1) * pageSize + index + 1}
                          </td>
                          <td className="py-1 px-1 text-center">
                            <div className="truncate mx-auto text-base">
                              {formatDateTime(bill.transactionTime || '')}
                            </div>
                          </td>
                          <td className="py-1 px-1 text-center" style={{ minWidth: '100px' }}>
                            <Tooltip title={bill.billType === 'income' ? '收入' : '支出'}>
                              <div className="truncate mx-auto cursor-pointer text-base">
                                {bill.billType === 'income' ? '收入' : '支出'}
                              </div>
                            </Tooltip>
                          </td>
                          <td className="py-1 px-1 text-center">
                            <div className="truncate mx-auto text-base">
                              {storeName}
                            </div>
                          </td>
                          <td className="py-1 px-1 text-center">
                            <div className="truncate mx-auto text-base">
                              {categoryName}
                            </div>
                          </td>
                          <td className="py-1 px-1 text-center">
                            <div className="truncate mx-auto text-base">
                              {incomeSource}
                            </div>
                          </td>
                          <td className="py-1 px-1 text-center text-base" style={{ minWidth: '100px' }}>
                            {bill.billType === 'expense' ? (
                              bill.isSplit === 1 ? (
                                <div className="flex items-center justify-center gap-2">
                                  <span>是</span>
                                  <AntdButton
                                    onClick={() => handleViewSplitRecords(bill)}
                                    className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
                                  >
                                    查看分摊金额
                                  </AntdButton>
                                </div>
                              ) : '否'
                            ) : ''}
                          </td>
                          <td className="py-1 px-1 text-center" style={{ minWidth: '120px' }}>
                            <Tooltip title={bill.billAmount || ''}>
                              <div className="truncate mx-auto cursor-pointer text-base">
                                {bill.billAmount || ''}
                              </div>
                            </Tooltip>
                          </td>
                          <td className="py-1 px-1 text-center" style={{ minWidth: '250px' }}>
                            <Tooltip title={bill.opposingUnit}>
                              <div className="truncate max-w-[250px] mx-auto cursor-pointer text-base">
                                {bill.opposingUnit}
                              </div>
                            </Tooltip>
                          </td>
                          <td className="py-1 px-1 text-center text-base min-w-[120px]">
                            <AntdButton
                              type="primary"
                              size="small"
                              style={{ backgroundColor: '#1977ff', borderColor: '#1977ff' }}
                              onClick={() => handleShowJsonModal(bill.originData)}
                            >
                              查看
                            </AntdButton>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              <div className="py-4 px-6 border-t bg-gray-50 flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  共{bills.length}条数据
                </div>
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={bills.length}
                  onChange={(page) => setCurrentPage(page)}
                  showQuickJumper
                  showSizeChanger={false}
                />
              </div>
            </div>
          </ConfigProvider>

          {/* JSON格式化模态框 */}
          <Modal
            title="原始数据详情"
            open={jsonModalVisible}
            onCancel={() => setJsonModalVisible(false)}
            footer={[
              <AntdButton key="close" onClick={() => setJsonModalVisible(false)}>
                关闭
              </AntdButton>
            ]}
            width={800}
          >
            <pre className="bg-gray-50 p-4 rounded-md overflow-auto max-h-[70vh] text-sm">
              {formattedJson}
            </pre>
          </Modal>
          
          {/* 均摊详情模态框 */}
          <Modal
            title="均摊详情"
            open={isSplitModalVisible}
            onCancel={() => setIsSplitModalVisible(false)}
            footer={[
              <AntdButton key="close" onClick={() => setIsSplitModalVisible(false)}>
                关闭
              </AntdButton>
            ]}
            width={600}
          >
            <div className="space-y-4">
              {(!currentBill?.splitRecords || currentBill.splitRecords.length === 0) ? (
                <div className="text-center py-8 text-gray-500">
                  未找到此账单的均摊详情
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-2 gap-4 font-bold mb-2">
                    <div>门店名称</div>
                    <div>均摊金额</div>
                  </div>
                  {currentBill?.splitRecords?.map((record, index) => {
                    const store = stores.find(s => s.id === record.storeId);
                    // 确保splitAmount是数字类型
                    const splitAmount = typeof record.splitAmount === 'number' 
                      ? record.splitAmount 
                      : parseFloat(String(record.splitAmount)) || 0;
                    
                    return (
                      <div key={index} className="grid grid-cols-2 gap-4 items-center">
                        <div>
                          {store?.store_name || '未知门店'}
                        </div>
                        <div className="text-green-600 font-medium">
                          ￥{splitAmount.toFixed(2)}
                        </div>
                      </div>
                    );
                  })}
                  <div className="text-right mt-4 text-gray-500 text-sm">
                    共{currentBill?.splitRecords?.length || 0}家门店均摊
                  </div>
                </>
              )}
            </div>
          </Modal>
        </div>
      </div>
    </ClientLayout>
  );
} 