export interface BankAccount {
  id: string;
  name: string;
  number: string;
}

export interface Company {
  id: string;
  name: string;
}

export interface StatementFile {
  id: string;
  name: string;
  account: string;
  company: string;
  status: 'initial' | 'processing' | 'uploaded' | 'analyzed' | 'confirmed' | 'written' | 'completed' | 'failed';
  progress?: number;
  errorMessage?: string;
  createdAt?: string;
  menuId?: number;
  bankId?: string;
}

export interface TransactionRow {
  id: string;
  date: string;
  counterparty: string;
  amount: number;
  store: string;
  transactionType: '收入' | '支出' | '转账';
  incomeSource?: string;
  isShared: boolean;
  description: string;
  originalRecord?: string;
  account: string;
  company: string;
}

export interface ProcessStatusProps {
  currentStep: 1 | 2 | 3 | 4;
}
