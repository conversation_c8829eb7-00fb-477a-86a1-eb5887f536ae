import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Mail, Phone, User, Lock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useRouter, useSearchParams } from 'next/navigation';
import { Checkbox } from "@/components/ui/checkbox";
import { setupTokenRefreshTimer } from '@/lib/auth/token';

interface LoginFormProps {
  loginType: 'email' | 'phone' | 'username';
  onPasswordReset: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ loginType, onPasswordReset }) => {
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [remainingAttempts, setRemainingAttempts] = useState<number | null>(null);
  const [rememberMe, setRememberMe] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get('redirect') || '/pages/dashboard';

  const getPlaceholder = () => {
    switch (loginType) {
      case 'email':
        return '请输入邮箱（如*****************）';
      case 'phone':
        return '请输入手机号（如13800138000）';
      case 'username':
        return '请输入用户名（如admin）';
    }
  };

  const getIcon = () => {
    switch (loginType) {
      case 'email':
        return Mail;
      case 'phone':
        return Phone;
      case 'username':
        return User;
    }
  };

  const getLabel = () => {
    switch (loginType) {
      case 'email':
        return '邮箱';
      case 'phone':
        return '手机号';
      case 'username':
        return '用户名';
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier,
          password,
          loginType,
          rememberMe,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // 处理错误
        if (response.status === 429) {
          // 请求频率限制
          toast({
            variant: "destructive",
            title: "登录失败",
            description: data.message || "登录尝试次数过多，请稍后再试",
          });
        } else if (response.status === 401) {
          // 凭据无效
          const attempts = remainingAttempts ?? 5;
          const newAttempts = attempts - 1;
          setRemainingAttempts(newAttempts);
          
          toast({
            variant: "destructive",
            title: "登录失败",
            description: data.message || `${getLabel()}或密码错误，剩余 ${newAttempts} 次尝试`,
          });
        } else if (response.status === 403) {
          // 账号被禁用
          toast({
            variant: "destructive",
            title: "账号已禁用",
            description: data.message || "账号已被禁用，请联系管理员",
          });
        } else {
          // 其他错误
          toast({
            variant: "destructive",
            title: "登录失败",
            description: data.message || "登录时发生错误，请稍后再试",
          });
        }
      } else {
        // 登录成功
        toast({
          title: "登录成功",
          description: "欢迎回来！",
        });

        // 保存访问令牌到sessionStorage
        if (data.accessToken) {
          sessionStorage.setItem('accessToken', data.accessToken);
        }
        
        // 保存刷新令牌到sessionStorage (如果选择了记住我)
        if (data.refreshToken && rememberMe) {
          sessionStorage.setItem('refreshToken', data.refreshToken);
        }
        
        // 保存用户信息到sessionStorage
        if (data.user) {
          sessionStorage.setItem('userInfo', JSON.stringify(data.user));
        }
        
        // 保存令牌过期信息，用于自动刷新
        if (data.expiresIn) {
          const expiresAt = Date.now() + (data.expiresIn * 1000);
          sessionStorage.setItem('tokenData', JSON.stringify({ 
            expiresAt,
            expiresIn: data.expiresIn
          }));
          setupTokenRefreshTimer(data.expiresIn);
        }

        // 如果初次登录密码是默认密码，需要修改
        if (password === '123456') {
          onPasswordReset();
        } else {
          // 重定向到之前尝试访问的页面或默认页面
          router.push(redirectPath);
        }
      }
    } catch (error) {
      console.error('登录请求失败:', error);
      toast({
        variant: "destructive",
        title: "登录失败",
        description: "网络错误，请检查您的网络连接",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const IconComponent = getIcon();

  return (
    <form onSubmit={handleLogin} className="space-y-6">
      {/* Identifier Field */}
      <div className="space-y-2">
        <Label htmlFor="identifier" className="text-slate-100 font-medium">
          {getLabel()}
        </Label>
        <div className="relative">
          <IconComponent className="absolute left-3 top-3 h-5 w-5 text-violet-400" />
          <Input
            id="identifier"
            type={loginType === 'email' ? 'email' : 'text'}
            value={identifier}
            onChange={(e) => setIdentifier(e.target.value)}
            placeholder={getPlaceholder()}
            className="pl-10 bg-slate-600/70 border-slate-500 text-white placeholder:text-slate-300 focus:border-violet-400"
            required
          />
        </div>
      </div>

      {/* Password Field */}
      <div className="space-y-2">
        <Label htmlFor="password" className="text-slate-100 font-medium">
          密码
        </Label>
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-5 w-5 text-violet-400" />
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="请输入密码"
            className="pl-10 pr-10 bg-slate-600/70 border-slate-500 text-white placeholder:text-slate-300 focus:border-violet-400"
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-3 text-violet-400 hover:text-violet-300"
          >
            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
          </button>
        </div>
      </div>

      {/* Remember Me Checkbox */}
      <div className="flex items-center space-x-2">
        <Checkbox 
          id="rememberMe" 
          checked={rememberMe} 
          onCheckedChange={(checked) => setRememberMe(checked === true)}
          className="data-[state=checked]:bg-violet-500 border-slate-400" 
        />
        <Label 
          htmlFor="rememberMe" 
          className="text-sm text-slate-200 font-normal cursor-pointer"
        >
          记住我（7天内自动登录）
        </Label>
      </div>

      {/* Error Message */}
      {remainingAttempts !== null && remainingAttempts < 5 && (
        <div className="text-red-300 text-sm bg-red-900/40 border border-red-700/60 rounded-lg p-3">
          {getLabel()}或密码错误，剩余 {remainingAttempts} 次尝试
        </div>
      )}

      {/* Login Button */}
      <Button
        type="submit"
        disabled={isLoading}
        className="w-full bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-400 hover:to-purple-400 text-white font-medium"
      >
        {isLoading ? (
          <div className="flex items-center">
            <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
            登录中...
          </div>
        ) : (
          '登录'
        )}
      </Button>
    </form>
  );
};

export default LoginForm;
