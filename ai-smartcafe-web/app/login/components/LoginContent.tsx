'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { <PERSON>Left, BrainCircuit } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import LoginForm from './LoginForm';
import PasswordResetModal from './PasswordResetModal';

const LoginContent = () => {
  const [showPasswordReset, setShowPasswordReset] = useState(false);

  const handlePasswordResetSuccess = () => {
    setShowPasswordReset(false);
  };

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-black"></div>
      <div className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1)_0%,rgba(0,0,0,0)_70%)] animate-[spin_20s_linear_infinite]"></div>
      <div className="hidden md:block absolute top-1/4 left-1/4 w-32 h-32 bg-violet-500/10 rounded-full filter blur-2xl animate-pulse"></div>
      <div className="hidden md:block absolute bottom-1/4 right-1/4 w-48 h-48 bg-purple-500/10 rounded-full filter blur-3xl animate-pulse"></div>
      
      <div className="w-full max-w-md relative z-10">
        {/* Back button */}
        <Link 
          href="/" 
          className="inline-flex items-center text-slate-300 hover:text-white mb-8 transition-all duration-300 group bg-slate-800/50 hover:bg-slate-700/80 px-4 py-2 rounded-full"
        >
          <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
          返回
        </Link>

        {/* Login Card */}
        <div className="bg-slate-800/60 backdrop-blur-lg border border-slate-700/60 rounded-2xl p-8 shadow-2xl transition-all duration-300 hover:shadow-[0_0_2rem_-0.5rem_#8b5cf6] hover:border-violet-400/60">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-violet-600 to-purple-600 rounded-xl mb-4 shadow-lg shadow-violet-500/20">
              <BrainCircuit className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">慧账AI</h1>
            <p className="text-slate-300">欢迎回来，请选择登录方式</p>
          </div>

          {/* Login Tabs */}
          <Tabs defaultValue="email" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6 bg-slate-900/70 border border-slate-700/50 p-1">
              <TabsTrigger 
                value="email" 
                className="text-slate-300 data-[state=active]:bg-violet-600 data-[state=active]:text-white rounded-md transition-all duration-300"
              >
                邮箱
              </TabsTrigger>
              <TabsTrigger 
                value="phone"
                className="text-slate-300 data-[state=active]:bg-violet-600 data-[state=active]:text-white rounded-md transition-all duration-300"
              >
                手机号
              </TabsTrigger>
              <TabsTrigger 
                value="username"
                className="text-slate-300 data-[state=active]:bg-violet-600 data-[state=active]:text-white rounded-md transition-all duration-300"
              >
                用户名
              </TabsTrigger>
            </TabsList>

            <TabsContent value="email" className="space-y-0">
              <LoginForm 
                loginType="email" 
                onPasswordReset={() => setShowPasswordReset(true)} 
              />
            </TabsContent>

            <TabsContent value="phone" className="space-y-0">
              <LoginForm 
                loginType="phone" 
                onPasswordReset={() => setShowPasswordReset(true)} 
              />
            </TabsContent>

            <TabsContent value="username" className="space-y-0">
              <LoginForm 
                loginType="username" 
                onPasswordReset={() => setShowPasswordReset(true)} 
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-slate-300 text-sm">
          © 2025 慧账AI. 保留所有权利。
        </div>
      </div>

      {/* Password Reset Modal */}
      <PasswordResetModal
        isOpen={showPasswordReset}
        onClose={() => setShowPasswordReset(false)}
        onSuccess={handlePasswordResetSuccess}
        userId={typeof window !== 'undefined' && sessionStorage.getItem('userInfo') 
          ? JSON.parse(sessionStorage.getItem('userInfo') || '{}').id 
          : undefined}
      />
    </div>
  );
};

export default LoginContent; 