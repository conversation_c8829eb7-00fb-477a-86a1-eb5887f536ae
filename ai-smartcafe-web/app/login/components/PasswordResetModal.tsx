import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Eye, EyeOff, Lock, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PasswordResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userId?: number; // 添加可选的用户ID属性
}

const PasswordResetModal: React.FC<PasswordResetModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  userId,
}) => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // 密码验证规则
  const validatePassword = (password: string) => {
    const minLength = password.length >= 8 && password.length <= 16;
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      minLength,
      hasLetter,
      hasNumber,
      hasSpecial,
      isValid: minLength && hasLetter && hasNumber && hasSpecial,
    };
  };

  const passwordValidation = validatePassword(newPassword);
  const passwordsMatch = newPassword === confirmPassword && confirmPassword !== '';

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!passwordValidation.isValid) {
      toast({
        variant: "destructive",
        title: "密码格式错误",
        description: "请确保密码符合所有要求",
      });
      return;
    }

    if (!passwordsMatch) {
      toast({
        variant: "destructive",
        title: "密码不匹配",
        description: "两次输入的密码不一致",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // 使用sessionStorage中保存的用户信息
      let userInfo;
      try {
        const userInfoStr = sessionStorage.getItem('userInfo');
        if (userInfoStr) {
          userInfo = JSON.parse(userInfoStr);
        }
      } catch (err) {
        console.error('解析用户信息失败:', err);
      }
      
      // 调用密码修改API
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionStorage.getItem('accessToken') || ''}`
        },
        body: JSON.stringify({
          oldPassword: '123456', // 默认密码
          newPassword,
          userId: userInfo?.id || userId, // 尝试使用存储的用户ID或传入的用户ID
        }),
        credentials: 'include'
      });

      const data = await response.json();

      if (!response.ok) {
        toast({
          variant: "destructive",
          title: "密码修改失败",
          description: data.message || "修改密码时发生错误，请稍后再试",
        });
      } else {
        toast({
          title: "密码修改成功",
          description: "您的密码已成功修改，请使用新密码登录",
        });
        
        // 清空输入框
        setNewPassword('');
        setConfirmPassword('');
        
        // 调用成功回调
        onSuccess();
      }
    } catch (error) {
      console.error('密码修改请求失败:', error);
      toast({
        variant: "destructive",
        title: "密码修改失败",
        description: "网络错误，请检查您的网络连接",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 验证项目组件
  const ValidationItem = ({ isValid, text }: { isValid: boolean; text: string }) => (
    <div className={`flex items-center text-sm ${isValid ? 'text-emerald-300' : 'text-slate-300'}`}>
      {isValid ? (
        <CheckCircle className="w-4 h-4 mr-2" />
      ) : (
        <XCircle className="w-4 h-4 mr-2" />
      )}
      {text}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-700/95 border-slate-600/50 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold bg-gradient-to-r from-violet-300 to-purple-300 bg-clip-text text-transparent">
            首次登录密码修改
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-center text-slate-200 text-sm">
            为了您的账户安全，首次登录需要修改密码
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 新密码字段 */}
            <div className="space-y-2">
              <Label htmlFor="newPassword" className="text-slate-100 font-medium">
                新密码
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-5 w-5 text-violet-400" />
                <Input
                  id="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="请输入新密码"
                  className="pl-10 pr-10 bg-slate-600/70 border-slate-500 text-white placeholder:text-slate-300"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-3 top-3 text-violet-400 hover:text-violet-300"
                >
                  {showNewPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* 确认密码字段 */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-slate-100 font-medium">
                确认密码
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-5 w-5 text-violet-400" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="请再次输入新密码"
                  className="pl-10 pr-10 bg-slate-600/70 border-slate-500 text-white placeholder:text-slate-300"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 text-violet-400 hover:text-violet-300"
                >
                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* 密码要求 */}
            <div className="bg-slate-600/60 border border-slate-500/60 rounded-lg p-4 space-y-2">
              <div className="text-sm font-medium text-slate-100 mb-2">密码要求：</div>
              <ValidationItem isValid={passwordValidation.minLength} text="8-16位字符" />
              <ValidationItem isValid={passwordValidation.hasLetter} text="包含字母" />
              <ValidationItem isValid={passwordValidation.hasNumber} text="包含数字" />
              <ValidationItem isValid={passwordValidation.hasSpecial} text="包含特殊字符" />
              {confirmPassword && (
                <ValidationItem isValid={passwordsMatch} text="两次密码一致" />
              )}
            </div>

            {/* 提交按钮 */}
            <Button
              type="submit"
              disabled={!passwordValidation.isValid || !passwordsMatch || isLoading}
              className="w-full bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-400 hover:to-purple-400 text-white font-medium"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                  修改中...
                </div>
              ) : (
                '确认修改'
              )}
            </Button>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PasswordResetModal;
