import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
// import { db } from '@/lib/db';
// import { getCurrentCompanyId } from '@/lib/auth';

const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

const getCurrentCompanyId = async (req: NextRequest): Promise<number | null> => {
  const companyIdHeader = req.headers.get('X-Company-ID');
  if (companyIdHeader && !isNaN(parseInt(companyIdHeader))) {
    return parseInt(companyIdHeader);
  }
  return 1; // Placeholder, replace with actual auth
};

interface RouteParams {
  params: { userId: string };
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let connection: mysql.Connection | null = null;
  try {
    const companyId = await getCurrentCompanyId(request);
    const { userId } = params;

    if (!companyId) {
      return NextResponse.json({ message: '未授权' }, { status: 401 });
    }
    if (!userId || isNaN(parseInt(userId))) {
      return NextResponse.json({ message: '无效或缺少用户ID' }, { status: 400 });
    }

    const { username, name, contact, role } = await request.json();

    if (!username || !name || !contact || !role) {
      return NextResponse.json({ message: '用户名、姓名、联系方式和角色均不能为空' }, { status: 400 });
    }

    // Prevent updating super_admin status or changing username of super_admin directly here for safety.
    // Super admin role should be managed carefully, perhaps via a separate process or by ensuring only one exists.
    // Username for super admin is typically tied to company's primary phone and shouldn't change easily.

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    // Prevent editing super admin
    const [userToEditRows] = await connection.execute<mysql.RowDataPacket[]>(
      'SELECT is_super_admin FROM account WHERE id = ? AND company_id = ? AND deleted_at IS NULL',
      [parseInt(userId), companyId]
    );

    if (userToEditRows.length === 0) {
      await connection.rollback();
      return NextResponse.json({ message: '用户未找到或无权修改' }, { status: 404 });
    }
    if (userToEditRows[0].is_super_admin) {
      await connection.rollback();
      return NextResponse.json({ message: '不允许直接修改超级管理员信息' }, { status: 403 });
    }

    // Check for username uniqueness (globally, excluding current user, including logically deleted if strict)
    const [existingUserWithSameUsername] = await connection.execute<mysql.RowDataPacket[]>(
      'SELECT id FROM account WHERE username = ? AND id != ? AND deleted_at IS NULL',
      [username, parseInt(userId)]
    );
    if (existingUserWithSameUsername.length > 0) {
      await connection.rollback();
      return NextResponse.json({ message: '该用户名已被其他账号使用' }, { status: 409 });
    }

    const [result] = await connection.execute<mysql.ResultSetHeader>(
      'UPDATE account SET username = ?, full_name = ?, contact_info = ?, role = ?, updated_at = NOW() WHERE id = ? AND company_id = ? AND deleted_at IS NULL AND is_super_admin = FALSE',
      [username, name, contact, role, parseInt(userId), companyId]
    );

    if (result.affectedRows === 0) {
      // This could happen if the user was deleted or became super admin between checks, or no actual change
      await connection.rollback();
      return NextResponse.json({ message: '更新用户失败，用户可能已被修改或删除' }, { status: 404 });
    }

    await connection.commit();
    return NextResponse.json({
      id: userId,
      username,
      name,
      contact,
      role,
      message: '用户信息更新成功'
    });

  } catch (error: any) {
    if (connection) await connection.rollback();
    console.error('更新用户失败:', error);
    if (error.code === 'ER_DUP_ENTRY' && error.sqlMessage && error.sqlMessage.includes('username')) {
      return NextResponse.json({ message: '该用户名已被使用' }, { status: 409 });
    }
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// DELETE for user is logical via status update, actual deletion is not directly exposed here for now.
// If physical delete is needed, a DELETE handler can be added.
// For this task, '离职关闭账户' implies changing isActive to false, handled by the status route. 