import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
// import { db } from '@/lib/db';
// import { getCurrentCompanyId } from '@/lib/auth';

const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

const getCurrentCompanyId = async (req: NextRequest): Promise<number | null> => {
  const companyIdHeader = req.headers.get('X-Company-ID');
  if (companyIdHeader && !isNaN(parseInt(companyIdHeader))) {
    return parseInt(companyIdHeader);
  }
  return 1; // Placeholder, replace with actual auth
};

interface RouteParams {
  params: { userId: string };
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  let connection: mysql.Connection | null = null;
  try {
    const companyId = await getCurrentCompanyId(request);
    const { userId } = params;

    if (!companyId) {
      return NextResponse.json({ message: '未授权' }, { status: 401 });
    }
    if (!userId || isNaN(parseInt(userId))) {
      return NextResponse.json({ message: '无效或缺少用户ID' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction(); // Start transaction

    const [userRows] = await connection.execute(
        'SELECT is_active, is_super_admin FROM account WHERE id = ? AND company_id = ? AND deleted_at IS NULL FOR UPDATE',
        [parseInt(userId), companyId]
    ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

    if (userRows.length === 0) {
      await connection.rollback();
      return NextResponse.json({ message: '用户未找到' }, { status: 404 });
    }
    const userInfo = userRows[0];

    if (userInfo.is_super_admin) {
      await connection.rollback();
      return NextResponse.json({ message: '不能禁用超级管理员账户' }, { status: 403 });
    }

    const newStatus = !userInfo.is_active;

    if (newStatus === true) { // Activating a user
      const [companyRows] = await connection.execute(
          'SELECT max_users_allowed FROM companies WHERE id = ? AND deleted_at IS NULL FOR UPDATE',
          [companyId]
      ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

      if (companyRows.length === 0) {
        await connection.rollback();
        return NextResponse.json({ message: '未找到公司设置' }, { status: 404 });
      }
      const companySettings = companyRows[0];

      const [userCountRows] = await connection.execute(
          'SELECT COUNT(*) as count FROM account WHERE company_id = ? AND is_active = TRUE AND deleted_at IS NULL',
          [companyId]
      )  as [mysql.RowDataPacket[], mysql.FieldPacket[]];
      const activeUsersCount = userCountRows[0].count;

      if (activeUsersCount >= companySettings.max_users_allowed) {
        await connection.rollback();
        return NextResponse.json({ message: `激活失败：已达到最大有效账号数 (${companySettings.max_users_allowed})` }, { status: 403 });
      }
    }

    const [result] = await connection.execute<mysql.ResultSetHeader>(
      'UPDATE account SET is_active = ?, updated_at = NOW() WHERE id = ? AND company_id = ? AND deleted_at IS NULL AND is_super_admin = FALSE',
      [newStatus, parseInt(userId), companyId]
    );

    if (result.affectedRows === 0) {
      // This case should ideally be caught by earlier checks (user not found, or super_admin)
      await connection.rollback();
      return NextResponse.json({ message: '操作失败，用户未找到或无法修改状态' }, { status: 404 });
    }
    
    await connection.commit(); // Commit transaction

    return NextResponse.json({ 
      message: `用户账号已${newStatus ? '启用' : '禁用'}`,
      newStatus: newStatus
    });

  } catch (error) {
    if (connection) await connection.rollback(); // Rollback on error
    console.error('切换用户状态失败:', error);
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
} 