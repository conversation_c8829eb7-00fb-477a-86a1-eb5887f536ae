import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// 报销数据接口
interface ReimbursementData {
  nominalExpense: number; // 名义支出 
  actualExpense: number; // 实际支出
  pendingReimbursement: number; // 待报销总额
  previousPendingReimbursement: number; // 上个月待报销总额
  trend: number; // 趋势波动值
  expenseTypes: ExpenseTypeData[]; // 各类型的支出明细
}

// 支出类型数据接口
interface ExpenseTypeData {
  expenseType: string;
  amount: number;
  percentage: number;
}

export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    // 从URL获取查询参数
    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month') || '';
    const entity = searchParams.get('entity') || '总公司';

    console.log('请求报销数据参数:', { month, entity });

    // 验证月份格式 (YYYY-MM)
    if (!month || !/^\d{4}-\d{2}$/.test(month)) {
      return NextResponse.json(
        { success: false, message: '无效的月份格式，请使用YYYY-MM格式' },
        { status: 400 }
      );
    }

    const connection = await mysql.createConnection(dbConfig);

    try {
      // 解析月份参数，获取年和月
      const [year, monthNum] = month.split('-');
      const startDate = `${year}-${monthNum}-01`;
      
      // 计算当月最后一天
      const nextMonth = parseInt(monthNum) === 12 ? 1 : parseInt(monthNum) + 1;
      const nextYear = parseInt(monthNum) === 12 ? parseInt(year) + 1 : parseInt(year);
      const endDate = `${nextYear}-${String(nextMonth).padStart(2, '0')}-01`;

      // 计算上个月的日期范围
      const prevMonth = parseInt(monthNum) === 1 ? 12 : parseInt(monthNum) - 1;
      const prevYear = parseInt(monthNum) === 1 ? parseInt(year) - 1 : parseInt(year);
      const prevStartDate = `${prevYear}-${String(prevMonth).padStart(2, '0')}-01`;
      const prevEndDate = startDate;
      
      console.log('查询日期范围:', {
          currentMonth: { startDate, endDate },
          prevMonth: { startDate: prevStartDate, endDate: prevEndDate }
      });

      // 根据entity参数决定是否过滤门店
      let storeIds: number[] = [];
      
      if (entity !== '总公司') {
        // 判断是品牌还是门店（添加 ap_company_id 筛选）
        const [brandStoresResult] = await connection.execute(
          `SELECT id FROM stores WHERE ap_company_id = ? AND brand = ?`,
          [apCompanyId, entity]
        );
        
        if ((brandStoresResult as any[]).length > 0) {
          // 是品牌，获取该品牌下所有门店的ID
          storeIds = (brandStoresResult as any[]).map(store => store.id);
          console.log(`品牌 ${entity} 下的门店IDs:`, storeIds);
        } else {
          // 不是品牌，尝试作为门店名查询（添加 ap_company_id 筛选）
          const [storeResult] = await connection.execute(
            `SELECT id FROM stores WHERE ap_company_id = ? AND store_name = ?`,
            [apCompanyId, entity]
          );
          
          if ((storeResult as any[]).length > 0) {
            // 是门店
            storeIds = (storeResult as any[]).map(store => store.id);
            console.log(`门店 ${entity} 的ID:`, storeIds);
          }
        }
        
        if (storeIds.length === 0) {
          // 既不是有效的品牌也不是有效的门店
          console.log(`未找到实体 ${entity} 的相关门店`);
          return NextResponse.json({
            success: true,
            data: {
              nominalExpense: 0,
              actualExpense: 0,
              pendingReimbursement: 0,
              previousPendingReimbursement: 0,
              trend: 0,
              expenseTypes: []
            }
          });
        }
      }

      // ====== 当前月数据查询 ======
      
      // 1. 查询名义支出数据 - 从approval_expense_details表（添加 ap_company_id 筛选）
      let nominalExpenseQuery = `
        SELECT 
          expense_type, 
          SUM(amount) as total_amount
        FROM 
          approval_expense_details aed
        JOIN 
          approval_header ah ON aed.approval_id = ah.id
        WHERE 
          aed.ap_company_id = ? AND expense_date >= ? 
          AND expense_date < ?
      `;
      
      let nominalExpenseParams: any[] = [apCompanyId, startDate, endDate];
      
      // 如果有具体门店，添加门店条件
      if (storeIds.length > 0) {
        const placeholders = storeIds.map(() => '?').join(',');
        nominalExpenseQuery += ` AND ah.department IN (SELECT store_name FROM stores WHERE ap_company_id = ? AND id IN (${placeholders}))`;
        nominalExpenseParams = [...nominalExpenseParams, apCompanyId, ...storeIds];
      }
      
      nominalExpenseQuery += ` GROUP BY expense_type`;

      // 执行名义支出查询
      const [nominalExpenseResult] = await connection.execute(nominalExpenseQuery, nominalExpenseParams);
      console.log('当前月名义支出查询结果:', nominalExpenseResult);

      // 2. 查询实际支出数据 - 从bills表获取已标记为报销的数据（添加 ap_company_id 筛选）
      let actualExpenseQuery = `
        SELECT 
          SUM(bill_amount) as total_amount
        FROM 
          bills
        WHERE 
          ap_company_id = ? AND transaction_time >= ?
          AND transaction_time < ?
          AND is_reimburse = 1
          AND status = 1
      `;
      
      let actualExpenseParams: any[] = [apCompanyId, startDate, endDate];
      
      // 如果有具体门店，添加门店条件
      if (storeIds.length > 0) {
        const placeholders = storeIds.map(() => '?').join(',');
        actualExpenseQuery += ` AND store_id IN (${placeholders})`;
        actualExpenseParams = [...actualExpenseParams, ...storeIds];
      }

      // 执行实际支出查询
      const [actualExpenseResult] = await connection.execute(actualExpenseQuery, actualExpenseParams);
      console.log('当前月实际支出查询结果:', actualExpenseResult);
      
      // ====== 上个月数据查询 ======
      
      // 1. 查询上个月名义支出（添加 ap_company_id 筛选）
      let prevNominalExpenseQuery = `
        SELECT 
          SUM(amount) as total_amount
        FROM 
          approval_expense_details aed
        JOIN 
          approval_header ah ON aed.approval_id = ah.id
        WHERE 
          aed.ap_company_id = ? AND expense_date >= ? 
          AND expense_date < ?
      `;
      
      let prevNominalExpenseParams: any[] = [apCompanyId, prevStartDate, prevEndDate];
      
      // 如果有具体门店，添加门店条件
      if (storeIds.length > 0) {
        const placeholders = storeIds.map(() => '?').join(',');
        prevNominalExpenseQuery += ` AND ah.department IN (SELECT store_name FROM stores WHERE ap_company_id = ? AND id IN (${placeholders}))`;
        prevNominalExpenseParams = [...prevNominalExpenseParams, apCompanyId, ...storeIds];
      }

      // 执行上个月名义支出查询
      const [prevNominalExpenseResult] = await connection.execute(prevNominalExpenseQuery, prevNominalExpenseParams);
      console.log('上个月名义支出查询结果:', prevNominalExpenseResult);
      
      // 2. 查询上个月实际支出（添加 ap_company_id 筛选）
      let prevActualExpenseQuery = `
        SELECT 
          SUM(bill_amount) as total_amount
        FROM 
          bills
        WHERE 
          ap_company_id = ? AND transaction_time >= ?
          AND transaction_time < ?
          AND is_reimburse = 1
          AND status = 1
      `;
      
      let prevActualExpenseParams: any[] = [apCompanyId, prevStartDate, prevEndDate];
      
      // 如果有具体门店，添加门店条件
      if (storeIds.length > 0) {
        const placeholders = storeIds.map(() => '?').join(',');
        prevActualExpenseQuery += ` AND store_id IN (${placeholders})`;
        prevActualExpenseParams = [...prevActualExpenseParams, ...storeIds];
      }

      // 执行上个月实际支出查询
      const [prevActualExpenseResult] = await connection.execute(prevActualExpenseQuery, prevActualExpenseParams);
      console.log('上个月实际支出查询结果:', prevActualExpenseResult);

      // ====== 处理结果 ======
      
      // 处理当前月查询结果
      const nominalExpense = (nominalExpenseResult as any[]).reduce((sum, item) => sum + parseFloat(item.total_amount || 0), 0);
      const actualExpense = (actualExpenseResult as any[])[0]?.total_amount || 0;
      
      // 计算当前月待报销总额
      const pendingReimbursement = nominalExpense - actualExpense;
      
      // 处理上个月查询结果
      const prevNominalExpense = (prevNominalExpenseResult as any[])[0]?.total_amount || 0;
      const prevActualExpense = (prevActualExpenseResult as any[])[0]?.total_amount || 0;
      
      // 计算上个月待报销总额
      const previousPendingReimbursement = prevNominalExpense - prevActualExpense;
      
      // 计算趋势波动值（百分比）
      let trend = 0;
      if (previousPendingReimbursement !== 0) {
        // 计算波动率
        const diff = pendingReimbursement - previousPendingReimbursement;
        
        // 特殊情况处理：确保当月正数上月负数时波动率为正，当月负数上月正数时波动率为负
        if ((pendingReimbursement >= 0 && previousPendingReimbursement < 0) || 
            (pendingReimbursement < 0 && previousPendingReimbursement >= 0)) {
          // 正负号变化的情况，根据变化方向决定波动率的正负
          trend = Math.abs(diff / Math.abs(previousPendingReimbursement)) * 100;
          // 设置符号
          if (pendingReimbursement >= 0 && previousPendingReimbursement < 0) {
            // 从负到正，视为正向趋势
            trend = Math.abs(trend);
          } else {
            // 从正到负，视为负向趋势
            trend = -Math.abs(trend);
          }
        } else {
          // 同号情况，使用标准计算
          trend = (diff / Math.abs(previousPendingReimbursement)) * 100;
        }
      }
      
      // 处理各类型支出明细
      const expenseTypes: ExpenseTypeData[] = [];
      let totalAmount = nominalExpense;
      
      if (totalAmount > 0) {
        (nominalExpenseResult as any[]).forEach(item => {
          const amount = parseFloat(item.total_amount || 0);
          const percentage = (amount / totalAmount) * 100;
          
          expenseTypes.push({
            expenseType: item.expense_type,
            amount: amount,
            percentage: parseFloat(percentage.toFixed(1))
          });
        });
      }
      
      // 按金额降序排序
      expenseTypes.sort((a, b) => b.amount - a.amount);
      
      // 返回结果
      return NextResponse.json({
        success: true,
        data: {
          nominalExpense: nominalExpense,
          actualExpense: actualExpense,
          pendingReimbursement: pendingReimbursement,
          previousPendingReimbursement: previousPendingReimbursement,
          trend: parseFloat(trend.toFixed(1)),
          expenseTypes: expenseTypes
        }
      });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取报销数据失败:', error);
    return NextResponse.json({
      success: false,
      message: "获取报销数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 