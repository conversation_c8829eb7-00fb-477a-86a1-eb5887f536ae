import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// 支出类型接口
interface ExpenseType {
  id: number;
  name: string;
  expense_nature: string;
  notes: string | null;
}

// 支出子类型接口
interface ExpenseSubtype {
  id: number;
  parent_id: number;
  name: string;
  notes: string | null;
}

// 账单接口
interface Bill {
  id: number;
  bill_type: string;
  income_expense_type_id: string | null;
  bill_amount: number | null;
  store_id: number | null;
  // 其他字段...
}

// 支出类型数据接口
interface ExpenseTypeData {
  id: number;
  expenseType: string;
  amount: number;
  subtypes: ExpenseSubtypeData[];
}

// 支出子类型数据接口
interface ExpenseSubtypeData {
  id: number;
  expenseSubtype: string;
  amount: number;
}

export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    // 从URL获取查询参数
    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month') || '';
    // 获取entity参数，将来可以用于按门店筛选数据
    const entity = searchParams.get('entity') || '总公司';

    // 验证月份格式 (YYYY-MM)
    if (!month || !/^\d{4}-\d{2}$/.test(month)) {
      return NextResponse.json(
        { success: false, message: '无效的月份格式，请使用YYYY-MM格式' },
        { status: 400 }
      );
    }

    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 1. 从expense_types表中获取所有支出类型（添加 ap_company_id 筛选）
      const [expenseTypes] = await connection.execute(`
        SELECT et.id, et.name, et.expense_nature, et.notes
        FROM expense_types et
        WHERE et.ap_company_id = ?
      `, [apCompanyId]);
      
      // 2. 从expense_subtypes表中获取所有支出子类型（添加 ap_company_id 筛选）
      const [expenseSubtypes] = await connection.execute(`
        SELECT es.id, es.parent_id, es.name, es.notes
        FROM expense_subtypes es
        WHERE es.ap_company_id = ?
      `, [apCompanyId]);
      
      // 3. 从bills表中获取本月的全部支出数据
      // 解析月份参数，获取年和月
      const [year, monthNum] = month.split('-');
      const startDate = `${year}-${monthNum}-01`;
      
      // 计算当月最后一天
      const nextMonth = parseInt(monthNum) === 12 ? 1 : parseInt(monthNum) + 1;
      const nextYear = parseInt(monthNum) === 12 ? parseInt(year) + 1 : parseInt(year);
      const endDate = `${nextYear}-${String(nextMonth).padStart(2, '0')}-01`;
      
      // 根据entity参数过滤数据
      let billsQuery = 'SELECT * FROM bills WHERE ap_company_id = ? AND transaction_time >= ? AND transaction_time < ? AND bill_type = "expense" AND status = 1';
      let queryParams: (string | number)[] = [apCompanyId, startDate, endDate];
      let storeIds: (string | number)[] = [];
      let isStoreEntity = false;

      if (entity !== '总公司') {
        // 尝试查询是否为品牌（添加 ap_company_id 筛选）
        const [brandStoresResult] = await connection.execute(
          `SELECT id FROM stores WHERE ap_company_id = ? AND brand = ?`,
          [apCompanyId, entity]
        );
        
        if ((brandStoresResult as any[]).length > 0) {
          // 是品牌，获取该品牌下所有门店的ID
          storeIds = (brandStoresResult as any[]).map(store => store.id);
          console.log(`品牌 ${entity} 下的门店IDs:`, storeIds);
        } else {
          // 不是品牌，尝试作为门店名查询（添加 ap_company_id 筛选）
          const [storeResult] = await connection.execute(
            `SELECT id FROM stores WHERE ap_company_id = ? AND store_name = ?`,
            [apCompanyId, entity]
          );
          
          if ((storeResult as any[]).length > 0) {
            // 是门店
            storeIds = (storeResult as any[]).map(store => store.id);
            isStoreEntity = true; // 标记为门店实体
            console.log(`门店 ${entity} 的ID:`, storeIds);
          }
        }
        
        if (storeIds.length === 0) {
          // 既不是有效的品牌也不是有效的门店
          console.log(`未找到实体 ${entity} 的相关门店`);
          return NextResponse.json({
            success: true,
            data: {
              totalExpense: 0,
              operatingExpense: {
                total: 0,
                types: [],
              },
              nonOperatingExpense: {
                total: 0,
                types: [],
              }
            }
          });
        }
        
        // 构建IN查询的参数占位符
        const placeholders = storeIds.map(() => '?').join(',');
        
        // 添加门店筛选条件
        billsQuery += ` AND store_id IN (${placeholders})`;
        queryParams = [...queryParams, ...storeIds];
      }
      
      // 执行查询
      const [currentMonthBills] = await connection.execute(billsQuery, queryParams);
      
      // 4. 创建支出类型和子类型的映射，用于通过ID查找
      const expenseTypeMap: Record<number, ExpenseType> = {};
      const expenseSubtypeMap: Record<number, ExpenseSubtype> = {};
      const expenseTypeTotals: Record<number, number> = {};
      const expenseSubtypeTotals: Record<number, Record<number, number>> = {};
      
      // 初始化支出类型映射和总额
      (expenseTypes as ExpenseType[]).forEach(type => {
        expenseTypeMap[type.id] = type;
        expenseTypeTotals[type.id] = 0;
        expenseSubtypeTotals[type.id] = {};
      });
      
      // 初始化支出子类型映射和总额
      (expenseSubtypes as ExpenseSubtype[]).forEach(subtype => {
        expenseSubtypeMap[subtype.id] = subtype;
        if (expenseSubtypeTotals[subtype.parent_id]) {
          expenseSubtypeTotals[subtype.parent_id][subtype.id] = 0;
        }
      });
      
      // 5. 处理每个账单记录，累加支出金额
      (currentMonthBills as Bill[]).forEach(bill => {
        if (bill.income_expense_type_id) {
          const typeIdParts = bill.income_expense_type_id.split('-');
          const primaryTypeId = parseInt(typeIdParts[0]); // 获取一级type
          const secondaryTypeId = typeIdParts.length > 1 ? parseInt(typeIdParts[1]) : null; // 获取二级type（如果有）
          
          // 累加一级类型金额
          if (expenseTypeMap[primaryTypeId]) {
            expenseTypeTotals[primaryTypeId] += parseFloat(String(bill.bill_amount || 0));
            
            // 如果有二级类型，也累加二级类型金额
            if (secondaryTypeId && expenseSubtypeMap[secondaryTypeId]) {
              if (!expenseSubtypeTotals[primaryTypeId][secondaryTypeId]) {
                expenseSubtypeTotals[primaryTypeId][secondaryTypeId] = 0;
              }
              expenseSubtypeTotals[primaryTypeId][secondaryTypeId] += parseFloat(String(bill.bill_amount || 0));
            }
          }
        }
      });
      
      // 6. 如果是门店实体，处理分摊支出数据（添加 ap_company_id 筛选）
      if (isStoreEntity) {
        // 查询分摊到该门店的支出数据
        const splitQuery = `
          SELECT SUM(bsr.split_amount) as total_split_amount
          FROM bill_split_records bsr
          JOIN bills b ON bsr.bill_id = b.id
          WHERE bsr.ap_company_id = ? AND b.transaction_time >= ? AND b.transaction_time < ?
          AND bsr.store_id = ?
          AND bsr.status = 1
        `;
        
        const [splitResult] = await connection.execute(splitQuery, [apCompanyId, startDate, endDate, storeIds[0]]);
        const totalSplitAmount = parseFloat(String((splitResult as any[])[0]?.total_split_amount || 0));
        
        if (totalSplitAmount > 0) {
          // 查找"管理费用"类型和"其他"子类型
          let managementExpenseTypeId: number | null = null;
          let otherSubtypeId: number | null = null;
          
          // 查找管理费用类型
          (expenseTypes as ExpenseType[]).forEach(type => {
            if (type.name === '管理费用') {
              managementExpenseTypeId = type.id;
            }
          });
          
          // 如果找到管理费用类型，查找其下的"其他"子类型
          if (managementExpenseTypeId) {
            (expenseSubtypes as ExpenseSubtype[]).forEach(subtype => {
              if (subtype.parent_id === managementExpenseTypeId && subtype.name === '其他') {
                otherSubtypeId = subtype.id;
              }
            });
          }
          
          // 如果没有找到管理费用或其他子类型，创建虚拟的类型来存放分摊数据
          if (!managementExpenseTypeId) {
            // 创建一个虚拟的管理费用类型ID（使用9999作为虚拟ID）
            managementExpenseTypeId = 9999;
            expenseTypeMap[managementExpenseTypeId] = {
              id: managementExpenseTypeId,
              name: '管理费用',
              expense_nature: '经营支出',
              notes: '虚拟类型（用于分摊数据）'
            };
            expenseTypeTotals[managementExpenseTypeId] = 0;
            expenseSubtypeTotals[managementExpenseTypeId] = {};
          }
          
          if (!otherSubtypeId) {
            // 创建一个虚拟的"其他"子类型ID
            otherSubtypeId = 9998;
            expenseSubtypeMap[otherSubtypeId] = {
              id: otherSubtypeId,
              parent_id: managementExpenseTypeId,
              name: '其他',
              notes: '虚拟子类型（用于分摊数据）'
            };
            expenseSubtypeTotals[managementExpenseTypeId][otherSubtypeId] = 0;
          }
          
          // 将分摊金额加入到管理费用-其他类目
          expenseTypeTotals[managementExpenseTypeId] += totalSplitAmount;
          if (!expenseSubtypeTotals[managementExpenseTypeId][otherSubtypeId]) {
            expenseSubtypeTotals[managementExpenseTypeId][otherSubtypeId] = 0;
          }
          expenseSubtypeTotals[managementExpenseTypeId][otherSubtypeId] += totalSplitAmount;
          
          console.log(`门店 ${entity} 分摊支出金额: ¥${totalSplitAmount}, 归类到管理费用-其他`);
        }
      }
      
      // 7. 按expense_nature分类
      const operatingExpenseTypes: ExpenseTypeData[] = [];
      const nonOperatingExpenseTypes: ExpenseTypeData[] = [];
      let totalOperatingExpense = 0;
      let totalNonOperatingExpense = 0;
      
      // 处理所有支出类型及其子类型
      Object.values(expenseTypeMap).forEach(type => {
        const typeTotal = expenseTypeTotals[type.id] || 0;
        
        // 处理子类型数据
        const subtypesData: ExpenseSubtypeData[] = [];
        Object.values(expenseSubtypeMap).forEach(subtype => {
          if (subtype.parent_id === type.id) {
            const subtypeTotal = expenseSubtypeTotals[type.id][subtype.id] || 0;
            subtypesData.push({
              id: subtype.id,
              expenseSubtype: subtype.name,
              amount: subtypeTotal
            });
          }
        });
        
        const typeData: ExpenseTypeData = {
          id: type.id,
          expenseType: type.name,
          amount: typeTotal,
          subtypes: subtypesData
        };
        
        if (type.expense_nature === '经营支出') {
          operatingExpenseTypes.push(typeData);
          totalOperatingExpense += typeTotal;
        } else {
          nonOperatingExpenseTypes.push(typeData);
          totalNonOperatingExpense += typeTotal;
        }
      });
      
      // 计算总支出
      const totalExpense = totalOperatingExpense + totalNonOperatingExpense;
      
      // 整理响应数据
      const response = {
        totalExpense,
        operatingExpense: {
          total: totalOperatingExpense,
          types: operatingExpenseTypes,
        },
        nonOperatingExpense: {
          total: totalNonOperatingExpense,
          types: nonOperatingExpenseTypes,
        }
      };
      
      return NextResponse.json({ 
        success: true, 
        data: response
      });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取支出报表数据失败:', error);
    return NextResponse.json(
      { success: false, message: '获取支出报表数据失败' },
      { status: 500 }
    );
  }
} 