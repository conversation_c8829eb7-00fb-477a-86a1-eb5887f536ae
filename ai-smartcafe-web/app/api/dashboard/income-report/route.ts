import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// 类型定义
interface IncomeType {
  id: number;
  income_type: string;
  income_nature: string;
  notes: string | null;
}

interface Bill {
  id: number;
  bill_type: string;
  income_expense_type_id: string | null;
  bill_amount: number | null;
  store_id: number | null;
}

interface IncomeTypeData {
  id: number;
  incomeType: string;
  amount: number;
}

export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    // 从URL获取查询参数
    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month') || '';
    // 获取entity参数，将来可以用于按门店筛选数据
    const entity = searchParams.get('entity') || '总公司';

    // 验证月份格式 (YYYY-MM)
    if (!month || !/^\d{4}-\d{2}$/.test(month)) {
      return NextResponse.json(
        { success: false, message: '无效的月份格式，请使用YYYY-MM格式' },
        { status: 400 }
      );
    }

    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 1. 从income_types表中获取所有收入类型（添加 ap_company_id 筛选）
      const [incomeTypes] = await connection.execute(
        'SELECT * FROM income_types WHERE ap_company_id = ?', 
        [apCompanyId]
      );
      
      // 2. 从bills表中获取本月的全部数据
      // 解析月份参数，获取年和月
      const [year, monthNum] = month.split('-');
      const startDate = `${year}-${monthNum}-01`;
      
      // 计算当月最后一天
      const nextMonth = parseInt(monthNum) === 12 ? 1 : parseInt(monthNum) + 1;
      const nextYear = parseInt(monthNum) === 12 ? parseInt(year) + 1 : parseInt(year);
      const endDate = `${nextYear}-${String(nextMonth).padStart(2, '0')}-01`;

      // 根据entity参数过滤数据
      let billsQuery = 'SELECT * FROM bills WHERE ap_company_id = ? AND transaction_time >= ? AND transaction_time < ? AND bill_type = "income" AND status = 1';
      let queryParams: (string | number)[] = [apCompanyId, startDate, endDate];

      if (entity !== '总公司') {
        // 判断是品牌还是门店
        let storeIds: (string | number)[] = [];
        
        // 尝试查询是否为品牌（添加 ap_company_id 筛选）
        const [brandStoresResult] = await connection.execute(
          `SELECT id FROM stores WHERE ap_company_id = ? AND brand = ?`,
          [apCompanyId, entity]
        );
        
        if ((brandStoresResult as any[]).length > 0) {
          // 是品牌，获取该品牌下所有门店的ID
          storeIds = (brandStoresResult as any[]).map(store => store.id);
          console.log(`品牌 ${entity} 下的门店IDs:`, storeIds);
        } else {
          // 不是品牌，尝试作为门店名查询（添加 ap_company_id 筛选）
          const [storeResult] = await connection.execute(
            `SELECT id FROM stores WHERE ap_company_id = ? AND store_name = ?`,
            [apCompanyId, entity]
          );
          
          if ((storeResult as any[]).length > 0) {
            // 是门店
            storeIds = (storeResult as any[]).map(store => store.id);
            console.log(`门店 ${entity} 的ID:`, storeIds);
          }
        }
        
        if (storeIds.length === 0) {
          // 既不是有效的品牌也不是有效的门店
          console.log(`未找到实体 ${entity} 的相关门店`);
          return NextResponse.json({
            success: true,
            data: {
              totalIncome: 0,
              operatingIncome: {
                total: 0,
                types: [],
              },
              nonOperatingIncome: {
                total: 0,
                types: [],
              }
            }
          });
        }
        
        // 构建IN查询的参数占位符
        const placeholders = storeIds.map(() => '?').join(',');
        
        // 添加门店筛选条件
        billsQuery += ` AND store_id IN (${placeholders})`;
        queryParams = [...queryParams, ...storeIds];
      }
      
      // 执行查询
      const [currentMonthBills] = await connection.execute(billsQuery, queryParams);
      
      // 3 & 4. 匹配income_types和计算每种类型的总收入
      const incomeTypeTotals: Record<number, number> = {};
      const incomeTypeMap: Record<number, IncomeType> = {};
      
      // 创建income_types的映射，便于通过ID查找
      (incomeTypes as IncomeType[]).forEach(type => {
        incomeTypeMap[type.id] = type;
        incomeTypeTotals[type.id] = 0; // 初始化总额为0
      });
      
      // 处理每个账单记录
      (currentMonthBills as Bill[]).forEach(bill => {
        // 处理income_expense_type_id，获取一级type
        if (bill.income_expense_type_id) {
          const typeIdParts = bill.income_expense_type_id.split('-');
          const primaryTypeId = parseInt(typeIdParts[0]); // 获取一级type
          
          // 如果能匹配到收入类型，累加金额
          if (incomeTypeMap[primaryTypeId]) {
            incomeTypeTotals[primaryTypeId] += parseFloat(String(bill.bill_amount || 0));
          }
        }
      });
      
      // 5. 按income_nature分类
      const operatingIncomeTypes: IncomeTypeData[] = [];
      const nonOperatingIncomeTypes: IncomeTypeData[] = [];
      let totalOperatingIncome = 0;
      let totalNonOperatingIncome = 0;
      
      // 将所有收入类型都包含在结果中，即使金额为0
      (incomeTypes as IncomeType[]).forEach(type => {
        const typeTotal = incomeTypeTotals[type.id] || 0;
        
        const typeData: IncomeTypeData = {
          id: type.id,
          incomeType: type.income_type,
          amount: typeTotal
        };
        
        if (type.income_nature === '经营收入') {
          operatingIncomeTypes.push(typeData);
          totalOperatingIncome += typeTotal;
        } else {
          nonOperatingIncomeTypes.push(typeData);
          totalNonOperatingIncome += typeTotal;
        }
      });
      
      // 计算总收入
      const totalIncome = totalOperatingIncome + totalNonOperatingIncome;
      
      // 整理响应数据
      const response = {
        totalIncome,
        operatingIncome: {
          total: totalOperatingIncome,
          types: operatingIncomeTypes,
        },
        nonOperatingIncome: {
          total: totalNonOperatingIncome,
          types: nonOperatingIncomeTypes,
        }
      };
      
      return NextResponse.json({ 
        success: true, 
        data: response
      });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取收入报表数据失败:', error);
    return NextResponse.json(
      { success: false, message: '获取收入报表数据失败' },
      { status: 500 }
    );
  }
} 