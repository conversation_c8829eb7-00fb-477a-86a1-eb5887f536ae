import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function POST(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { fileName, menuId } = await request.json();

    // 检查必要参数
    if (!fileName || !menuId) {
      return NextResponse.json(
        { success: false, message: "文件名和菜单ID都是必需的" },
        { status: 400 }
      );
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 通过文件名查找文件记录并更新menuId
      const [files] = await connection.execute(
        `SELECT * FROM bank_statement_files 
         WHERE file_name = ? AND del_flag = 0 AND ap_company_id = ?
         ORDER BY created_at DESC LIMIT 1`,
        [fileName, apCompanyId]
      ) as any[];

      if (!files || files.length === 0) {
        console.log(`[API] 未找到与文件名=${fileName}匹配的对账单文件`);
        return NextResponse.json(
          { success: false, message: "未找到需要更新的对账单文件" },
          { status: 404 }
        );
      }

      // 只更新找到的第一个（最新的）文件的menuId
      const file = files[0];
      await connection.execute(
        `UPDATE bank_statement_files SET menu_id = ? WHERE id = ?`,
        [menuId, file.id]
      );
      console.log(`[API] 更新对账单文件menuId成功: fileId=${file.id}, menuId=${menuId}`);

      return NextResponse.json(
        { success: true, message: "对账单文件menuId更新成功", fileId: file.id },
        { status: 200 }
      );
    } finally {
      // 关闭数据库连接
      await connection.end();
    }
  } catch (error: any) {
    console.error("[API] 更新对账单文件menuId失败:", error);
    return NextResponse.json(
      { success: false, message: error.message || "更新对账单文件menuId失败" },
      { status: 500 }
    );
  }
} 