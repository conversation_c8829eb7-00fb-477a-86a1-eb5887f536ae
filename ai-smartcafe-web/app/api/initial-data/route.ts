import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置 (与 expense-types/route.ts 保持一致)
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// GET /api/initial-data/monthly?storeId=xxx
export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const storeId = searchParams.get('storeId');

    if (!storeId) {
      return NextResponse.json({ success: false, message: '缺少 storeId 参数' }, { status: 400 });
    }

    const connection = await mysql.createConnection(dbConfig);
    try {
      const [rows] = await connection.execute(
        'SELECT id, `year_month`, operating_income, non_operating_income, operating_expense, non_operating_expense, initial_inventory_value, final_inventory_value, pending_reimbursement, unadjusted_net_profit, adjusted_net_profit FROM store_monthly_financials WHERE store_id = ? AND ap_company_id = ? ORDER BY `year_month` DESC',
        [storeId, apCompanyId]
      );
      return NextResponse.json({ success: true, data: rows });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取月度财务数据时出错:', error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : '获取月度财务数据失败' },
      { status: 500 }
    );
  }
}

// POST /api/initial-data/investment (更新 stores 表)
// POST /api/initial-data/monthly (批量新增/更新 store_monthly_financials 表)
// DELETE /api/initial-data/monthly?id=xxx (删除 store_monthly_financials 表的条目)
export async function POST(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action'); // 使用 action 参数区分是保存初始投入还是月度数据

    const connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    try {
      if (action === 'saveInvestment') {
        const { storeId, amount, remarks } = await request.json();
        if (!storeId) {
          return NextResponse.json({ success: false, message: '缺少 storeId' }, { status: 400 });
        }
        await connection.execute(
          'UPDATE stores SET initial_amount = ?, initial_remarks = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
          [amount || null, remarks || null, apAccountId, storeId, apCompanyId]
        );
        await connection.commit();
        return NextResponse.json({ success: true, message: '初始投入保存成功' });
      } else if (action === 'saveMonthlyData') {
        const { storeId, monthlyDataList } = await request.json();
        if (!storeId || !monthlyDataList) {
          return NextResponse.json({ success: false, message: '缺少 storeId 或 monthlyDataList' }, { status: 400 });
        }

        // 先按年月份排序，以便处理期初期末库存传递
        const sortedData = [...monthlyDataList].sort((a, b) => a.yearMonth.localeCompare(b.yearMonth));

        for (const data of sortedData) {
          const { 
            id, 
            yearMonth, 
            operatingIncome, 
            nonOperatingIncome, 
            operatingExpense, 
            nonOperatingExpense, 
            initialInventoryValue, 
            finalInventoryValue, 
            pendingReimbursement 
          } = data;
          
          // 计算调整前净利润和利润率
          const unadjustedNetProfit = (parseFloat(operatingIncome) || 0) - (parseFloat(operatingExpense) || 0);
          const unadjustedProfitRate = parseFloat(operatingIncome) ? (unadjustedNetProfit / parseFloat(operatingIncome) * 100) : 0;
          
          // 计算库存成本调整
          const inventoryAdjustment = (parseFloat(initialInventoryValue) || 0) - (parseFloat(finalInventoryValue) || 0);
          
          // 计算调整后净利润和利润率
          const adjustedNetProfit = (parseFloat(operatingIncome) || 0) - ((parseFloat(operatingExpense) || 0) + inventoryAdjustment);
          const adjustedProfitRate = parseFloat(operatingIncome) ? (adjustedNetProfit / parseFloat(operatingIncome) * 100) : 0;

          if (id && typeof id === 'number') { 
            await connection.execute(
              'UPDATE store_monthly_financials SET `year_month` = ?, operating_income = ?, non_operating_income = ?, operating_expense = ?, non_operating_expense = ?, initial_inventory_value = ?, final_inventory_value = ?, pending_reimbursement = ?, unadjusted_net_profit = ?, adjusted_net_profit = ?, unadjusted_profit_rate = ?, adjusted_profit_rate = ?, ap_account_id = ? WHERE id = ? AND store_id = ? AND ap_company_id = ?',
              [
                yearMonth, 
                operatingIncome || 0, 
                nonOperatingIncome || 0, 
                operatingExpense || 0, 
                nonOperatingExpense || 0, 
                initialInventoryValue || 0, 
                finalInventoryValue || 0, 
                pendingReimbursement || 0, 
                unadjustedNetProfit,
                adjustedNetProfit,
                unadjustedProfitRate,
                adjustedProfitRate,
                apAccountId, 
                id, 
                storeId, 
                apCompanyId
              ]
            );
          } else { 
            const [existingRows]: [any[], any] = await connection.execute(
              'SELECT id FROM store_monthly_financials WHERE store_id = ? AND `year_month` = ? AND ap_company_id = ?',
              [storeId, yearMonth, apCompanyId]
            );

            if (existingRows.length > 0) {
              const existingId = existingRows[0].id;
              await connection.execute(
                'UPDATE store_monthly_financials SET operating_income = ?, non_operating_income = ?, operating_expense = ?, non_operating_expense = ?, initial_inventory_value = ?, final_inventory_value = ?, pending_reimbursement = ?, unadjusted_net_profit = ?, adjusted_net_profit = ?, unadjusted_profit_rate = ?, adjusted_profit_rate = ?, ap_account_id = ? WHERE id = ? AND store_id = ? AND ap_company_id = ?',
                [
                  operatingIncome || 0, 
                  nonOperatingIncome || 0, 
                  operatingExpense || 0, 
                  nonOperatingExpense || 0, 
                  initialInventoryValue || 0, 
                  finalInventoryValue || 0, 
                  pendingReimbursement || 0, 
                  unadjustedNetProfit,
                  adjustedNetProfit,
                  unadjustedProfitRate,
                  adjustedProfitRate,
                  apAccountId, 
                  existingId, 
                  storeId, 
                  apCompanyId
                ]
              );
            } else {
              await connection.execute(
                'INSERT INTO store_monthly_financials (ap_company_id, ap_account_id, store_id, `year_month`, operating_income, non_operating_income, operating_expense, non_operating_expense, initial_inventory_value, final_inventory_value, pending_reimbursement, unadjusted_net_profit, adjusted_net_profit, unadjusted_profit_rate, adjusted_profit_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                [
                  apCompanyId, 
                  apAccountId, 
                  storeId, 
                  yearMonth, 
                  operatingIncome || 0, 
                  nonOperatingIncome || 0, 
                  operatingExpense || 0, 
                  nonOperatingExpense || 0, 
                  initialInventoryValue || 0, 
                  finalInventoryValue || 0, 
                  pendingReimbursement || 0, 
                  unadjustedNetProfit,
                  adjustedNetProfit,
                  unadjustedProfitRate,
                  adjustedProfitRate
                ]
              );
            }
          }
        }
        await connection.commit();
        return NextResponse.json({ success: true, message: '月度数据保存成功' });
      } else {
        await connection.rollback();
        return NextResponse.json({ success: false, message: '无效的 action 参数' }, { status: 400 });
      }
    } catch (error) {
      await connection.rollback();
      throw error; // 抛出错误，由外层catch处理
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('处理 POST 请求时出错:', error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : '数据保存失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ success: false, message: '缺少 id 参数' }, { status: 400 });
    }

    const connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();
    try {
      const [result] = await connection.execute(
        'DELETE FROM store_monthly_financials WHERE id = ? AND ap_company_id = ?',
        [id, apCompanyId]
      );
      
      //  MySQL execute for DELETE returns an object with affectedRows.
      //  (result as any).affectedRows is not standard for all drivers/versions for SELECT but okay for DML.
      const affectedRows = (result as any).affectedRows;

      if (affectedRows > 0) {
        await connection.commit();
        return NextResponse.json({ success: true, message: '月度数据删除成功' });
      } else {
        await connection.rollback();
        return NextResponse.json({ success: false, message: '未找到要删除的数据或删除失败' }, { status: 404 });
      }
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除月度数据时出错:', error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : '删除月度数据失败' },
      { status: 500 }
    );
  }
} 