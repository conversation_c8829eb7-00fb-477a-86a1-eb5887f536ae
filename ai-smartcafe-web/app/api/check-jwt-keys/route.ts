import { NextResponse } from 'next/server';
import { checkJWTKeys } from '@/lib/auth/keys';

/**
 * 检查JWT密钥是否正确加载
 * 仅在开发环境中使用
 */
export async function GET() {
  // 只在开发环境中允许
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: '此API仅在开发环境中可用' }, { status: 403 });
  }
  
  // 检查JWT密钥
  const result = checkJWTKeys();
  
  return NextResponse.json({
    timestamp: new Date().toISOString(),
    ...result,
  });
} 