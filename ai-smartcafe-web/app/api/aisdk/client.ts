// src/aiClient.ts
import { generateObject } from 'ai';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { z, ZodSchema } from 'zod';

// —— 1. 创建兼容 OpenAI 的火山方舟提供商实例 ——
// 使用 @ai-sdk/openai-compatible，可以接入任何 OpenAI 兼容的服务（如火山方舟）&#8203;:contentReference[oaicite:0]{index=0}
const volcProvider = createOpenAICompatible({
    name: 'volcengine',                                // 自定义提供商名称
    apiKey: process.env.HUOSAN_API_KEY!,               // 火山方舟 Access Key
    baseURL: process.env.OPENAI_BASE_URL!,               // 例如：https://openai.volcengineapi.com/v1
    // headers: {                                         // 如有必要，可添加额外 Header
    //     'X-Volc-Secret': process.env.VOLC_SECRETKEY!
    // },
});

// —— 2. 封装一个通用的结构化调用函数 ——
// schema: ZodSchema<T>，prompt: 提示词，modelId: 火山方舟上的模型标识
export async function callStructured<T>(
    modelId: string,
    prompt: string,
    schema: ZodSchema<T>,
    maxRetries = 2
): Promise<T> {
    const model = volcProvider(modelId);

    // 严格 JSON 模式，温度设为 0，确保输出可解析且稳定&#8203;:contentReference[oaicite:1]{index=1}
    const options = {
        model,
        prompt,
        schema,
        output: 'object' as const,
        mode: 'json' as const,
        temperature: 0,
    };

    let lastError: any;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const { object } = await generateObject<T>(options);
            return object;
        } catch (err) {
            lastError = err;
            console.warn(`generateObject 第 ${attempt} 次尝试失败：`, err);
            // 如果是最后一次，跳出循环
            if (attempt === maxRetries) break;
        }
    }

    // —— 3. 最后一次手动降级解析 ——  
    // 如果 SDK 严格模式依然失败，改为 no‑schema 输出，再手动 JSON.parse + zod 校验
    try {
        const raw = await generateObject({
            ...options,
            output: 'no-schema' as const,
        });
        const text = (raw as any).text as string;
        const data = JSON.parse(text);
        const result = schema.parse(data);
        return result;
    } catch (err) {
        console.error('手动解析也失败：', err);
        throw lastError;
    }
}
