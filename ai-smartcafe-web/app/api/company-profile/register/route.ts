import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import crypto from 'crypto'; // 引入 crypto 模块
import { RateLimiterMemory } from 'rate-limiter-flexible';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

// 创建限流器实例，每天最多300次请求
const rateLimiter = new RateLimiterMemory({
  points: 300, // 允许的请求数
  duration: 60 * 60 * 24, // 时间窗口，以秒为单位（24小时）
  keyPrefix: 'company-profile-register', // 为这个路由添加一个键前缀
});

// 限流中间件函数
async function rateLimiterMiddleware(request: NextRequest) {
  try {
    // 使用IP地址作为限流键
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const method = request.method;
    const key = `${ip}:${method}`; // 组合IP和方法作为键，提高限流精度
    
    console.log(`[限流] 请求来自 IP: ${ip}, 方法: ${method}`);
    
    // 消耗一个点数
    const rateLimiterRes = await rateLimiter.consume(key);
    
    // 记录剩余点数
    console.log(`[限流] 剩余请求次数: ${rateLimiterRes.remainingPoints}, 重置时间: ${new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString()}`);
    
    return null; // 没有超出限制，返回null
  } catch (error) {
    // 超出限制，返回错误响应
    console.error(`[限流] 请求被限制，IP: ${request.headers.get('x-forwarded-for') || 'unknown'}, 方法: ${request.method}`);
    return NextResponse.json(
      { message: '请求频率超出限制，请稍后再试', error: 'TOO_MANY_REQUESTS' }, 
      { status: 429 }
    );
  }
}

// 查询账套申请状态
export async function GET(request: NextRequest) {
  // 应用限流中间件
  const limiterResult = await rateLimiterMiddleware(request);
  if (limiterResult) return limiterResult;

  let connection: mysql.Connection | null = null;
  try {
    // 检查查询参数
    const searchParams = request.nextUrl.searchParams;
    const phoneNumber = searchParams.get('phoneNumber');
    const applicationId = searchParams.get('applicationId');

    if (!phoneNumber && !applicationId) {
      return NextResponse.json({ message: '手机号或申请ID不能为空' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);

    // 通过手机号或申请ID查询申请状态
    let query = 'SELECT status, version FROM companies WHERE deleted_at IS NULL';
    const params = [];

    query += ' AND primary_phone_number = ?';
    params.push(phoneNumber);

    query += ' AND code = ?';
    params.push(applicationId);

    const [rows] = await connection.execute(query, params) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

    if (rows.length === 0) {
      return NextResponse.json({ message: '未找到该申请记录' }, { status: 404 });
    }

    const applicationInfo = rows[0];
    return NextResponse.json({
      status: applicationInfo.status,
      version: applicationInfo.version,
      message: '查询成功'
    });
  } catch (error) {
    console.error(`获取申请状态失败:`, error);
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 提交账套申请
export async function POST(request: NextRequest) {
  // 应用限流中间件
  const limiterResult = await rateLimiterMiddleware(request);
  if (limiterResult) return limiterResult;

  let connection: mysql.Connection | null = null;
  try {
    const { companyName, adminName, adminPhone, adminEmail, businessType, maxStores, version } = await request.json();

    // 验证必填字段
    if (!companyName || !adminName || !adminPhone || !adminEmail || !businessType || !maxStores) {
      return NextResponse.json({ message: '所有字段均为必填项' }, { status: 400 });
    }

    // 手机号格式验证
    if (!/^1[3-9]\d{9}$/.test(adminPhone)) {
      return NextResponse.json({ message: '无效的手机号码格式' }, { status: 400 });
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(adminEmail)) {
      return NextResponse.json({ message: '无效的邮箱格式' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    // 检查是否为更新操作（通过版本号判断）
    if (version) {
      // 更新操作：先查询版本是否匹配
      // 检查是否提供了申请编号
      const applicationId = request.headers.get('X-Application-Id');

      // 如果有申请编号，则使用申请编号+版本号验证；没有则降级为手机号+版本号
      let query = 'SELECT id, status FROM companies WHERE version = ? AND deleted_at IS NULL';
      let params: any[] = [version];

      query += ' AND code = ?';
      params.push(applicationId);

      const [existingRows] = await connection.execute(query, params) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

      if (existingRows.length > 0) {
        // 检查申请状态，如果已通过则不允许修改
        if (existingRows[0].status === '1' || existingRows[0].status === '2') {
          await connection.rollback();
          return NextResponse.json({ message: '当前状态下，不能再修改' }, { status: 403 });
        }
        // 版本匹配，更新记录
        const companyId = existingRows[0].id;
        const newVersion = crypto.randomUUID();

        await connection.execute(
          `UPDATE companies SET 
            company_name = ?, 
            email = ?, 
            name = ?, 
            business_type = ?, 
            max_stores = ?,
            status = '0',
            version = ?,
            updated_at = NOW() 
          WHERE id = ? and status = '3'`,
          [companyName, adminEmail, adminName, businessType, maxStores, newVersion, companyId]
        );

        await connection.commit();

        return NextResponse.json({
          success: true,
          message: '账套申请已更新，我们将尽快处理，请留意手机或邮箱通知',
          applicationId: applicationId,
          applicationTime: new Date().toISOString(),
          version: newVersion
        });
      } else {
        return NextResponse.json({ message: '数据已被修改，请刷新后重试' }, { status: 409 });
      }
    } else {
      // 新增操作：检查手机号是否已存在
      const [existingPhoneRows] = await connection.execute(
        'SELECT id FROM companies WHERE primary_phone_number = ? AND deleted_at IS NULL',
        [adminPhone]
      ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

      if (existingPhoneRows.length > 0) {
        await connection.rollback();
        return NextResponse.json({ message: '该手机号码已被注册，请使用其他手机号' }, { status: 409 });
      }

      // 检查邮箱是否已存在
      const [existingEmailRows] = await connection.execute(
        'SELECT id FROM companies WHERE email = ? AND deleted_at IS NULL',
        [adminEmail]
      ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

      if (existingEmailRows.length > 0) {
        await connection.rollback();
        return NextResponse.json({ message: '该邮箱已被注册，请使用其他邮箱' }, { status: 409 });
      }

      // 将账套申请信息插入到数据库
      const newVersion = crypto.randomUUID();
      const [result] = await connection.execute<mysql.ResultSetHeader>(
        `INSERT INTO companies (
          company_name, primary_phone_number, email, name, business_type, max_stores, 
          status, is_active, version, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, '0', TRUE, ?, NOW(), NOW())`,
        [companyName, adminPhone, adminEmail, adminName, businessType, maxStores, newVersion]
      );

      // 使用ID生成应用码 (6位数字，不足补0)
      const newId = result.insertId;
      const applicationCode = `APP${newId.toString().padStart(6, '0')}`;

      // 更新应用码
      await connection.execute(
        'UPDATE companies SET code = ? WHERE id = ?',
        [applicationCode, newId]
      );

      await connection.commit();

      // 返回成功信息和申请ID
      return NextResponse.json({
        success: true,
        message: '账套申请已提交，我们将尽快处理，请留意手机或邮箱通知',
        applicationId: applicationCode,
        applicationTime: new Date().toISOString(),
        version: newVersion
      });
    }
  } catch (error: any) {
    if (connection) await connection.rollback();
    console.error('提交账套申请失败:', error);

    if (error.code === 'ER_DUP_ENTRY') {
      if (error.sqlMessage && error.sqlMessage.includes('primary_phone_number')) {
        return NextResponse.json({ message: '该手机号码已被注册，请使用其他手机号' }, { status: 409 });
      }
      if (error.sqlMessage && error.sqlMessage.includes('email')) {
        return NextResponse.json({ message: '该邮箱已被注册，请使用其他邮箱' }, { status: 409 });
      }
    }

    return NextResponse.json({ message: '服务器内部错误，提交失败，请稍后重试' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 撤销申请
export async function DELETE(request: NextRequest) {
  // 应用限流中间件
  const limiterResult = await rateLimiterMiddleware(request);
  if (limiterResult) return limiterResult;

  let connection: mysql.Connection | null = null;
  try {
    const { phoneNumber, applicationId, version } = await request.json();

    if (!phoneNumber && !applicationId) {
      return NextResponse.json({ message: '手机号或申请ID不能为空' }, { status: 400 });
    }

    if (!version) {
      return NextResponse.json({ message: '版本号不能为空' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    // 查询申请状态
    let query = 'SELECT id, status, version FROM companies WHERE deleted_at IS NULL';
    const params = [];

    query += ' AND primary_phone_number = ?';
    params.push(phoneNumber);

    query += ' AND code = ?';
    params.push(applicationId);

    const [rows] = await connection.execute(query, params) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

    if (rows.length === 0) {
      await connection.rollback();
      return NextResponse.json({ message: '未找到该申请记录' }, { status: 404 });
    }

    const applicationInfo = rows[0];

    // 检查版本是否匹配
    if (applicationInfo.version !== version) {
      await connection.rollback();
      return NextResponse.json({ message: '数据已被修改，请刷新后重试' }, { status: 409 });
    }

    // 检查申请状态是否为"待处理"或"已驳回"
    if (applicationInfo.status !== '0' && applicationInfo.status !== '2') {
      await connection.rollback();
      return NextResponse.json({ message: '当前状态下无法撤销申请' }, { status: 400 });
    }

    // 生成新版本号
    const newVersion = crypto.randomUUID();

    // 更新为"已撤销"状态，更新版本号和更新时间（不修改删除时间）
    await connection.execute(
      'UPDATE companies SET status = "3", version = ?, updated_at = NOW() WHERE id = ? and status = "0"',
      [newVersion, applicationInfo.id]
    );

    await connection.commit();

    return NextResponse.json({
      success: true,
      message: '申请已成功撤销',
      version: newVersion
    });
  } catch (error) {
    if (connection) await connection.rollback();
    console.error('撤销申请失败:', error);
    return NextResponse.json({ message: '服务器内部错误，撤销申请失败' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
