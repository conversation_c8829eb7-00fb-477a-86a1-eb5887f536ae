import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import crypto from 'crypto'; // 引入 crypto 模块
import { getRequiredAuthSession } from '@/lib/auth';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

// 固定公司ID为1
const THE_COMPANY_ID = 1;
const DEFAULT_PASSWORD = '123456';

// Function to MD5 hash a password
const md5 = (content: string) => {
  return crypto.createHash('md5').update(content).digest('hex');
};

export async function GET(request: NextRequest) {
  let connection: mysql.Connection | null = null;
  const session = getRequiredAuthSession(request);
  const { companyId } = session;

  try {
    connection = await mysql.createConnection(dbConfig);
    const [rows] = await connection.execute(
      'SELECT id, company_name, primary_phone_number, email, is_active, max_users_allowed, max_stores FROM companies WHERE id = ? AND status = 1 AND deleted_at IS NULL',
      [companyId]
    ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

    if (rows.length === 0) {
      // If company ID 1 doesn't exist, return a specific structure or allow frontend to handle it (e.g. by pre-filling for creation)
      // For now, let's return 404, frontend can interpret this as needing to create the profile.
      return NextResponse.json({ message: `公司信息 (ID: ${companyId}) 未找到，请先保存。` }, { status: 404 });
    }
    const companyInfo = rows[0];

    return NextResponse.json({
      id: companyInfo.id,
      companyName: companyInfo.company_name,
      phoneNumber: companyInfo.primary_phone_number,
      email: companyInfo.email,
      isActive: companyInfo.is_active,
      maxUsersAllowed: companyInfo.max_users_allowed,
      maxStores: companyInfo.max_stores
    });
  } catch (error) {
    console.error(`获取公司信息 (ID: ${companyId}) 失败:`, error);
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function PUT(request: NextRequest) {
  let connection: mysql.Connection | null = null;
  let isNewCompany = false; // Flag to track if a new company was created
  const session = getRequiredAuthSession(request);
  const { companyId } = session;
  try {
    const { companyName, phoneNumber, email, isActive, maxStores} = await request.json();
    const maxUsersAllowed = 5;

    if (!companyName || !phoneNumber) {
      return NextResponse.json({ message: '公司名称和手机号不能为空' }, { status: 400 });
    }
    if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
      return NextResponse.json({ message: '无效的手机号码格式' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    const [existingCompanyRows] = await connection.execute(
      'SELECT id, deleted_at FROM companies WHERE id = ?',
      [companyId]
    ) as [mysql.RowDataPacket[], mysql.FieldPacket[]];

    if (existingCompanyRows.length > 0 && existingCompanyRows[0].deleted_at !== null) {
      await connection.rollback();
      return NextResponse.json({ message: `公司 (ID: ${companyId}) 已被停用且无法直接更新，请联系管理员。` }, { status: 409 });
    }

    if (existingCompanyRows.length === 0) {
      // Company does not exist, it's an insert
      isNewCompany = true;
      try {
        await connection.execute<mysql.ResultSetHeader>(
          'INSERT INTO companies (id, company_name, primary_phone_number, email, is_active, max_users_allowed, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
          [companyId, companyName, phoneNumber, email, isActive === undefined ? true : !!isActive, maxUsersAllowed === undefined ? 5 : Number(maxUsersAllowed)]
        );
      } catch (insertError: any) {
        await connection.rollback();
        if (insertError.code === 'ER_DUP_ENTRY') {
          if (insertError.sqlMessage && insertError.sqlMessage.includes('PRIMARY')) { // Duplicate ID
            return NextResponse.json({ message: `创建公司信息 (ID: ${companyId}) 失败，ID已存在。` }, { status: 409 });
          } else if (insertError.sqlMessage && insertError.sqlMessage.includes('primary_phone_number')) { // Duplicate phone
            return NextResponse.json({ message: '该手机号码已被其他账套使用' }, { status: 409 });
          }
        }
        console.error('公司信息插入失败:', insertError);
        throw insertError;
      }
    } else {
      // Company exists and is not deleted, it's an update
      const [updateResult] = await connection.execute<mysql.ResultSetHeader>(
        'UPDATE companies SET company_name = ?, primary_phone_number = ?, email = ?, is_active = ?, max_users_allowed = ?, updated_at = NOW() WHERE id = ? AND deleted_at IS NULL',
        [companyName, phoneNumber, email, isActive === undefined ? true : !!isActive, maxUsersAllowed === undefined ? 10 : Number(maxUsersAllowed), companyId]
      );
      if (updateResult.affectedRows === 0) {
        // This case should ideally not be hit frequently if pre-check for deleted_at is done.
        // Could mean data changed between select and update, or no actual change in values.
        // For simplicity, we can treat "no change" as a successful no-op for an update.
      }
    }

    let superAdminCreated = false;
    if (isNewCompany) {
      const passwordHash = md5(DEFAULT_PASSWORD);
      const superAdminUsername = phoneNumber; // Use phone number as username
      const superAdminName = "超级管理员";
      const superAdminContact = phoneNumber; // This variable holds the phone number, which will be inserted into contact_info

      try {
        await connection.execute<mysql.ResultSetHeader>(
          'INSERT INTO account (company_id, username, full_name, contact_info, role, password_hash, is_super_admin, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, TRUE, TRUE, NOW(), NOW())',
          [companyId, superAdminUsername, superAdminName, superAdminContact, 'super_admin', passwordHash]
        );
        superAdminCreated = true;
      } catch (adminInsertError: any) {
        await connection.rollback();
        if (adminInsertError.code === 'ER_DUP_ENTRY') {
          if (adminInsertError.sqlMessage && adminInsertError.sqlMessage.includes('username')) {
            return NextResponse.json({ message: '创建公司信息成功，但创建超级管理员失败：该手机号已被用作其他系统账号的用户名。' }, { status: 409 });
          } else if (adminInsertError.sqlMessage && adminInsertError.sqlMessage.includes('PRIMARY')) {
            // This should ideally not happen if account.id is auto-increment
            return NextResponse.json({ message: '创建公司信息成功，但创建超级管理员失败：管理员账户ID冲突。' }, { status: 409 });
          }
        }
        console.error('超级管理员账户创建失败:', adminInsertError);
        // Keep the company creation, but notify about admin creation failure. Or rollback?
        // For now, rolling back company creation as well if admin fails to ensure consistency.
        return NextResponse.json({ message: '创建公司信息成功，但创建超级管理员账户时发生内部错误，操作已回滚。' }, { status: 500 });
      }
    }

    await connection.commit();

    let successMessage = '公司信息保存成功';
    if (isNewCompany && superAdminCreated) {
      successMessage = '公司信息已创建，并已自动生成超级管理员账号（用户名为手机号）。';
    } else if (isNewCompany) { // This case should not be hit if admin creation failure rolls back everything
      successMessage = '公司信息已创建，但超级管理员账号生成失败。请手动配置。'
    }

    return NextResponse.json({ message: successMessage });

  } catch (error: any) {
    if (connection) await connection.rollback();
    console.error(`更新/创建公司信息 (ID: ${companyId}) 失败:`, error);
    // Specific check for primary_phone_number unique constraint violation during UPDATE (if it wasn't an INSERT attempt)
    if (!isNewCompany && error.code === 'ER_DUP_ENTRY' && error.sqlMessage && error.sqlMessage.includes('primary_phone_number')) {
      return NextResponse.json({ message: '该手机号码已被其他账套使用' }, { status: 409 });
    }
    return NextResponse.json({ message: '服务器内部错误，操作已回滚。' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
} 