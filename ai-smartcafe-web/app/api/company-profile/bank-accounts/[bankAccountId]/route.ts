import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectTimeout: process.env.DB_CONNECT_TIMEOUT ? parseInt(process.env.DB_CONNECT_TIMEOUT) : 10000,
};

// 固定公司ID为1
const THE_COMPANY_ID = 1;

interface RouteContext {
  params: { bankAccountId: string };
}

export async function PUT(request: NextRequest, context: RouteContext) {
  let connection: mysql.Connection | null = null;
  const { bankAccountId } = context.params;
  try {
    if (!bankAccountId || isNaN(parseInt(bankAccountId))) {
        return NextResponse.json({ message: '无效或缺少银行账户ID' }, { status: 400 });
    }

    const { bankName, accountHolder, accountNumber } = await request.json();

    if (!bankName || !accountHolder || !accountNumber) {
      return NextResponse.json({ message: '开户行、开户名和银行账号均不能为空' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    const [result] = await connection.execute<mysql.ResultSetHeader>(
      'UPDATE company_bank_accounts SET bank_name = ?, account_holder_name = ?, account_number = ?, updated_at = NOW() WHERE id = ? AND company_id = ? AND deleted_at IS NULL',
      [bankName, accountHolder, accountNumber, parseInt(bankAccountId), THE_COMPANY_ID]
    );

    if (result.affectedRows === 0) {
        return NextResponse.json({ message: '银行账户未找到或无权修改' }, { status: 404 });
    }

    return NextResponse.json({ 
        id: bankAccountId,
        bankName,
        accountHolder,
        accountNumber,
        message: '银行账户更新成功' 
    });
  } catch (error) {
    console.error('更新银行账户失败:', error);
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function DELETE(request: NextRequest, context: RouteContext) {
  let connection: mysql.Connection | null = null;
  const { bankAccountId } = context.params;
  try {
    if (!bankAccountId || isNaN(parseInt(bankAccountId))) {
        return NextResponse.json({ message: '无效或缺少银行账户ID' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    const [result] = await connection.execute<mysql.ResultSetHeader>(
      'UPDATE company_bank_accounts SET deleted_at = NOW(), updated_at = NOW() WHERE id = ? AND company_id = ? AND deleted_at IS NULL',
      [parseInt(bankAccountId), THE_COMPANY_ID]
    );

    if (result.affectedRows === 0) {
        return NextResponse.json({ message: '银行账户未找到或已被删除' }, { status: 404 });
    }

    return NextResponse.json({ message: '银行账户删除成功' });
  } catch (error) {
    console.error('删除银行账户失败:', error);
    return NextResponse.json({ message: '服务器内部错误' }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
} 