import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function POST(
  request: NextRequest,
  { params }: { params: { menuId: string } }
) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { menuId } = params;
    console.log("[API] 收到更新账单状态请求，menuId:", menuId);

    const body = await request.json();
    const { status } = body;
    console.log("[API] 请求体内容:", body);

    // 检查必要参数
    if (!menuId) {
      console.log("[API] 错误：菜单ID为空");
      return NextResponse.json(
        { success: false, message: "菜单ID不能为空" },
        { status: 400 }
      );
    }

    // 检查状态值是否有效
    if (status !== 0 && status !== 1) {
      console.log("[API] 错误：无效的状态值:", status);
      return NextResponse.json(
        { success: false, message: "无效的状态值，状态必须为0或1" },
        { status: 400 }
      );
    }

    // 创建数据库连接
    console.log("[API] 尝试连接数据库...");
    const connection = await mysql.createConnection(dbConfig);
    console.log("[API] 数据库连接成功");
    
    try {
      await connection.beginTransaction();
      console.log("[API] 开始事务");
      
      // 先查询当前状态（添加 ap_company_id 筛选）
      const [rows] = await connection.execute(
        `SELECT COUNT(*) as count, status FROM bills WHERE menu_id = ? AND ap_company_id = ? GROUP BY status`,
        [menuId, apCompanyId]
      );
      console.log("[API] 当前账单状态统计:", rows);
      
      // 更新该菜单下所有账单的状态（添加 ap_company_id 筛选）
      const [result] = await connection.execute(
        `UPDATE bills SET status = ? WHERE menu_id = ? AND ap_company_id = ?`,
        [status, menuId, apCompanyId]
      );
      console.log("[API] 更新账单状态结果:", result);
      
      await connection.commit();
      console.log("[API] 事务提交成功");

      return NextResponse.json(
        { success: true, message: "账单确认成功" },
        { status: 200 }
      );
    } catch (error) {
      console.error("[API] 数据库操作失败:", error);
      await connection.rollback();
      console.log("[API] 事务已回滚");
      throw error;
    } finally {
      // 关闭数据库连接
      await connection.end();
      console.log("[API] 数据库连接已关闭");
    }
  } catch (error: any) {
    console.error("[API] 账单确认失败:", error);
    return NextResponse.json(
      { success: false, message: error.message || "账单确认失败" },
      { status: 500 }
    );
  }
} 