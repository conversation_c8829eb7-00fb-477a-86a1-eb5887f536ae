import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { Connection } from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

interface BillData {
  id: string;
  billType: 'income' | 'expense';
  storeId: string | null;
  incomeSourceId: string | null;
  incomeExpenseTypeId: string | null;
  billAmount: number | null;
  billBalance: number | null;
  billDate: string;
  opposingUnit: string;
  isSplit: number | null;
  splitRecords?: { storeId: string; splitAmount: number; menuId: string }[];
  transactionTime?: string;
}

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export const maxDuration = 60;

export async function GET(
  request: NextRequest,
  { params }: { params: { menuId: string } }
) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { menuId } = await Promise.resolve(params);

    if (!menuId) {
      return NextResponse.json(
        { error: '菜单ID不能为空' },
        { status: 400 }
      );
    }

    const connection = await mysql.createConnection(dbConfig);
    
    // 查询账单数据（添加 ap_company_id 筛选）
    const [bills] = await connection.execute(
      `SELECT id, bill_type as billType, bill_type_confidence as billTypeConfidence, 
      store_id as storeId, store_id_confidence as storeIdConfidence, 
      income_source_id as incomeSourceId, income_source_id_confidence as incomeSourceIdConfidence, 
      income_expense_type_id as incomeExpenseTypeId, income_expense_type_id_confidence as incomeExpenseTypeIdConfidence, 
      bill_amount as billAmount, bill_amount_confidence as billAmountConfidence, 
      bill_balance as billBalance, bill_balance_confidence as billBalanceConfidence, 
      bill_date as billDate, bill_date_confidence as billDateConfidence, 
      opposing_unit as opposingUnit, opposing_unit_confidence as opposingUnitConfidence, 
      transaction_time as transactionTime,
      origin_data as originData, status, is_split as isSplit FROM bills WHERE menu_id = ? AND ap_company_id = ?`,
      [menuId, apCompanyId]
    );

    // 查询均摊记录（添加 ap_company_id 筛选）
    const [splitRecords] = await connection.execute(
      `SELECT bsr.bill_id as billId, bsr.store_id as storeId, bsr.split_amount as splitAmount, bsr.menu_id as menuId
       FROM bill_split_records bsr 
       WHERE bsr.menu_id = ? AND bsr.ap_company_id = ?`,
      [menuId, apCompanyId]
    );

    await connection.end();

    // 将均摊记录按账单ID分组
    const splitRecordsMap = (splitRecords as any[]).reduce((acc, record) => {
      if (!acc[record.billId]) {
        acc[record.billId] = [];
      }
      acc[record.billId].push({
        storeId: record.storeId,
        splitAmount: record.splitAmount,
        menuId: record.menuId
      });
      return acc;
    }, {});

    // 将均摊记录添加到账单数据中
    const billsWithSplitRecords = (bills as any[]).map(bill => ({
      ...bill,
      splitRecords: splitRecordsMap[bill.id] || []
    }));

    return NextResponse.json({
      success: true,
      data: billsWithSplitRecords
    });
  } catch (error) {
    console.error('查询账单数据失败:', error);
    return NextResponse.json({
      success: false,
      message: "查询账单数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { menuId: string } }
) {
  let connection: Connection | undefined;
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { menuId } = await Promise.resolve(params);
    const bills = await request.json() as BillData[];

    if (!menuId) {
      return NextResponse.json(
        { error: '菜单ID不能为空' },
        { status: 400 }
      );
    }

    connection = await mysql.createConnection(dbConfig);
    
    try {
      await connection!.beginTransaction();

      if (!connection) {
        throw new Error('数据库连接未建立');
      }

      // 批量更新优化（添加 ap_company_id 筛选）
      const updateQuery = `
        UPDATE bills SET 
          bill_type = ?, 
          store_id = ?, 
          income_source_id = ?, 
          income_expense_type_id = ?, 
          bill_amount = ?, 
          bill_balance = ?, 
          bill_date = ?, 
          opposing_unit = ?,
          is_split = ?,
          transaction_time = ?,
          ap_account_id = ?
        WHERE id = ? AND menu_id = ? AND ap_company_id = ?
      `;

      // 分批处理数据，每批100条
      const batchSize = 100;
      for (let i = 0; i < bills.length; i += batchSize) {
        const batch = bills.slice(i, i + batchSize);
        const updatePromises = batch.map(bill => {
          // 格式化transaction_time为MySQL兼容的datetime格式
          let formattedTransactionTime = '1970-01-01 00:00:00';
          if (bill.transactionTime) {
            try {
              const date = new Date(bill.transactionTime);
              formattedTransactionTime = date.toISOString().slice(0, 19).replace('T', ' ');
            } catch (e) {
              console.warn('日期格式转换失败:', bill.transactionTime, e);
            }
          }
          
          if (!connection) {
            throw new Error('数据库连接未建立');
          }
          
          return connection.execute(updateQuery, [
            bill.billType,
            bill.storeId,
            bill.incomeSourceId,
            bill.incomeExpenseTypeId,
            bill.billAmount,
            bill.billBalance,
            bill.billDate,
            bill.opposingUnit,
            bill.isSplit,
            formattedTransactionTime,
            apAccountId,
            bill.id,
            menuId,
            apCompanyId
          ]);
        });
        await Promise.all(updatePromises);
      }

      // 处理均摊记录
      // 1. 删除该菜单下的所有均摊记录（添加 ap_company_id 筛选）
      await connection.execute(
        'DELETE FROM bill_split_records WHERE menu_id = ? AND ap_company_id = ?',
        [menuId, apCompanyId]
      );

      // 2. 插入新的均摊记录（添加 ap_company_id 和 ap_account_id 字段）
      const insertSplitRecordQuery = `
        INSERT INTO bill_split_records 
        (ap_company_id, ap_account_id, menu_id, bill_id, store_id, split_amount) 
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      // 收集所有需要插入的均摊记录
      const splitRecordsToInsert = bills
        .filter(bill => bill.isSplit === 1 && bill.billType === 'expense')
        .flatMap(bill => {
          if (!bill.splitRecords || !Array.isArray(bill.splitRecords)) {
            return [];
          }
          return bill.splitRecords.map(record => [
            apCompanyId,
            apAccountId,
            menuId,
            bill.id,
            record.storeId,
            record.splitAmount
          ]);
        });

      // 如果有均摊记录需要插入，则批量插入
      if (splitRecordsToInsert.length > 0) {
        if (!connection) {
          throw new Error('数据库连接未建立');
        }
        const insertPromises = splitRecordsToInsert.map(record =>
          (connection as Connection).execute(insertSplitRecordQuery, record)
        );
        await Promise.all(insertPromises);
      }
      
      // // 更新菜单状态为已处理
      // if (!connection) {
      //   throw new Error('数据库连接未建立');
      // }
      // await connection.execute(
      //   'UPDATE menus SET status = 1 WHERE id = ?',
      //   [menuId]
      // );
      
      await connection.commit();
      return NextResponse.json({
        success: true,
        message: "账单数据保存成功"
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  } catch (error) {
    console.error('保存账单数据失败:', error);
    return NextResponse.json({
      success: false,
      message: "保存账单数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 