import mysql from 'mysql2/promise';
import { ExcelDataDetail } from '@/lib/schema/inventory';
import { z } from 'zod';
import { sq } from 'date-fns/locale';

export type ExcelDataType = z.infer<typeof ExcelDataDetail>;

/**
 * 数据库操作类，负责处理所有与数据库相关的操作
 */
export class DatabaseService {
    private dbConfig: mysql.ConnectionOptions;
    private apCompanyId: number;

    constructor(apCompanyId: number) {
        this.apCompanyId = apCompanyId;
        this.dbConfig = {
            host: process.env.DB_HOST,
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
        };
    }

    /**
     * 创建数据库连接
     */
    private async createConnection() {
        return await mysql.createConnection(this.dbConfig);
    }

    /**
     * 执行SQL查询
     */
    private async executeQuery(sql: string, params: any[] = []) {
        const connection = await this.createConnection();
        try {
            const [result] = await connection.execute(sql, params);
            return result as any[];
        } finally {
            await connection.end();
        }
    }

    /**
     * 格式化金额，保留两位小数
     */
    private formatAmount(amount: number | undefined): number | undefined {
        if (amount === undefined) return undefined;
        return Math.round(amount * 100) / 100;
    }

    /**
     * 获取所有门店信息（添加多租户筛选）
     */
    async getStores() {
        return await this.executeQuery('SELECT id,company_name,store_name FROM stores WHERE ap_company_id = ?', [this.apCompanyId]);
    }

    /**
     * 获取收入来源（添加多租户筛选）
     */
    async getIncomeSources() {
        return await this.executeQuery('SELECT * FROM income_sources WHERE ap_company_id = ?', [this.apCompanyId]);
    }

    /**
     * 获取收入类型（添加多租户筛选）
     */
    async getIncomeTypes() {
        return await this.executeQuery('SELECT * FROM income_types WHERE ap_company_id = ?', [this.apCompanyId]);
    }

    /**
     * 获取支出类型及子类型（添加多租户筛选）
     */
    async getExpenseTypes() {
        return await this.executeQuery(`
      SELECT 
        et.id,
        et.name,
        et.notes,
        es.id as subtype_id,
        es.name as subtype_name,
        es.notes as subtype_notes
      FROM expense_types et
      LEFT JOIN expense_subtypes es ON et.id = es.parent_id AND es.ap_company_id = ?
      WHERE et.ap_company_id = ?
      ORDER BY et.id, es.id
    `, [this.apCompanyId, this.apCompanyId]);
    }

    /**
     * 获取支出主类型（添加多租户筛选）
     */
    async getMainExpenseTypes() {
        return await this.executeQuery(`SELECT et.id,et.name FROM expense_types et WHERE et.ap_company_id = ?`, [this.apCompanyId]);
    }

    /**
     * 获取支出子类型（添加多租户筛选）
     */
    async getExpenseSubTypes() {
        return await this.executeQuery(`SELECT et.id,et.name FROM expense_subtypes et WHERE et.ap_company_id = ?`, [this.apCompanyId]);
    }

    /**
     * 重组支出类型数据结构
     */
    processExpenseTypesData(expenseTypes: any[]) {
        return expenseTypes.reduce((acc: any[], curr) => {
            const existingType = acc.find(type => type.id === curr.id);

            if (!existingType) {
                acc.push({
                    id: curr.id,
                    name: curr.name,
                    notes: curr.notes,
                    subtypes: curr.subtype_id ? [{
                        id: curr.subtype_id,
                        name: curr.subtype_name,
                        notes: curr.subtype_notes
                    }] : []
                });
            } else if (curr.subtype_id) {
                existingType.subtypes.push({
                    id: curr.subtype_id,
                    name: curr.subtype_name,
                    notes: curr.subtype_notes
                });
            }
            return acc;
        }, []);
    }

    /**
     * 生成基础数据提示词
     */
    async buildPrompt() {
        // 获取各种数据
        const storesData = await this.getStores();
        const incomeSourcesData = await this.getIncomeSources();
        const incomeTypesData = await this.getIncomeTypes();
        const expenseTypesRaw = await this.getExpenseTypes();
        const expenseTypesData = this.processExpenseTypesData(expenseTypesRaw);

        // 构建提示词
        let groupPrompt = `基础数据信息:\n`;

        // 添加门店信息
        groupPrompt += `门店信息:\n`;
        if (storesData.length > 0) {
            storesData.forEach(store => {
                groupPrompt += `- 门店ID: ${store.id}, 公司名称: ${store.company_name}, 门店名称: ${store.store_name}\n`;
            });
        } else {
            groupPrompt += `- 暂无门店信息\n`;
        }

        // 添加收入类型信息
        groupPrompt += `\n收入类型信息:\n`;
        if (incomeTypesData.length > 0) {
            incomeTypesData.forEach(type => {
                groupPrompt += `- 收入类型ID: ${type.id}, 类型名称: ${type.income_type}\n`;
            });
        } else {
            groupPrompt += `- 暂无收入类型信息\n`;
        }

        // 添加收入来源信息
        groupPrompt += `\n收入来源信息:\n`;
        if (incomeSourcesData.length > 0) {
            incomeSourcesData.forEach(source => {
                const store = storesData.find(s => s.id === source.store_id);
                const storeName = store ? store.store_name : '未知门店';
                groupPrompt += `- 收入来源ID: ${source.id}, 收入类型: ${source.income_type}, 代号: ${source.code}, 所属门店: ${storeName}\n`;
            });
        } else {
            groupPrompt += `- 暂无收入来源信息\n`;
        }

        // 添加支出类型信息
        groupPrompt += `\n支出类型信息:\n`;
        if (expenseTypesData.length > 0) {
            expenseTypesData.forEach(type => {
                groupPrompt += `- 支出类型ID: ${type.id}, 类型名称: ${type.name}\n`;
                if (type.subtypes && type.subtypes.length > 0) {
                    groupPrompt += `  二级类型:\n`;
                    type.subtypes.forEach((subtype: { id: number; name: string; notes: string | null }) => {
                        groupPrompt += `  - 二级类型ID: ${subtype.id}, 类型名称: ${subtype.name}\n`;
                    });
                }
            });
        } else {
            groupPrompt += `- 暂无支出类型信息\n`;
        }
        return groupPrompt;
    }

    /**
     * 处理SQL查询结果并格式化为ExcelDataType数组（添加多租户筛选）
     */
    async processQueryResult(sql: string): Promise<ExcelDataType[]> {
        // 在SQL中添加多租户筛选条件
        const modifiedSql = this.addCompanyFilter(sql);
        const rows = await this.executeQuery(modifiedSql);
        const expenseTypesData = await this.getMainExpenseTypes();
        const expenseSubTypesData = await this.getExpenseSubTypes();

        let result: ExcelDataType[] = [];

        if (rows && Array.isArray(rows) && rows.length > 0) {
            // 先收集所有is_split=1的账单ID，用于后续批量查询
            const splitBillIds = rows
                .filter(row => row.is_split === 1)
                .map(row => row.id);
            
            // 如果有需要均摊的账单，查询均摊记录（添加多租户筛选）
            let splitRecords: any[] = [];
            if (splitBillIds.length > 0) {
                try {
                    // 查询bill_split_records表中status=1(已处理)的均摊记录
                    const splitRecordsSql = `
                        SELECT bsr.*, s.company_name, s.store_name 
                        FROM bill_split_records bsr
                        LEFT JOIN stores s ON bsr.store_id = s.id AND s.ap_company_id = ?
                        WHERE bsr.bill_id IN (${splitBillIds.join(',')})
                        AND bsr.status = 1
                        AND bsr.ap_company_id = ?
                    `;
                    splitRecords = await this.executeQuery(splitRecordsSql, [this.apCompanyId, this.apCompanyId]);
                    console.log(`查询到${splitRecords.length}条均摊记录`);
                } catch (error) {
                    console.error('查询均摊记录失败:', error);
                    splitRecords = [];
                }
            }
            
            // 将查询结果按bill_id分组，便于后续处理
            const splitRecordsByBillId = splitRecords.reduce((acc: Record<number, any[]>, record) => {
                if (!acc[record.bill_id]) {
                    acc[record.bill_id] = [];
                }
                acc[record.bill_id].push(record);
                return acc;
            }, {});

            // 记录已处理的均摊账单ID
            const processedSplitBillIds = new Set<number>();

            // 处理每一行数据
            for (const row of rows) {
                let typeName = row.type_name || '';
                if (row.bill_type_name === '支出' && row.income_expense_type_id) {
                    const idParts = row.income_expense_type_id.split('-');
                    const mainTypeId = idParts[0];
                    const subTypeId = idParts.length > 1 ? idParts[1] : '';
                    const mainType = expenseTypesData.find(type => type.id.toString() === mainTypeId);
                    if (mainType) {
                        typeName = mainType.name;
                        if (subTypeId) {
                            const subType = expenseSubTypesData.find(type => type.id.toString() === subTypeId);
                            if (subType) {
                                typeName += ` / ${subType.name}`;
                            }
                        }
                    }
                }

                // 如果是需要均摊的账单且有均摊记录，添加均摊行并跳过原始账单
                if (row.is_split === 1 && splitRecordsByBillId[row.id]?.length > 0) {
                    // 记录该账单已处理
                    processedSplitBillIds.add(row.id);
                    
                    // 添加均摊记录
                    for (const splitRecord of splitRecordsByBillId[row.id]) {
                        result.push({
                            id: `${row.id}_${splitRecord.id}`, // 生成唯一ID，使用字符串形式
                            bill_type: row.bill_type_name,
                            company_name: splitRecord.company_name || '',
                            store_name: splitRecord.store_name || '',
                            income_type: row.income_type || '',
                            code: row.code || '',
                            type_name: typeName || '',
                            bill_amount: this.formatAmount(splitRecord.split_amount) || undefined,
                            transaction_time: row.transaction_time || '',
                            opposing_unit: row.opposing_unit || '',
                            is_split_name: '均摊明细',
                            is_split_record: true,
                            original_bill_id: row.id
                        });
                    }
                } else if (row.is_split !== 1) {
                    // 不是均摊账单，直接添加原始账单行
                    result.push({
                        id: row.id,
                        bill_type: row.bill_type_name,
                        company_name: row.company_name || '',
                        store_name: row.store_name || '',
                        income_type: row.income_type || '',
                        code: row.code || '',
                        type_name: typeName || '',
                        bill_amount: this.formatAmount(row.bill_amount) || undefined,
                        transaction_time: row.transaction_time || '',
                        opposing_unit: row.opposing_unit || '',
                        is_split_name: row.is_split_name,
                        is_split_record: false
                    });
                } else {
                    // 是均摊账单但没有均摊记录，仍然添加原始账单行
                    result.push({
                        id: row.id,
                        bill_type: row.bill_type_name,
                        company_name: row.company_name || '',
                        store_name: row.store_name || '',
                        income_type: row.income_type || '',
                        code: row.code || '',
                        type_name: typeName || '',
                        bill_amount: this.formatAmount(row.bill_amount) || undefined,
                        transaction_time: row.transaction_time || '',
                        opposing_unit: row.opposing_unit || '',
                        is_split_name: '待均摊', // 标记为待均摊状态
                        is_split_record: false
                    });
                }
            }
        }
        return result;
    }

    /**
     * 在SQL中添加公司筛选条件
     */
    private addCompanyFilter(sql: string): string {
        // 简单的SQL修改逻辑，在WHERE子句中添加ap_company_id筛选
        // 这里需要根据具体的SQL结构进行更精确的处理
        if (sql.toLowerCase().includes('where')) {
            // 如果已有WHERE子句，添加AND条件
            return sql.replace(/where/i, `WHERE ap_company_id = ${this.apCompanyId} AND`);
        } else {
            // 如果没有WHERE子句，添加WHERE条件
            // 需要在FROM子句后添加WHERE
            const fromMatch = sql.match(/from\s+\w+/i);
            if (fromMatch) {
                const insertPos = fromMatch.index! + fromMatch[0].length;
                return sql.slice(0, insertPos) + ` WHERE ap_company_id = ${this.apCompanyId}` + sql.slice(insertPos);
            }
        }
        return sql;
    }

    /**
     * 获取标准查询SQL
     * @param whereSql 条件SQL语句，如不提供则默认查询状态为1的记录
     * @returns 完整的SQL查询语句
     * 
     * 注意：
     * 1. 返回结果中is_split字段表示该账单是否需要均摊
     * 2. 当is_split=1时，需要查询bill_split_records表获取均摊明细
     * 3. 均摊明细会作为额外记录添加到结果中，通过is_split_record字段标识
     */
    getStandSql(whereSql: string) {
        if (!whereSql) {
            whereSql = " where b.status = 1 ";
        }
        return `
        SELECT b.id,
            case
                when b.bill_type = 'income' then '收入'
                when b.bill_type = 'expense' then '支出'
                else '未知类型'
                end        as bill_type_name,
            s.company_name,
            s.store_name,
            o.income_type,
            o.code,
            it.income_type as type_name,
            case
                when b.bill_type = 'income' then ''
                else b.income_expense_type_id
                end        as income_expense_type_id,
            b.bill_amount,
            b.bill_balance,
            DATE_FORMAT(b.transaction_time, '%Y-%m-%d') as transaction_time,
            b.opposing_unit,
            case
                when b.is_split = 1 then '是'
                else '否'
                end        as is_split_name,
            b.is_split
        FROM bills b
                LEFT JOIN stores s ON b.store_id = s.id
                LEFT JOIN income_sources o ON b.income_source_id = o.id
                LEFT JOIN income_types it ON b.bill_type = 'income' AND b.income_expense_type_id = it.id
        ` + whereSql;
    }
}

/**
 * 数据转换类，负责处理数据格式转换
 */
export class DataTransformer {
    /**
     * 转换数据为Excel格式
     */
    static transformToExcelFormat(data: ExcelDataType[]) {
        return data.map(item => {
            // 对均摊记录做特殊处理
            const isDistributedRecord = item.is_split_record === true;
            
            return {
                "ID": isDistributedRecord 
                    ? `均摊-${item.original_bill_id || ''}-${(item.id || '').toString().split('_')[1] || ''}`
                    : item.id,
                "账单类型": item.bill_type,
                "公司": item.company_name,
                "门店": item.store_name,
                "收入类型": item.income_type,
                "编码": item.code,
                "支出来源/类型": item.type_name,
                "账单金额": item.bill_amount,
                "交易日期": item.transaction_time,
                "对方单位": item.opposing_unit,
                "均摊状态": item.is_split_name
            };
        });
    }
}

// 导出面向函数式的API以保持兼容性
export async function buildPrompt(apCompanyId: number) {
    const dbService = new DatabaseService(apCompanyId);
    return await dbService.buildPrompt();
}

export async function dealSql(sql: string, apCompanyId: number): Promise<ExcelDataType[]> {
    const dbService = new DatabaseService(apCompanyId);
    return await dbService.processQueryResult(sql);
}

export async function dealWhereSql(sql: string, apCompanyId: number): Promise<ExcelDataType[]> {
    const dbService = new DatabaseService(apCompanyId);
    return await dbService.processQueryResult(dbService.getStandSql(sql));
}

export function getExcelData(data: ExcelDataType[]) {
    return DataTransformer.transformToExcelFormat(data);
}


