import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { ErrorResponse } from '@/lib/auth';

/**
 * 注销/登出接口
 */
export async function POST(request: NextRequest) {
  try {
    // 创建响应对象
    const response = NextResponse.json({ message: '注销成功' });
    
    // 清除认证相关的所有cookie
    response.cookies.delete('access_token');
    response.cookies.delete('refresh_token');
    
    return response;
    
  } catch (error) {
    console.error('注销失败:', error);
    
    return NextResponse.json<ErrorResponse>(
      { message: '服务器内部错误，请稍后再试' },
      { status: 500 }
    );
  }
} 