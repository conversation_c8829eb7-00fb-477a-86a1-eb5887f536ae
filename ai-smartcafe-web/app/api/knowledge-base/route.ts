import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// 批量新增知识库内容
export async function POST(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const knowledgeItems = await request.json();
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      await connection.beginTransaction();
      
      for (const item of knowledgeItems) {
        if (item.id) {
          // 更新已存在的记录
          await connection.execute(
            'UPDATE knowledge_base SET content = ?, type = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
            [item.content, item.type, apAccountId, item.id, apCompanyId]
          );
        } else {
          // 插入新记录
          await connection.execute(
            'INSERT INTO knowledge_base (ap_company_id, ap_account_id, content, type, del_flag) VALUES (?, ?, ?, ?, 0)',
            [apCompanyId, apAccountId, item.content, item.type]
          );
        }
      }
      
      await connection.commit();
      return NextResponse.json({ success: true, message: '知识库数据保存成功' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('保存知识库数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '保存知识库数据失败' },
      { status: 500 }
    );
  }
}

// 查询知识库内容
export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get('keyword');
    
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      let sql = 'SELECT * FROM knowledge_base WHERE del_flag = 0 AND ap_company_id = ?';
      let params: any[] = [apCompanyId];
      
      // 使用LIKE进行模糊匹配（可以匹配中间的字段）
      if (keyword) {
        sql = 'SELECT * FROM knowledge_base WHERE content LIKE ? AND del_flag = 0 AND ap_company_id = ?';
        params = [`%${keyword}%`, apCompanyId];
      }
      
      const [rows] = await connection.execute(sql, params);
      return NextResponse.json({ success: true, data: rows });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取知识库数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '获取知识库数据失败' },
      { status: 500 }
    );
  }
}

// 根据id更新知识库内容
export async function PUT(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const item = await request.json();
    
    if (!item.id) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数: id' },
        { status: 400 }
      );
    }
    
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      await connection.execute(
        'UPDATE knowledge_base SET content = ?, type = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
        [item.content, item.type, apAccountId, item.id, apCompanyId]
      );
      
      return NextResponse.json({ success: true, message: '知识库数据更新成功' });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('更新知识库数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '更新知识库数据失败' },
      { status: 500 }
    );
  }
}

// 根据id假删除知识库内容
export async function DELETE(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { id } = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数: id' },
        { status: 400 }
      );
    }
    
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 假删除，将del_flag设置为1
      await connection.execute(
        'UPDATE knowledge_base SET del_flag = 1, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
        [apAccountId, id, apCompanyId]
      );
      
      return NextResponse.json({ success: true, message: '知识库数据删除成功' });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除知识库数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '删除知识库数据失败' },
      { status: 500 }
    );
  }
} 