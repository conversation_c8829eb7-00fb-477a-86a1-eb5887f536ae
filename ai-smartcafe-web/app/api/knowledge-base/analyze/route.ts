import { NextResponse } from 'next/server';
import OpenAI from 'openai';

// OpenAI配置
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
});

interface RuleExtractResult {
  业务规则?: string[];
  特殊描述?: string[];
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { description } = body;
    
    if (!description) {
      return NextResponse.json({
        success: false,
        message: "描述内容不能为空"
      }, { status: 400 });
    }
    
    const systemPrompt = `给你一段描述：
${description}
你先将上面的描述切分成单独的句子
再判断上面的内容中，哪些是业务规则，哪些是特殊描述。
业务描述指的是：帮助AI理解业务规则，比如如何根据备注说明匹配对应的收入或支出类型；
特殊描述指的是：帮助AI理解文本的含义，比如人物的昵称、公司的简称、货品的品牌、常规的业务说法等；

以 json 格式输出，输出示例：
[
    {
        "业务规则": [
            "业务规则1",
            "业务规则2"
        ]
    },
    {
        "特殊描述": [
            "特殊描述1",
            "特殊描述2"
        ]
    }
]`;

    const response = await openai.chat.completions.create({
      // model: 'deepseek-r1-250120',
      model: 'deepseek-v3-250324',
      stream: false,
      response_format: { type: "json_object" },
      messages: [
        { 
          role: 'system', 
          content: systemPrompt
        }
      ],
      temperature: 0.1,
    });

    const content = response.choices[0].message.content;
    if (!content) {
      return NextResponse.json({
        success: false,
        message: "模型响应内容为空"
      }, { status: 500 });
    }

    let result: RuleExtractResult = {};
    try {
      // 清理响应内容，移除可能的 Markdown 代码块标记
      let cleanContent = content;
      // 完全移除Markdown格式，处理多行代码块
      if (content.includes('```')) {
        cleanContent = content.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      }
      // 移除前后可能的空白字符
      cleanContent = cleanContent.trim();
      
      const parsedContent = JSON.parse(cleanContent);
      
      // 处理直接返回对象的情况
      if (!Array.isArray(parsedContent)) {
        if (parsedContent['业务规则']) {
          result.业务规则 = parsedContent['业务规则'];
        }
        if (parsedContent['特殊描述']) {
          result.特殊描述 = parsedContent['特殊描述'];
        }
      } else {
        // 处理返回数组的情况
        parsedContent.forEach((item: any) => {
          if (item['业务规则']) {
            result.业务规则 = item['业务规则'];
          }
          if (item['特殊描述']) {
            result.特殊描述 = item['特殊描述'];
          }
        });
      }
    } catch (error) {
      console.error('JSON解析错误:', error);
      console.error('原始响应内容:', content);
      return NextResponse.json({
        success: false,
        message: `JSON解析错误: ${error instanceof Error ? error.message : String(error)}`
      }, { status: 500 });
    }

    return NextResponse.json(result);
    
  } catch (error) {
    console.error('API处理请求时出错:', error);
    return NextResponse.json({
      success: false,
      message: `处理请求时出错: ${error instanceof Error ? error.message : String(error)}`
    }, { status: 500 });
  }
} 