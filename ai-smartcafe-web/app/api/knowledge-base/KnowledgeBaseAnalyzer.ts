import OpenAI from 'openai';

interface RuleExtractResult {
  业务规则?: string[];
  特殊描述?: string[];
}

// 客户端API函数
export async function analyzeDescription(description: string): Promise<RuleExtractResult> {
  try {
    // 发送请求到服务端API
    const response = await fetch('/api/knowledge-base/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ description }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API请求失败: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('调用分析API时出错:', error);
    throw error;
  }
}
