import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
};

export async function operatorSql(data: any, apCompanyId: string, apAccountId: string) {
    // 兼容 data 既可以是对象也可以是数组
    if (!Array.isArray(data)) data = [data];
    const connection = await mysql.createConnection(dbConfig);
    try {
        await connection.beginTransaction();

        // 处理审批数据
        if (Array.isArray(data)) {
            for (const approval of data) {
                let headerId;

                // 先查询是否存在相同审批号的记录（添加 ap_company_id 筛选）
                const [rows]: [any[], mysql.FieldPacket[]] = await connection.execute(
                    'SELECT id FROM approval_header WHERE approval_no = ? AND ap_company_id = ?',
                    [approval.approvalNo, apCompanyId]
                );

                if (rows.length > 0) {
                    // 存在记录，进行更新
                    headerId = rows[0].id;
                    await connection.execute(
                        'UPDATE approval_header SET applicant_name = ?, department = ?, type = ?, submit_time = ?, total_amount = ?, status = ?, actual_payer = ?, remarks = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
                        [
                            approval.applicantName,
                            approval.department,
                            approval.type,
                            new Date(approval.submitTime),
                            approval.totalAmount,
                            approval.status,
                            approval.actualPayer,
                            approval.remarks || '',
                            apAccountId,
                            headerId,
                            apCompanyId
                        ]
                    );

                    // 删除旧的明细数据（添加 ap_company_id 筛选）
                    await connection.execute(
                        'DELETE FROM approval_expense_details WHERE approval_id = ? AND ap_company_id = ?',
                        [headerId, apCompanyId]
                    );
                } else {
                    // 不存在记录，插入新记录
                    const [headerResult] = await connection.execute<mysql.ResultSetHeader>(
                        'INSERT INTO approval_header (ap_company_id, ap_account_id, approval_no, applicant_name, department, type, submit_time, total_amount, status, actual_payer, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                        [
                            apCompanyId,
                            apAccountId,
                            approval.approvalNo,
                            approval.applicantName,
                            approval.department,
                            approval.type,
                            new Date(approval.submitTime),
                            approval.totalAmount,
                            approval.status,
                            approval.actualPayer,
                            approval.remarks || ''
                        ]
                    );

                    headerId = headerResult.insertId;
                }

                // 插入明细数据
                if (Array.isArray(approval.details)) {
                    for (const detail of approval.details) {
                        await connection.execute(
                            'INSERT INTO approval_expense_details (ap_company_id, ap_account_id, approval_id, expense_type, expense_date, amount, cost_center, attachment_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                            [
                                apCompanyId,
                                apAccountId,
                                headerId,
                                detail.expenseType,
                                new Date(detail.expenseDate),
                                detail.amount,
                                detail.costCenter,
                                detail.attachmentName || null
                            ]
                        );
                    }
                }
            }
        }

        await connection.commit();
    } catch (error) {
        await connection.rollback();
        throw error;
    } finally {
        await connection.end();
    }
}

export async function queryApporoveSql(approvalNos: string[], apCompanyId: string): Promise<string[]> {
    if (!approvalNos || approvalNos.length === 0) {
        return [];
    }

    const connection = await mysql.createConnection(dbConfig);
    try {
        // 使用IN查询多个审批号（添加 ap_company_id 筛选）
        const placeholders = approvalNos.map(() => '?').join(',');
        const [rows]: [any[], mysql.FieldPacket[]] = await connection.execute(
            `SELECT approval_no FROM approval_header WHERE approval_no IN (${placeholders}) AND ap_company_id = ?`,
            [...approvalNos, apCompanyId]
        );

        // 从查询结果中提取审批号，返回已存在的审批号数组
        return rows.map(row => row.approval_no);
    } catch (error) {
        console.error('查询审批号出错:', error);
        return [];
    } finally {
        await connection.end();
    }
}

/**
 * 通用SQL查询接口，只允许执行SELECT语句
 * @param sql 由AI生成的SQL语句
 * @param apCompanyId 公司ID
 * @returns 查询结果数组
 */
export async function executeQuerySql(sql: string, apCompanyId: string): Promise<any[]> {
    // 只允许执行SELECT语句，防止SQL注入和误操作
    const trimmedSql = sql.trim().toLowerCase();
    if (!trimmedSql.startsWith('select')) {
        return [];
    }
    
    // 在SQL中添加 ap_company_id 筛选条件
    let modifiedSql = sql;
    if (!sql.toLowerCase().includes('ap_company_id')) {
        // 简单的SQL修改，在WHERE子句中添加ap_company_id条件
        if (sql.toLowerCase().includes('where')) {
            modifiedSql = sql.replace(/where/i, `WHERE ap_company_id = '${apCompanyId}' AND `);
        } else {
            modifiedSql = sql + ` WHERE ap_company_id = '${apCompanyId}'`;
        }
    }
    
    const connection = await mysql.createConnection(dbConfig);
    try {
        const [rows]: [any[], mysql.FieldPacket[]] = await connection.query(modifiedSql);
        return rows;
    } catch (error) {
        console.error('执行SQL查询出错:', error);
        return [];
    } finally {
        await connection.end();
    }
}

