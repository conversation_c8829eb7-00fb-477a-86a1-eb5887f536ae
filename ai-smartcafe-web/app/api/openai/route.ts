import { deepseek } from '@ai-sdk/deepseek';
import { streamObject, generateText, generateObject } from 'ai';
import Client from '@/app/api/ocr/route';
import { ReimbursementMainSchemaResponse, ContractPaymentRecordResponse } from '@/lib/schema/inventory';
import { operatorSql, queryApporoveSql, executeQuerySql } from './approvalHeader';
import { selectAllContract, insertOrUpdateContractSql } from './contractPayment';
import { getRequiredAuthSession } from "@/lib/auth";
import { NextRequest } from 'next/server';
import { z } from 'zod';
export const maxDuration = 60;

export async function POST(req: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(req);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const body = await req.json();
    const { message: userInput = "", uploadedFiles = [], state = 0 } = body.data || {};
    const type = body.type || '';
    const historyChat = body.historyChat || [];

    // 过滤历史记录，只保留与当前类型匹配的记录
    const filteredHistory = filterHistoryByType(historyChat, type);
    // console.log("过滤后的历史聊天记录:", filteredHistory);

    switch (type) {
      case 'upload_expense':
        return await upload_expense(userInput, uploadedFiles, state, filteredHistory, apCompanyId.toString(), apAccountId.toString());
      case 'upload_contract':
        return await upload_contract(userInput, state, filteredHistory, apCompanyId.toString(), apAccountId.toString());
      default:
        return new Response(
          "处理请求时出错: 请选择要操作的类型！", { status: 500 }
        );
    }
  } catch (error) {
    return new Response(
      "处理请求时出错:" + error, { status: 500 }
    );
  }
}

/**
 * 根据操作类型过滤历史记录
 * @param history 历史记录数组
 * @param currentType 当前操作类型
 * @returns 过滤后的历史记录
 */
function filterHistoryByType(history: any[], currentType: string) {
  if (!history || history.length === 0 || !currentType) {
    return [];
  }

  return history.filter(item => {
    // 检查历史记录中的operationType是否与当前type相同
    return item.operationType === currentType;
  });
}

async function upload_expense(userInput: string, uploadedFiles: any[], state: number, historyChat: any[], apCompanyId: string, apAccountId: string) {
  const now = new Date();
  const formattedDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  let taskPrompt = `回复请用中文。当前时间：${formattedDate}。用户问题：${userInput}。`;
  if (state === 1) {
    const insertData = historyChat[historyChat.length - 1].data;
    // 插入数据库
    operatorSql(insertData, apCompanyId, apAccountId);
    console.log('@@@@@##@@@@@', insertData);
    taskPrompt = '请在message中直接告知用户此数据已经入库了。并且清空data中的结构化数据。';
  } else {
    if (uploadedFiles && uploadedFiles.length > 0) {
      const ocrResult = await Client.processOCRFiles(uploadedFiles);

      if (!ocrResult.success) {
        return new Response("文件识别失败", {
          status: 400,
        });
      }
      taskPrompt += `请从以下报销单文本中提取信息。报销单文本内容：${ocrResult.ocrResults || ''}。`
    }
    const obj = await generateObject({
      model: deepseek('deepseek-chat'),
      schema: z.object({
        approvalNos: z.array(z.string()).optional(),
        querySql: z.string().optional()
      }),
      prompt: taskPrompt + `
仔细分析用户的问题，结合对话历史，分析用户的意图是什么（是新增的报销单信息，或者补充完整新增的报销单信息，还是去查询报销单数据）？

如果从提供的信息中能读取到报销单编号就放到approvalNos返回。

如果用户是要查询数据，结合历史对话，加上用户的问题，解析出完整的where查询条件，最后拼接出完整的查询sql（注意：我的mysql 5版本，要注意提供的sql必须可执行），放到参数querySql中返回。

以下是用到的表结构：

-- 报销单信息主表
CREATE TABLE approval_header (
    id INT AUTO_INCREMENT PRIMARY KEY,
    approval_no VARCHAR(15) UNIQUE NOT NULL  comment '审批编号',
    applicant_name VARCHAR(50) NOT NULL  comment '申请人',
    department VARCHAR(100) NOT NULL  comment '所在部门',
    type VARCHAR(50) NOT NULL  comment '报销类型',
    submit_time DATETIME NOT NULL comment '提交时间',
    total_amount DECIMAL(10,2) NOT NULL comment '总金额',
    status VARCHAR(20) NOT NULL comment '报销单状态',
    pay_status int default 0 null comment '报销单付款状态：0-待付款，1-已付款',
    is_delete int default 0 null comment '是否已删除：0-否（默认），1-是',
    actual_payer VARCHAR(50) NOT NULL comment '实际付款人',
    remarks TEXT comment '报销事由，费用说明',
    cost_center VARCHAR(100) NULL COMMENT '成本中心',
    rsf_id bigint null comment 'reimburse_statement_files文件表ID',
    ap_company_id VARCHAR(50) NOT NULL comment '公司ID',
    ap_account_id VARCHAR(50) NOT NULL comment '账户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  comment '更新时间'
);

-- 报销费用明细表
CREATE TABLE approval_expense_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    approval_id INT NOT NULL comment '审批主表ID',
    expense_type VARCHAR(50) NOT NULL comment '费用类型',
    expense_date DATE NOT NULL comment '发生日期',
    amount DECIMAL(10,2) NOT NULL comment '费用金额',
    cost_center VARCHAR(100) NOT NULL comment '成本中心',
    attachment_name VARCHAR(255) comment '附件名称',
    ap_company_id VARCHAR(50) NOT NULL comment '公司ID',
    ap_account_id VARCHAR(50) NOT NULL comment '账户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  comment '更新时间'
);

历史对话如下：

${JSON.stringify(historyChat)}

`,
    });

    console.log('@@@@@@@@@********@@@@@@', obj.object);

    if (obj.object.approvalNos && obj.object.approvalNos.length > 0) {
      // 查询数据库，获取已存在的报销单编号
      const existingApprovalNos = await queryApporoveSql(obj.object.approvalNos, apCompanyId);

      if (existingApprovalNos.length > 0) {
        taskPrompt += `报销单编号为${JSON.stringify(existingApprovalNos)}的报销单数据已存在，不要在AI响应data中返回该结构化数据，在AI响应message中告知用户即可。`;
      }
    }

    if (obj.object.querySql) {
      const queryResult = await executeQuerySql(obj.object.querySql, apCompanyId);

      if (queryResult.length > 0) {
        taskPrompt += `根据用户问题，查询出来的报销单数据如下： ${JSON.stringify(queryResult)} 。该数据不要在AI响应data中返回。`;
      } else {
        taskPrompt += `根据您的问题，我没有查到报销单数据。`;
      }
    }
  }

  console.log("@@@@@@@@@@@@1111111111@@@@@@@@@@@@", taskPrompt);

  historyChat.push({ "role": "user", "content": taskPrompt });

  const response = streamObject({
    model: deepseek('deepseek-chat'),
    schema: ReimbursementMainSchemaResponse,
    system: `
你是一个高情商财务专家，日常工作是报销单解析/查询/聊天。请根据用户的问题，结合上下文，回答用户问题或者提取报销单信息。

处理规则：
0，如果用户只是聊天，你不需要返回data，直接在message中回答用户或者给与合理响应即可。
1，你需要解析用户输入，结合对话，来提取补全报销单信息。
2，如果data中必填信息不完整，不要返回data，并且要在message中明确告知用户缺少哪些信息，让用户继续补充。
3，如果data中信息已经补充完整，请在data中返回结构化数据，但不要在message中显示告知用户data的存在。
3，将最终的解析结果data包含在AI响应信息message中展示给用户看，并使用markdown格式回应，使数据直观易读。
    `,
    prompt: JSON.stringify(historyChat),
  });
  return response.toTextStreamResponse();
}

async function upload_contract(userInput: string, state: number, historyChat: any[], apCompanyId: string, apAccountId: string) {
  let taskPrompt = '回复请用中文。';
  if (state === 1) {
    // 插入数据库
    console.log("执行数据库插入操作, state=1");
    insertOrUpdateContractSql(historyChat[historyChat.length - 1].data, apCompanyId, apAccountId);
    taskPrompt = '请在message中直接告知用户此数据已经入库了，并清空message中合同信息。并且清空data结构化数据。';
  } else {

    // 先查询数据库中的数据，然后扔给ai，跟当前进行比对，不存在就新增，存在就告知用户已存在，需要更新
    const allContractArr = await selectAllContract(apCompanyId);
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    taskPrompt += `
如果最终解析的结构化数据data中，存在字段格式错误时，请不要返回data，直接在message中进行提示用户正确的录入格式。

用户输入：
${userInput}

当前时间：${formattedDate}

数据库中已存在的合同列表：
${JSON.stringify(allContractArr, null, 2)}
`;
  }
  historyChat.push({ "role": "user", "content": taskPrompt });

  const response = streamObject({
    model: deepseek('deepseek-chat'),
    schema: ContractPaymentRecordResponse,
    system: `
你是一个合同内容解析和高情商聊天专家。请根据用户的问题，结合上下文以及合同列表，回答用户问题或者补全合同信息。

处理规则：
0，如果用户只是问你问题，你不需要返回data，直接在message中回答用户或者给与合理响应即可。
1，你需要解析用户输入，结合对话，来提取补全合同信息。
2，如果信息不完整，不要返回data，并且要明确告知用户缺少哪些信息，让用户继续补充。
3，如果信息完整，你需要检查数据库中是否已存在相同门店名称、门店地址和支出类型的合同，然后做如下处理。
  - 如果数据库中存在相同记录，你需要在data中补充返回ID字段值，并告知用户数据存在，是否更新。
  - 如果数据库中不存在相同记录，你需要告知用户该操作是新增操作，返回的data中的ID字段去掉。
4，将最终的解析结果data包含在AI响应信息message中展示给用户看，并使用markdown格式回应，使数据直观易读。
`,
    prompt: JSON.stringify(historyChat),
  });
  return response.toTextStreamResponse();
}
