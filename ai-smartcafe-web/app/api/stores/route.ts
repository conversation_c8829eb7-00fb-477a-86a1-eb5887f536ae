import { NextResponse, NextRequest } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from '@/lib/auth';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// 类型定义
interface CompanyRow {
  max_stores: number;
  company_name: string;
}

interface CountRow {
  count: number;
}

// 格式化日期为 YYYY-MM-DD，如果为空则返回null
const formatDate = (dateString: string | null) => {
  if (!dateString) return null;

  try {
    // 如果已经是YYYY-MM-DD格式，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return dateString;
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return null;

    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('日期格式化错误:', error);
    return null;
  }
};

// 检查门店是否在其他表中有关联数据（添加多租户筛选）
async function hasRelatedRecords(connection: mysql.Connection, storeId: number, apCompanyId: number) {
  try {
    // 检查bills表（添加 ap_company_id 筛选）
    const [billRows] = await connection.execute('SELECT 1 FROM bills WHERE store_id = ? AND ap_company_id = ? LIMIT 1', [storeId, apCompanyId]);
    if (Array.isArray(billRows) && billRows.length > 0) return true;

    // 检查bill_split_records表（添加 ap_company_id 筛选）
    const [splitRows] = await connection.execute('SELECT 1 FROM bill_split_records WHERE store_id = ? AND ap_company_id = ? LIMIT 1', [storeId, apCompanyId]);
    if (Array.isArray(splitRows) && splitRows.length > 0) return true;

    // 检查inventory_value_monthly_record表（添加 ap_company_id 筛选）
    const [inventoryRows] = await connection.execute('SELECT 1 FROM inventory_value_monthly_record WHERE store_id = ? AND ap_company_id = ? LIMIT 1', [storeId, apCompanyId]);
    if (Array.isArray(inventoryRows) && inventoryRows.length > 0) return true;

    // 检查store_monthly_financials表（添加 ap_company_id 筛选）
    const [financialRows] = await connection.execute('SELECT 1 FROM store_monthly_financials WHERE store_id = ? AND ap_company_id = ? LIMIT 1', [storeId, apCompanyId]);
    if (Array.isArray(financialRows) && financialRows.length > 0) return true;

    // 检查income_sources表（添加 ap_company_id 筛选）
    const [incomeSourceRows] = await connection.execute('SELECT 1 FROM income_sources WHERE store_id = ? AND ap_company_id = ? LIMIT 1', [storeId, apCompanyId]);
    if (Array.isArray(incomeSourceRows) && incomeSourceRows.length > 0) return true;

    return false;
  } catch (error) {
    console.error('检查门店关联记录时出错:', error);
    throw error;
  }
}

// 检查公司门店数量是否已达到上限（添加多租户筛选）
async function checkStoreLimit(connection: mysql.Connection, companyId: number) {
  try {
    // 获取公司允许的最大门店数
    const [companyRows] = await connection.execute<mysql.RowDataPacket[]>('SELECT max_stores FROM companies WHERE id = ?', [companyId]);
    if (!Array.isArray(companyRows) || companyRows.length === 0) {
      throw new Error('未找到公司信息');
    }

    const maxStores = (companyRows[0] as CompanyRow).max_stores;

    // 获取当前门店数量（添加 ap_company_id 筛选）
    const [storeCountRows] = await connection.execute<mysql.RowDataPacket[]>('SELECT COUNT(*) as count FROM stores WHERE ap_company_id = ?', [companyId]);
    const currentCount = (storeCountRows[0] as CountRow).count;

    return { maxStores, currentCount, hasReachedLimit: currentCount >= maxStores };
  } catch (error) {
    console.error('检查门店数量限制时出错:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const stores = await request.json();
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 检查新增门店是否超过限制
      let newStoreCount = 0;
      for (const store of stores) {
        if (!store.id) {
          newStoreCount++;
        }
      }

      if (newStoreCount > 0) {
        const { maxStores, currentCount, hasReachedLimit } = await checkStoreLimit(connection, apCompanyId);
        if (hasReachedLimit || currentCount + newStoreCount > maxStores) {
          await connection.rollback();
          return NextResponse.json(
            { success: false, message: `门店数量已达到上限(${maxStores})，无法添加更多门店` },
            { status: 400 }
          );
        }
      }

      for (const store of stores) {
        const openTime = formatDate(store.openTime);
        const closeTime = formatDate(store.closeTime);

        if (store.id) {
          // Update existing store（添加 ap_company_id 筛选和 ap_account_id 更新）
          await connection.execute(
            'UPDATE stores SET company_name = ?, store_name = ?, brand = ?, remarks = ?, open_time = ?, close_time = ?, state = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?',
            [
              store.companyName,
              store.storeName,
              store.brand,
              store.remarks,
              openTime,
              closeTime,
              store.state,
              apAccountId,
              store.id,
              apCompanyId
            ]
          );
        } else {
          // Insert new store（添加 ap_company_id 和 ap_account_id 字段）
          await connection.execute(
            'INSERT INTO stores (ap_company_id, ap_account_id, company_name, store_name, brand, remarks, open_time, close_time, state) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [
              apCompanyId,
              apAccountId,
              store.companyName,
              store.storeName,
              store.brand,
              store.remarks,
              openTime,
              closeTime,
              store.state
            ]
          );
        }
      }

      await connection.commit();
      return NextResponse.json({ success: true, message: '门店数据保存成功' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('保存门店数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '保存门店数据失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const connection = await mysql.createConnection(dbConfig);
    try {
      // 根据公司ID获取门店数据（添加 ap_company_id 筛选）
      const [rows] = await connection.execute('SELECT * FROM stores WHERE ap_company_id = ?', [apCompanyId]);
      
      // 格式化返回数据，确保类型匹配前端接口定义
      const formattedData = (rows as any[]).map(row => ({
        id: String(row.id), // 转换为字符串类型
        company_name: row.company_name,
        store_name: row.store_name,
        brand: row.brand || '',
        remarks: row.remarks || '',
        open_time: row.open_time,
        close_time: row.close_time,
        state: row.state,
        initial_amount: row.initial_amount,
        initial_remarks: row.initial_remarks,
        created_at: row.created_at,
        updated_at: row.updated_at
      }));
      
      // 获取门店数量限制信息
      const [limitRows] = await connection.execute<mysql.RowDataPacket[]>('SELECT max_stores FROM companies WHERE id = ?', [apCompanyId]);
      const maxStores = (limitRows[0] as CompanyRow).max_stores;
      const currentStoreCount = formattedData.length;

      return NextResponse.json({
        success: true,
        data: formattedData,
        meta: {
          maxStores,
          currentStoreCount,
          remaining: maxStores - currentStoreCount
        }
      });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取门店数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '获取门店数据失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查门店是否存在关联记录（添加多租户筛选）
      const hasRelated = await hasRelatedRecords(connection, id, apCompanyId);
      if (hasRelated) {
        return NextResponse.json(
          { success: false, message: '该门店已有数据记录关联，无法删除' },
          { status: 400 }
        );
      }

      // 删除门店（添加 ap_company_id 筛选）
      await connection.execute('DELETE FROM stores WHERE id = ? AND ap_company_id = ?', [id, apCompanyId]);
      return NextResponse.json({ success: true, message: '门店删除成功' });
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除门店数据时出错:', error);
    return NextResponse.json(
      { success: false, message: '删除门店数据失败' },
      { status: 500 }
    );
  }
}