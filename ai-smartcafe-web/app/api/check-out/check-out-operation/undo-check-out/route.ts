import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null;
  
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { yearMonth } = await request.json();

    // 参数验证
    if (!yearMonth) {
      return NextResponse.json(
        { success: false, message: 'yearMonth 参数不能为空' },
        { status: 400 }
      );
    }

    // 验证 yearMonth 格式 (yyyy-MM)
    const yearMonthRegex = /^\d{4}-\d{2}$/;
    if (!yearMonthRegex.test(yearMonth)) {
      return NextResponse.json(
        { success: false, message: 'yearMonth 格式错误，应为 yyyy-MM 格式' },
        { status: 400 }
      );
    }

    connection = await mysql.createConnection(dbConfig);
    
    // 开启事务
    await connection.beginTransaction();
    
    try {
      // 1. 删除当前月份的所有财务数据（添加 ap_company_id 筛选）
      await connection.execute(`
        DELETE FROM store_monthly_financials 
        WHERE ap_company_id = ? AND \`year_month\` = ?
      `, [apCompanyId, yearMonth]);

      // 2. 插入反结账记录（添加 ap_company_id 和 ap_account_id 字段）
      await connection.execute(
        `INSERT INTO check_out_records (ap_company_id, ap_account_id, \`year_month\`, check_out_status) 
         VALUES (?, ?, ?, 0)`,
        [apCompanyId, apAccountId, yearMonth]
      );

      // 提交事务
      await connection.commit();

      return NextResponse.json({
        success: true,
        message: '反结账操作完成'
      });

    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    }
  } catch (error) {
    console.error('执行反结账操作时出错:', error);
    return NextResponse.json(
      { success: false, message: '执行反结账操作失败' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
} 