import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function POST(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { fileName } = await request.json();
    
    if (!fileName) {
      return NextResponse.json(
        { success: false, message: "文件名不能为空" },
        { status: 400 }
      );
    }

    const connection = await mysql.createConnection(dbConfig);
    
    try {
      const [rows] = await connection.execute(
        'SELECT COUNT(*) as count FROM menus WHERE menu_name = ? AND ap_company_id = ?',
        [fileName, apCompanyId]
      );
      
      const exists = (rows as any[])[0].count > 0;
      
      return NextResponse.json({
        success: true,
        exists
      });
      
    } finally {
      await connection.end();
    }
    
  } catch (error) {
    console.error("检查文件是否存在时出错:", error);
    return NextResponse.json({
      success: false,
      message: "检查文件是否存在失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 