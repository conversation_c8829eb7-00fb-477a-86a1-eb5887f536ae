import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 查询status=1的菜单数据，按created_at倒序排列
      const [rows] = await connection.execute(
        'SELECT id, menu_name, status, created_at FROM menus WHERE status = 1 AND ap_company_id = ? ORDER BY created_at DESC',
        [apCompanyId]
      );
      
      return NextResponse.json({
        success: true,
        data: rows
      });
      
    } finally {
      await connection.end();
    }
    
  } catch (error) {
    console.error("[API] 获取已完成菜单数据时出错:", error);
    return NextResponse.json({
      success: false,
      message: "获取已完成菜单数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 