import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function PUT(
  request: NextRequest,
  { params }: { params: { menuId: string } }
) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { menuId } = params;
    const { status } = await request.json();

    // 检查必要参数
    if (!menuId) {
      return NextResponse.json(
        { success: false, message: "菜单ID不能为空" },
        { status: 400 }
      );
    }

    // 检查状态值是否有效
    if (status !== 0 && status !== 1) {
      return NextResponse.json(
        { success: false, message: "无效的状态值，状态必须为0或1" },
        { status: 400 }
      );
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 更新菜单状态
      await connection.execute(
        `UPDATE menus SET status = ?, ap_account_id = ? WHERE id = ? AND ap_company_id = ?`,
        [status, apAccountId, menuId, apCompanyId]
      );

      // 记录日志
      console.log(`[API] 更新菜单状态成功: menuId=${menuId}, status=${status}`);

      return NextResponse.json(
        { success: true, message: "菜单状态更新成功" },
        { status: 200 }
      );
    } finally {
      // 关闭数据库连接
      await connection.end();
    }
  } catch (error: any) {
    console.error("[API] 更新菜单状态失败:", error);
    return NextResponse.json(
      { success: false, message: error.message || "更新菜单状态失败" },
      { status: 500 }
    );
  }
} 