import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const connection = await mysql.createConnection(dbConfig);
    
    try {
      const [rows] = await connection.execute(
        'SELECT id, menu_name, status FROM menus WHERE ap_company_id = ? ORDER BY id DESC limit 5',
        [apCompanyId]
      );
      
      return NextResponse.json({
        success: true,
        data: rows
      });
      
    } finally {
      await connection.end();
    }
    
  } catch (error) {
    console.error("获取菜单数据时出错:", error);
    return NextResponse.json({
      success: false,
      message: "获取菜单数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 