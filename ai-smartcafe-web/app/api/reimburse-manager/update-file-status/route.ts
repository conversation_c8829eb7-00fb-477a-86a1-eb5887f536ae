import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库配置 (与项目中其他API路由一致)
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function PUT(request: NextRequest) {
  let connection: mysql.Connection | null = null;
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const body = await request.json();
    const { fileIds } = body;

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return NextResponse.json({ error: 'fileIds array is required and must not be empty' }, { status: 400 });
    }

    // 假设我们总是处理数组中的第一个ID，因为您提到一次只有一个ID
    const fileIdToUpdate = String(fileIds[0]);
    if (!fileIdToUpdate) { // 额外的检查，以防 fileIds[0] 是 undefined 或 null
        return NextResponse.json({ error: 'A valid fileId was not provided in the array' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);

    const updateQuery = `
      UPDATE reimburse_statement_files
      SET status = 2, ap_account_id = ?
      WHERE id = ? AND ap_company_id = ?;
    `; // status = 2 表示 '已入库'

    // 执行查询（添加 ap_company_id 筛选）
    const [result] = await connection.execute(updateQuery, [apAccountId, fileIdToUpdate, apCompanyId]);

    await connection.end();

    const affectedRows = (result as any).affectedRows || 0;

    if (affectedRows > 0) {
      return NextResponse.json({ 
        success: true, 
        message: `Successfully updated status for ${affectedRows} file(s).`, 
        updatedCount: affectedRows 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'No files were updated. They might not exist or their status was already set.', 
        updatedCount: 0 
      }, { status: 404 }); // 404 if no records matched the criteria
    }

  } catch (error) {
    console.error('Error updating file statuses:', error);
    if (connection) {
      await connection.end();
    }
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to update file statuses', details: errorMessage },
      { status: 500 }
    );
  }
} 