import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { generateObject } from 'ai';
import { z } from 'zod';
import { getRequiredAuthSession } from "@/lib/auth";
import '@/app/utils/logger'; // 导入日志模块以应用补丁
// import { createOpenAI } from "@ai-sdk/openai";
import { createDeepSeek } from '@ai-sdk/deepseek';
export const maxDuration = 60;
// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// const openai = createOpenAI({
//   compatibility: "compatible", // 使用兼容模式
//   apiKey: process.env.HUOSAN_API_KEY,
//   baseURL: process.env.OPENAI_BASE_URL,
// });
const deepseek = createDeepSeek({
  apiKey: process.env.HUOSAN_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
});

// 获取报销管理数据
export async function GET(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const fileId = searchParams.get('fileId') || '';
    const offset = (page - 1) * pageSize;

    const connection = await mysql.createConnection(dbConfig);

    // 构建基本条件
    const baseWhere = `ah.is_delete = 0 AND ah.ap_company_id = ?`;
    // 如果fileId不为空，则添加筛选条件
    const fileCondition = fileId ? `AND ah.rsf_id = ?` : '';
    const fileParam = fileId || null;

    // 查询报销数据，关联账单匹配记录表（添加 ap_company_id 筛选）
    const query = `
    SELECT  ah.id as header_id,
        ah.approval_no,
        ah.applicant_name,
        ah.type,
        DATE_FORMAT(ah.submit_time, '%Y-%m-%d %H:%i:%s') as submit_time,
        ah.status,
        ah.pay_status,
        ah.remarks,
        ah.total_amount,
        bm.id as match_id,
        b.id as bill_id,
        b.bill_amount,
        DATE_FORMAT(b.bill_date, '%Y-%m-%d') as bill_date
      FROM
        approval_header ah
      LEFT JOIN
        approval_bill_match_record bm ON ah.id = bm.approval_header_id AND bm.ap_company_id = ?
      LEFT JOIN
        bills b ON bm.bills_id = b.id and b.status = 1 AND b.ap_company_id = ?
      WHERE 
        ${baseWhere} ${fileCondition}
      ORDER BY 
        ah.submit_time DESC, ah.id ASC
      LIMIT ? OFFSET ?
    `;

    // 查询总报销头记录数（添加 ap_company_id 筛选）
    const countQuery = `
      SELECT 
        COUNT(ah.id) as total
      FROM 
        approval_header ah
      WHERE 
        ${baseWhere} ${fileCondition}
    `;

    // 查询总已匹配报销头记录数（添加 ap_company_id 筛选）
    const matchedCountQuery = `
      SELECT
        COUNT(DISTINCT ah.id) as totalMatched
      FROM 
        approval_header ah
      JOIN 
        approval_bill_match_record abmr ON ah.id = abmr.approval_header_id AND abmr.ap_company_id = ?
      WHERE 
        ${baseWhere} ${fileCondition}
    `;

    // 查询所有未关联报销的对账单数据（添加 ap_company_id 筛选）
    const queryBills = `
     SELECT b.id, b.bill_amount, DATE_FORMAT(b.bill_date, '%Y-%m-%d') as bill_date, b.opposing_unit
      FROM bills b
      WHERE b.is_reimburse = 1
      AND b.status = 1
      AND b.ap_company_id = ?
      AND NOT EXISTS (
        SELECT 1 FROM approval_bill_match_record abmr
        WHERE abmr.bills_id = b.id AND abmr.ap_company_id = ?
      )
    `;

    // 执行查询时，根据fileId是否存在来构建参数数组
    const queryParams = fileId
      ? [apCompanyId, apCompanyId, apCompanyId, fileId, pageSize, offset]
      : [apCompanyId, apCompanyId, apCompanyId, pageSize, offset];

    const countQueryParams = fileId
      ? [apCompanyId, fileId]
      : [apCompanyId];

    const matchedCountQueryParams = fileId
      ? [apCompanyId, apCompanyId, fileId]
      : [apCompanyId, apCompanyId];

    // 执行查询
    const [rows] = await connection.query(query, queryParams);
    const [countResult] = await connection.query(countQuery, countQueryParams);
    const [matchedCountResult] = await connection.query(matchedCountQuery, matchedCountQueryParams);
    const [bills] = await connection.query(queryBills, [apCompanyId, apCompanyId]);

    await connection.end();

    // 处理结果
    const totalItems = (countResult as any[])[0]?.total || 0;
    const totalMatchedItems = (matchedCountResult as any[])[0]?.totalMatched || 0;
    const totalPages = Math.ceil(totalItems / pageSize);

    return NextResponse.json({
      success: true,
      data: {
        items: rows,
        bills: bills,
        pagination: {
          page,
          pageSize,
          totalItems,
          totalPages,
          totalMatchedItems
        }
      }
    });
  } catch (error) {
    console.error('获取报销管理数据失败:', error);
    return NextResponse.json({
      success: false,
      message: "获取报销管理数据失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// 添加报销与账单匹配记录 (Auto Match)
export async function POST(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const connection = await mysql.createConnection(dbConfig);

    // 1. 查询未匹配的报销记录（添加 ap_company_id 筛选）
    const [unmatchedApprovals] = await connection.execute(`
      SELECT ah.id as approval_id, ah.applicant_name, DATE_FORMAT(ah.submit_time, '%Y-%m-%d %H:%i:%s') as submit_time, ah.total_amount, ah.type, ah.remarks
      FROM approval_header ah
      WHERE ah.is_delete = 0
      AND ah.ap_company_id = ?
      AND NOT EXISTS (
        SELECT 1 FROM approval_bill_match_record abmr
        WHERE abmr.approval_header_id = ah.id AND abmr.ap_company_id = ?
      )
    `, [apCompanyId, apCompanyId]);

    // 2. 查询未匹配的账单记录（添加 ap_company_id 筛选）
    const [unmatchedBills] = await connection.execute(`
      SELECT b.id as bill_id, b.bill_amount, DATE_FORMAT(b.bill_date, '%Y-%m-%d') as bill_date, b.opposing_unit
      FROM bills b
      WHERE b.is_reimburse = 1
      AND b.status = 1
      AND b.ap_company_id = ?
      AND NOT EXISTS (
        SELECT 1 FROM approval_bill_match_record abmr
        WHERE abmr.bills_id = b.id AND abmr.ap_company_id = ?
      )
    `, [apCompanyId, apCompanyId]);

    if ((unmatchedApprovals as any[]).length === 0 || (unmatchedBills as any[]).length === 0) {
      await connection.end();
      return NextResponse.json({
        success: false,
        message: "没有可匹配的报销或账单数据"
      }, { status: 400 });
    }

    const preparedApprovals = (unmatchedApprovals as any[]).map(approval => ({
      approval_id: approval.approval_id,
      applicant_name: approval.applicant_name,
      submit_time: approval.submit_time,
      total_amount: approval.total_amount,
      description: `${approval.type || 'N/A'} (${approval.remarks || ''})`
    }));

    const preparedBills = (unmatchedBills as any[]).map(bill => ({
      bill_id: bill.bill_id,
      bill_amount: bill.bill_amount,
      bill_date: bill.bill_date,
      opposing_unit: bill.opposing_unit
    }));

    const modelPrompt = `
<报销申请数据>
${JSON.stringify(preparedApprovals, null, 2)}
</报销申请数据>

<账单数据>
${JSON.stringify(preparedBills, null, 2)}
</账单数据>

# 匹配规则
- 在<账单数据>中，能看到对方单位为个人的，有多条数据，这几条数据就意味着是企业给公司内部员工的报销支付。
- 那么我需要你帮我根据<账单数据>中银行的支付信息，去匹配<报销申请数据>中对应的报销申请，根据申请人的姓名和金额进行匹配，能匹配上的帮我关联上ID。

# 注意
1. **报销金额total_amount应该跟账单金额bill_amount必须完全一致，不能有任何差异**。
2. **申请人姓名applicant_name应该跟账单的对方单位opposing_unit完全一致**。
3. **每条报销申请数据和每条账单数据都只能被关联一次**。

请返回确认100%完全匹配的组合，格式为：[{approval_id: xxx, bill_id: xxx}, ...]。否则，请返回空数组[]。
`;

    console.log('--------------------------------');
    console.log(modelPrompt);
    console.log('--------------------------------');
    let matchedData = [];
    try {
      const { object } = await generateObject({
        model: deepseek("deepseek-r1-250528"),
        schema: z.object({
          data: z.array(
            z.object({
              approval_id: z.number(),
              bill_id: z.number()
            }))
        }),
        prompt: modelPrompt
      });

      console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@');
      console.log(JSON.stringify(object, null, 2));
      console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@');

      matchedData = object.data || [];
    } catch (error) {
      console.error('AI匹配错误1111，在尝试一次:', error);
      try {
        const { object } = await generateObject({
          model: deepseek("deepseek-r1-250528"),
          schema: z.object({
            data: z.array(
              z.object({
                approval_id: z.number(), // This now refers to approval_header.id
                bill_id: z.number()
              }))
          }),
          prompt: modelPrompt
        });

        console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@');
        console.log(JSON.stringify(object, null, 2));
        console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@');

        matchedData = object.data || [];
      } catch (error) {
        console.error('AI匹配错误222:', error);
        // 出错时返回空数组，继续处理
        return NextResponse.json({
          success: true, // 即使出错也返回成功状态，避免前端报错
          message: "自动匹配结束，如没有匹配到记录，请再尝试一次",
          data: {
            matchedData: [],
            actualMatchesCount: 0
          }
        });
      }
    }

    let actualMatchesCount = 0;

    if (Array.isArray(matchedData) && matchedData.length > 0) {
      await connection.beginTransaction();
      try {
        for (const match of matchedData) {
          const { approval_id, bill_id } = match; // approval_id is approval_header.id

          const [existingMatch] = await connection.execute(
            `SELECT * FROM approval_bill_match_record WHERE (approval_header_id = ? OR bills_id = ?) AND ap_company_id = ?`,
            [approval_id, bill_id, apCompanyId]
          );

          if ((existingMatch as any[]).length === 0) {
            await connection.execute(
              `INSERT INTO approval_bill_match_record (ap_company_id, ap_account_id, bills_id, approval_header_id) VALUES (?, ?, ?, ?)`,
              [apCompanyId, apAccountId, bill_id, approval_id]
            );
            await connection.execute(
              `UPDATE approval_header SET pay_status = 1, ap_account_id = ? WHERE id = ? AND ap_company_id = ?`,
              [apAccountId, approval_id, apCompanyId]
            );
            // No need to update bills.is_reimburse here if it's already a precondition for selection
            actualMatchesCount++;
          }
        }
        await connection.commit();
      } catch (error) {
        await connection.rollback();
        console.error('Error during DB operations for auto-match:', error);
        throw error;
      }
    }

    await connection.end();

    return NextResponse.json({
      success: true,
      message: actualMatchesCount > 0 ? `成功匹配 ${actualMatchesCount} 条记录` : "没有新的匹配记录",
      data: {
        matchedData: matchedData,
        actualMatchesCount
      }
    });
  } catch (error) {
    console.error('自动匹配记录失败:', error);
    return NextResponse.json({
      success: false,
      message: "自动匹配记录失败",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// 手工匹配报销与账单
export async function PATCH(request: NextRequest) {
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const body = await request.json();
    // reimbursement is now an array of objects like { id: string } where id is approval_no
    const { reimbursement, paymentId } = body;

    if (!reimbursement || !Array.isArray(reimbursement) || reimbursement.length === 0 || !paymentId) {
      return NextResponse.json({
        success: false,
        message: "请提供有效的报销记录和账单ID"
      }, { status: 400 });
    }

    const connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    try {
      const [existingBillRows] = await connection.execute(
        `SELECT * FROM bills WHERE id = ? AND is_reimburse = 1 AND status = 1 AND ap_company_id = ?`,
        [paymentId, apCompanyId]
      );

      if ((existingBillRows as any[]).length === 0) {
        await connection.rollback();
        await connection.end();
        return NextResponse.json({ success: false, message: "指定的账单不存在或不可用于匹配" }, { status: 400 });
      }

      const [existingMatchForBill] = await connection.execute(
        `SELECT * FROM approval_bill_match_record WHERE bills_id = ? AND ap_company_id = ?`,
        [paymentId, apCompanyId]
      );

      if ((existingMatchForBill as any[]).length > 0) {
        await connection.rollback();
        await connection.end();
        return NextResponse.json({ success: false, message: "此账单已被其他报销记录匹配，无法重复匹配" }, { status: 400 });
      }

      let matchedCount = 0;
      for (const item of reimbursement) {
        const approvalNo = item.id; // item.id is approval_no from frontend
        if (!approvalNo) {
          console.warn("Skipping item due to missing approval_no:", item);
          continue;
        }

        // Get approval_header.id from approval_no（添加 ap_company_id 筛选）
        const [headerRows] = await connection.execute(
          `SELECT id FROM approval_header WHERE approval_no = ? AND is_delete = 0 AND ap_company_id = ?`,
          [approvalNo, apCompanyId]
        ) as any[];

        if (!headerRows || headerRows.length === 0) {
          console.warn(`报销单号 ${approvalNo} 不存在或已被删除。`);
          // Optionally, collect these errors and return them, or throw to stop the transaction.
          // For now, skipping this item.
          continue;
        }
        const approvalHeaderId = headerRows[0].id;

        const [approvalMatch] = await connection.execute(
          `SELECT * FROM approval_bill_match_record WHERE approval_header_id = ? AND ap_company_id = ?`,
          [approvalHeaderId, apCompanyId]
        ) as any[];

        if (approvalMatch && approvalMatch.length > 0) {
          console.warn(`报销单号 ${approvalNo} (ID: ${approvalHeaderId}) 已被匹配，无法重复匹配。`);
          continue;
        }

        await connection.execute(
          `INSERT INTO approval_bill_match_record (ap_company_id, ap_account_id, bills_id, approval_header_id) VALUES (?, ?, ?, ?)`,
          [apCompanyId, apAccountId, paymentId, approvalHeaderId]
        );

        await connection.execute(
          `UPDATE approval_header SET pay_status = 1, ap_account_id = ? WHERE id = ? AND ap_company_id = ?`,
          [apAccountId, approvalHeaderId, apCompanyId]
        );

        matchedCount++;
      }

      if (matchedCount === 0 && reimbursement.length > 0) {
        await connection.rollback();
        await connection.end();
        // This message can be more specific if we collect reasons for non-match
        return NextResponse.json({ success: false, message: "提供的所有报销单均无法匹配（可能已匹配或ID无效）" }, { status: 400 });
      }

      await connection.commit();
      await connection.end();

      return NextResponse.json({
        success: true,
        message: `手工匹配成功 ${matchedCount} 条报销单`,
        data: {
          matchedCount: matchedCount
        }
      });

    } catch (error) {
      await connection.rollback();
      await connection.end();
      console.error('手工匹配操作数据库失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      // More specific error messages can be returned if thrown from the loop
      return NextResponse.json({
        success: false,
        message: "手工匹配失败，数据库操作错误", // Generic for now
        error: errorMessage
      }, { status: 500 });
    }
  } catch (error) {
    console.error('手工匹配请求处理失败:', error);
    const specificError = error as any;
    return NextResponse.json({
      success: false,
      message: specificError instanceof Error ? specificError.message : "手工匹配请求处理失败",
      error: specificError instanceof Error ? specificError.message : String(specificError)
    }, { status: 500 });
  }
}
