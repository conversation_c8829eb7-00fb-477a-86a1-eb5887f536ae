import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置 (与 income-types/route.ts 保持一致或通过共享模块导入)
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

export async function GET(request: NextRequest) {
  let connection;
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    connection = await mysql.createConnection(dbConfig);
    const [stores] = await connection.execute(
      'SELECT id, store_name, open_time, close_time, initial_amount,initial_remarks FROM stores WHERE ap_company_id = ? ORDER BY store_name ASC',
      [apCompanyId]
    );
    await connection.end();
    // API 返回的数据结构直接是门店数组，例如: [{id: 1, store_name: "门店X", open_time: "2023-01-01T10:00:00.000Z", close_time: null}, ...]
    return NextResponse.json(stores);
  } catch (error) {
    console.error('Failed to fetch stores:', error);
    if (connection) {
      await connection.end();
    }
    return NextResponse.json(
      { message: '获取门店列表失败', error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 