import { NextResponse, NextRequest } from 'next/server';
import mysql from 'mysql2/promise';
import { getRequiredAuthSession } from "@/lib/auth";
import '@/app/utils/logger';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

interface OkPacket {
  affectedRows: number;
  insertId: number;
  // ... other properties from OkPacket
}

export async function POST(request: NextRequest) {
  let connection;
  console.log('Received POST request to /api/inventory-value/records');
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const body = await request.json();
    console.log('Request body:', body);

    const {
      storeId,
      year,
      month,
      endingValue,
      updatedId = 1 // Default updatedId to 1 as per user confirmation
    } = body;

    if (!storeId || !year || !month || endingValue === undefined || endingValue === null) {
      console.error('Validation Error: Missing required fields.');
      return NextResponse.json({ message: '必填字段缺失: 需要提供门店ID、年份、月份和期末库存价值。' }, { status: 400 });
    }
    if (isNaN(parseFloat(endingValue))) {
      console.error('Validation Error: endingValue is not a valid number.');
      return NextResponse.json({ message: '期末库存价值必须是有效的数字。' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();
    console.log('Database transaction started.');

    // 1. 计算期初库存价值 (上月期末)
    let calculatedBeginningValue = '0.00';
    const prevMonth = month === 1 ? 12 : month - 1;
    const prevYear = month === 1 ? year - 1 : year;
    console.log(`Calculating beginning value for store ${storeId}, year ${prevYear}, month ${prevMonth}`);
    const [prevMonthRows]: any[] = await connection.execute(
      'SELECT ending_inventory_value FROM inventory_value_monthly_record WHERE store_id = ? AND year = ? AND month = ? AND ap_company_id = ? ORDER BY id DESC LIMIT 1',
      [storeId, prevYear, prevMonth, apCompanyId]
    );
    if (prevMonthRows.length > 0 && prevMonthRows[0].ending_inventory_value !== null) {
      calculatedBeginningValue = prevMonthRows[0].ending_inventory_value;
    }
    console.log('Calculated beginning value:', calculatedBeginningValue);

    // 2. 计算当月采购支出 (物资采购: "库存商品" - "材料采购")
    let calculatedPurchaseExpense = '0.00';
    console.log(`Calculating purchase expense for store ${storeId}, year ${year}, month ${month}`);
    const [parentTypeRows]: any[] = await connection.execute(
      'SELECT id FROM expense_types WHERE name = ? AND ap_company_id = ? LIMIT 1',
      ['库存商品', apCompanyId]
    );

    if (parentTypeRows.length > 0) {
      const parentTypeId = parentTypeRows[0].id;
      const [subTypeRows]: any[] = await connection.execute(
        'SELECT id FROM expense_subtypes WHERE name = ? AND parent_id = ? AND ap_company_id = ? LIMIT 1',
        ['材料采购', parentTypeId, apCompanyId]
      );

      if (subTypeRows.length > 0) {
        const subTypeId = subTypeRows[0].id;
        const incomeExpenseTypeIdFilter = `${parentTypeId}-${subTypeId}`;
        console.log('Purchase expense type filter:', incomeExpenseTypeIdFilter);

        const getPurchaseExpenseSql = `
            SELECT COALESCE(SUM(b.bill_amount), 0) as total_purchase
            FROM bills b
            WHERE b.store_id = ?
              AND YEAR(b.transaction_time) = ?
              AND MONTH(b.transaction_time) = ?
              AND b.bill_type = 'expense'
              AND b.income_expense_type_id = ?
              AND b.ap_company_id = ?;
        `;
        const [purchaseRows]: any[] = await connection.execute(getPurchaseExpenseSql, [
          storeId, year, month, incomeExpenseTypeIdFilter, apCompanyId
        ]);
        if (purchaseRows.length > 0 && purchaseRows[0].total_purchase !== null) {
          calculatedPurchaseExpense = parseFloat(purchaseRows[0].total_purchase).toFixed(2);
        }
      } else {
        console.warn(`Purchase expense calculation warning: Subtype "材料采购" (parent ID: ${parentTypeId}) not found.`);
      }
    } else {
      console.warn('Purchase expense calculation warning: Type "库存商品" not found.');
    }
    console.log('Calculated purchase expense:', calculatedPurchaseExpense);

    // 3. UPSERT 数据
    const upsertSql = `
      INSERT INTO inventory_value_monthly_record 
        (ap_company_id, ap_account_id, store_id, year, month, beginning_inventory_value, purchase_expense_value, ending_inventory_value, is_ending_value_auto_generated, updated_id, created_at, updated_at) 
      VALUES 
        (?, ?, ?, ?, ?, ?, ?, ?, FALSE, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON DUPLICATE KEY UPDATE
        beginning_inventory_value = VALUES(beginning_inventory_value),
        purchase_expense_value = VALUES(purchase_expense_value),
        ending_inventory_value = VALUES(ending_inventory_value),
        is_ending_value_auto_generated = FALSE, 
        updated_id = VALUES(updated_id),
        ap_account_id = VALUES(ap_account_id),
        updated_at = CURRENT_TIMESTAMP;
    `;

    const upsertParams = [
      apCompanyId,
      apAccountId,
      storeId, year, month,
      calculatedBeginningValue,
      calculatedPurchaseExpense,
      parseFloat(endingValue).toFixed(2),
      updatedId
    ];
    console.log('Executing UPSERT SQL:', upsertSql);
    console.log('UPSERT parameters:', upsertParams);

    const [result] = await connection.execute(upsertSql, upsertParams) as [OkPacket, any];
    console.log('UPSERT result from database:', result);

    let recordId = result.insertId;
    // Log current recordId before potentially re-fetching
    console.log(`Initial recordId (from result.insertId): ${recordId}, affectedRows: ${result.affectedRows}`);

    if (result.affectedRows > 1 || (result.affectedRows === 1 && result.insertId === 0)) {
      console.log('Condition for re-fetching ID met. Fetching ID from database...');
      const [updatedRows]: any[] = await connection.execute(
        'SELECT id FROM inventory_value_monthly_record WHERE store_id = ? AND year = ? AND month = ? AND ap_company_id = ?',
        [storeId, year, month, apCompanyId]
      );
      if (updatedRows.length > 0) {
        recordId = updatedRows[0].id;
        console.log('Re-fetched recordId:', recordId);
      } else {
        console.warn('Attempted to re-fetch ID, but no record found for store/year/month combination.');
      }
    } else if (result.affectedRows === 0 && result.insertId === 0) {
      // This case might indicate an update where values did not change,
      // or potentially an issue if no rows were affected and it wasn't an insert.
      // We should still try to get the ID as the record should exist.
      console.log('UPSERT affectedRows is 0 and insertId is 0. Assuming existing record, attempting to fetch ID.');
      const [existingRows]: any[] = await connection.execute(
        'SELECT id FROM inventory_value_monthly_record WHERE store_id = ? AND year = ? AND month = ? AND ap_company_id = ?',
        [storeId, year, month, apCompanyId]
      );
      if (existingRows.length > 0) {
        recordId = existingRows[0].id;
        console.log('Fetched existing recordId:', recordId);
      } else {
        console.error('CRITICAL: UPSERT affected 0 rows, insertId 0, and no existing record found for store/year/month. This should not happen with a unique key.');
      }
    }


    await connection.commit();
    console.log('Database transaction committed.');

    console.log('Final determined recordId:', recordId);
    return NextResponse.json({
      message: '库存价值更新成功',
      recordId: recordId,
      calculatedBeginningValue,
      calculatedPurchaseExpense
    }, { status: 200 });

  } catch (error) {
    if (connection) {
      console.log('Rolling back database transaction due to error.');
      await connection.rollback();
    }
    console.error('Error updating inventory value:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    if ((error as any).code === 'ER_DUP_ENTRY') {
      console.error('Database Error: ER_DUP_ENTRY - Attempted to create duplicate entry.');
      return NextResponse.json(
        { message: '数据库错误: 试图创建重复的库存记录 (门店, 年份, 月份组合已存在)。', error: errorMessage },
        { status: 409 } // Conflict
      );
    }
    return NextResponse.json(
      { message: '更新库存价值失败', error: errorMessage },
      { status: 500 }
    );
  } finally {
    if (connection) {
      console.log('Closing database connection.');
      await connection.end();
    }
  }
}

export async function GET(request: NextRequest) {
  let connection;
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(request);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const { searchParams } = new URL(request.url);
    const yearStr = searchParams.get('year');
    const storeIdStr = searchParams.get('storeId');

    if (!yearStr || !storeIdStr) {
      return NextResponse.json({ message: '必须提供年份和门店ID作为查询参数。' }, { status: 400 });
    }
    const year = parseInt(yearStr);
    const storeId = parseInt(storeIdStr);

    if (isNaN(year) || isNaN(storeId)) {
      return NextResponse.json({ message: '年份或门店ID格式无效。' }, { status: 400 });
    }

    connection = await mysql.createConnection(dbConfig);
    const sql = `
      SELECT id, month, 
             COALESCE(beginning_inventory_value, '0.00') as initialValue, 
             COALESCE(purchase_expense_value, '0.00') as purchaseValue, 
             COALESCE(ending_inventory_value, '0.00') as endValue, 
             updated_at as updateTime,
             is_ending_value_auto_generated as isAutoGenerated -- include this field if UI needs it
      FROM inventory_value_monthly_record 
      WHERE year = ? AND store_id = ? AND ap_company_id = ?
      ORDER BY month ASC`;
    const [records]: any[] = await connection.execute(sql, [year, storeId, apCompanyId]);

    const formattedRecords = records.map((r: any) => ({
      id: r.id,
      month: String(r.month),
      initialValue: String(r.initialValue),
      purchaseValue: String(r.purchaseValue),
      endValue: String(r.endValue),
      updateTime: r.updateTime ? new Date(r.updateTime).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      isAutoGenerated: Boolean(r.isAutoGenerated) // Convert to boolean
    }));

    await connection.end();
    return NextResponse.json(formattedRecords);
  } catch (error) {
    if (connection) {
      await connection.end();
    }
    console.error('获取库存历史记录失败:', error);
    return NextResponse.json(
      { message: '获取库存历史记录失败', error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 