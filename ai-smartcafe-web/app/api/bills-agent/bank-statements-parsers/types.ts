// 银行流水解析策略类型定义

// 解析后的数据项结构（适配bills-agent）
export interface ParsedDataItem {
  directlyExtracted: {
    transactionTime: string | null;
    billType: 'income' | 'expense' | null;
    opposingUnit: string | null;
    billAmount: number | null;
    billBalance: number | null;
  };
  forModel: {
    originData: string;
  };
}

// 银行解析策略接口
export interface BankParserStrategy {
  // 银行名称标识
  bankName: string;
  
  // 检测是否为该银行的对账单格式
  canParse(fileBuffer: ArrayBuffer): boolean;
  
  // 解析对账单内容
  parseContent(fileBuffer: ArrayBuffer): ParsedDataItem[];
  
  // 获取该银行支持的字段映射
  getFieldMapping(): {
    transactionTime: string[];
    loanFlag: string[];
    opposingUnit: string[];
    transferOut: string[];
    transferIn: string[];
    balance: string[];
    usage: string[];
    summary: string[];
    remark: string[];
  };
}

// 字段索引结构
export interface FieldIndexes {
  transactionTimeIndex: number;
  loanFlagIndex: number;
  opposingUnitIndex: number;
  transferOutIndex: number;
  transferInIndex: number;
  balanceIndex: number;
  usageIndex: number;
  summaryIndex: number;
  remarkIndex: number;
}

// CSV解析结果
export interface CSVParseResult {
  headers: string[];
  data: string[][];
  separator: string;
} 