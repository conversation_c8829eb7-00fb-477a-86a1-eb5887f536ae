// 通用银行对账单解析策略

import { BankParserStrategy, ParsedDataItem } from './types';
import { CSVUtils } from './csv-utils';
import * as XLSX from 'xlsx';

export class UniversalBankParser implements BankParserStrategy {
  bankName: string;

  constructor(bankName: string) {
    this.bankName = bankName;
  }

  canParse(fileBuffer: ArrayBuffer): boolean {
    try {
      const workbook = XLSX.read(fileBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      
      const fieldMapping = this.getFieldMapping();
      const requiredFields = [
        ...fieldMapping.transactionTime,
        ...fieldMapping.loanFlag,
        ...fieldMapping.opposingUnit
      ];
      
      for (let row = range.s.r; row <= Math.min(range.s.r + 10, range.e.r); row++) {
        let foundFields = 0;
        
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          
          if (cell && cell.v) {
            const cellValue = String(cell.v).trim();
            
            if (requiredFields.some(field => cellValue.includes(field))) {
              foundFields++;
            }
          }
        }
        
        if (foundFields >= 2) {
          return true;
        }
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  getFieldMapping() {
    return {
      transactionTime: ['交易时间', '交易日期', '日期', '时间', 'Transaction Date', 'Transaction time'],
      loanFlag: ['借贷标志', '借贷方向', '收支标志', '借', '贷', '收入', '支出', '交易类型', 'Transaction Type'],
      opposingUnit: ['对方单位', '对方户名', '付款人名称', '收款人名称', '交易对手', '对方', 'Payer\'s Name', 'Payee\'s Name'],
      transferOut: ['转出金额', '借方金额', '支出金额', '扣款金额', '交易金额', 'Trade Amount'],
      transferIn: ['转入金额', '贷方金额', '收入金额', '入账金额', '交易金额', 'Trade Amount'],
      balance: ['余额', '账户余额', '交易后余额', 'After-transaction balance'],
      usage: ['用途', '交易用途', '备注', '摘要', '附言', 'Purpose'],
      summary: ['摘要', '交易摘要', '说明', 'Reference'],
      remark: ['附言', '备注', '说明', '交易附言', 'Remark']
    };
  }

  parseContent(fileBuffer: ArrayBuffer): ParsedDataItem[] {
    const workbook = XLSX.read(fileBuffer, { type: 'array' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
    
    const { headerRowIndex, fieldIndexes, headers } = this.findHeaderRow(worksheet, range);
    
    if (headerRowIndex === -1) {
      throw new Error('未找到有效的表头行');
    }
    
    const preProcessedData: ParsedDataItem[] = [];
    
    for (let row = headerRowIndex + 1; row <= range.e.r; row++) {
      const rowData: any = {};
      
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        const headerName = headers[col] || `col_${col}`;
        rowData[headerName] = cell ? String(cell.v || '') : '';
      }
      
      if (Object.values(rowData).some(value => String(value).trim() !== '')) {
        const parsedItem = this.parseRowData(rowData, fieldIndexes, headers);
        if (parsedItem) {
          preProcessedData.push(parsedItem);
        }
      }
    }
    
    return preProcessedData;
  }

  private findHeaderRow(worksheet: any, range: any): { headerRowIndex: number; fieldIndexes: any; headers: string[] } {
    const fieldMapping = this.getFieldMapping();
    
    for (let row = range.s.r; row <= Math.min(range.s.r + 10, range.e.r); row++) {
      const fieldIndexes: any = {};
      const headers: string[] = [];
      let foundFields = 0;
      
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        const cellValue = cell && cell.v ? String(cell.v).trim() : '';
        headers[col] = cellValue;
      }
      
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellValue = headers[col];
        
        if (cellValue) {
          Object.entries(fieldMapping).forEach(([key, fields]) => {
            if (fields.some(field => cellValue.includes(field))) {
              fieldIndexes[key] = col;
              foundFields++;
            }
          });
        }
      }
      
      if (foundFields >= 2) {
        return { headerRowIndex: row, fieldIndexes, headers };
      }
    }
    
    return { headerRowIndex: -1, fieldIndexes: {}, headers: [] };
  }

  private parseRowData(rowData: any, fieldIndexes: any, headers: string[]): ParsedDataItem | null {
    const getValueByFieldIndex = (fieldKey: string) => {
      const colIndex = fieldIndexes[fieldKey];
      if (colIndex !== undefined) {
        const headerName = headers[colIndex] || `col_${colIndex}`;
        return rowData[headerName] || '';
      }
      return '';
    };
    
    const transactionTime = getValueByFieldIndex('transactionTime');
    const loanFlag = getValueByFieldIndex('loanFlag');
    const opposingUnit = getValueByFieldIndex('opposingUnit');
    const transferOut = getValueByFieldIndex('transferOut');
    const transferIn = getValueByFieldIndex('transferIn');  
    const balance = getValueByFieldIndex('balance');
    
    let billType: 'income' | 'expense' = 'income';
    
    if (loanFlag) {
      const flag = loanFlag.toLowerCase();
      if (flag.includes('借') || flag.includes('支出') || flag.includes('扣款') || flag.includes('出') || flag.includes('往账')) {
        billType = 'expense';
      } else if (flag.includes('贷') || flag.includes('收入') || flag.includes('入账') || flag.includes('进') || flag.includes('来账')) {
        billType = 'income';
      }
    }
    
    if (!loanFlag || loanFlag.trim() === '') {
      const outAmount = CSVUtils.convertAmountStringToNumber(transferOut);
      const inAmount = CSVUtils.convertAmountStringToNumber(transferIn);
      
      if (outAmount && outAmount > 0) {
        billType = 'expense';
      } else if (inAmount && inAmount > 0) {
        billType = 'income';
      }
    }
    
    const billAmount = billType === 'expense' 
      ? CSVUtils.convertAmountStringToNumber(transferOut)
      : CSVUtils.convertAmountStringToNumber(transferIn);
    
    let finalAmount = billAmount;
    if (!finalAmount || finalAmount === 0) {
      const alternativeAmount = billType === 'expense' 
        ? CSVUtils.convertAmountStringToNumber(transferIn)
        : CSVUtils.convertAmountStringToNumber(transferOut);
      if (alternativeAmount && alternativeAmount > 0) {
        finalAmount = alternativeAmount;
        billType = billType === 'expense' ? 'income' : 'expense';
      }
    }
    
    return {
      directlyExtracted: {
        transactionTime: transactionTime || null,
        billType: billType,
        opposingUnit: opposingUnit || null,
        billAmount: finalAmount,
        billBalance: CSVUtils.convertAmountStringToNumber(balance)
      },
      forModel: {
        originData: JSON.stringify(rowData)
      }
    };
  }
} 