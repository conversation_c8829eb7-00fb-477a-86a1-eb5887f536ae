// 工商银行对账单解析策略

import { BankParserStrategy, ParsedDataItem, FieldIndexes } from './types';
import { CSVUtils } from './csv-utils';
import * as XLSX from 'xlsx';

export class ICBCParser implements BankParserStrategy {
  bankName = '工商银行';

  /**
   * 检测是否为工商银行对账单格式
   */
  canParse(fileBuffer: ArrayBuffer): boolean {
    console.log(`[工商银行] 开始检测文件格式，文件大小: ${fileBuffer.byteLength} bytes`);
    
    try {
      // 解析XLSX文件
      console.log(`[工商银行] 开始解析XLSX文件...`);
      const workbook = XLSX.read(fileBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      console.log(`[工商银行] 工作表名称: ${firstSheetName}`);
      
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`[工商银行] 工作表范围: 行${range.s.r}-${range.e.r}, 列${range.s.c}-${range.e.c}`);
      
      // 检查前10行，寻找表头
      const fieldMapping = this.getFieldMapping();
      const requiredFields = [
        ...fieldMapping.transactionTime,
        ...fieldMapping.loanFlag,
        ...fieldMapping.opposingUnit
      ];
      console.log(`[工商银行] 必要字段: ${requiredFields.join(', ')}`);
      
      for (let row = range.s.r; row <= Math.min(range.s.r + 9, range.e.r); row++) {
        let matchedFields = 0;
        const rowFields: string[] = [];
        
        // 检查这一行的所有列
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          
          if (cell && cell.v) {
            const cellValue = String(cell.v).trim();
            rowFields.push(cellValue);
            if (requiredFields.some(field => cellValue.includes(field))) {
              matchedFields++;
            }
          }
        }
        
        console.log(`[工商银行] 第${row + 1}行字段: [${rowFields.join(', ')}], 匹配字段数: ${matchedFields}`);
        
        // 如果匹配到3个以上必要字段，认为是工商银行格式
        if (matchedFields >= 3) {
          console.log(`[工商银行] ✅ 格式检测成功，在第${row + 1}行找到${matchedFields}个匹配字段`);
          return true;
        }
      }
      
      console.log(`[工商银行] ❌ 格式检测失败，未找到足够的匹配字段`);
      return false;
    } catch (error) {
      console.error('[工商银行] 格式检测失败:', error);
      return false;
    }
  }

  /**
   * 获取工商银行字段映射
   */
  getFieldMapping() {
    return {
      transactionTime: ['交易时间'],
      loanFlag: ['借贷标志'],
      opposingUnit: ['对方单位'],
      transferOut: ['转出金额'],
      transferIn: ['转入金额'],
      balance: ['余额'],
      usage: ['用途'],
      summary: ['摘要'],
      remark: ['附言']
    };
  }

  /**
   * 解析工商银行对账单内容
   */
  parseContent(fileBuffer: ArrayBuffer): ParsedDataItem[] {
    console.log(`[工商银行] 开始解析对账单内容，文件大小: ${fileBuffer.byteLength} bytes`);
    
    try {
      // 解析XLSX文件
      console.log(`[工商银行] 解析XLSX文件...`);
      const workbook = XLSX.read(fileBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`[工商银行] 工作表范围: ${range.s.r}-${range.e.r} 行, ${range.s.c}-${range.e.c} 列`);
      
      // 1. 找到表头行
      console.log(`[工商银行] 开始查找表头行...`);
      const { headerRowIndex, fieldIndexes } = this.findHeaderRow(worksheet, range);
      
      if (headerRowIndex === -1) {
        console.error(`[工商银行] ❌ 未找到有效的表头行`);
        throw new Error('未找到有效的表头行');
      }
      
      console.log(`[工商银行] ✅ 找到表头行: 第${headerRowIndex + 1}行`);
      console.log(`[工商银行] 字段索引:`, fieldIndexes);
      
      // 2. 解析数据行
      console.log(`[工商银行] 开始解析数据行，从第${headerRowIndex + 2}行到第${range.e.r + 1}行...`);
      const preProcessedData: ParsedDataItem[] = [];
      let processedCount = 0;
      let skippedCount = 0;
      
      for (let row = headerRowIndex + 1; row <= range.e.r; row++) {
        const dataItem = this.parseDataRow(worksheet, row, fieldIndexes, range);
        if (dataItem) {
          preProcessedData.push(dataItem);
          processedCount++;
          
          // 每处理100行输出一次进度
          if (processedCount % 100 === 0) {
            console.log(`[工商银行] 已处理 ${processedCount} 行数据...`);
          }
        } else {
          skippedCount++;
        }
      }
      
      console.log(`[工商银行] ✅ 解析完成，共解析${preProcessedData.length}条有效数据，跳过${skippedCount}条无效数据`);
      return preProcessedData;
      
    } catch (error) {
      console.error('[工商银行] 解析失败:', error);
      throw error;
    }
  }

  /**
   * 找到表头行和字段索引
   */
  private findHeaderRow(worksheet: XLSX.WorkSheet, range: XLSX.Range): { headerRowIndex: number, fieldIndexes: FieldIndexes } {
    console.log(`[工商银行] 在前10行中查找表头...`);
    
    const fieldMapping = this.getFieldMapping();
    const allFields = [
      ...fieldMapping.transactionTime,
      ...fieldMapping.loanFlag,
      ...fieldMapping.opposingUnit,
      ...fieldMapping.transferOut,
      ...fieldMapping.transferIn,
      ...fieldMapping.balance,
      ...fieldMapping.usage,
      ...fieldMapping.summary,
      ...fieldMapping.remark
    ];
    
    console.log(`[工商银行] 所有可识别字段: ${allFields.join(', ')}`);
    
    // 从上向下遍历行，找到第一个包含5个以上表头字段的行
    for (let row = range.s.r; row <= Math.min(range.s.r + 9, range.e.r); row++) {
      const headers: string[] = [];
      let matchedFieldCount = 0;
      const matchedFields: string[] = [];
      
      // 读取这一行的所有列
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        const cellValue = cell && cell.v ? String(cell.v).trim() : '';
        headers.push(cellValue);
        
        if (cellValue && allFields.some(field => cellValue.includes(field))) {
          matchedFieldCount++;
          matchedFields.push(cellValue);
        }
      }
      
      console.log(`[工商银行] 第${row + 1}行: 总字段${headers.length}个, 匹配字段${matchedFieldCount}个 [${matchedFields.join(', ')}]`);
      
      // 如果匹配到5个以上字段，认为是表头行
      if (matchedFieldCount >= 5) {
        console.log(`[工商银行] ✅ 确定第${row + 1}行为表头行`);
        const fieldIndexes = this.findFieldIndexes(headers);
        return { headerRowIndex: row, fieldIndexes };
      }
    }
    
    console.log(`[工商银行] ❌ 未找到符合条件的表头行`);
    return { headerRowIndex: -1, fieldIndexes: this.getEmptyFieldIndexes() };
  }

  /**
   * 查找字段在表头中的索引
   */
  private findFieldIndexes(headers: string[]): FieldIndexes {
    console.log(`[工商银行] 开始查找字段索引...`);
    
    const fieldMapping = this.getFieldMapping();
    
    const fieldIndexes = {
      transactionTimeIndex: headers.findIndex(h => fieldMapping.transactionTime.some(field => h.includes(field))),
      loanFlagIndex: headers.findIndex(h => fieldMapping.loanFlag.some(field => h.includes(field))),
      opposingUnitIndex: headers.findIndex(h => fieldMapping.opposingUnit.some(field => h.includes(field))),
      transferOutIndex: headers.findIndex(h => fieldMapping.transferOut.some(field => h.includes(field))),
      transferInIndex: headers.findIndex(h => fieldMapping.transferIn.some(field => h.includes(field))),
      balanceIndex: headers.findIndex(h => fieldMapping.balance.some(field => h.includes(field))),
      usageIndex: headers.findIndex(h => fieldMapping.usage.some(field => h.includes(field))),
      summaryIndex: headers.findIndex(h => fieldMapping.summary.some(field => h.includes(field))),
      remarkIndex: headers.findIndex(h => fieldMapping.remark.some(field => h.includes(field)))
    };
    
    // 输出每个字段的索引
    Object.entries(fieldIndexes).forEach(([key, index]) => {
      const fieldName = key.replace('Index', '');
      if (index !== -1) {
        console.log(`[工商银行] ${fieldName}: 列${index + 1} (${headers[index]})`);
      } else {
        console.log(`[工商银行] ${fieldName}: 未找到`);
      }
    });
    
    return fieldIndexes;
  }

  /**
   * 获取空的字段索引
   */
  private getEmptyFieldIndexes(): FieldIndexes {
    return {
      transactionTimeIndex: -1,
      loanFlagIndex: -1,
      opposingUnitIndex: -1,
      transferOutIndex: -1,
      transferInIndex: -1,
      balanceIndex: -1,
      usageIndex: -1,
      summaryIndex: -1,
      remarkIndex: -1
    };
  }

  /**
   * 解析单行数据
   */
  private parseDataRow(worksheet: XLSX.WorkSheet, row: number, fieldIndexes: FieldIndexes, range: XLSX.Range): ParsedDataItem | null {
    // 读取这一行的所有数据
    const rowData: string[] = [];
    let hasData = false;
    
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      const cell = worksheet[cellAddress];
      const cellValue = cell && cell.v ? String(cell.v).trim() : '';
      rowData.push(cellValue);
      
      if (cellValue) {
        hasData = true;
      }
    }
    
    // 如果这一行没有任何数据，跳过
    if (!hasData) {
      console.log(`[工商银行] 第${row + 1}行: 空行，跳过`);
      return null;
    }
    
    // 创建原始数据对象
    const originData: Record<string, string> = {};
    rowData.forEach((value, index) => {
      originData[`col_${index}`] = value;
    });
    
    // 确定账单类型
    let billType = '';
    if (fieldIndexes.loanFlagIndex !== -1 && rowData[fieldIndexes.loanFlagIndex]) {
      const loanFlag = rowData[fieldIndexes.loanFlagIndex];
      billType = loanFlag === '借' ? 'expense' : 'income';
      console.log(`[工商银行] 第${row + 1}行: 借贷标志="${loanFlag}", 账单类型="${billType}"`);
    } else {
      console.log(`[工商银行] 第${row + 1}行: 无法确定账单类型，跳过`);
      return null;
    }
    
    // 确定金额
    let billAmount = null;
    if (billType === 'expense' && fieldIndexes.transferOutIndex !== -1) {
      billAmount = CSVUtils.parseAmount(rowData[fieldIndexes.transferOutIndex]);
      console.log(`[工商银行] 第${row + 1}行: 支出金额=${billAmount}`);
    } else if (billType === 'income' && fieldIndexes.transferInIndex !== -1) {
      billAmount = CSVUtils.parseAmount(rowData[fieldIndexes.transferInIndex]);
      console.log(`[工商银行] 第${row + 1}行: 收入金额=${billAmount}`);
    }
    
    // 转换余额
    let billBalance = null;
    if (fieldIndexes.balanceIndex !== -1) {
      billBalance = CSVUtils.parseAmount(rowData[fieldIndexes.balanceIndex]);
    }
    
    // 提取其他字段
    const transactionTime = fieldIndexes.transactionTimeIndex !== -1 ? rowData[fieldIndexes.transactionTimeIndex] : '';
    const opposingUnit = fieldIndexes.opposingUnitIndex !== -1 ? rowData[fieldIndexes.opposingUnitIndex] : '';
    const usage = fieldIndexes.usageIndex !== -1 ? rowData[fieldIndexes.usageIndex] : '';
    const summary = fieldIndexes.summaryIndex !== -1 ? rowData[fieldIndexes.summaryIndex] : '';
    const remark = fieldIndexes.remarkIndex !== -1 ? rowData[fieldIndexes.remarkIndex] : '';
    
    console.log(`[工商银行] 第${row + 1}行: 交易时间="${transactionTime}", 对方单位="${opposingUnit}", 金额=${billAmount}, 余额=${billBalance}`);
    
    const dataItem: ParsedDataItem = {
      directlyExtracted: {
        transactionTime: transactionTime || null,
        billType: billType as 'income' | 'expense',
        opposingUnit: opposingUnit || null,
        billAmount,
        billBalance
      },
      forModel: {
        originData: JSON.stringify(originData)
      }
    };
    
    return dataItem;
  }
} 