import { NextResponse } from 'next/server';
import { downloadFile } from '@/lib/file-utils';
import { BankParserFactory } from '../bank-statements-parsers';

/**
 * 处理银行对账单解析的步骤
 * @param body 请求体，包含 fileUrl, fileName, bankId
 * @returns NextResponse 解析结果
 */
export async function handleParseBankStatement(body: any): Promise<NextResponse> {
  const { fileUrl, fileName, bankId } = body;
  
  if (!fileUrl || !fileName || !bankId) {
    return NextResponse.json({
      success: false,
      message: "缺少必要参数",
      error: "请提供 fileUrl, fileName 和 bankId"
    }, { status: 400 });
  }

  // 下载文件
  const fileBuffer = await downloadFile(fileUrl, fileName);
  
  // 解析对账单
  const bankStatementData = BankParserFactory.parseContentByBankId(fileBuffer, bankId);
  
  return NextResponse.json({
    success: true,
    data: bankStatementData
  });
} 