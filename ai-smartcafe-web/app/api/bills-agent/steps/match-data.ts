import { NextResponse } from 'next/server';
import { openRouterService } from '@/lib/open-router';

// AI调用统计接口 (从原文件抽取)
interface AICallStats {
  totalCalls: number;
  successCalls: number;
  failedCalls: number;
  failureReasons: Record<string, number>;
  totalPromptLength: number;
  totalResponseTime: number;
  batchProcessingCount: number;
}

/**
 * 处理数据匹配的步骤
 * @param body 请求体，包含 bankStatementData, referenceData, contentAnalysis
 * @returns NextResponse 匹配结果
 */
export async function handleMatchData(body: any): Promise<NextResponse> {
  const { bankStatementData, referenceData, contentAnalysis } = body;
  
  if (!bankStatementData || !referenceData || !contentAnalysis) {
    return NextResponse.json({
      success: false,
      message: "缺少必要参数",
      error: "请提供完整的数据"
    }, { status: 400 });
  }

  try {
    console.log(`\n🔄 开始数据匹配处理`);
    console.log(`📊 银行对账单数量: ${bankStatementData.length}`);
    console.log(`📁 参考文件数量: ${referenceData.length}`);
    
    // 调用核心匹配处理函数
    const result = await processMatchData(bankStatementData, referenceData, contentAnalysis);
    console.log(`✅ 数据匹配处理完成`);
    
    return NextResponse.json({
      success: true,
      message: "数据匹配成功",
      data: result.data,
      meta: result.meta
    });
    
  } catch (error) {
    console.error('数据匹配失败:', error);
    return NextResponse.json({
      success: false,
      message: "数据匹配失败",
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// ==================== 以下是从原文件抽取的所有匹配相关函数 ====================

// 计算字符串相似度 (从原文件抽取)
function calculateSimilarity(str1: string, str2: string): number {
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0) return len2 === 0 ? 1 : 0;
  if (len2 === 0) return 0;
  
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  
  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len1][len2]) / maxLen;
}

// 根据金额匹配参考资料，时间在±1天内 (新增函数)
function findReferenceDataByAmount(bankTransaction: any, allReferenceData: any[], contentAnalysis: any[]): any[] {
  const matchedReferences: any[] = [];
  const targetAmount = bankTransaction.directlyExtracted?.billAmount;
  const transactionTime = new Date(bankTransaction.directlyExtracted?.transactionTime);
  
  if (!targetAmount || isNaN(targetAmount)) {
    console.warn(`⚠️ 银行交易缺少有效金额，跳过金额匹配`);
    return [];
  }
  
  if (isNaN(transactionTime.getTime())) {
    console.warn(`⚠️ 银行交易缺少有效时间，跳过金额匹配`);
    return [];
  }
  
  console.log(`💰 开始金额匹配: 目标金额=${targetAmount}, 交易时间=${transactionTime.toISOString()}`);
  
  for (let fileIndex = 0; fileIndex < allReferenceData.length; fileIndex++) {
    const refFile = allReferenceData[fileIndex];
    const fileAnalysis = contentAnalysis[fileIndex];
    
    if (!refFile.sheets || !fileAnalysis?.sheets) {
      continue;
    }
    
    for (const [sheetName, sheetData] of Object.entries(refFile.sheets)) {
      const sheet = sheetData as any;
      const sheetAnalysis = fileAnalysis.sheets[sheetName];
      
      if (!sheet.data || !Array.isArray(sheet.data)) {
        continue;
      }
      
      const timeFields = sheetAnalysis?.timeRelatedFieldNames || [];
      
      for (let rowIndex = 0; rowIndex < sheet.data.length; rowIndex++) {
        const row = sheet.data[rowIndex];
        
        // 1. 检查金额是否匹配
        let amountMatched = false;
        const amountFields = Object.keys(row).filter(key => 
          key.includes('金额') || key.includes('amount') || key.includes('价格') || 
          key.includes('费用') || key.includes('total') || key.includes('合计') ||
          key.includes('Amount') || key.includes('AMOUNT')
        );
        
        for (const amountField of amountFields) {
          const amountValue = parseFloat(String(row[amountField]).replace(/[^\d.-]/g, ''));
          if (!isNaN(amountValue) && Math.abs(amountValue - targetAmount) < 0.01) {
            amountMatched = true;
            console.log(`💰 金额完全匹配: ${amountValue} (字段: ${amountField})`);
            break;
          }
        }
        
        if (!amountMatched) {
          continue; // 金额不匹配，跳过这行
        }
        
        // 2. 检查时间是否在±1天内
        let timeMatched = false;
        const matchedTimeFields: any[] = [];
        
        for (const timeField of timeFields) {
          if (row[timeField]) {
            try {
              const refTime = new Date(row[timeField]);
              if (!isNaN(refTime.getTime())) {
                const timeDiff = Math.abs(transactionTime.getTime() - refTime.getTime());
                const daysDiff = timeDiff / (1000 * 60 * 60 * 24); // 转换为天数
                
                if (daysDiff <= 1) { // ±1天内
                  timeMatched = true;
                  matchedTimeFields.push({
                    fieldName: timeField,
                    fieldValue: row[timeField],
                    parsedDate: refTime.toISOString(),
                    daysDiff: daysDiff
                  });
                  console.log(`📅 时间匹配: ${refTime.toISOString()} (相差${daysDiff.toFixed(2)}天, 字段: ${timeField})`);
                }
              }
            } catch (error) {
              // 忽略时间解析错误
            }
          }
        }
        
        // 3. 如果金额匹配且时间在范围内，则加入结果
        if (amountMatched && timeMatched) {
          const uniqueId = generateReferenceId({
            fileName: refFile.fileName,
            sheetName: sheetName,
            rowData: row,
            rowIndex: rowIndex
          }, rowIndex);
          
          const matchedRef = {
            fileName: refFile.fileName,
            sheetName: sheetName,
            rowData: row,
            contentType: sheetAnalysis?.description || '未知类型',
            correlation: sheetAnalysis?.correlation || '未知',
            timeFields: timeFields,
            uniqueId: uniqueId,
            rowIndex: rowIndex,
            originalTimes: matchedTimeFields.map(tf => new Date(tf.parsedDate)),
            matchedTimeFields: matchedTimeFields,
            matchType: 'amount', // 标记这是通过金额匹配找到的
            amountFields: amountFields // 记录匹配的金额字段
          };
          
          matchedReferences.push(matchedRef);
          console.log(`✅ 金额+时间匹配成功: ${refFile.fileName}-${sheetName}-${rowIndex}`);
        }
      }
    }
  }
  
  console.log(`💰 金额匹配完成，找到 ${matchedReferences.length} 条匹配的参考资料`);
  return matchedReferences;
}

// 合并时间窗口匹配和金额匹配的参考资料，去重 (新增函数)
function mergeReferenceData(timeWindowRefs: any[], amountMatchRefs: any[]): any[] {
  const uniqueRefs = new Map<string, any>();
  
  // 添加时间窗口匹配的数据
  for (const ref of timeWindowRefs) {
    const key = ref.uniqueId || `${ref.fileName}-${ref.sheetName}-${ref.rowIndex}`;
    if (!uniqueRefs.has(key)) {
      uniqueRefs.set(key, {
        ...ref,
        matchType: 'time'
      });
    }
  }
  
  // 添加金额匹配的数据，如果已存在则合并匹配类型
  for (const ref of amountMatchRefs) {
    const key = ref.uniqueId || `${ref.fileName}-${ref.sheetName}-${ref.rowIndex}`;
    if (uniqueRefs.has(key)) {
      // 已存在，标记为同时通过时间和金额匹配
      const existing = uniqueRefs.get(key);
      existing.matchType = 'time+amount';
    } else {
      // 新的，标记为仅通过金额匹配
      uniqueRefs.set(key, {
        ...ref,
        matchType: 'amount'
      });
    }
  }
  
  const merged = Array.from(uniqueRefs.values());
  console.log(`🔗 合并参考资料: 时间窗口${timeWindowRefs.length}条 + 金额匹配${amountMatchRefs.length}条 = 去重后${merged.length}条`);
  
  // 输出匹配类型统计
  const matchTypeStats = merged.reduce((acc: Record<string, number>, ref) => {
    acc[ref.matchType] = (acc[ref.matchType] || 0) + 1;
    return acc;
  }, {});
  console.log(`📊 匹配类型统计:`, matchTypeStats);
  
  return merged;
}

// 匹配对账单数据与参考资料 (从原文件抽取)
function matchTransactionWithReference(transaction: any, referenceData: any[]): any[] {
  const matches: any[] = [];

  // 提取对账单中的关键信息
  const transactionAmount = transaction.directlyExtracted.billAmount;
  const transactionTime = transaction.directlyExtracted.transactionTime;
  const opposingUnit = transaction.directlyExtracted.opposingUnit;
  
  // 在参考资料中查找可能的匹配
  for (const refFile of referenceData) {
    for (const [sheetName, sheetData] of Object.entries(refFile.sheets || {})) {
      const sheet = sheetData as any;
      if (!sheet.data || !Array.isArray(sheet.data)) continue;
      
      for (const row of sheet.data) {
        let score = 0;
        let maxScore = 0;
        
        // 金额匹配
        const amountFields = Object.keys(row).filter(key => 
          key.includes('金额') || key.includes('amount') || key.includes('价格') || 
          key.includes('费用') || key.includes('total') || key.includes('合计')
        );
        
        for (const field of amountFields) {
          maxScore += 3;
          const value = parseFloat(String(row[field]).replace(/[^\d.-]/g, ''));
          if (!isNaN(value) && Math.abs(value - transactionAmount) < 0.01) {
            score += 3; // 金额完全匹配，高分
          } else if (!isNaN(value) && Math.abs(value - transactionAmount) < transactionAmount * 0.1) {
            score += 1; // 金额接近，低分
          }
        }
        
        // 时间匹配
        const dateFields = Object.keys(row).filter(key => 
          key.includes('时间') || key.includes('日期') || key.includes('date') || key.includes('time')
        );
        
        for (const field of dateFields) {
          maxScore += 2;
          const dateStr = String(row[field]);
          const date = new Date(dateStr);
          const transDate = new Date(transactionTime);
          
          if (!isNaN(date.getTime()) && !isNaN(transDate.getTime())) {
            const timeDiff = Math.abs(date.getTime() - transDate.getTime());
            const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
            
            if (daysDiff < 1) {
              score += 2; // 同一天，高分
            } else if (daysDiff < 7) {
              score += 1; // 一周内，低分
            }
          }
        }
        
        // 文本匹配
        const textFields = Object.keys(row).filter(key => 
          !amountFields.includes(key) && !dateFields.includes(key)
        );
        
        for (const field of textFields) {
          maxScore += 1;
          const value = String(row[field]).toLowerCase();
          const opposingLower = opposingUnit?.toLowerCase() || '';
          
          if (value && opposingLower && (
            value.includes(opposingLower) || 
            opposingLower.includes(value) ||
            calculateSimilarity(value, opposingLower) > 0.7
          )) {
            score += 1;
          }
        }
        
        // 如果匹配度足够高，加入结果
        if (maxScore > 0 && score / maxScore > 0.3) {
          matches.push({
            fileName: refFile.fileName,
            sheetName,
            rowData: row,
            matchScore: score / maxScore,
            contentType: refFile.contentAnalysis?.[sheetName] || '未知'
          });
        }
      }
    }
  }
  
  // 按匹配度排序
  matches.sort((a, b) => b.matchScore - a.matchScore);
  
  return matches.slice(0, 5); // 返回前5个最佳匹配
}

// 将银行对账单数据按小时分组 (从原文件抽取)
function groupBankStatementByHour(bankStatementData: any[]): Record<string, any[]> {
  const groupedData: Record<string, any[]> = {};
  
  for (const transaction of bankStatementData) {
    try {
      const transactionTime = transaction.directlyExtracted?.transactionTime;
      if (!transactionTime) {
        console.warn(`⚠️ 交易记录缺少时间信息，跳过分组:`, transaction);
        continue;
      }
      
      // 解析日期并格式化为 YYYY-MM-DD HH 按小时分组
      const date = new Date(transactionTime);
      if (isNaN(date.getTime())) {
        console.warn(`⚠️ 无效的交易时间格式: ${transactionTime}，跳过分组`);
        continue;
      }
      
      // 格式化为 YYYY-MM-DD-HH 按小时分组
      const hourKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}-${String(date.getHours()).padStart(2, '0')}`;
      
      if (!groupedData[hourKey]) {
        groupedData[hourKey] = [];
      }
      
      groupedData[hourKey].push(transaction);
    } catch (error) {
      console.error(`❌ 处理交易记录时出错:`, error, transaction);
    }
  }
  
  return groupedData;
}

// 生成时间窗口的小时键列表（包含±10分钟缓冲） (从原文件抽取)
function generateTimeWindowHourKeys(targetTime: Date): string[] {
  const keys = new Set<string>();
  
  // 生成±10分钟范围内的时间点
  const startTime = new Date(targetTime.getTime() - 10 * 60 * 1000); // -10分钟
  const endTime = new Date(targetTime.getTime() + 10 * 60 * 1000);   // +10分钟
  
  // 添加开始时间对应的小时
  const startHourKey = `${startTime.getFullYear()}-${String(startTime.getMonth() + 1).padStart(2, '0')}-${String(startTime.getDate()).padStart(2, '0')}-${String(startTime.getHours()).padStart(2, '0')}`;
  keys.add(startHourKey);
  
  // 添加结束时间对应的小时  
  const endHourKey = `${endTime.getFullYear()}-${String(endTime.getMonth() + 1).padStart(2, '0')}-${String(endTime.getDate()).padStart(2, '0')}-${String(endTime.getHours()).padStart(2, '0')}`;
  keys.add(endHourKey);
  
  // 添加目标时间对应的小时
  const targetHourKey = `${targetTime.getFullYear()}-${String(targetTime.getMonth() + 1).padStart(2, '0')}-${String(targetTime.getDate()).padStart(2, '0')}-${String(targetTime.getHours()).padStart(2, '0')}`;
  keys.add(targetHourKey);
  
  return Array.from(keys);
}

// 检查时间是否在±10分钟窗口内 (从原文件抽取)
function isWithinTimeWindow(targetTime: Date, compareTime: Date): boolean {
  const timeDiff = Math.abs(targetTime.getTime() - compareTime.getTime());
  return timeDiff <= 10 * 60 * 1000; // 10分钟 = 600秒 = 600000毫秒
}

// 将参考资料数据按小时分组（考虑±10分钟缓冲） (从原文件抽取)
function groupReferenceDataByHour(referenceData: any[], contentAnalysis: any[]): Record<string, any[]> {
  const groupedData: Record<string, any[]> = {};
  
  for (let fileIndex = 0; fileIndex < referenceData.length; fileIndex++) {
    const refFile = referenceData[fileIndex];
    const fileAnalysis = contentAnalysis[fileIndex];
    
    if (!refFile.sheets || !fileAnalysis?.sheets) {
      console.warn(`⚠️ 文件 ${refFile.fileName} 缺少工作表数据或分析结果，跳过`);
      continue;
    }
    
    for (const [sheetName, sheetData] of Object.entries(refFile.sheets)) {
      const sheet = sheetData as any;
      const sheetAnalysis = fileAnalysis.sheets[sheetName];
      
      if (!sheet.data || !Array.isArray(sheet.data)) {
        console.warn(`⚠️ 工作表 ${sheetName} 缺少数据，跳过`);
        continue;
      }
      
      if (!sheetAnalysis?.timeRelatedFieldNames || !Array.isArray(sheetAnalysis.timeRelatedFieldNames)) {
        console.warn(`⚠️ 工作表 ${sheetName} 缺少时间相关字段分析，跳过`);
        continue;
      }
      
      const timeFields = sheetAnalysis.timeRelatedFieldNames;
      console.log(`📅 处理工作表 ${refFile.fileName}-${sheetName}，时间字段: ${timeFields.join(', ')}，数据行数: ${sheet.data.length}`);
      
      for (let rowIndex = 0; rowIndex < sheet.data.length; rowIndex++) {
        const row = sheet.data[rowIndex];
        const matchedHours = new Set<string>(); // 用于收集该行数据匹配的所有小时键
        const originalTimes: Date[] = []; // 记录原始时间，用于后续精确匹配
        
        // 遍历所有时间相关字段，收集所有可能的时间
        for (const timeField of timeFields) {
          if (row[timeField]) {
            try {
              const dateValue = new Date(row[timeField]);
              if (!isNaN(dateValue.getTime())) {
                originalTimes.push(dateValue);
                // 生成时间窗口的小时键（考虑±10分钟缓冲）
                const hourKeys = generateTimeWindowHourKeys(dateValue);
                hourKeys.forEach(key => matchedHours.add(key));
              }
            } catch (error) {
              // 忽略解析错误，继续处理下一个字段
            }
          }
        }
        
        // 将该行数据添加到所有匹配的小时组中
        for (const hourKey of matchedHours) {
          if (!groupedData[hourKey]) {
            groupedData[hourKey] = [];
          }
          
          // 生成唯一标识符，用于去重
          const uniqueId = `${refFile.fileName}-${sheetName}-${rowIndex}`;
          
          // 检查是否已存在相同的数据（去重）
          const exists = groupedData[hourKey].some(item => item.uniqueId === uniqueId);
          
          if (!exists) {
            groupedData[hourKey].push({
              fileName: refFile.fileName,
              sheetName: sheetName,
              rowData: row,
              contentType: sheetAnalysis.description || '未知类型',
              correlation: sheetAnalysis.correlation || '未知',
              timeFields: timeFields,
              uniqueId: uniqueId, // 用于去重的唯一标识
              rowIndex: rowIndex, // 原始行索引
              originalTimes: originalTimes, // 记录原始时间用于精确匹配
              matchedTimeFields: originalTimes.map(time => ({
                fieldName: timeFields.find((field: string) => {
                  try {
                    const fieldTime = new Date(row[field]);
                    return !isNaN(fieldTime.getTime()) && fieldTime.getTime() === time.getTime();
                  } catch {
                    return false;
                  }
                }) || '',
                fieldValue: row[timeFields.find((field: string) => {
                  try {
                    const fieldTime = new Date(row[field]);
                    return !isNaN(fieldTime.getTime()) && fieldTime.getTime() === time.getTime();
                  } catch {
                    return false;
                  }
                }) || ''] || '',
                parsedDate: time.toISOString()
              }))
            });
          }
        }
      }
    }
  }
  
  // 输出分组统计信息
  console.log(`\n📊 参考资料按小时分组统计:`);
  const sortedHours = Object.keys(groupedData).sort();
  for (const hour of sortedHours) {
    const hourData = groupedData[hour];
    const fileStats = hourData.reduce((acc: Record<string, number>, item) => {
      const key = `${item.fileName}-${item.sheetName}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    console.log(`📅 ${hour}: 总计 ${hourData.length} 条数据`);
    for (const [fileSheet, count] of Object.entries(fileStats)) {
      console.log(`   - ${fileSheet}: ${count} 条`);
    }
  }
  
  return groupedData;
}

// 估算prompt长度（字符数）- 考虑数据压缩后的大小 (从原文件抽取)
function estimatePromptLength(bankData: any[], referenceData: any[]): number {
  const bankDataStr = JSON.stringify(bankData);
  
  // 模拟压缩后的参考资料大小估算
  // 压缩后大约能节省30-50%的空间（根据表头重复度）
  const originalRefDataStr = JSON.stringify(referenceData);
  const estimatedCompressedRefDataSize = Math.floor(originalRefDataStr.length * 0.7); // 保守估计70%大小
  
  const systemPromptLength = 2500; // 系统提示词大约2.5k字符（更新后稍长）
  const estimatedLength = systemPromptLength + bankDataStr.length + estimatedCompressedRefDataSize;
  
  console.log(`📏 长度估算详情: 系统提示词${systemPromptLength}字符 + 银行数据${bankDataStr.length}字符 + 压缩参考资料${estimatedCompressedRefDataSize}字符(原始${originalRefDataStr.length}字符) = ${estimatedLength}字符`);
  
  return estimatedLength;
}

// 验证匹配结果的数据一致性 (从原文件抽取)
function validateMatchResults(matchResults: any[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  for (let i = 0; i < matchResults.length; i++) {
    const result = matchResults[i];
    const transaction = result.transaction;
    
    if (!transaction || !transaction.directlyExtracted) {
      errors.push(`结果${i}: 缺少交易数据`);
      continue;
    }
    
    // 检查匹配结果中的交易数据是否一致
    if (result.matches && Array.isArray(result.matches)) {
      for (let j = 0; j < result.matches.length; j++) {
        const match = result.matches[j];
        
        // 验证匹配置信度
        if (typeof match.confidence !== 'number' || match.confidence < 0 || match.confidence > 1) {
          errors.push(`结果${i}匹配${j}: 无效的置信度值`);
        }
        
        // 验证时间窗口（如果有原始时间）
        if (match.rowData && match.matchedTimeFields) {
          const transactionTime = new Date(transaction.directlyExtracted.transactionTime);
          let hasValidTimeMatch = false;
          
          for (const timeField of match.matchedTimeFields) {
            if (timeField.parsedDate) {
              const refTime = new Date(timeField.parsedDate);
              if (isWithinTimeWindow(transactionTime, refTime)) {
                hasValidTimeMatch = true;
                break;
              }
            }
          }
          
          if (!hasValidTimeMatch && match.confidence > 0.5) {
            errors.push(`结果${i}匹配${j}: 高置信度匹配但时间窗口验证失败`);
          }
        }
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 查找交易记录在原始数据中的索引 (从原文件抽取)
function findOriginalIndexOfTransaction(transaction: any, originalBankStatementData: any[]): number {
  const transactionTime = transaction.directlyExtracted?.transactionTime;
  const billAmount = transaction.directlyExtracted?.billAmount;
  const opposingUnit = transaction.directlyExtracted?.opposingUnit;
  
  // 通过关键字段匹配找到原始索引
  for (let i = 0; i < originalBankStatementData.length; i++) {
    const original = originalBankStatementData[i];
    if (
      original.directlyExtracted?.transactionTime === transactionTime &&
      original.directlyExtracted?.billAmount === billAmount &&
      original.directlyExtracted?.opposingUnit === opposingUnit
    ) {
      return i;
    }
  }
  
  console.warn(`⚠️ 无法找到交易记录的原始索引:`, {
    transactionTime,
    billAmount,
    opposingUnit
  });
  return -1;
}

// 生成银行交易的唯一标识符 (从原文件抽取)
function generateTransactionId(transaction: any): string {
  const time = transaction.directlyExtracted?.transactionTime || '';
  const amount = transaction.directlyExtracted?.billAmount || 0;
  const opposing = transaction.directlyExtracted?.opposingUnit || '';
  
  // 使用时间+金额+对方单位的前10个字符生成ID
  const timeStr = time.replace(/[:\s-]/g, '');
  const amountStr = String(amount).replace('.', '');
  const opposingStr = opposing.substring(0, 10).replace(/[^\w]/g, '');
  
  return `TXN_${timeStr}_${amountStr}_${opposingStr}`;
}

// 生成参考资料的唯一标识符 (从原文件抽取)
function generateReferenceId(refData: any, dataIndex: number): string {
  const fileName = refData.fileName || '';
  const sheetName = refData.sheetName || '';
  const rowIndex = refData.rowIndex || dataIndex;
  
  // 尝试从数据中提取关键信息作为ID的一部分
  const rowData = refData.rowData || {};
  let keyInfo = '';
  
  // 寻找可能的关键字段（金额、时间、ID等）
  for (const [key, value] of Object.entries(rowData)) {
    if (key.includes('金额') || key.includes('amount') || key.includes('时间') || key.includes('date') || key.includes('ID') || key.includes('编号')) {
      keyInfo += String(value).replace(/[^\w]/g, '').substring(0, 8);
    }
  }
  
  return `REF_${fileName.substring(0, 8)}_${sheetName.substring(0, 8)}_${rowIndex}_${keyInfo}`;
}

// 处理过大的数据块，将其分割成更小的块 (从原文件抽取)
async function processLargeDataBlock(bankData: any[], referenceData: any[], hour: string, aiStats: AICallStats): Promise<any[]> {
  console.log(`🔄 处理大数据块: ${hour}，银行数据${bankData.length}条，参考数据${referenceData.length}条`);
  
  const results = [];
  const maxBankDataPerBatch = 10; // 每批最多处理10条银行数据
  
  for (let i = 0; i < bankData.length; i += maxBankDataPerBatch) {
    const batchBankData = bankData.slice(i, i + maxBankDataPerBatch);
    
    // 为每批银行数据筛选相关的参考资料（±10分钟窗口）
    const relevantRefData = [];
    for (const transaction of batchBankData) {
      const transactionTime = new Date(transaction.directlyExtracted.transactionTime);
      for (const refItem of referenceData) {
        if (refItem.originalTimes) {
          for (const refTime of refItem.originalTimes) {
            if (isWithinTimeWindow(transactionTime, refTime)) {
              relevantRefData.push(refItem);
              break; // 避免重复添加同一个参考资料
            }
          }
        }
      }
    }
    
    console.log(`📦 批次 ${Math.floor(i/maxBankDataPerBatch) + 1}: 银行数据${batchBankData.length}条，相关参考数据${relevantRefData.length}条`);
    
    aiStats.batchProcessingCount++;
    
    try {
      const batchResults = await matchDataWithAI(batchBankData, relevantRefData, `${hour}-batch-${Math.floor(i/maxBankDataPerBatch) + 1}`, aiStats);
      results.push(...batchResults);
    } catch (error) {
      console.error(`❌ 批次处理失败:`, error);
      // 统计失败信息
      aiStats.failedCalls++;
      const failureReason = error instanceof Error ? error.message : '批次处理失败';
      aiStats.failureReasons[failureReason] = (aiStats.failureReasons[failureReason] || 0) + 1;
      
      // 出错时返回空匹配
      for (const transaction of batchBankData) {
        results.push({
          transaction,
          matches: [],
          matchDate: hour,
          hasReferenceData: true,
          error: failureReason
        });
      }
    }
  }
  
  return results;
}

// 并发控制函数 - 限制同时执行的Promise数量
async function processWithConcurrency<T>(
  tasks: (() => Promise<T>)[],
  concurrency: number
): Promise<T[]> {
  console.log(`🚀 开始并发处理 ${tasks.length} 个任务，并发度: ${concurrency}`);
  
  const results: T[] = [];
  let index = 0;
  const executing: Promise<void>[] = [];
  
  // 创建工作函数
  const worker = async (): Promise<void> => {
    while (index < tasks.length) {
      const currentIndex = index++;
      const task = tasks[currentIndex];
      
      try {
        console.log(`⏳ 开始处理任务 ${currentIndex + 1}/${tasks.length}`);
        const result = await task();
        results[currentIndex] = result;
        console.log(`✅ 任务 ${currentIndex + 1}/${tasks.length} 完成`);
      } catch (error) {
        console.error(`❌ 任务 ${currentIndex + 1}/${tasks.length} 失败:`, error);
        // 对于失败的任务，我们需要提供一个默认值，这里根据具体类型处理
        results[currentIndex] = { hour: 'error', results: [] } as T;
      }
    }
  };
  
  // 启动并发工作者
  for (let i = 0; i < Math.min(concurrency, tasks.length); i++) {
    executing.push(worker());
  }
  
  // 等待所有工作者完成
  await Promise.all(executing);
  console.log(`🎉 所有 ${tasks.length} 个任务已完成`);
  
  return results;
}

// 处理银行对账单与参考资料的匹配 (从原文件抽取)
async function processMatchData(bankStatementData: any[], referenceData: any[], contentAnalysis: any[]): Promise<{
  success: boolean;
  data: any[];
  meta: {
    totalTransactions: number;
    processedHours: number;
    matchedResults: number;
    aiCallStats: AICallStats;
  };
}> {
  console.log(`\n🔄 开始按小时分块匹配银行对账单与参考资料`);
  console.log(`📊 银行对账单数量: ${bankStatementData.length}`);
  console.log(`📁 参考文件数量: ${referenceData.length}`);

  // 初始化AI调用统计
  const aiStats: AICallStats = {
    totalCalls: 0,
    successCalls: 0,
    failedCalls: 0,
    failureReasons: {},
    totalPromptLength: 0,
    totalResponseTime: 0,
    batchProcessingCount: 0
  };

  // 1. 将银行对账单按小时分块
  const bankDataByHour = groupBankStatementByHour(bankStatementData);
  console.log(`📅 银行对账单分成 ${Object.keys(bankDataByHour).length} 小时的数据`);

  // 2. 将参考资料按小时分块（考虑±10分钟缓冲）
  const referenceDataByHour = groupReferenceDataByHour(referenceData, contentAnalysis);
  console.log(`📅 参考资料分成 ${Object.keys(referenceDataByHour).length} 小时的数据`);

  // 3. 获取所有小时时段，进行匹配
  const allHours = new Set([
    ...Object.keys(bankDataByHour),
    ...Object.keys(referenceDataByHour)
  ]);

  console.log(`📅 需要处理的小时时段: ${Array.from(allHours).sort().join(', ')}`);

  // 创建一个Map来收集匹配结果，key为原始数据索引
  const matchResultsMap = new Map<number, any>();

  // 4. 准备并发处理任务
  const sortedHours = Array.from(allHours).sort();
  console.log(`\n🚀 开始20并发处理 ${sortedHours.length} 个小时时段`);
  
  // 创建处理任务数组
  const processingTasks = sortedHours.map((hour) => {
    return async () => {
      const hourBankData = bankDataByHour[hour] || [];
      const hourReferenceData = referenceDataByHour[hour] || [];

      if (hourBankData.length === 0) {
        console.log(`📅 ${hour}: 无银行对账单数据，跳过`);
        return { hour, results: [] };
      }

      console.log(`\n📅 [并发处理] 时段: ${hour}`);
      console.log(`💰 银行对账单数量: ${hourBankData.length}`);
      console.log(`📎 参考资料数量(时间窗口): ${hourReferenceData.length}`);

      // 5. 增强匹配：为每个银行交易额外进行金额匹配
      const enhancedReferenceData: any[] = [];
      const processedTransactions: any[] = [];

      for (const bankTransaction of hourBankData) {
        console.log(`\n🔍 处理银行交易: ${bankTransaction.directlyExtracted?.transactionTime}, 金额: ${bankTransaction.directlyExtracted?.billAmount}`);
        
        // 获取时间窗口匹配的参考资料（现有逻辑）
        const timeWindowRefs = hourReferenceData.filter(ref => {
          if (!ref.originalTimes || ref.originalTimes.length === 0) return false;
          const transactionTime = new Date(bankTransaction.directlyExtracted.transactionTime);
          return ref.originalTimes.some((refTime: Date) => isWithinTimeWindow(transactionTime, refTime));
        });

        // 进行金额匹配（新增逻辑）
        const amountMatchRefs = findReferenceDataByAmount(bankTransaction, referenceData, contentAnalysis);
        
        // 合并并去重参考资料
        const mergedRefs = mergeReferenceData(timeWindowRefs, amountMatchRefs);
        
        console.log(`📊 交易匹配统计: 时间窗口${timeWindowRefs.length}条, 金额匹配${amountMatchRefs.length}条, 合并后${mergedRefs.length}条`);
        
        // 为每个交易记录其对应的参考资料
        processedTransactions.push({
          transaction: bankTransaction,
          references: mergedRefs
        });
        
        // 将参考资料添加到总的参考资料池中
        for (const ref of mergedRefs) {
          const exists = enhancedReferenceData.find(existing => 
            existing.uniqueId === ref.uniqueId
          );
          if (!exists) {
            enhancedReferenceData.push(ref);
          }
        }
      }

      console.log(`🎯 增强匹配完成: 总参考资料${enhancedReferenceData.length}条`);

      // 估算prompt长度，控制在150k tokens内
      const estimatedPromptLength = estimatePromptLength(hourBankData, enhancedReferenceData);
      console.log(`📏 预估prompt长度: ${estimatedPromptLength} 字符`);
      
      if (estimatedPromptLength > 150000) {
        console.log(`⚠️ prompt过长，需要进一步分块处理`);
        // 如果数据过多，按更小的块处理
        const hourMatches = await processLargeDataBlock(hourBankData, enhancedReferenceData, hour, aiStats);
        return { hour, results: hourMatches };
      }

      if (enhancedReferenceData.length === 0) {
        console.log(`📅 ${hour}: 无参考资料数据，返回空匹配`);
        // 没有参考资料的情况下，返回空匹配
        const emptyMatches = hourBankData.map(transaction => ({
          transaction,
          matches: [],
          matchDate: hour,
          hasReferenceData: false
        }));
        return { hour, results: emptyMatches };
      }

      // 6. 调用大模型进行匹配（使用增强的参考资料）
      try {
        const hourMatches = await matchDataWithAI(hourBankData, enhancedReferenceData, hour, aiStats);
        console.log(`✅ [并发处理] ${hour} 匹配完成，结果: ${hourMatches.length} 条`);
        return { hour, results: hourMatches };
      } catch (error) {
        console.error(`❌ [并发处理] ${hour} 匹配失败:`, error);
        // 统计失败信息
        aiStats.failedCalls++;
        const failureReason = error instanceof Error ? error.message : '匹配失败';
        aiStats.failureReasons[failureReason] = (aiStats.failureReasons[failureReason] || 0) + 1;
        
        // 出错时返回空匹配
        const errorMatches = hourBankData.map(transaction => ({
          transaction,
          matches: [],
          matchDate: hour,
          hasReferenceData: true,
          error: failureReason
        }));
        return { hour, results: errorMatches };
      }
    };
  });

  // 执行20并发处理
  const startTime = Date.now();
  const hourResults = await processWithConcurrency(processingTasks, 20);
  const endTime = Date.now();
  
  console.log(`\n🎉 20并发处理完成，总耗时: ${((endTime - startTime) / 1000).toFixed(2)} 秒`);

     // 6. 汇总所有处理结果
   for (const hourResult of hourResults) {
     if (hourResult && hourResult.results) {
       for (const match of hourResult.results) {
         const originalIndex = findOriginalIndexOfTransaction(match.transaction, bankStatementData);
         if (originalIndex !== -1) {
           matchResultsMap.set(originalIndex, match);
         }
       }
     }
   }

  // 7. 按原始顺序重新组装结果
  const matchResults = [];
  for (let i = 0; i < bankStatementData.length; i++) {
    const result = matchResultsMap.get(i);
    if (result) {
      matchResults.push(result);
    } else {
      // 如果某条记录没有被处理到，添加默认结果
      console.warn(`⚠️ 交易记录 ${i} 未被处理，添加默认结果`);
      matchResults.push({
        transaction: bankStatementData[i],
        matches: [],
        matchDate: 'unknown',
        hasReferenceData: false,
        error: '未被处理'
      });
    }
  }

  console.log(`✅ 匹配完成，总共处理 ${matchResults.length} 条交易记录`);

  // 8. 数据一致性验证
  const validationResult = validateMatchResults(matchResults);
  if (!validationResult.isValid) {
    console.error(`❌ 数据一致性验证失败:`, validationResult.errors);
    throw new Error(`数据一致性验证失败: ${validationResult.errors.join('; ')}`);
  }

  // 输出最终统计信息
  console.log(`\n📊 AI调用全局统计:`);
  console.log(`🤖 总调用次数: ${aiStats.totalCalls}`);
  console.log(`✅ 成功次数: ${aiStats.successCalls}`);
  console.log(`❌ 失败次数: ${aiStats.failedCalls}`);
  console.log(`📏 总prompt长度: ${aiStats.totalPromptLength.toLocaleString()} 字符`);
  console.log(`⏱️ 总响应时间: ${(aiStats.totalResponseTime / 1000).toFixed(2)} 秒`);
  console.log(`📦 批次处理次数: ${aiStats.batchProcessingCount}`);
  
  if (aiStats.failedCalls > 0) {
    console.log(`📋 失败原因统计:`);
    for (const [reason, count] of Object.entries(aiStats.failureReasons)) {
      console.log(`   - ${reason}: ${count} 次`);
    }
  }

  const successRate = aiStats.totalCalls > 0 ? (aiStats.successCalls / aiStats.totalCalls * 100).toFixed(1) : '0';
  console.log(`📈 成功率: ${successRate}%`);

  return {
    success: true,
    data: matchResults,
    meta: {
      totalTransactions: bankStatementData.length,
      processedHours: Array.from(allHours).length,
      matchedResults: matchResults.length,
      aiCallStats: aiStats
    }
  };
}

// 使用大模型匹配同一时间段的银行对账单与参考资料（支持±10分钟时间窗口） (从原文件抽取)
async function matchDataWithAI(hourBankData: any[], hourReferenceData: any[], hour: string, aiStats: AICallStats): Promise<any[]> {
  console.log(`🤖 开始AI匹配 ${hour} 的数据`);
  console.log(`💰 银行数据: ${hourBankData.length} 条`);
  console.log(`📎 参考数据: ${hourReferenceData.length} 条`);
  
  const startTime = Date.now();
  
  try {
    // 统计此次调用
    aiStats.totalCalls++;
    
    // 为银行交易和参考资料生成唯一ID
    const bankDataWithIds = hourBankData.map((transaction, index) => {
      const uniqueId = generateTransactionId(transaction);
      console.log(`🔑 银行交易 ${index}: ID=${uniqueId}, 时间=${transaction.directlyExtracted?.transactionTime}, 金额=${transaction.directlyExtracted?.billAmount}`);
      return {
        ...transaction,
        uniqueId,
        originalIndex: index
      };
    });
    
    const referenceDataWithIds = hourReferenceData.map((refData, index) => {
      const uniqueId = generateReferenceId(refData, index);
      console.log(`🔑 参考资料 ${index}: ID=${uniqueId}, 文件=${refData.fileName}, 表=${refData.sheetName}`);
      return {
        ...refData,
        uniqueId,
        originalIndex: index
      };
    });
    
    // 构建系统提示词
    const systemPrompt = `你是一个专业的财务数据匹配专家，专门负责将银行对账单与参考资料进行匹配。

你的任务是：
1. 分析银行对账单交易记录与参考资料数据
2. 找出每条银行交易记录对应的参考资料（如果有的话）
3. 评估匹配的可信度

匹配规则（已通过算法预筛选）：
1. 优先根据金额进行匹配（完全相等或非常接近）
2. 结合时间信息进行验证：
   - 对于 matchType="time" 的参考资料：时间在±10分钟内
   - 对于 matchType="amount" 的参考资料：金额完全匹配且时间在±1天内
   - 对于 matchType="time+amount" 的参考资料：同时满足上述两个条件（最优匹配）
3. 参考对方单位名称、备注、摘要等文本信息
4. 综合评估匹配可信度（0-1之间，1表示完全确信）

特别注意：
- 参考资料已经通过算法预筛选，包含 matchType 字段标识匹配类型
- matchType="time+amount" 的参考资料应该获得最高优先级和置信度
- matchType="amount" 的参考资料虽然时间跨度较大（±1天），但金额精确匹配，也应重点考虑
- matchType="time" 的参考资料时间最接近，但需要验证其他字段

# 数据格式说明：
- 银行交易数据包含 uniqueId 字段作为唯一标识
- 参考资料数据包含 uniqueId 字段作为唯一标识
- 参考资料包含 matchType 字段，表示预筛选的匹配类型：
  - "time": 通过±10分钟时间窗口匹配
  - "amount": 通过金额完全匹配且时间在±1天内
  - "time+amount": 同时满足时间窗口和金额匹配（最优）

***重要提示：请只返回JSON数据，不要添加任何解释性文字、说明文字或markdown标记。直接以"{"开头，以"}"结尾。***

请严格按照以下JSON格式返回结果：
{
  "matches": [
    {
      "transactionId": "TXN_20250531231237_1936271_上海旺鼎阁",
      "referenceMatches": [
        {
          "referenceId": "REF_付款对应_付款列表_0_157831",
          "confidence": 0.95,
          "matchReasons": ["金额完全匹配", "时间+金额双重匹配", "对方单位名称相似"]
        }
      ]
    }
  ]
}

***再次强调：不要在JSON前后添加任何文字、说明或markdown格式。只返回纯JSON数据。***

字段说明：
- transactionId: 银行交易记录的唯一ID（使用uniqueId字段）
- referenceId: 参考资料的唯一ID（使用uniqueId字段）
- confidence: 匹配可信度（0-1）
- matchReasons: 匹配原因列表（可参考matchType信息）
- 如果某条交易找不到匹配的参考资料，referenceMatches 为空数组
- 一条交易可能匹配多条参考资料，按可信度排序`;

    // 构建用户消息 - 使用唯一ID
    const bankDataForPrompt = bankDataWithIds.map((transaction: any) => ({
      uniqueId: transaction.uniqueId,
      transactionTime: transaction.directlyExtracted.transactionTime,
      billType: transaction.directlyExtracted.billType,
      billAmount: transaction.directlyExtracted.billAmount,
      opposingUnit: transaction.directlyExtracted.opposingUnit,
      原始数据: JSON.parse(transaction.forModel.originData)
    }));

    // 构建参考资料数据 - 使用唯一ID，简化数据结构
    const referenceDataForPrompt = referenceDataWithIds.map((ref: any) => ({
      uniqueId: ref.uniqueId,
      fileName: ref.fileName,
      sheetName: ref.sheetName,
      contentType: ref.contentType,
      correlation: ref.correlation,
      timeFields: ref.timeFields || [],
      rowData: ref.rowData,
      originalTimes: ref.originalTimes?.map((time: Date) => time.toISOString()) || []
    }));

    const userContent = `时间段: ${hour}

银行对账单数据 (${hourBankData.length} 条):
${JSON.stringify(bankDataForPrompt, null, 2)}

参考资料数据 (${hourReferenceData.length} 条):
${JSON.stringify(referenceDataForPrompt, null, 2)}`;

    console.log(`📤 准备发送匹配请求，内容长度: ${userContent.length} 字符`);
    console.log(`📊 数据统计:`);
    console.log(`   - 银行对账单: ${bankDataWithIds.length} 条`);
    console.log(`   - 参考资料: ${referenceDataWithIds.length} 条`);
    console.log(`   - 用户消息总长度: ${userContent.length}字符`);
    
    aiStats.totalPromptLength += userContent.length;

    // 调用AI进行匹配
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      {
        role: 'user' as const,
        content: userContent + '\n\n请注意：你的回复必须是有效的JSON格式，不要包含任何解释文字。直接以{开头，以}结尾。'
      }
    ];

    const response = await openRouterService.call(messages, 'anthropic/claude-sonnet-4', `AI匹配 ${hour} 的数据`);
    const content = response.choices[0].message.content;

    if (!content) {
      throw new Error('AI返回内容为空');
    }

    // 清理和解析响应
    let cleanContent = content.trim();
    
    // 移除可能的 Markdown 代码块标记
    if (cleanContent.includes('```')) {
      cleanContent = cleanContent.replace(/```json/g, '').replace(/```/g, '').trim();
    }
    
    // 尝试提取JSON部分：找到第一个{和最后一个}之间的内容
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');
    
    if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
      console.log(`🧹 提取JSON部分: 从位置${firstBrace}到${lastBrace + 1}`);
    } else {
      console.log(`⚠️ 未找到有效的JSON括号结构，尝试直接解析`);
    }
    
    console.log(`🔍 最终用于解析的内容:`, cleanContent);

    let matchResult;
    try {
      matchResult = JSON.parse(cleanContent);
    } catch (parseError) {
      console.error(`❌ 第一次JSON解析失败，尝试更激进的清理方法:`, parseError);
      
      // 如果解析失败，尝试更激进的清理方法
      // 1. 移除所有换行符和多余空格
      let aggressiveClean = content.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
      
      // 2. 查找JSON模式
      const jsonMatch = aggressiveClean.match(/\{.*"matches".*\}/);
      if (jsonMatch) {
        console.log(`🔧 通过正则表达式找到JSON片段`);
        aggressiveClean = jsonMatch[0];
      } else {
        // 3. 如果还是找不到，尝试移除所有非JSON字符
        const startIdx = aggressiveClean.indexOf('{"matches"');
        if (startIdx === -1) {
          // 查找任何以{开头的JSON结构
          const jsonStart = aggressiveClean.indexOf('{');
          if (jsonStart !== -1) {
            aggressiveClean = aggressiveClean.substring(jsonStart);
          }
        } else {
          aggressiveClean = aggressiveClean.substring(startIdx);
        }
      }
      
      console.log(`🔧 激进清理后的内容:`, aggressiveClean);
      
      try {
        matchResult = JSON.parse(aggressiveClean);
        console.log(`✅ 激进清理后JSON解析成功`);
      } catch (secondParseError) {
        console.error(`❌ 激进清理后仍然解析失败:`, secondParseError);
        throw new Error(`AI返回的内容无法解析为JSON格式: ${parseError instanceof Error ? parseError.message : '未知错误'}`);
      }
    }
    
    if (!matchResult.matches || !Array.isArray(matchResult.matches)) {
      throw new Error('AI返回的匹配结果格式不正确');
    }

    console.log(`✅ AI匹配完成，找到 ${matchResult.matches.length} 个匹配结果`);
    
    // 记录成功统计
    aiStats.successCalls++;
    const endTime = Date.now();
    aiStats.totalResponseTime += (endTime - startTime);

    // 创建ID到数据的映射表，用于快速查找
    const bankDataMap = new Map();
    bankDataWithIds.forEach(transaction => {
      bankDataMap.set(transaction.uniqueId, transaction);
    });
    
    const referenceDataMap = new Map();
    referenceDataWithIds.forEach(refData => {
      referenceDataMap.set(refData.uniqueId, refData);
    });
    
    console.log(`🗺️ 创建映射表: 银行数据${bankDataMap.size}条, 参考数据${referenceDataMap.size}条`);

    // 转换结果格式 - 使用唯一ID进行绑定
    const results = [];
    
    // 为每个银行交易创建结果，确保顺序不丢失
    for (const bankTransaction of bankDataWithIds) {
      const transactionId = bankTransaction.uniqueId;
      const matchInfo = matchResult.matches.find((m: any) => m.transactionId === transactionId);
      
      let matches = [];
      if (matchInfo && matchInfo.referenceMatches && Array.isArray(matchInfo.referenceMatches)) {
        for (const refMatch of matchInfo.referenceMatches) {
          const refData = referenceDataMap.get(refMatch.referenceId);
          if (refData) {
            matches.push({
              fileName: refData.fileName || '未知文件',
              sheetName: refData.sheetName || '未知工作表',
              rowData: refData.rowData || {},
              contentType: refData.contentType || '未知类型',
              matchScore: refMatch.confidence || 0,
              matchReasons: refMatch.matchReasons || [],
              confidence: refMatch.confidence || 0
            });
          } else {
            console.warn(`⚠️ 无法找到参考资料ID: ${refMatch.referenceId}`);
          }
        }
      }

      console.log(`🔍 绑定交易记录 ${transactionId}:`);
      console.log(`- 交易时间: ${bankTransaction.directlyExtracted?.transactionTime}`);
      console.log(`- 交易金额: ${bankTransaction.directlyExtracted?.billAmount}`);
      console.log(`- 对方单位: ${bankTransaction.directlyExtracted?.opposingUnit}`);
      console.log(`- 匹配结果数量: ${matches.length}`);
      if (matches.length > 0) {
        console.log(`- 匹配的参考资料: ${matches.map((m: any) => `${m.fileName}-${m.sheetName}`).join(', ')}`);
        matches.forEach((match, idx) => {
          console.log(`  ${idx + 1}. 置信度: ${(match.confidence * 100).toFixed(1)}%, 原因: ${match.matchReasons.join(', ')}`);
        });
      }

      results.push({
        transaction: bankTransaction, // 使用带有uniqueId的完整数据
        matches,
        matchDate: hour,
        hasReferenceData: true,
        aiMatched: true
      });
    }

    console.log(`✅ 成功绑定 ${results.length} 条交易记录`);
    return results;

  } catch (error) {
    console.error(`❌ AI匹配失败:`, error);
    
    // 记录失败统计
    aiStats.failedCalls++;
    const endTime = Date.now();
    aiStats.totalResponseTime += (endTime - startTime);
    
    const failureReason = error instanceof Error ? error.message : '未知错误';
    aiStats.failureReasons[failureReason] = (aiStats.failureReasons[failureReason] || 0) + 1;
    
    throw error;
  }
} 