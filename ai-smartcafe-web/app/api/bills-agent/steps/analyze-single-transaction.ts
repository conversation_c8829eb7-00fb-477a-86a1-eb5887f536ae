import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { openRouterService } from '@/lib/open-router';

// 生成系统提示词的方法 (从原文件抽取)
async function generateSystemPrompt(connection: mysql.Connection, apCompanyId: string): Promise<string> {
  // 从数据库获取基础数据（添加 ap_company_id 筛选）
  let storesData: Array<{id: number; company_name: string; store_name: string}> = [];
  let incomeTypesData: Array<{id: number; income_type: string}> = [];
  let incomeSourcesData: Array<{id: number; income_type: string; code: string; store_id: number}> = [];
  let expenseTypesData: Array<{id: number; name: string; notes: string | null; subtypes: Array<{id: number; name: string; notes: string | null}>}> = [];
  let knowledgeBaseData: Array<{ type: number; content: string }> = [];

  // 获取门店信息（添加 ap_company_id 筛选）
  const [stores] = await connection.execute('SELECT * FROM stores WHERE state = 1 AND ap_company_id = ?', [apCompanyId]);
  storesData = stores as Array<{id: number; company_name: string; store_name: string}>;

  // 获取收入类型（添加 ap_company_id 筛选）
  const [incomeTypes] = await connection.execute('SELECT * FROM income_types WHERE ap_company_id = ?', [apCompanyId]);
  incomeTypesData = incomeTypes as Array<{id: number; income_type: string}>;

  // 获取收入来源（添加 ap_company_id 筛选）
  const [incomeSources] = await connection.execute('SELECT * FROM income_sources WHERE ap_company_id = ?', [apCompanyId]);
  incomeSourcesData = incomeSources as Array<{id: number; income_type: string; code: string; store_id: number}>;

  // 获取支出类型（添加 ap_company_id 筛选）
  const [expenseTypes] = await connection.execute(`
    SELECT 
      et.id,
      et.name,
      et.notes,
      es.id as subtype_id,
      es.name as subtype_name,
      es.notes as subtype_notes
    FROM expense_types et
    LEFT JOIN expense_subtypes es ON et.id = es.parent_id AND es.ap_company_id = ?
    WHERE et.ap_company_id = ?
    ORDER BY et.id, es.id
  `, [apCompanyId, apCompanyId]);

  // 获取知识库数据（添加 ap_company_id 筛选）
  const [knowledgeBase] = await connection.execute('SELECT * FROM knowledge_base WHERE del_flag = 0 AND ap_company_id = ?', [apCompanyId]);
  knowledgeBaseData = knowledgeBase as Array<{ type: number; content: string }>;

  // 重组支出类型数据结构
  expenseTypesData = (expenseTypes as any[]).reduce((acc: Array<{id: number; name: string; notes: string | null; subtypes: Array<{id: number; name: string; notes: string | null}>}>, curr) => {
    const existingType = acc.find(type => type.id === curr.id);

    if (!existingType) {
      acc.push({
        id: curr.id,
        name: curr.name,
        notes: curr.notes,
        subtypes: curr.subtype_id ? [{
          id: curr.subtype_id,
          name: curr.subtype_name,
          notes: curr.subtype_notes
        }] : []
      });
    } else if (curr.subtype_id) {
      existingType.subtypes.push({
        id: curr.subtype_id,
        name: curr.subtype_name,
        notes: curr.subtype_notes
      });
    }

    return acc;
  }, []);

  // 准备知识库数据
  const businessRules: Array<{ type: number; content: string }> = knowledgeBaseData.filter(kb => kb.type === 1);
  const specialDescriptions: Array<{ type: number; content: string }> = knowledgeBaseData.filter(kb => kb.type === 2);
  
  const systemPrompt =
  `你是一个经验十分丰富的会计师，下面我将给你一些银行对账单数据，你需要跟我给出的规则和信息对账单数据进行分析；

      # 基础数据信息:
      <门店信息>
      ${storesData.map(store => `- 门店ID: ${store.id}, 公司名称: ${store.company_name}, 门店名称: ${store.store_name}`).join('\n')}
      <门店信息/>
      
      <收入类型信息>
      ${incomeTypesData.map(type => `- 收入类型ID: ${type.id}, 类型名称: ${type.income_type}`).join('\n')}
      <收入类型信息/>
      
      <收入来源信息>
      ${incomeSourcesData.map(source => {
        const store = storesData.find(s => s.id === source.store_id);
        const storeName = store ? store.store_name : '未知门店';
        return `- 收入来源ID: ${source.id}, 收入类型: ${source.income_type}, 代号: ${source.code}, 所属门店: ${storeName}`;
      }).join('\n')}
      <收入来源信息/>
      
      <支出类型信息>
      ${expenseTypesData.map(type => {
        let result = `- 支出类型ID: ${type.id}, 类型名称: ${type.name}`;
        if (type.subtypes && type.subtypes.length > 0) {
          result += `\n  二级类型:\n${type.subtypes.map((subtype) => 
            `  - 二级类型ID: ${subtype.id}, 类型名称: ${subtype.name}`).join('\n')}`;
        }
        return result;
      }).join('\n')}
      <支出类型信息/>
      
      <业务规则知识库>
      ${businessRules.map(kb => `- ${kb.content}`).join('\n')}
      <业务规则知识库/>
      
      <特殊描述知识库>
      ${specialDescriptions.map(kb => `- ${kb.content}`).join('\n')}
      <特殊描述知识库/>
      
      <返回示例>
      {
        "data": [
          {
            "storeId": {"value": 1, "confidence": 1},
            "incomeSourceId": {"value": 10, "confidence": 1},
            "incomeExpenseTypeId": {"value": "1-9", "confidence": 1},
            "isReimburse": {"value": 0, "confidence": 1},
            "needSplit": [{"storeId": 1, "splitAmount": 123.45}, {"storeId": 2, "splitAmount": 234.56}]
          }
        ]
      }
      <返回示例/>

      # 解析规则如下：

      ## 核心原则
      「严格遵守：只有在非常确信判断某一字段的值的情况下，才可以给出结果。如果不能完全肯定，请将该字段赋值为 null！我们宁愿接受多一些 null 值，但绝对不能接受错误的判断！」

      ## 特别重要的判断原则
      **对于incomeExpenseTypeId字段的收入类型判断，必须严格遵守以下规则：**
      1. **企业标识优先原则**：只要opposingUnit中包含"有限公司"、"股份公司"、"集团"、"企业"、"公司"等企业标识词汇，无论后面跟什么内容，都必须归类为"分公司转入收入"
      2. **一次匹配原则**：一旦匹配到企业标识，立即停止后续判断，不要被"分公司"、"门店"等词汇干扰
      3. **绝对执行**：这个规则没有例外，必须100%执行

      ## 优先级规则
      1. "业务规则知识库"和"特殊描述知识库"中的规则优先级最高，必须严格遵守
      2. 如果知识库中明确表示某种情况"不匹配"/"不判断"，则将对应字段置为 null
      3. 无法通过已有知识判断的字段，赋值 null；所有赋值为 null 的字段 confidence=0

      ## 置信度评估
      - confidence=1：非常确信的判断结果
      - confidence=0：不确信或无法判断的结果

      ## 字段判断规则

      ### storeId 判断规则
      **步骤1**：聚合付款分账判断（仅适用于收入类型）
      - **前提条件**：billType 为 "income"
      - **判断标准**：如果分析发现这是一笔聚合付款，应该分配给多家门店，则：
        * 将 storeId 赋值为 null
        * 在 needSplit 数组中列出各门店的分账明细
        * needSplit 格式：[{"storeId": 门店ID, "splitAmount": 分账金额}, ...]
      - **判断依据**：
        * 匹配到多条参考资料
        * 用途/摘要/附言中有明确的“聚合付款”等相应字段提示
        * 用途/摘要/附言中“聚合支付”的金额与账单的交易金额相符
        * 账单的交易金额与多条参考资料的金额总和相符
      
      **步骤2**：直接匹配
      - 根据 opposingUnit 从"门店信息"中查找完全匹配的公司名称或门店名称
      
      **步骤3**：关键词推断（仅在步骤2无结果时执行）
      - 在用途/摘要/附言中查找门店相关关键词（如"七宝"对应B3咖啡七宝店）
      - 在用途/摘要/附言中查找"收入来源信息"中的代号（如"DCKJ05922620431201"）
      
      **步骤4**：无法判断
      - 如果以上步骤都无法确定门店，storeId 赋值为 null，needSplit 为空数组

      ### incomeSourceId 判断规则
      **前提条件**：只有当 billType 为 "income" 时才进行判断
      
      **判断步骤**：
      - 根据 opposingUnit/用途/摘要/附言 中的信息，通过"收入来源信息"进行精确匹配
      - 优先匹配代号（code）字段
      - 无法匹配时赋值为 null

      ### incomeExpenseTypeId 判断规则

      #### 收入类型判断（billType = "income"）
      
      **【重要】严格按以下顺序执行，一旦匹配即停止后续判断：**
      
      **第一优先级：公司转账收入识别**
      - **必须条件**：opposingUnit 包含以下任一企业标识词汇：
        * "有限公司" 或 "有限责任公司"
        * "股份公司" 或 "股份有限公司" 
        * "集团公司" 或 "集团"
        * "企业" 或 "公司"
      - **判断规则**：无论 opposingUnit 后面跟什么内容（如"分公司"、"门店"等），只要包含上述企业标识，就归类为"分公司转入收入"
      - **具体示例**：
        * "上海旺鼎阁餐饮管理有限公司七宝分公司" → 分公司转入收入 ✓
        * "北京XX科技有限公司" → 分公司转入收入 ✓
        * "深圳YY集团" → 分公司转入收入 ✓
      
      **第二优先级：门店营业收入识别**
      - **前提条件**：opposingUnit 不包含任何企业标识词汇
      - **判断条件**：opposingUnit/用途/摘要/附言 中包含以下特征：
        * 纯数字代号（如：DCKJ05922620431201）
        * 第三方支付平台标识（如：支付宝、微信支付、银联、钱袋宝等）
        * 外卖平台标识（如：美团、饿了么、百度外卖等）
        * 门店简称或代码（无公司后缀）
      - **结果**：归类为"门店收入"
      
      **第三优先级：无法判断**
      - 当以上条件都不满足时，incomeExpenseTypeId 赋值为 null

      #### 支出类型判断（billType = "expense"）
      - 根据"支出类型信息"进行匹配
      - 返回格式："{一级类型ID}-{二级类型ID}"（如："1-9"）
      - 如果只有一级类型，返回一级类型ID
      - 无法判断时赋值为 null

      ### isReimburse 判断规则
      **简化决策**：
      - 如果 billType = "expense" 且 opposingUnit 是个人姓名（非公司名称），则 isReimburse = 1
      - 其他所有情况，isReimburse = 0
      
      **报销后处理**：
      当 isReimburse = 1 时：
      - 重新评估 storeId 和 incomeExpenseTypeId
      - 结合"业务规则知识库"和"特殊描述知识库"进行判断
      - 如果无法确定，保持 null 值
      - 不要将报销归类为"人力成本"

      ## 输出要求
      严格按照"返回示例"的JSON格式返回数据，不要添加任何解释性文字或说明。
      
      **字段说明**：
      - needSplit：分账明细数组，仅在需要多门店分账时填写
        * 如果是单一门店收入，needSplit 为空数组 []
        * 如果是聚合付款需要分账，填写各门店的分账明细
        * 各门店的 splitAmount 总和应等于交易总金额
  `

  return systemPrompt;
}

// 处理单条数据的函数 (从原文件抽取)
async function processSingleDataItem(dataItem: any, systemPrompt: string, referenceContext?: string, storesData?: Array<{id: number; company_name: string; store_name: string}>): Promise<any> {
  console.log(`\n🔄 开始处理单条对账单数据`);
  console.log(`💰 交易金额: ${dataItem.directlyExtracted.billAmount}`);
  console.log(`🏢 对方单位: ${dataItem.directlyExtracted.opposingUnit}`);
  console.log(`📅 交易时间: ${dataItem.directlyExtracted.transactionTime}`);
  console.log(`📊 交易类型: ${dataItem.directlyExtracted.billType}`);
  
  try {
    // 构建用户消息内容
    let userContent = JSON.stringify([JSON.parse(dataItem.forModel.originData)]);
    
    // 如果有参考资料上下文，添加到用户消息中
    if (referenceContext) {
      console.log(`📎 包含参考资料上下文，长度: ${referenceContext.length} 字符`);
      userContent = `银行对账单数据：\n${userContent}\n\n参考资料上下文：\n${referenceContext}`;
    } else {
      console.log(`📝 无参考资料上下文，仅使用银行对账单数据`);
    }

    // 构建消息数组
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      {
        role: 'user' as const,
        content: userContent
      }
    ];
    
    console.log(`📤 准备发送单条数据分析请求...`);
    console.log(`- 系统提示词长度: ${systemPrompt.length} 字符`);
    console.log(`- 用户数据长度: ${userContent.length} 字符`);
    
    // 调用OpenRouter API
    const response = await openRouterService.call(messages, 'anthropic/claude-sonnet-4', `分析单条对账单: ${dataItem.directlyExtracted.opposingUnit}`);
    const content = response.choices[0].message.content;
    
    console.log(`📥 收到AI分析响应`);
    console.log(`📝 响应内容长度: ${content?.length || 0} 字符`);
    console.log(`📄 AI完整响应内容:`);
    console.log(content || '无内容');
    
    if (!content) {
      throw new Error('模型返回内容为空');
    }

    // 清理响应内容，移除可能的 Markdown 代码块标记
    let cleanContent = content.trim();
    if (cleanContent.includes('```')) {
      console.log(`🧹 清理响应内容中的Markdown代码块标记`);
      cleanContent = cleanContent.replace(/```json/g, '').replace(/```/g, '');
    }

    console.log(`🔍 准备解析JSON响应...`);
    console.log(`🧹 清理后的内容:`);
    console.log(cleanContent);
    
    const parsedContent = JSON.parse(cleanContent);
    
    if (!parsedContent || !parsedContent.data || !Array.isArray(parsedContent.data)) {
      throw new Error('模型返回的数据格式不正确');
    }
    
    const modelResult = parsedContent.data[0] || {};
    console.log(`✅ 成功解析AI响应，获得分析结果:`);
    console.log(JSON.stringify(modelResult, null, 2));
    
    // 创建默认的字段值
    const defaultField = { value: null, confidence: 0 };
    
    // 处理 storeId
    let storeId = modelResult.storeId || defaultField;
    if (typeof storeId.value === 'string') {
      const parsedValue = parseInt(storeId.value);
      storeId.value = isNaN(parsedValue) ? null : parsedValue;
    }
    
    // 处理 incomeSourceId
    let incomeSourceId = modelResult.incomeSourceId || defaultField;
    if (typeof incomeSourceId.value === 'string') {
      const parsedValue = parseInt(incomeSourceId.value);
      incomeSourceId.value = isNaN(parsedValue) ? null : parsedValue;
    }
    
    // 处理 incomeExpenseTypeId
    let incomeExpenseTypeId = modelResult.incomeExpenseTypeId || defaultField;
    if (typeof incomeExpenseTypeId.value === 'number') {
      incomeExpenseTypeId.value = incomeExpenseTypeId.value.toString();
    }
    
    // 处理 isReimburse
    let isReimburse = modelResult.isReimburse || { value: 0, confidence: 0 };
    
    // 处理 needSplit
    let needSplit = modelResult.needSplit || [];
    // 确保 needSplit 是数组格式
    if (!Array.isArray(needSplit)) {
      needSplit = [];
    }
    
    // 🔄 后处理逻辑：支出且未分配门店时，平均分账给所有门店
    if (dataItem.directlyExtracted.billType === 'expense' && 
        storeId.value === null && 
        storesData && 
        storesData.length > 0 &&
        dataItem.directlyExtracted.billAmount) {
      
      console.log(`🏪 检测到支出账单且未分配门店，开始平均分账...`);
      console.log(`💰 账单金额: ${dataItem.directlyExtracted.billAmount}`);
      console.log(`🏪 可用门店数量: ${storesData.length}`);
      
      const totalAmount = Math.abs(dataItem.directlyExtracted.billAmount); // 取绝对值，因为支出可能是负数
      const averageAmount = totalAmount / storesData.length;
      
      // 生成分账数据
      needSplit = storesData.map(store => ({
        storeId: store.id,
        splitAmount: parseFloat(averageAmount.toFixed(2)) // 保留2位小数
      }));
      
      // 处理四舍五入后的差额，将差额分配给第一个门店
      const splitTotal = needSplit.reduce((sum: number, split: any) => sum + split.splitAmount, 0);
      const difference = parseFloat((totalAmount - splitTotal).toFixed(2));
      if (difference !== 0 && needSplit.length > 0) {
        needSplit[0].splitAmount = parseFloat((needSplit[0].splitAmount + difference).toFixed(2));
      }
      
      console.log(`✅ 平均分账完成，分账数据:`, needSplit);
      console.log(`💰 分账总额: ${needSplit.reduce((sum: number, split: any) => sum + split.splitAmount, 0)}`);
    }
    
    return {
      billType: { value: dataItem.directlyExtracted.billType, confidence: 1 },
      storeId: storeId,
      incomeSourceId: incomeSourceId,
      incomeExpenseTypeId: incomeExpenseTypeId,
      billAmount: { value: dataItem.directlyExtracted.billAmount, confidence: dataItem.directlyExtracted.billAmount !== null ? 1 : 0 },
      billBalance: { value: dataItem.directlyExtracted.billBalance, confidence: dataItem.directlyExtracted.billBalance !== null ? 1 : 0 },
      billDate: { value: null, confidence: 0 },
      opposingUnit: { value: dataItem.directlyExtracted.opposingUnit, confidence: 1 },
      isReimburse: isReimburse,
      needSplit: needSplit,
      originData: dataItem.forModel.originData,
      transactionTime: dataItem.directlyExtracted.transactionTime
    };
  } catch (error) {
    console.error(`❌ 处理单条数据时出错:`, error);
    console.error(`🔍 错误详情: ${error instanceof Error ? error.message : '未知错误'}`);
    // 返回默认数据
    const defaultField = { value: null, confidence: 0 };
    return {
      billType: { value: dataItem.directlyExtracted.billType, confidence: 1 },
      storeId: defaultField,
      incomeSourceId: defaultField,
      incomeExpenseTypeId: defaultField,
      billAmount: { value: dataItem.directlyExtracted.billAmount, confidence: dataItem.directlyExtracted.billAmount !== null ? 1 : 0 },
      billBalance: { value: dataItem.directlyExtracted.billBalance, confidence: dataItem.directlyExtracted.billBalance !== null ? 1 : 0 },
      billDate: { value: null, confidence: 0 },
      opposingUnit: { value: dataItem.directlyExtracted.opposingUnit, confidence: 1 },
      isReimburse: { value: 0, confidence: 0 },
      needSplit: [],
      originData: dataItem.forModel.originData,
      transactionTime: dataItem.directlyExtracted.transactionTime
    };
  }
}

/**
 * 并发处理多条交易数据的分析
 * @param transactionDataList 交易数据列表
 * @param systemPrompt 系统提示词
 * @param matchedReferenceDataList 匹配的参考资料列表
 * @param concurrencyLimit 并发限制，默认100
 * @returns Promise<any[]> 分析结果列表
 */
async function processBatchDataItems(
  transactionDataList: any[], 
  systemPrompt: string, 
  matchedReferenceDataList?: any[],
  concurrencyLimit: number = 100,
  storesData?: Array<{id: number; company_name: string; store_name: string}>
): Promise<any[]> {
  console.log(`\n🚀 开始批量并发处理 ${transactionDataList.length} 条交易数据`);
  console.log(`🔄 并发限制: ${concurrencyLimit} 线程`);
  
  const results: any[] = new Array(transactionDataList.length);
  
  // 使用信号量控制并发数
  let activePromises = 0;
  const waitingQueue: Array<() => void> = [];
  
  const acquireSlot = (): Promise<void> => {
    return new Promise((resolve) => {
      if (activePromises < concurrencyLimit) {
        activePromises++;
        resolve();
      } else {
        waitingQueue.push(resolve);
      }
    });
  };
  
  const releaseSlot = (): void => {
    activePromises--;
    const next = waitingQueue.shift();
    if (next) {
      activePromises++;
      next();
    }
  };
  
  // 创建处理任务的函数
  const processTask = async (index: number): Promise<void> => {
    await acquireSlot();
    
    try {
      const dataItem = transactionDataList[index];
      const matchedReferenceData = matchedReferenceDataList?.[index];
      
      // 构建参考资料上下文
      let referenceContext = '';
      if (matchedReferenceData && matchedReferenceData.matches && matchedReferenceData.matches.length > 0) {
        referenceContext = '匹配的参考资料：\n';
        matchedReferenceData.matches.forEach((match: any, matchIndex: number) => {
          referenceContext += `${matchIndex + 1}. 文件：${match.fileName}, 工作表：${match.sheetName}\n`;
          referenceContext += `   内容类型：${match.contentType}\n`;
          referenceContext += `   匹配度：${(match.matchScore * 100).toFixed(1)}%\n`;
          referenceContext += `   数据：${JSON.stringify(match.rowData)}\n\n`;
        });
      }
      
      console.log(`📤 处理第 ${index + 1}/${transactionDataList.length} 条数据: ${dataItem.directlyExtracted?.opposingUnit}`);
      const result = await processSingleDataItem(dataItem, systemPrompt, referenceContext, storesData);
      results[index] = result;
      console.log(`✅ 第 ${index + 1} 条数据处理完成`);
    } catch (error) {
      console.error(`❌ 第 ${index + 1} 条数据处理失败:`, error);
      // 设置默认结果
      const dataItem = transactionDataList[index];
      const defaultField = { value: null, confidence: 0 };
      results[index] = {
        billType: { value: dataItem.directlyExtracted?.billType || null, confidence: dataItem.directlyExtracted?.billType ? 1 : 0 },
        storeId: defaultField,
        incomeSourceId: defaultField,
        incomeExpenseTypeId: defaultField,
        billAmount: { value: dataItem.directlyExtracted?.billAmount || null, confidence: dataItem.directlyExtracted?.billAmount !== null ? 1 : 0 },
        billBalance: { value: dataItem.directlyExtracted?.billBalance || null, confidence: dataItem.directlyExtracted?.billBalance !== null ? 1 : 0 },
        billDate: { value: null, confidence: 0 },
        opposingUnit: { value: dataItem.directlyExtracted?.opposingUnit || null, confidence: dataItem.directlyExtracted?.opposingUnit ? 1 : 0 },
        isReimburse: { value: 0, confidence: 0 },
        needSplit: [],
        originData: dataItem.forModel?.originData || null,
        transactionTime: dataItem.directlyExtracted?.transactionTime || null,
        error: error instanceof Error ? error.message : '处理失败'
      };
    } finally {
      releaseSlot();
    }
  };
  
  // 创建所有任务
  const tasks = transactionDataList.map((_, index) => processTask(index));
  
  // 等待所有任务完成
  await Promise.allSettled(tasks);
  
  console.log(`🎉 批量处理完成，共处理 ${results.length} 条数据`);
  
  // 统计处理结果
  const successCount = results.filter(r => !r.error).length;
  const errorCount = results.filter(r => r.error).length;
  console.log(`📊 处理统计: 成功 ${successCount} 条，失败 ${errorCount} 条`);
  
  return results;
}

/**
 * 处理批量交易分析的步骤
 * @param connection 数据库连接
 * @param apCompanyId 公司ID
 * @param body 请求体，包含 transactionDataList, matchedReferenceDataList
 * @returns NextResponse 分析结果
 */
export async function handleAnalyzeBatchTransactions(
  connection: mysql.Connection, 
  apCompanyId: string, 
  body: any
): Promise<NextResponse> {
  const { transactionDataList, matchedReferenceDataList } = body;
  
  if (!transactionDataList || !Array.isArray(transactionDataList)) {
    return NextResponse.json({
      success: false,
      message: "缺少必要参数",
      error: "请提供 transactionDataList 数组"
    }, { status: 400 });
  }

  if (transactionDataList.length === 0) {
    return NextResponse.json({
      success: true,
      message: "无数据需要处理",
      data: []
    });
  }

  try {
    console.log(`\n🔍 开始批量交易分析`);
    console.log(`📊 待处理交易数量: ${transactionDataList.length} 条`);
    
    // 获取门店数据（用于后处理逻辑）
    console.log(`🏪 获取门店数据...`);
    const [stores] = await connection.execute('SELECT * FROM stores WHERE state = 1 AND ap_company_id = ?', [apCompanyId]);
    const storesData = stores as Array<{id: number; company_name: string; store_name: string}>;
    console.log(`✅ 获取到 ${storesData.length} 个门店`);
    
    // 生成系统提示词（只构建一次）
    console.log(`📋 生成系统提示词...`);
    const systemPrompt = await generateSystemPrompt(connection, apCompanyId);
    console.log(`✅ 系统提示词生成完成，长度: ${systemPrompt.length} 字符`);
    
    // 批量并发处理
    console.log(`🤖 开始批量AI分析，使用100线程并发...`);
    const startTime = Date.now();
    const results = await processBatchDataItems(
      transactionDataList, 
      systemPrompt, 
      matchedReferenceDataList,
      100,
      storesData
    );
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log(`✅ 批量交易分析完成，总耗时: ${processingTime.toFixed(2)} 秒`);
    console.log(`⚡ 平均每条数据处理时间: ${(processingTime / transactionDataList.length).toFixed(3)} 秒`);
    
    return NextResponse.json({
      success: true,
      message: "批量交易分析成功",
      data: results,
      stats: {
        totalCount: transactionDataList.length,
        successCount: results.filter(r => !r.error).length,
        errorCount: results.filter(r => r.error).length,
        processingTimeSeconds: processingTime,
        averageTimePerItem: processingTime / transactionDataList.length
      }
    });
    
  } catch (error) {
    console.error('批量交易分析失败:', error);
    return NextResponse.json({
      success: false,
      message: "批量交易分析失败",
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

/**
 * 处理单条交易分析的步骤（保持向后兼容）
 * @param connection 数据库连接
 * @param apCompanyId 公司ID
 * @param body 请求体，包含 transactionData, matchedReferenceData
 * @returns NextResponse 分析结果
 */
export async function handleAnalyzeSingleTransaction(
  connection: mysql.Connection, 
  apCompanyId: string, 
  body: any
): Promise<NextResponse> {
  const { transactionData, matchedReferenceData } = body;
  
  if (!transactionData) {
    return NextResponse.json({
      success: false,
      message: "缺少必要参数",
      error: "请提供 transactionData"
    }, { status: 400 });
  }

  try {
    console.log(`\n🔍 开始单条交易分析`);
    console.log(`💰 交易金额: ${transactionData.directlyExtracted?.billAmount}`);
    console.log(`🏢 对方单位: ${transactionData.directlyExtracted?.opposingUnit}`);
    console.log(`📅 交易时间: ${transactionData.directlyExtracted?.transactionTime}`);
    
    // 获取门店数据（用于后处理逻辑）
    console.log(`🏪 获取门店数据...`);
    const [stores] = await connection.execute('SELECT * FROM stores WHERE state = 1 AND ap_company_id = ?', [apCompanyId]);
    const storesData = stores as Array<{id: number; company_name: string; store_name: string}>;
    console.log(`✅ 获取到 ${storesData.length} 个门店`);
    
    // 生成系统提示词
    console.log(`📋 生成系统提示词...`);
    const systemPrompt = await generateSystemPrompt(connection, apCompanyId);
    console.log(`✅ 系统提示词生成完成，长度: ${systemPrompt.length} 字符`);
    
    // 构建参考资料上下文
    let referenceContext = '';
    if (matchedReferenceData && matchedReferenceData.matches && matchedReferenceData.matches.length > 0) {
      console.log(`✅ 找到参考资料匹配: ${matchedReferenceData.matches.length} 条`);
      referenceContext = '匹配的参考资料：\n';
      matchedReferenceData.matches.forEach((match: any, index: number) => {
        referenceContext += `${index + 1}. 文件：${match.fileName}, 工作表：${match.sheetName}\n`;
        referenceContext += `   内容类型：${match.contentType}\n`;
        referenceContext += `   匹配度：${(match.matchScore * 100).toFixed(1)}%\n`;
        referenceContext += `   数据：${JSON.stringify(match.rowData)}\n\n`;
      });
      console.log(`📎 构建的参考资料上下文长度: ${referenceContext.length} 字符`);
    } else {
      console.log(`❌ 未找到参考资料匹配`);
    }
    
    // 处理单条数据
    console.log(`🤖 开始AI分析...`);
    const result = await processSingleDataItem(transactionData, systemPrompt, referenceContext, storesData);
    console.log(`✅ 单条交易分析完成`);
    
    return NextResponse.json({
      success: true,
      message: "单条交易分析成功",
      data: result
    });
    
  } catch (error) {
    console.error('单条交易分析失败:', error);
    return NextResponse.json({
      success: false,
      message: "单条交易分析失败",
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 