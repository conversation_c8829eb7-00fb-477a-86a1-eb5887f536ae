import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import mysql from 'mysql2/promise';
import { BankParserFactory } from '../bank-parsers';
import { getRequiredAuthSession } from "@/lib/auth";

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

// OpenAI配置
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
});

export const maxDuration = 60;

// 从URL下载文件
async function downloadFile(fileUrl: string, fileName: string): Promise<ArrayBuffer> {
  try {
    console.log(`[文件下载] 开始下载文件: ${fileName} from ${fileUrl}`);
    
    // 下载文件
    const response = await fetch(fileUrl);
    if (!response.ok) {
      throw new Error(`下载文件失败: ${response.status} ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    console.log(`[文件下载] 文件下载成功，大小: ${arrayBuffer.byteLength} bytes`);
    
    return arrayBuffer;
  } catch (error) {
    console.error(`[文件下载] 下载文件失败:`, error);
    throw new Error(`文件下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 生成系统提示词的方法
async function generateSystemPrompt(connection: mysql.Connection, apCompanyId: string): Promise<string> {
  // 从数据库获取基础数据（添加 ap_company_id 筛选）
  let storesData: Array<{id: number; company_name: string; store_name: string}> = [];
  let incomeTypesData: Array<{id: number; income_type: string}> = [];
  let incomeSourcesData: Array<{id: number; income_type: string; code: string; store_id: number}> = [];
  let expenseTypesData: Array<{id: number; name: string; notes: string | null; subtypes: Array<{id: number; name: string; notes: string | null}>}> = [];
  let knowledgeBaseData: Array<{ type: number; content: string }> = [];

  // 获取门店信息（添加 ap_company_id 筛选）
  const [stores] = await connection.execute('SELECT * FROM stores WHERE state = 1 AND ap_company_id = ?', [apCompanyId]);
  storesData = stores as Array<{id: number; company_name: string; store_name: string}>;

  // 获取收入类型（添加 ap_company_id 筛选）
  const [incomeTypes] = await connection.execute('SELECT * FROM income_types WHERE ap_company_id = ?', [apCompanyId]);
  incomeTypesData = incomeTypes as Array<{id: number; income_type: string}>;

  // 获取收入来源（添加 ap_company_id 筛选）
  const [incomeSources] = await connection.execute('SELECT * FROM income_sources WHERE ap_company_id = ?', [apCompanyId]);
  incomeSourcesData = incomeSources as Array<{id: number; income_type: string; code: string; store_id: number}>;

  // 获取支出类型（添加 ap_company_id 筛选）
  const [expenseTypes] = await connection.execute(`
    SELECT 
      et.id,
      et.name,
      et.notes,
      es.id as subtype_id,
      es.name as subtype_name,
      es.notes as subtype_notes
    FROM expense_types et
    LEFT JOIN expense_subtypes es ON et.id = es.parent_id AND es.ap_company_id = ?
    WHERE et.ap_company_id = ?
    ORDER BY et.id, es.id
  `, [apCompanyId, apCompanyId]);

  // 获取知识库数据（添加 ap_company_id 筛选）
  const [knowledgeBase] = await connection.execute('SELECT * FROM knowledge_base WHERE del_flag = 0 AND ap_company_id = ?', [apCompanyId]);
  knowledgeBaseData = knowledgeBase as Array<{ type: number; content: string }>;

  // 重组支出类型数据结构
  expenseTypesData = (expenseTypes as any[]).reduce((acc: Array<{id: number; name: string; notes: string | null; subtypes: Array<{id: number; name: string; notes: string | null}>}>, curr) => {
    const existingType = acc.find(type => type.id === curr.id);

    if (!existingType) {
      acc.push({
        id: curr.id,
        name: curr.name,
        notes: curr.notes,
        subtypes: curr.subtype_id ? [{
          id: curr.subtype_id,
          name: curr.subtype_name,
          notes: curr.subtype_notes
        }] : []
      });
    } else if (curr.subtype_id) {
      existingType.subtypes.push({
        id: curr.subtype_id,
        name: curr.subtype_name,
        notes: curr.subtype_notes
      });
    }

    return acc;
  }, []);

  // 准备知识库数据
  const businessRules: Array<{ type: number; content: string }> = knowledgeBaseData.filter(kb => kb.type === 1);
  const specialDescriptions: Array<{ type: number; content: string }> = knowledgeBaseData.filter(kb => kb.type === 2);
  
  const systemPrompt =
  `你是一个经验十分丰富的会计师，下面我将给你一些银行对账单数据，你需要跟我给出的规则和信息对账单数据进行分析；

      # 基础数据信息:
      <门店信息>
      ${storesData.map(store => `- 门店ID: ${store.id}, 公司名称: ${store.company_name}, 门店名称: ${store.store_name}`).join('\n')}
      <门店信息/>
      
      <收入类型信息>
      ${incomeTypesData.map(type => `- 收入类型ID: ${type.id}, 类型名称: ${type.income_type}`).join('\n')}
      <收入类型信息/>
      
      <收入来源信息>
      ${incomeSourcesData.map(source => {
        const store = storesData.find(s => s.id === source.store_id);
        const storeName = store ? store.store_name : '未知门店';
        return `- 收入来源ID: ${source.id}, 收入类型: ${source.income_type}, 代号: ${source.code}, 所属门店: ${storeName}`;
      }).join('\n')}
      <收入来源信息/>
      
      <支出类型信息>
      ${expenseTypesData.map(type => {
        let result = `- 支出类型ID: ${type.id}, 类型名称: ${type.name}`;
        if (type.subtypes && type.subtypes.length > 0) {
          result += `\n  二级类型:\n${type.subtypes.map((subtype) => 
            `  - 二级类型ID: ${subtype.id}, 类型名称: ${subtype.name}`).join('\n')}`;
        }
        return result;
      }).join('\n')}
      <支出类型信息/>
      
      <业务规则知识库>
      ${businessRules.map(kb => `- ${kb.content}`).join('\n')}
      <业务规则知识库/>
      
      <特殊描述知识库>
      ${specialDescriptions.map(kb => `- ${kb.content}`).join('\n')}
      <特殊描述知识库/>
      
      <返回示例>
      {
        \"data\": [
          {
            \"storeId\": {\"value\": 1, \"confidence\": 1},
            \"incomeSourceId\": {\"value\": \"10\", \"confidence\": 1},
            \"incomeExpenseTypeId\": {\"value\": \"1-9\", \"confidence\": 1},
            \"isReimburse\": {\"value\": 0, \"confidence\": 1}
          }
        ]
      }
      <返回示例/>

      # 解析规则如下：
      「注意，在开始解析之前，你要严格遵守：你务必在非常确信判断某一字段的值的情况下，才可以给出结果，如果你不能完全肯定地判断出来，请放心大胆地将该字段赋值为 null！我们宁愿接受多一些 null 值，但绝对不能接受错误的判断！」
      1. 在进行每个字段的判断时，你必须充分考虑"业务规则知识库"和"特殊描述知识库"，这些是我们根据业务场景总结出来的特殊判断规则，优先级最高；
      2. "业务规则知识库"和"特殊描述知识库"中给出的特殊规则，如果明确表示了某种情况"不匹配"/"不判断"，则将这种情况对应的字段置为 null；
      3. 以上字段如果通过已有知识无法判断的，赋值 null；所有赋值为 null 的字段 confidence=0；
      4. 你需要对每一个字段的判断结果进行评估，并给出 confidence；
         判断结果分为"非常确信"，和"不确信"，你必须对该字段判断结果非常确信的时候，才能使 confidence=1，否则使 confidence=0；
      5. storeId 判断规则
          5.1）根据 opposingUnit 对应的值，从"基础数据信息"中的"门店信息"进行匹配；
          5.2）如果没有匹配到，可以根据用途/摘要/附言中相关的信息，根据"门店信息"或"收入来源信息"去推断，例如：
          5.3）出现"七宝xx"字样，我们可以从"门店信息"中推断出，对应的是B3咖啡七宝店；
          5.4）出现"代号DCKJ05922620431201"等字样，我们可以从"收入来源信息"中推断出，对应的是B3咖啡七宝店；
          5.5）如果数据中的信息不足以判断出来对应的门店，storeId 赋值为 null；
      6. incomeSourceId 判断规则
          6.1）只有当 billType 为 income 的时候才进行 incomeSourceId 的判断；
          6.2）参考第 1 步中的判断规则，根据 opposingUnit/用途/摘要/附言 中的信息，通过"收入来源信息"进行判断；
          6.3）如果数据中的信息不足以判断出来对应的收入来源，incomeSourceId 赋值为 null；
      7. incomeExpenseTypeId 判断规则
          7.1) billType 为 income 的，根据"收入类型信息"进行判断
              7.1.1) 如果 opposingUnit 是对方公司名称的，我们认为是对方银行卡直接转账过来的，算作"分公司转入收入"；如果 opposingUnit/用途/摘要/附言 中的信息是 代号/代码/线上渠道标识的，我们才认为是 "门店收入"；
          7.2）billType 为 expense 的，根据"支出类型信息"进行判断
              7.2.1) "支出类型信息"有两级类型，你需要找到对应的一级、二级类型ID，用 - 连接；
          7.3）如果数据中的信息不足以判断出来对应的收支来源，incomeExpenseTypeId 赋值为 null；
      8. isReimburse 判断规则
          8.1）“只要交易类型为支出（expense）且对方单位是个人（非公司非门店），则直接标记为报销 isReimburse=1，其他情况标记 isReimburse=0。忽略用途、摘要等其他信息的影响。”
          8.2）如果识别出来是报销打款，你需要将 storeId 和 incomeExpenseTypeId 置为 null，结合"业务规则知识库"和"特殊描述知识库"重新判断这两个字段；
              - 如果不能完全确定地识别出 storeId，则将 storeId 保持为 null；
              - 如果不能完全确定地识别出 incomeExpenseTypeId，则将 incomeExpenseTypeId 保持为 null；
          8.3）识别为报销打款的数据，不要归类于"人力成本"；
      9. 你需要严格按照"返回示例"的格式返回 json 格式数据，务必不要增加任何解释性回答或说明；
  `

  return systemPrompt;
}

// 处理单条数据的函数
async function processSingleDataItem(dataItem: any, systemPrompt: string): Promise<any> {
  try {
    // 构建大模型请求结构体
    const requestPayload = {
      model: 'deepseek-v3-250324',
      // model: 'deepseek-r1-250120',
      stream: false as const,
      response_format: { type: "json_object" as const },
      messages: [
        {
          role: 'system' as const,
          content: systemPrompt
        },
        {
          role: 'user' as const,
          content: JSON.stringify([JSON.parse(dataItem.forModel.originData)])
        }
      ],
    };
    
    // 调用大模型
    const response = await openai.chat.completions.create(requestPayload);
    const content = response.choices[0].message.content;
    
    if (!content) {
      throw new Error('模型返回内容为空');
    }

    // 清理响应内容，移除可能的 Markdown 代码块标记
    let cleanContent = content.trim();
    if (cleanContent.includes('```')) {
      cleanContent = cleanContent.replace(/```json/g, '').replace(/```/g, '');
    }

    const parsedContent = JSON.parse(cleanContent);
    if (!parsedContent || !parsedContent.data || !Array.isArray(parsedContent.data)) {
      throw new Error('模型返回的数据格式不正确');
    }
    
    const modelResult = parsedContent.data[0] || {};
    
    // 创建默认的字段值
    const defaultField = { value: null, confidence: 0 };
    
    // 处理 storeId
    let storeId = modelResult.storeId || defaultField;
    if (typeof storeId.value === 'string') {
      const parsedValue = parseInt(storeId.value);
      storeId.value = isNaN(parsedValue) ? null : parsedValue;
    }
    
    // 处理 incomeSourceId
    let incomeSourceId = modelResult.incomeSourceId || defaultField;
    if (typeof incomeSourceId.value === 'string') {
      const parsedValue = parseInt(incomeSourceId.value);
      incomeSourceId.value = isNaN(parsedValue) ? null : parsedValue;
    }
    
    // 处理 incomeExpenseTypeId
    let incomeExpenseTypeId = modelResult.incomeExpenseTypeId || defaultField;
    if (typeof incomeExpenseTypeId.value === 'number') {
      incomeExpenseTypeId.value = incomeExpenseTypeId.value.toString();
    }
    
    // 处理 isReimburse
    let isReimburse = modelResult.isReimburse || { value: 0, confidence: 0 };
    
    return {
      billType: { value: dataItem.directlyExtracted.billType, confidence: 1 },
      storeId: storeId,
      incomeSourceId: incomeSourceId,
      incomeExpenseTypeId: incomeExpenseTypeId,
      billAmount: { value: dataItem.directlyExtracted.billAmount, confidence: dataItem.directlyExtracted.billAmount !== null ? 1 : 0 },
      billBalance: { value: dataItem.directlyExtracted.billBalance, confidence: dataItem.directlyExtracted.billBalance !== null ? 1 : 0 },
      billDate: { value: null, confidence: 0 },
      opposingUnit: { value: dataItem.directlyExtracted.opposingUnit, confidence: 1 },
      isReimburse: isReimburse,
      originData: dataItem.forModel.originData,
      transactionTime: dataItem.directlyExtracted.transactionTime
    };
  } catch (error) {
    console.error('处理单条数据时出错:', error);
    // 返回默认数据
    const defaultField = { value: null, confidence: 0 };
    return {
      billType: { value: dataItem.directlyExtracted.billType, confidence: 1 },
      storeId: defaultField,
      incomeSourceId: defaultField,
      incomeExpenseTypeId: defaultField,
      billAmount: { value: dataItem.directlyExtracted.billAmount, confidence: dataItem.directlyExtracted.billAmount !== null ? 1 : 0 },
      billBalance: { value: dataItem.directlyExtracted.billBalance, confidence: dataItem.directlyExtracted.billBalance !== null ? 1 : 0 },
      billDate: { value: null, confidence: 0 },
      opposingUnit: { value: dataItem.directlyExtracted.opposingUnit, confidence: 1 },
      isReimburse: { value: 0, confidence: 0 },
      originData: dataItem.forModel.originData,
      transactionTime: dataItem.directlyExtracted.transactionTime
    };
  }
}

export async function POST(req: NextRequest) {
  let connection: mysql.Connection | undefined;
  try {
    // 获取用户身份信息
    const session = getRequiredAuthSession(req);
    const { companyId: apCompanyId, userId: apAccountId } = session;

    const body = await req.json();
    
    // 支持两种请求格式：
    // 1. 新格式：{ fileUrl, fileName, bankId }
    // 2. 旧格式：{ data: { uploadedFiles: [...] } }
    let fileName: string;
    let fileBuffer: ArrayBuffer;
    let bankId: string | undefined;
    
    if (body.fileUrl && body.fileName) {
      // 新格式：直接接收文件URL
      console.log(`[新格式] 接收到文件URL: ${body.fileUrl}, 文件名: ${body.fileName}, 银行ID: ${body.bankId}`);
      fileName = body.fileName;
      bankId = body.bankId;
      
      // 检查文件是否是Excel/CSV格式
      const isExcelFile = fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.csv');
      
      if (!isExcelFile) {
        return NextResponse.json({
          success: false,
          message: "不支持的文件格式",
          error: "目前只支持Excel和CSV文件格式"
        }, {
          status: 400
        });
      }
      
      // 下载文件
      try {
        fileBuffer = await downloadFile(body.fileUrl, fileName);
        console.log(`[文件下载] 成功下载文件，大小: ${fileBuffer.byteLength} bytes`);
      } catch (error) {
        console.error(`[文件下载] 下载文件失败:`, error);
        return NextResponse.json({
          success: false,
          message: "文件下载失败",
          error: error instanceof Error ? error.message : '未知错误'
        }, {
          status: 400
        });
      }
    } else {
      return NextResponse.json({
        success: false,
        message: "请求格式错误",
        error: "请提供 fileUrl 和 fileName"
      }, {
        status: 400
      });
    }

    // 建立数据库连接
    connection = await mysql.createConnection(dbConfig);

    // 使用银行解析策略工厂解析文件
    let preProcessedData;
    try {
      if (bankId) {
        // 如果有银行ID，使用指定的银行解析器
        console.log(`使用银行ID ${bankId} 进行解析`);
        preProcessedData = BankParserFactory.parseContentByBankId(fileBuffer, bankId);
        console.log(`使用银行ID ${bankId} 解析成功，共 ${preProcessedData.length} 条数据`);
      } else {
        // 如果没有银行ID，使用自动检测
        console.log(`没有银行ID，尝试自动检测`);
        preProcessedData = BankParserFactory.parseContent(fileBuffer);
        console.log(`自动检测解析成功，共 ${preProcessedData.length} 条数据`);
      }
    } catch (error) {
      console.error(`银行解析失败:`, error);
      return NextResponse.json({
        success: false,
        message: "银行对账单解析失败",
        error: error instanceof Error ? error.message : '未知错误'
      }, {
        status: 400
      });
    }

    if (preProcessedData.length === 0) {
      console.warn(`没有解析到有效数据`);
      return NextResponse.json({
        success: false,
        message: "没有解析到有效数据",
        error: "文件中没有找到有效的银行对账单数据"
      }, {
        status: 400
      });
    }

    // 生成系统提示词
    const systemPrompt = await generateSystemPrompt(connection, apCompanyId.toString());

    console.log("@@@@@@@@@@@@@@@@@@@@@@\n:", systemPrompt);

    // 并发处理数据，每行数据一个线程，最多300线程
    console.log(`开始并发处理 ${preProcessedData.length} 条数据，最多300线程`);
    const maxConcurrency = Math.min(300, preProcessedData.length);
    const allResponseData: any[] = [];

    // 分批处理，每批最多300条
    const batchSize = 300;
    const totalBatches = Math.ceil(preProcessedData.length / batchSize);

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * batchSize;
      const end = Math.min(start + batchSize, preProcessedData.length);
      const batchData = preProcessedData.slice(start, end);
      
      console.log(`处理第 ${batchIndex + 1}/${totalBatches} 批数据，条数: ${batchData.length}`);

      // 创建并发任务
      const processingTasks = batchData.map((dataItem, index) => 
        processSingleDataItem(dataItem, systemPrompt)
      );

      try {
        // 并发执行当前批次的所有任务
        const batchResults = await Promise.all(processingTasks);
        allResponseData.push(...batchResults);
        console.log(`第 ${batchIndex + 1}/${totalBatches} 批处理完成`);
      } catch (error) {
        console.error(`第 ${batchIndex + 1}/${totalBatches} 批处理失败:`, error);
        // 继续处理下一批，不中断整个流程
      }
    }

    // 检查有效数据数量
    console.log(`解析出的有效数据条数: ${allResponseData.length}`);

    // 验证每条数据，确保 billType 不为空
    for (let i = 0; i < allResponseData.length; i++) {
      const item = allResponseData[i];
      if (!item.billType || !item.billType.value) {
        console.warn(`第 ${i+1} 条数据缺少 billType 值，将设置默认值`);
        item.billType = { value: 'unknown', confidence: 0 };
      }
    }
    
    // 存储数据到数据库
    try {
      // 关闭之前的连接（如果已关闭则忽略）
      try {
        await connection.end();
      } catch (error) {
        console.log('关闭旧连接时出现非致命错误', error);
      }
      
      // 创建新连接
      connection = await mysql.createConnection(dbConfig);
      
      // 开始事务
      await connection.beginTransaction();

      // 获取门店数据，用于分摊计算（添加 ap_company_id 筛选）
      const [stores] = await connection.execute('SELECT * FROM stores WHERE state = 1 AND ap_company_id = ?', [apCompanyId.toString()]);
      const currentStoresData = stores as Array<{id: number; company_name: string; store_name: string}>;

      // 1. 插入菜单表（添加 ap_company_id 和 ap_account_id 字段）
      console.log('开始插入菜单表数据...');
      const [menuResult] = await connection.execute(
        'INSERT INTO menus (ap_company_id, ap_account_id, menu_name, status) VALUES (?, ?, ?, ?)',
        [apCompanyId.toString(), apAccountId.toString(), fileName || '未命名文件', 0]
      );
      const menuId = (menuResult as any).insertId;
      console.log('菜单表插入成功，menuId:', menuId);

      // 2. 插入账单数据
      if (allResponseData.length > 0) {
        console.log('开始插入账单数据，总条数:', allResponseData.length);
        const batchSize = 500;
        const totalBatches = Math.ceil(allResponseData.length / batchSize);
        console.log('将分', totalBatches, '批插入数据');

        // 存储所有需要分摊的账单信息，用于后续插入分摊记录
        const splitBillsInfo: Array<{
          billAmount: number;
          billIndex: number; // 在原数组中的索引，用于后续查找bill_id
        }> = [];

        for (let i = 0; i < totalBatches; i++) {
          const start = i * batchSize;
          const end = Math.min(start + batchSize, allResponseData.length);
          const batchData = allResponseData.slice(start, end);
          console.log(`处理第 ${i + 1}/${totalBatches} 批数据，条数: ${batchData.length}`);

          const values = [];
          // 单独处理每个账单数据，以便于捕获单条数据的错误
          for (let j = 0; j < batchData.length; j++) {
            try {
              const bill = batchData[j];
              // 使用直接提取的交易时间
              let transactionTime = bill.transactionTime || '1970-01-01 00:00:00';
              
              // 验证必要字段存在
              if (!bill.billType || !bill.billType.value || bill.billType.value === '') {
                console.warn(`第 ${i + 1} 批第 ${j + 1} 条数据缺少bill_type字段，跳过`, bill);
                continue; // 跳过这条数据
              }

              // 判断是否需要分摊：支出类型且没有指定门店
              let isSplit = 0;
              if (bill.billType.value === 'expense' && (!bill.storeId?.value || bill.storeId.value === null)) {
                isSplit = 1;
                
                // 记录需要分摊的账单信息
                if (bill.billAmount?.value && bill.billAmount.value > 0) {
                  splitBillsInfo.push({
                    billAmount: bill.billAmount.value,
                    billIndex: start + j // 在原数组中的全局索引
                  });
                }
              }
              
              values.push([
                apCompanyId.toString(),
                apAccountId.toString(),
                menuId,
                transactionTime,
                bill.billType.value,
                bill.billType.confidence || 0,
                bill.storeId?.value ?? null,
                bill.storeId?.confidence || 0,
                bill.incomeSourceId?.value ?? null,
                bill.incomeSourceId?.confidence || 0,
                bill.incomeExpenseTypeId?.value ?? null,
                bill.incomeExpenseTypeId?.confidence || 0,
                bill.billAmount?.value ?? null,
                bill.billAmount?.confidence || 0,
                bill.billBalance?.value ?? null,
                bill.billBalance?.confidence || 0,
                null,
                0,
                bill.opposingUnit?.value ?? null,
                bill.opposingUnit?.confidence || 0,
                isSplit, // 设置分摊标志
                bill.originData || '{}',
                bill.isReimburse?.value ?? 0,
                0
              ]);
            } catch (error) {
              console.error(`处理第 ${i + 1} 批第 ${j + 1} 条数据时出错，此条跳过:`, error);
              // 继续处理其他数据，不中断整个批次
            }
          }
          
          // 如果当前批次没有有效数据，跳过
          if (values.length === 0) {
            console.warn(`第 ${i + 1}/${totalBatches} 批没有有效数据，跳过`);
            continue;
          }

          const placeholders = values.map(() => '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)').join(',');

          try {
            console.log(`执行第 ${i + 1} 批数据插入...`);
            await connection.execute(
              `INSERT INTO bills (
                ap_company_id, ap_account_id, menu_id, transaction_time, bill_type, bill_type_confidence, store_id, store_id_confidence,
                income_source_id, income_source_id_confidence, income_expense_type_id, income_expense_type_id_confidence,
                bill_amount, bill_amount_confidence, bill_balance, bill_balance_confidence,
                bill_date, bill_date_confidence, opposing_unit, opposing_unit_confidence,
                is_split, origin_data, is_reimburse, status
              ) VALUES ${placeholders}`,
              values.flat()
            );
            console.log(`第 ${i + 1} 批数据插入成功`);
          } catch (error) {
            console.error(`第 ${i + 1} 批数据插入失败:`, error);
            console.error('SQL语句:', `INSERT INTO bills (...) VALUES ${placeholders}`);
            console.error('当前批次数据条数:', values.length);
            throw new Error(`数据库插入错误: ${error instanceof Error ? error.message : String(error)}`);
          }
        }

        // 3. 处理分摊记录
        if (splitBillsInfo.length > 0 && currentStoresData.length > 0) {
          console.log(`开始处理分摊记录，共 ${splitBillsInfo.length} 条需要分摊的账单，门店数量: ${currentStoresData.length}`);
          
          // 查询刚插入的账单ID，按插入顺序排序（添加 ap_company_id 筛选）
          const [insertedBills] = await connection.execute(
            'SELECT id FROM bills WHERE menu_id = ? AND ap_company_id = ? ORDER BY id ASC',
            [menuId, apCompanyId.toString()]
          );
          const billIds = (insertedBills as any[]).map(bill => bill.id);
          
          if (billIds.length >= allResponseData.length) {
            // 准备分摊记录数据
            const splitRecordsToInsert: Array<[string, string, number, number, number, number, number]> = [];
            
            for (const splitInfo of splitBillsInfo) {
              const billId = billIds[splitInfo.billIndex];
              const splitAmount = Number((splitInfo.billAmount / currentStoresData.length).toFixed(2));
              
              // 为每个门店创建分摊记录
              for (const store of currentStoresData) {
                splitRecordsToInsert.push([
                  apCompanyId.toString(),
                  apAccountId.toString(),
                  menuId,
                  billId,
                  store.id,
                  splitAmount,
                  0 // status: 0-未处理
                ]);
              }
            }
            
            if (splitRecordsToInsert.length > 0) {
              console.log(`准备插入 ${splitRecordsToInsert.length} 条分摊记录`);
              
              // 批量插入分摊记录
              const splitBatchSize = 1000;
              const splitTotalBatches = Math.ceil(splitRecordsToInsert.length / splitBatchSize);
              
              for (let i = 0; i < splitTotalBatches; i++) {
                const start = i * splitBatchSize;
                const end = Math.min(start + splitBatchSize, splitRecordsToInsert.length);
                const batchSplitData = splitRecordsToInsert.slice(start, end);
                
                const splitPlaceholders = batchSplitData.map(() => '(?, ?, ?, ?, ?, ?, ?)').join(',');
                
                try {
                  await connection.execute(
                    `INSERT INTO bill_split_records 
                    (ap_company_id, ap_account_id, menu_id, bill_id, store_id, split_amount, status) 
                    VALUES ${splitPlaceholders}`,
                    batchSplitData.flat()
                  );
                  console.log(`第 ${i + 1}/${splitTotalBatches} 批分摊记录插入成功，条数: ${batchSplitData.length}`);
                } catch (error) {
                  console.error(`第 ${i + 1}/${splitTotalBatches} 批分摊记录插入失败:`, error);
                  throw new Error(`分摊记录插入错误: ${error instanceof Error ? error.message : String(error)}`);
                }
              }
              
              console.log(`所有分摊记录插入完成，总计 ${splitRecordsToInsert.length} 条`);
            }
          } else {
            console.warn('查询到的账单ID数量与预期不符，跳过分摊记录插入');
          }
        } else {
          console.log('没有需要分摊的账单或没有门店数据，跳过分摊记录处理');
        }
      } else {
        console.warn('没有有效的账单数据需要插入');
      }

      await connection.commit();
      console.log('所有数据插入成功，事务已提交');
      return NextResponse.json({
        success: true,
        message: "数据解析并存储成功",
        menuId: menuId
      }, {
        status: 200
      });

    } catch (error: any) {
      if (connection) {
        try {
          await connection.rollback();
          console.log('事务已回滚');
        } catch (rollbackError) {
          console.error('回滚事务时出错:', rollbackError);
        }
      }
      console.error("数据库操作时出错:", error);
      return NextResponse.json({
        success: false,
        message: "处理请求时出错",
        error: error instanceof Error ? error.message : String(error)
      }, {
        status: 500
      });
    } finally {
      if (connection) {
        try {
          await connection.end();
          console.log('数据库连接已关闭');
        } catch (endError) {
          console.error('关闭连接时出错:', endError);
        }
      }
    }

  } catch (error) {
    console.error("处理请求时出错:", error);
    // 确保关闭数据库连接
    if (connection) {
      try {
        await connection.end();
      } catch (endError) {
        console.error('关闭连接时出错:', endError);
      }
    }
    return NextResponse.json({
      success: false,
      message: "处理请求时出错",
      error: error instanceof Error ? error.message : String(error)
    }, {
      status: 500
    });
  }
}