# 银行对账单解析策略

这个模块实现了银行对账单解析的策略模式，支持多种银行的对账单格式解析。

## 架构设计

### 核心组件

1. **BankParserStrategy** - 银行解析策略接口
2. **BankParserFactory** - 银行解析器工厂类
3. **CSVUtils** - CSV解析工具类
4. **ICBCParser** - 工商银行解析策略实现
5. **BOCParser** - 中国银行解析策略实现
6. **SPDBParser** - 浦发银行解析策略实现

### 文件结构

```
bank-parsers/
├── types.ts              # 类型定义
├── csv-utils.ts          # CSV解析工具
├── icbc-parser.ts        # 工商银行解析器
├── boc-parser.ts         # 中国银行解析器
├── spdb-parser.ts        # 浦发银行解析器
├── bank-parser-factory.ts # 解析器工厂
├── index.ts              # 模块导出
└── README.md             # 说明文档
```

## 使用方法

### 基于用户选择的银行解析（推荐）

```typescript
import { BankParserFactory } from './bank-parsers';

// 根据用户选择的银行ID解析对账单
const bankId = 'icbc'; // 用户在前端选择的银行ID
const content = `交易时间,借贷标志,对方单位,转出金额,转入金额,余额
2024-01-01 10:00:00,借,测试商户,100.00,,1000.00`;

try {
  const parsedData = BankParserFactory.parseContentByBankId(content, bankId);
  console.log('解析结果:', parsedData);
} catch (error) {
  console.error('解析失败:', error);
}
```

### 自动检测银行类型（备用方案）

```typescript
// 自动检测银行类型并解析
try {
  const parsedData = BankParserFactory.parseContent(content);
  console.log('解析结果:', parsedData);
} catch (error) {
  console.error('解析失败:', error);
}
```

### 检测银行类型

```typescript
const parser = BankParserFactory.getParser(content);
if (parser) {
  console.log('检测到的银行:', parser.bankName);
} else {
  console.log('不支持的银行格式');
}
```

### 获取支持的银行列表

```typescript
const supportedBanks = BankParserFactory.getSupportedBanks();
console.log('支持的银行:', supportedBanks);

const bankIdMapping = BankParserFactory.getBankIdMapping();
console.log('银行ID映射:', bankIdMapping);
```

## 银行ID映射

系统支持以下银行ID到银行名称的映射：

| 银行ID | 银行名称 | 解析器状态 | 特殊处理 |
|--------|----------|------------|----------|
| `icbc` | 工商银行 | ✅ 完全支持 | 支持特殊格式，智能CSV解析 |
| `boc`  | 中国银行 | ✅ 基础支持 | 标准CSV格式 |
| `spdb` | 浦发银行 | ✅ 完全支持 | 交易时间拼接，借贷金额判断 |

## 添加新银行支持

### 1. 创建新的解析器类

```typescript
// 例如：cmb-parser.ts
import { BankParserStrategy, ParsedDataItem } from './types';

export class CMBParser implements BankParserStrategy {
  bankName = '招商银行';

  canParse(content: string): boolean {
    // 实现银行格式检测逻辑
    const lines = content.trim().split('\n');
    if (lines.length < 1) return false;
    
    const firstLine = lines[0];
    const fieldMapping = this.getFieldMapping();
    
    const hasTransactionTime = fieldMapping.transactionTime.some(field => firstLine.includes(field));
    const hasLoanFlag = fieldMapping.loanFlag.some(field => firstLine.includes(field));
    const hasOpposingUnit = fieldMapping.opposingUnit.some(field => firstLine.includes(field));
    
    return hasTransactionTime && hasLoanFlag && hasOpposingUnit;
  }

  getFieldMapping() {
    return {
      transactionTime: ['交易日期', '记账日期'],
      loanFlag: ['收支标志', '借贷方向'],
      opposingUnit: ['对方户名', '交易对手'],
      transferOut: ['支出金额', '借方金额'],
      transferIn: ['收入金额', '贷方金额'],
      balance: ['余额'],
      usage: ['用途'],
      summary: ['摘要'],
      remark: ['附言']
    };
  }

  parseContent(content: string): ParsedDataItem[] {
    // 实现具体的解析逻辑
    // 可以参考 ICBCParser 的实现
    return [];
  }
}
```

### 2. 注册新解析器

在 `bank-parser-factory.ts` 中添加：

```typescript
import { CMBParser } from './cmb-parser';

export class BankParserFactory {
  private static parsers: BankParserStrategy[] = [
    new ICBCParser(),
    new BOCParser(),
    new SPDBParser(),
    new CMBParser(), // 添加新解析器
  ];

  private static bankIdMapping: Record<string, string> = {
    'icbc': '工商银行',
    'boc': '中国银行',
    'spdb': '浦发银行',
    'cmb': '招商银行', // 添加银行ID映射
  };
}
```

### 3. 导出新解析器

在 `index.ts` 中添加：

```typescript
export * from './cmb-parser';
```

### 4. 更新前端银行选择器

在 `BankSelector.tsx` 中添加新银行选项：

```typescript
const banks: Bank[] = [
  { id: "icbc", name: "工商银行", icon: "/banks/工商银行.png" },
  { id: "boc", name: "中国银行", icon: "/banks/中国银行.png" },
  { id: "spdb", name: "浦发银行", icon: "/banks/浦发银行.png" },
  { id: "cmb", name: "招商银行", icon: "/banks/招商银行.png" }, // 新增
];
```

## 数据结构

### ParsedDataItem

```typescript
interface ParsedDataItem {
  directlyExtracted: {
    transactionTime: string;    // 交易时间
    billType: string;          // 账单类型 (income/expense)
    opposingUnit: string;      // 对方单位
    billAmount: number | null; // 金额
    billBalance: number | null; // 余额
  };
  forModel: {
    usage: string;             // 用途
    summary: string;           // 摘要
    remark: string;            // 附言
    originData: string;        // 原始数据JSON字符串
  };
}
```

## 支持的银行

目前支持的银行：

- ✅ 工商银行 (ICBC) - 完全支持，包括特殊格式和智能CSV解析
- ✅ 中国银行 (BOC) - 基础支持，标准CSV格式
- ✅ 浦发银行 (SPDB) - 完全支持，包括交易时间拼接和借贷金额判断
- 🔄 招商银行 (CMB) - 待实现
- 🔄 农业银行 (ABC) - 待实现
- 🔄 建设银行 (CCB) - 待实现

## 特性

- 🎯 **基于用户选择** - 优先使用用户在前端选择的银行进行解析
- 🔍 **自动银行检测** - 当没有用户选择时，根据对账单内容自动识别银行类型
- 📊 **智能CSV解析** - 支持包含千分位逗号的金额解析
- 🔧 **可扩展架构** - 易于添加新银行支持
- 🛡️ **错误处理** - 完善的错误处理和日志记录
- 📝 **类型安全** - 完整的TypeScript类型定义
- 🏦 **多银行支持** - 支持多种银行的不同字段格式

## 银行特殊处理说明

### 浦发银行 (SPDB)

浦发银行的对账单格式有以下特殊处理：

1. **交易时间拼接**
   - 需要将 `交易日期` 和 `交易时间` 两个字段拼接
   - 交易日期格式：`20250401` → `2025-04-01`
   - 交易时间格式处理：
     - `190611` → `19:06:11` (6位完整格式)
     - `85840` → `08:58:40` (5位格式，补零)
     - `512` → `00:05:12` (3位格式，补零)

2. **借贷标志判断**
   - 通过 `借方金额` 和 `贷方金额` 字段判断收支类型
   - 借方金额有值且非零 → `expense` (支出)
   - 贷方金额有值且非零 → `income` (收入)
   - 两个字段互斥，只有一个有值

3. **用途字段优先级**
   - 优先使用 `费用明细` 字段作为用途
   - 如果 `费用明细` 为空，则使用 `备注` 字段

### 工商银行 (ICBC)

工商银行支持多种格式：

1. **标准CSV格式** - 正常的逗号分隔格式
2. **特殊合并格式** - 所有表头合并在一起的格式
3. **智能千分位处理** - 自动识别和处理金额中的千分位逗号

### 中国银行 (BOC)

中国银行目前支持标准CSV格式，使用标准的字段映射。

## 工作流程

1. **用户上传文件** - 在前端选择银行并上传对账单文件
2. **传递银行信息** - 前端将银行ID随文件内容一起发送到API
3. **选择解析策略** - 后端根据银行ID选择对应的解析器
4. **解析对账单** - 使用选定的解析器解析文件内容
5. **数据处理** - 将解析结果传递给AI模型进行进一步分析

## 注意事项

1. 新增银行解析器时，需要实现 `BankParserStrategy` 接口的所有方法
2. `canParse` 方法应该尽可能准确地识别银行格式，避免误判
3. 字段映射应该包含该银行对账单的所有可能字段名称
4. 解析失败时应该返回空数组，而不是抛出异常
5. 优先使用用户选择的银行ID进行解析，自动检测作为备用方案
6. 每个银行的字段名称可能不同，需要在字段映射中包含所有可能的变体 