// 银行解析器工厂类

import { BankParserStrategy, ParsedDataItem } from './types';
import { ICBCParser } from './icbc-parser';
import { BOCParser } from './boc-parser';
import { SPDBParser } from './spdb-parser';

export class BankParserFactory {
  private static parsers: BankParserStrategy[] = [
    new ICBCParser(),
    new BOCParser(),
    new SPDBParser(),
    // 后续可以添加其他银行的解析器
    // new CMBParser(),
    // new ABCParser(),
    // new CCBParser(),
  ];

  // 银行ID到解析器的映射
  private static bankIdMapping: Record<string, string> = {
    'icbc': '工商银行',
    'boc': '中国银行',
    'spdb': '浦发银行',
    // 后续添加更多银行映射
  };

  /**
   * 根据银行ID获取解析器
   */
  static getParserByBankId(bankId: string): BankParserStrategy | null {
    const bankName = this.bankIdMapping[bankId];
    if (!bankName) {
      console.warn(`不支持的银行ID: ${bankId}`);
      return null;
    }

    const parser = this.parsers.find(p => p.bankName === bankName);
    if (parser) {
      console.log(`根据银行ID ${bankId} 找到解析器: ${parser.bankName}`);
      return parser;
    }

    console.warn(`未找到银行 ${bankName} 的解析器`);
    return null;
  }

  /**
   * 根据内容自动选择合适的银行解析器
   */
  static getParser(fileBuffer: ArrayBuffer): BankParserStrategy | null {
    console.log('开始自动检测银行类型...');
    console.log(`文件大小: ${fileBuffer.byteLength} bytes`);
    
    for (const parser of this.parsers) {
      console.log(`尝试检测银行: ${parser.bankName}`);
      try {
        if (parser.canParse(fileBuffer)) {
          console.log(`✅ 检测到银行类型: ${parser.bankName}`);
          return parser;
        } else {
          console.log(`❌ ${parser.bankName} 检测失败`);
        }
      } catch (error) {
        console.error(`检测 ${parser.bankName} 时出错:`, error);
      }
    }
    
    console.warn('❌ 未找到匹配的银行解析器');
    console.warn('支持的银行列表:', this.parsers.map(p => p.bankName));
    return null;
  }

  /**
   * 根据银行ID解析银行对账单内容
   */
  static parseContentByBankId(fileBuffer: ArrayBuffer, bankId: string): ParsedDataItem[] {
    console.log(`使用银行ID ${bankId} 解析文件`);
    const parser = this.getParserByBankId(bankId);
    if (!parser) {
      throw new Error(`不支持的银行: ${bankId}，支持的银行: ${Object.keys(this.bankIdMapping).join(', ')}`);
    }

    try {
      const result = parser.parseContent(fileBuffer);
      console.log(`银行 ${parser.bankName} 解析成功，共 ${result.length} 条数据`);
      return result;
    } catch (error) {
      console.error(`银行 ${parser.bankName} 解析失败:`, error);
      throw error;
    }
  }

  /**
   * 解析银行对账单内容（自动检测银行类型）
   */
  static parseContent(fileBuffer: ArrayBuffer): ParsedDataItem[] {
    const parser = this.getParser(fileBuffer);
    if (!parser) {
      throw new Error(`不支持的银行对账单格式，支持的银行: ${this.parsers.map(p => p.bankName).join(', ')}`);
    }

    try {
      const result = parser.parseContent(fileBuffer);
      console.log(`自动检测的银行 ${parser.bankName} 解析成功，共 ${result.length} 条数据`);
      return result;
    } catch (error) {
      console.error(`自动检测的银行 ${parser.bankName} 解析失败:`, error);
      throw error;
    }
  }

  /**
   * 获取所有支持的银行列表
   */
  static getSupportedBanks(): string[] {
    return this.parsers.map(parser => parser.bankName);
  }

  /**
   * 获取银行ID映射
   */
  static getBankIdMapping(): Record<string, string> {
    return { ...this.bankIdMapping };
  }

  /**
   * 注册新的银行解析器
   */
  static registerParser(parser: BankParserStrategy): void {
    this.parsers.push(parser);
  }

  /**
   * 注册银行ID映射
   */
  static registerBankIdMapping(bankId: string, bankName: string): void {
    this.bankIdMapping[bankId] = bankName;
  }
} 