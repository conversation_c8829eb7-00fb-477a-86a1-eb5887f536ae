// 中国银行对账单解析策略

import { BankParserStrategy, ParsedDataItem, FieldIndexes } from './types';
import { CSVUtils } from './csv-utils';
import * as XLSX from 'xlsx';

// 中国银行专用字段索引接口
interface BOCFieldIndexes {
  transactionDateIndex: number;
  transactionTimeIndex: number;
  loanFlagIndex: number;
  payerNameIndex: number;
  payeeNameIndex: number;
  tradeAmountIndex: number;
  balanceIndex: number;
  usageIndex: number;
  summaryIndex: number;
  remarkIndex: number;
}

export class BOCParser implements BankParserStrategy {
  bankName = '中国银行';

  /**
   * 检测是否为中国银行对账单格式
   */
  canParse(fileBuffer: ArrayBuffer): boolean {
    console.log(`[中国银行] 开始检测文件格式，文件大小: ${fileBuffer.byteLength} bytes`);
    
    try {
      // 解析XLSX文件
      console.log(`[中国银行] 开始解析XLSX文件...`);
      const workbook = XLSX.read(fileBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      console.log(`[中国银行] 工作表名称: ${firstSheetName}`);
      
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`[中国银行] 工作表范围: 行${range.s.r}-${range.e.r}, 列${range.s.c}-${range.e.c}`);
      
      // 检查前10行，寻找表头
      const fieldMapping = this.getBOCFieldMapping();
      const requiredFields = [
        ...fieldMapping.transactionDate,
        ...fieldMapping.transactionTime,
        ...fieldMapping.loanFlag,
        ...fieldMapping.payerName,
        ...fieldMapping.payeeName
      ];
      console.log(`[中国银行] 必要字段: ${requiredFields.join(', ')}`);
      
      for (let row = range.s.r; row <= Math.min(range.s.r + 9, range.e.r); row++) {
        let matchedFields = 0;
        const rowFields: string[] = [];
        
        // 检查这一行的所有列
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          
          if (cell && cell.v) {
            const cellValue = String(cell.v).trim();
            rowFields.push(cellValue);
            if (requiredFields.some(field => cellValue.includes(field))) {
              matchedFields++;
            }
          }
        }
        
        console.log(`[中国银行] 第${row + 1}行字段: [${rowFields.join(', ')}], 匹配字段数: ${matchedFields}`);
        
        // 如果匹配到3个以上必要字段，认为是中国银行格式
        if (matchedFields >= 3) {
          console.log(`[中国银行] ✅ 格式检测成功，在第${row + 1}行找到${matchedFields}个匹配字段`);
          return true;
        }
      }
      
      console.log(`[中国银行] ❌ 格式检测失败，未找到足够的匹配字段`);
      return false;
    } catch (error) {
      console.error('[中国银行] 格式检测失败:', error);
      return false;
    }
  }

  /**
   * 获取中国银行字段映射（符合接口要求）
   */
  getFieldMapping() {
    return {
      transactionTime: ['交易日期[ Transaction Date ]', '交易时间[ Transaction time ]', '交易日期', '交易时间'],
      loanFlag: ['交易类型[ Transaction Type ]', '交易类型'],
      opposingUnit: ['付款人名称[ Payer\'s Name ]', '收款人名称[ Payee\'s Name ]', '付款人名称', '收款人名称'],
      transferOut: ['交易金额[ Trade Amount ]', '交易金额'],
      transferIn: ['交易金额[ Trade Amount ]', '交易金额'],
      balance: ['交易后余额[ After-transaction balance ]', '交易后余额'],
      usage: ['用途[ Purpose ]', '用途'],
      summary: ['摘要[ Reference ]', '摘要'],
      remark: ['交易附言[ Remark ]', '交易附言']
    };
  }

  /**
   * 获取中国银行专用字段映射
   */
  private getBOCFieldMapping() {
    return {
      transactionDate: ['交易日期[ Transaction Date ]', '交易日期'],
      transactionTime: ['交易时间[ Transaction time ]', '交易时间'],
      loanFlag: ['交易类型[ Transaction Type ]', '交易类型'],
      payerName: ['付款人名称[ Payer\'s Name ]', '付款人名称'],
      payeeName: ['收款人名称[ Payee\'s Name ]', '收款人名称'],
      tradeAmount: ['交易金额[ Trade Amount ]', '交易金额'],
      balance: ['交易后余额[ After-transaction balance ]', '交易后余额'],
      usage: ['用途[ Purpose ]', '用途'],
      summary: ['摘要[ Reference ]', '摘要'],
      remark: ['交易附言[ Remark ]', '交易附言']
    };
  }

  /**
   * 解析中国银行对账单内容
   */
  parseContent(fileBuffer: ArrayBuffer): ParsedDataItem[] {
    console.log(`[中国银行] 开始解析对账单内容，文件大小: ${fileBuffer.byteLength} bytes`);
    
    try {
      // 解析XLSX文件
      console.log(`[中国银行] 解析XLSX文件...`);
      const workbook = XLSX.read(fileBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`[中国银行] 工作表范围: ${range.s.r}-${range.e.r} 行, ${range.s.c}-${range.e.c} 列`);
      
      // 1. 找到表头行
      console.log(`[中国银行] 开始查找表头行...`);
      const { headerRowIndex, fieldIndexes } = this.findHeaderRow(worksheet, range);
      
      if (headerRowIndex === -1) {
        console.error(`[中国银行] ❌ 未找到有效的表头行`);
        throw new Error('未找到有效的表头行');
      }
      
      console.log(`[中国银行] ✅ 找到表头行: 第${headerRowIndex + 1}行`);
      console.log(`[中国银行] 字段索引:`, fieldIndexes);
      
      // 2. 解析数据行
      console.log(`[中国银行] 开始解析数据行，从第${headerRowIndex + 2}行到第${range.e.r + 1}行...`);
      const preProcessedData: ParsedDataItem[] = [];
      let processedCount = 0;
      let skippedCount = 0;
      
      for (let row = headerRowIndex + 1; row <= range.e.r; row++) {
        const dataItem = this.parseDataRow(worksheet, row, fieldIndexes, range);
        if (dataItem) {
          preProcessedData.push(dataItem);
          processedCount++;
          
          // 每处理100行输出一次进度
          if (processedCount % 100 === 0) {
            console.log(`[中国银行] 已处理 ${processedCount} 行数据...`);
          }
        } else {
          skippedCount++;
        }
      }
      
      console.log(`[中国银行] ✅ 解析完成，共解析${preProcessedData.length}条有效数据，跳过${skippedCount}条无效数据`);
      return preProcessedData;
      
    } catch (error) {
      console.error('[中国银行] 解析失败:', error);
      throw error;
    }
  }

  /**
   * 找到表头行和字段索引
   */
  private findHeaderRow(worksheet: XLSX.WorkSheet, range: XLSX.Range): { headerRowIndex: number, fieldIndexes: BOCFieldIndexes } {
    console.log(`[中国银行] 在前10行中查找表头...`);
    
    const fieldMapping = this.getBOCFieldMapping();
    const allFields = [
      ...fieldMapping.transactionDate,
      ...fieldMapping.transactionTime,
      ...fieldMapping.loanFlag,
      ...fieldMapping.payerName,
      ...fieldMapping.payeeName,
      ...fieldMapping.tradeAmount,
      ...fieldMapping.balance,
      ...fieldMapping.usage,
      ...fieldMapping.summary,
      ...fieldMapping.remark
    ];
    
    console.log(`[中国银行] 所有可识别字段: ${allFields.join(', ')}`);
    
    // 从上向下遍历行，找到第一个包含5个以上表头字段的行
    for (let row = range.s.r; row <= Math.min(range.s.r + 9, range.e.r); row++) {
      const headers: string[] = [];
      let matchedFieldCount = 0;
      const matchedFields: string[] = [];
      
      // 读取这一行的所有列
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        const cellValue = cell && cell.v ? String(cell.v).trim() : '';
        headers.push(cellValue);
        
        if (cellValue && allFields.some(field => cellValue.includes(field))) {
          matchedFieldCount++;
          matchedFields.push(cellValue);
        }
      }
      
      console.log(`[中国银行] 第${row + 1}行: 总字段${headers.length}个, 匹配字段${matchedFieldCount}个 [${matchedFields.join(', ')}]`);
      
      // 如果匹配到5个以上字段，认为是表头行
      if (matchedFieldCount >= 5) {
        console.log(`[中国银行] ✅ 确定第${row + 1}行为表头行`);
        const fieldIndexes = this.findBOCFieldIndexes(headers);
        return { headerRowIndex: row, fieldIndexes };
      }
    }
    
    console.log(`[中国银行] ❌ 未找到符合条件的表头行`);
    return { headerRowIndex: -1, fieldIndexes: this.getEmptyBOCFieldIndexes() };
  }

  /**
   * 查找中国银行字段在表头中的索引
   */
  private findBOCFieldIndexes(headers: string[]): BOCFieldIndexes {
    console.log(`[中国银行] 开始查找字段索引...`);
    
    const fieldMapping = this.getBOCFieldMapping();
    
    const fieldIndexes: BOCFieldIndexes = {
      transactionDateIndex: headers.findIndex(h => fieldMapping.transactionDate.some(field => h.includes(field))),
      transactionTimeIndex: headers.findIndex(h => fieldMapping.transactionTime.some(field => h.includes(field))),
      loanFlagIndex: headers.findIndex(h => fieldMapping.loanFlag.some(field => h.includes(field))),
      payerNameIndex: headers.findIndex(h => fieldMapping.payerName.some(field => h.includes(field))),
      payeeNameIndex: headers.findIndex(h => fieldMapping.payeeName.some(field => h.includes(field))),
      tradeAmountIndex: headers.findIndex(h => fieldMapping.tradeAmount.some(field => h.includes(field))),
      balanceIndex: headers.findIndex(h => fieldMapping.balance.some(field => h.includes(field))),
      usageIndex: headers.findIndex(h => fieldMapping.usage.some(field => h.includes(field))),
      summaryIndex: headers.findIndex(h => fieldMapping.summary.some(field => h.includes(field))),
      remarkIndex: headers.findIndex(h => fieldMapping.remark.some(field => h.includes(field)))
    };
    
    // 输出每个字段的索引
    Object.entries(fieldIndexes).forEach(([key, index]) => {
      const fieldName = key.replace('Index', '');
      if (index !== -1) {
        console.log(`[中国银行] ${fieldName}: 列${index + 1} (${headers[index]})`);
      } else {
        console.log(`[中国银行] ${fieldName}: 未找到`);
      }
    });
    
    return fieldIndexes;
  }

  /**
   * 获取空的中国银行字段索引
   */
  private getEmptyBOCFieldIndexes(): BOCFieldIndexes {
    return {
      transactionDateIndex: -1,
      transactionTimeIndex: -1,
      loanFlagIndex: -1,
      payerNameIndex: -1,
      payeeNameIndex: -1,
      tradeAmountIndex: -1,
      balanceIndex: -1,
      usageIndex: -1,
      summaryIndex: -1,
      remarkIndex: -1
    };
  }

  /**
   * 解析单行数据
   */
  private parseDataRow(worksheet: XLSX.WorkSheet, row: number, fieldIndexes: BOCFieldIndexes, range: XLSX.Range): ParsedDataItem | null {
    // 读取这一行的所有数据
    const rowData: string[] = [];
    let hasData = false;
    
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      const cell = worksheet[cellAddress];
      const cellValue = cell && cell.v ? String(cell.v).trim() : '';
      rowData.push(cellValue);
      
      if (cellValue) {
        hasData = true;
      }
    }
    
    // 如果这一行没有任何数据，跳过
    if (!hasData) {
      console.log(`[中国银行] 第${row + 1}行: 空行，跳过`);
      return null;
    }
    
    // 创建原始数据对象
    const originData: Record<string, string> = {};
    rowData.forEach((value, index) => {
      originData[`col_${index}`] = value;
    });
    
    // 确定账单类型 - 中国银行使用"来账"/"往账"标识
    let billType = '';
    if (fieldIndexes.loanFlagIndex !== -1 && rowData[fieldIndexes.loanFlagIndex]) {
      const loanFlag = rowData[fieldIndexes.loanFlagIndex];
      // 中国银行使用 "来账" 表示收入，"往账" 表示支出
      if (loanFlag === '往账') {
        billType = 'expense';
      } else if (loanFlag === '来账') {
        billType = 'income';
      }
      console.log(`[中国银行] 第${row + 1}行: 交易类型="${loanFlag}", 账单类型="${billType}"`);
    } else {
      console.log(`[中国银行] 第${row + 1}行: 无法确定账单类型，跳过`);
      return null;
    }
    
    // 确定金额 - 中国银行统一使用交易金额字段
    let billAmount = null;
    if (fieldIndexes.tradeAmountIndex !== -1) {
      const amountStr = rowData[fieldIndexes.tradeAmountIndex];
      billAmount = CSVUtils.parseAmount(amountStr);
      // 对于支出，取绝对值
      if (billType === 'expense' && billAmount !== null) {
        billAmount = Math.abs(billAmount);
      }
      console.log(`[中国银行] 第${row + 1}行: ${billType === 'expense' ? '支出' : '收入'}金额=${billAmount}`);
    }
    
    // 转换余额
    let billBalance = null;
    if (fieldIndexes.balanceIndex !== -1) {
      billBalance = CSVUtils.parseAmount(rowData[fieldIndexes.balanceIndex]);
    }
    
    // 提取其他字段
    const transactionDate = fieldIndexes.transactionDateIndex !== -1 ? rowData[fieldIndexes.transactionDateIndex] : '';
    const transactionTime = fieldIndexes.transactionTimeIndex !== -1 ? rowData[fieldIndexes.transactionTimeIndex] : '';
    const payerName = fieldIndexes.payerNameIndex !== -1 ? rowData[fieldIndexes.payerNameIndex] : '';
    const payeeName = fieldIndexes.payeeNameIndex !== -1 ? rowData[fieldIndexes.payeeNameIndex] : '';
    const usage = fieldIndexes.usageIndex !== -1 ? rowData[fieldIndexes.usageIndex] : '';
    const summary = fieldIndexes.summaryIndex !== -1 ? rowData[fieldIndexes.summaryIndex] : '';
    const remark = fieldIndexes.remarkIndex !== -1 ? rowData[fieldIndexes.remarkIndex] : '';
    
    // 组合交易日期和时间
    let combinedTransactionTime = '';
    if (transactionDate && transactionTime) {
      // 格式化日期：20250401 -> 2025-04-01
      const formattedDate = transactionDate.length === 8 ? 
        `${transactionDate.substring(0, 4)}-${transactionDate.substring(4, 6)}-${transactionDate.substring(6, 8)}` : 
        transactionDate;
      combinedTransactionTime = `${formattedDate} ${transactionTime}`;
    } else if (transactionDate) {
      combinedTransactionTime = transactionDate;
    } else if (transactionTime) {
      combinedTransactionTime = transactionTime;
    }
    
    // 确定对方单位
    let opposingUnit = '';
    if (billType === 'income' && payerName) {
      opposingUnit = payerName;
    } else if (billType === 'expense' && payeeName) {
      opposingUnit = payeeName;
    }
    
    console.log(`[中国银行] 第${row + 1}行: 交易时间="${combinedTransactionTime}", 对方单位="${opposingUnit}", 金额=${billAmount}, 余额=${billBalance}`);
    
    const dataItem: ParsedDataItem = {
      directlyExtracted: {
        transactionTime: combinedTransactionTime,
        billType,
        opposingUnit,
        billAmount,
        billBalance
      },
      forModel: {
        usage,
        summary,
        remark,
        originData: JSON.stringify(originData)
      }
    };
    
    return dataItem;
  }
} 