import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Loader2, Clock, CheckCircle, XCircle } from 'lucide-react';

interface FormData {
  companyName: string;
  adminName: string;
  adminPhone: string;
  adminEmail: string;
  businessType: string;
  maxStores: string;
  applicationId?: string;  // 申请编号
  version?: string;       // 版本号
}

interface ApplicationResponse {
  success: boolean;
  message: string;
  applicationId?: string;
  applicationTime?: string;
  version?: string;
}

interface ApplicationInfo {
  id: string;
  time: string;
  status: string;
  version: string;
}

const AccountApplicationForm = () => {
  const [formData, setFormData] = useState<FormData>({
    companyName: '',
    adminName: '',
    adminPhone: '',
    adminEmail: '',
    businessType: '',
    maxStores: '',
    applicationId: '',
    version: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [applicationInfo, setApplicationInfo] = useState<ApplicationInfo>({
    id: '',
    time: '',
    status: '0',
    version: ''
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    if (!formData.companyName || !formData.adminName || !formData.adminPhone || !formData.adminEmail || !formData.businessType || !formData.maxStores) {
      toast.error("请填写完整信息", { description: "所有字段都是必填项" });
      return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.adminEmail)) {
      toast.error("邮箱格式错误", { description: "请输入正确的邮箱地址" });
      return;
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.adminPhone)) {
      toast.error("手机号格式错误", { description: "请输入正确的手机号码" });
      return;
    }
    setIsSubmitting(true);

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // 如果是撤销后重新提交，则添加申请编号头
      if (formData.applicationId) {
        headers['X-Application-Id'] = formData.applicationId;
      }
      
      const response = await fetch('/api/company-profile/register', {
        method: 'POST',
        headers,
        body: JSON.stringify(formData),
      });

      const data: ApplicationResponse = await response.json();

      if (!response.ok) {
        // 处理特定错误状态
        if (response.status === 403 && data.message.includes('申请已通过')) {
          toast.info("申请状态提示", { description: "申请已经通过审核，无需再修改" });
          // 刷新当前申请状态
          await fetchApplicationStatus();
          return;
        }
        
        throw new Error(data.message || '提交失败，请稍后重试');
      }

      setIsSubmitting(false);
      setIsSubmitted(true);
      
      // 保存申请ID、时间和版本号用于显示
      if (data.applicationId && data.applicationTime) {
        setApplicationInfo({
          id: data.applicationId,
          time: new Date(data.applicationTime).toLocaleString('zh-CN'),
          status: '0', // 默认为待处理
          version: data.version || ''
        });
      }

      toast.success("申请提交成功！", { description: data.message || "我们将在5个工作日内审核开通，请关注邮件或短信通知。" });
    } catch (error: any) {
      setIsSubmitting(false);
      toast.error("提交失败", { description: error.message || "请稍后重试" });
      console.error("提交申请错误:", error);
    }
  };

  const fetchApplicationStatus = async () => {
    if (!applicationInfo.id || !formData.adminPhone) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/company-profile/register?phoneNumber=${formData.adminPhone}&applicationId=${applicationInfo.id}`);
      
      if (!response.ok) {
        throw new Error('获取申请状态失败');
      }
      
      const data = await response.json();
      
      setApplicationInfo({
        id: data.applicationId || applicationInfo.id,
        time: applicationInfo.time, // 保持原有时间
        status: data.status || '0',
        version: data.version || ''
      });

      // 根据状态显示不同的提示信息
      let statusMessage = '';
      switch (data.status) {
        case '0':
          statusMessage = '申请状态仍为待处理';
          break;
        case '1':
          statusMessage = '申请已通过！您可以使用手机号登录系统';
          break;
        case '2':
          statusMessage = '很遗憾，您的申请被驳回';
          break;
        case '3':
          statusMessage = '申请已撤销';
          break;
        default:
          statusMessage = '申请状态未知';
      }
      
      toast.info("状态已刷新", { description: statusMessage });
    } catch (error) {
      toast.error("获取状态失败", { description: "请稍后再试" });
      console.error("获取申请状态错误:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelApplication = async () => {
    if (!applicationInfo.id || !formData.adminPhone || !applicationInfo.version) {
      toast.error("撤销失败", { description: "缺少必要信息" });
      return;
    }
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/company-profile/register`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formData.adminPhone,
          applicationId: applicationInfo.id,
          version: applicationInfo.version
        })
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '撤销申请失败');
      }
      
      const result = await response.json();
      
      // 撤销成功，保留申请ID和新的版本号用于重新提交
      const prevAppId = applicationInfo.id;
      const newVersion = result.version || applicationInfo.version; // 使用接口返回的新版本号
      
      // 重置状态但保留申请编号和版本号
      setIsSubmitted(false);
      setFormData(prev => ({
        ...prev,
        applicationId: prevAppId,
        version: newVersion
      }));
      
      // 更新申请信息中的版本号
      setApplicationInfo(prev => ({
        ...prev,
        version: newVersion,
        status: '3' // 已撤销状态
      }));
      
      toast.success("申请已撤销", { description: "您可以修改信息后重新提交" });
    } catch (error: any) {
      toast.error("撤销失败", { description: error.message || "请稍后再试" });
      console.error("撤销申请错误:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 渲染状态图标
  const renderStatusIcon = () => {
    switch (applicationInfo.status) {
      case '1': // 已通过
        return (
          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center">
            <CheckCircle className="w-6 h-6 text-white" />
          </div>
        );
      case '2': // 已驳回
        return (
          <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center">
            <XCircle className="w-6 h-6 text-white" />
          </div>
        );
      case '3': // 已撤销
        return (
          <div className="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center">
            <XCircle className="w-6 h-6 text-white" />
          </div>
        );
      default: // 待处理
        return (
          <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
            <Clock className="w-6 h-6 text-white" />
          </div>
        );
    }
  };

  // 渲染状态文本
  const renderStatusText = () => {
    switch (applicationInfo.status) {
      case '1':
        return '已通过';
      case '2':
        return '已驳回';
      case '3':
        return '已撤销';
      default:
        return '待处理';
    }
  };

  if (isSubmitted) {
    
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm rounded-3xl">
          <CardContent className="p-8 text-center">
            {/* 状态图标 */}
            <div className="w-20 h-20 bg-gradient-to-br from-yellow-100 to-yellow-50 rounded-full flex items-center justify-center mx-auto mb-8">
              {renderStatusIcon()}
            </div>
            
            {/* 申请状态 */}
            <p className="text-gray-500 text-sm mb-2">申请状态</p>
            <h2 className="text-3xl font-bold text-gray-800 mb-8">
              {renderStatusText()}
            </h2>
            
            {/* 申请信息 */}
            <div className="space-y-3 mb-8">
              <p className="text-gray-600">
                申请ID: <span className="text-gray-800 font-medium">{applicationInfo.id || 'APP115067'}</span>
              </p>
              <p className="text-gray-600">
                提交时间: <span className="text-gray-800 font-medium">{applicationInfo.time || '2025/5/31 17:01:55'}</span>
              </p>
            </div>
            
            {/* 按钮组 */}
            <div className="flex gap-4">
              {/* 只有在待处理或已驳回状态才显示撤销按钮 */}
              {(applicationInfo.status === '0' || applicationInfo.status === '2') && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="flex-1 h-12 rounded-2xl border-gray-300 text-gray-700 hover:bg-gray-50">
                      撤销申请
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="w-[90vw] max-w-md mx-auto rounded-3xl border-0 shadow-2xl p-6">
                    <AlertDialogHeader className="text-center">
                      <AlertDialogTitle className="text-xl font-bold text-gray-900 mb-2">
                        确认撤销申请?
                      </AlertDialogTitle>
                      <AlertDialogDescription className="text-gray-600 text-base leading-relaxed">
                        此操作无法撤回。撤销后，您的申请将不再被处理。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex-col gap-3 mt-6 sm:flex-row">
                      <AlertDialogCancel className="w-full h-12 rounded-2xl border-gray-300 text-gray-700 hover:bg-gray-50 order-2 sm:order-1">
                        取消
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        className="w-full h-12 rounded-2xl bg-gray-900 text-white hover:bg-gray-800 order-1 sm:order-2"
                        onClick={handleCancelApplication}
                      >
                        确认撤销
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
              
              {/* 已撤销状态下显示重新编辑按钮 */}
              {applicationInfo.status === '3' && (
                <Button 
                  variant="outline" 
                  className="flex-1 h-12 rounded-2xl border-gray-300 text-gray-700 hover:bg-gray-50"
                  onClick={() => setIsSubmitted(false)}
                >
                  重新编辑
                </Button>
              )}
              
              <Button 
                className={`${applicationInfo.status === '0' || applicationInfo.status === '2' || applicationInfo.status === '3' ? 'flex-1' : 'w-full'} h-12 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-2xl`} 
                onClick={fetchApplicationStatus}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    刷新中...
                  </>
                ) : (
                  '刷新状态'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 p-4">
      <div className="w-full max-w-md mx-auto">
        {/* 顶部品牌区域 */}
        <div className="text-center mb-8 pt-8">
          <h1 className="text-lg font-bold text-black mb-2">
            <span className="text-black text-lg">欢迎申请</span>
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-3xl font-medium text-purple-700">慧账AI</span>
          </h1>
          <p className="text-lg text-gray-700 mb-2 leading-relaxed">
            即刻解锁AI驱动的精准经营分析！
          </p>
          <p className="text-gray-600">
            请填写以下信息以完成账套申请
          </p>
        </div>

        {/* 表单区域 */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <h2 className="text-xl font-semibold text-center text-gray-800">账套申请信息</h2>
          </CardHeader>
          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="companyName" className="text-gray-700 font-medium">公司名称 *</Label>
                <Input id="companyName" placeholder="请输入公司名称" value={formData.companyName} onChange={e => handleInputChange('companyName', e.target.value)} className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="adminName" className="text-gray-700 font-medium">管理员姓名 *</Label>
                <Input id="adminName" placeholder="请输入管理员姓名" value={formData.adminName} onChange={e => handleInputChange('adminName', e.target.value)} className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="adminPhone" className="text-gray-700 font-medium">管理员手机号 *</Label>
                <Input id="adminPhone" placeholder="请输入管理员手机号" value={formData.adminPhone} onChange={e => handleInputChange('adminPhone', e.target.value)} className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="adminEmail" className="text-gray-700 font-medium">管理员邮箱 *</Label>
                <Input id="adminEmail" type="email" placeholder="请输入邮箱，如 <EMAIL>" value={formData.adminEmail} onChange={e => handleInputChange('adminEmail', e.target.value)} className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType" className="text-gray-700 font-medium">业务类型 *</Label>
                <Select value={formData.businessType} onValueChange={value => handleInputChange('businessType', value)}>
                  <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12">
                    <SelectValue placeholder="请选择业务类型" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl">
                    <SelectItem value="零售连锁">零售连锁</SelectItem>
                    <SelectItem value="餐饮服务">餐饮服务</SelectItem>
                    <SelectItem value="美容美发">美容美发</SelectItem>
                    <SelectItem value="健身娱乐">健身娱乐</SelectItem>
                    <SelectItem value="教育培训">教育培训</SelectItem>
                    <SelectItem value="汽车服务">汽车服务</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxStores" className="text-gray-700 font-medium">账套下预期门店数量 *</Label>
                <Select value={formData.maxStores} onValueChange={value => handleInputChange('maxStores', value)}>
                  <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-400 focus:ring-purple-400 h-12">
                    <SelectValue placeholder="请选择预期门店数" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl">
                    <SelectItem value="5">1-5家</SelectItem>
                    <SelectItem value="20">6-20家</SelectItem>
                    <SelectItem value="50">21-50家</SelectItem>
                    <SelectItem value="100">51-100家</SelectItem>
                    <SelectItem value="200">100家以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button type="submit" disabled={isSubmitting} className="w-full h-12 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-medium rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 active:scale-95">
                {isSubmitting ? <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    提交中...
                  </> : '提交申请'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* 底部信息 */}
        <div className="text-center mt-6 text-sm text-gray-500">
          <p>数据更新时间：2025-05-31 01:45 AM PDT</p>
        </div>
      </div>
    </div>
  );
};

export default AccountApplicationForm;