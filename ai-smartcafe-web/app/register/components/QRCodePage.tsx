import React, { useEffect, useState } from 'react';
import QRCode from 'qrcode';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface QRCodePageProps {
  onApplyClick: () => void;
}

const QRCodePage = ({ onApplyClick }: QRCodePageProps) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  useEffect(() => {
    // 生成指向申请表单的二维码
    const formUrl = `${window.location.origin}/register`;

    console.log('@@@@@@@@@@@@@', formUrl);

    QRCode.toDataURL(formUrl, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
      .then(url => {
        setQrCodeUrl(url);
      })
      .catch(err => {
        console.error('生成二维码失败:', err);
      });
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        {/* 顶部品牌区域 */}
        <div className="text-center mb-8">
          <h1 className="text-lg font-bold text-black mb-2">
            <span className="text-black text-lg">欢迎申请</span>
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-3xl font-medium text-purple-700">慧账AI</span>
          </h1>
          <p className="text-lg text-gray-700 mb-6 leading-relaxed">
            即刻解锁AI驱动的精准经营分析！
          </p>
        </div>

        {/* 二维码区域 */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <p className="text-gray-600 mb-4">请扫描下方二维码进入申请页面</p>
              {qrCodeUrl ? (
                <div className="flex justify-center mb-4">
                  <img src={qrCodeUrl} alt="申请表单二维码" className="rounded-lg shadow-md" />
                </div>
              ) : (
                <div className="w-64 h-64 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                  <p className="text-gray-500">正在生成二维码...</p>
                </div>
              )}
            </div>

            <div className="border-t pt-4">
              <p className="text-sm text-gray-500 mb-4">或者直接点击下方按钮进入</p>
              <Button
                className="w-full h-12 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-medium rounded-xl"
                onClick={onApplyClick}
              >
                立即申请
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default QRCodePage;