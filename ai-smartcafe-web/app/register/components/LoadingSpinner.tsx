
import React from 'react';

const LoadingSpinner = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 flex items-center justify-center">
      <div className="text-center">
        <div className="relative">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg animate-pulse">
            <span className="text-white text-xl font-bold">慧</span>
          </div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-purple-200 rounded-2xl animate-spin mx-auto"></div>
        </div>
        <p className="text-gray-600 mt-4">正在加载...</p>
      </div>
    </div>
  );
};

export default LoadingSpinner;
