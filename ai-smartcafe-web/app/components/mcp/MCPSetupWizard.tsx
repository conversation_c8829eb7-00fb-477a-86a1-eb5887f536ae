"use client"

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Circle, ArrowRight, Settings, Users } from "lucide-react";
import { AccountSetupClient } from "./AccountSetupClient";

type SetupStep = 'overview' | 'account-management' | 'system-accounts' | 'complete';

interface MCPSetupWizardProps {
  onComplete?: () => void;
}

export const MCPSetupWizard = ({ onComplete }: MCPSetupWizardProps) => {
  const [currentStep, setCurrentStep] = useState<SetupStep>('overview');
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const steps = [
    {
      id: 'account-management',
      title: '账套管理',
      description: '配置公司基本信息和银行账户',
      icon: Settings,
      component: AccountSetupClient
    },
    {
      id: 'system-accounts',
      title: '系统账号管理',
      description: '设置用户账号和权限',
      icon: Users,
      component: null // 将在后续实现
    }
  ];

  const handleStepComplete = (stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
    
    // 自动进入下一步
    const currentIndex = steps.findIndex(step => step.id === stepId);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id as SetupStep);
    } else {
      // 所有步骤完成
      setCurrentStep('complete');
      setTimeout(() => {
        onComplete?.();
      }, 2000);
    }
  };

  const renderOverview = () => (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">欢迎使用 AI智能咖啡厅管理系统</CardTitle>
          <p className="text-gray-600 mt-2">
            为了确保系统正常运行，我们需要引导您完成以下配置步骤
          </p>
        </CardHeader>
      </Card>

      <div className="grid gap-4">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.has(step.id);
          const IconComponent = step.icon;
          
          return (
            <Card 
              key={step.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                isCompleted ? 'bg-green-50 border-green-200' : 'hover:bg-gray-50'
              }`}
              onClick={() => setCurrentStep(step.id as SetupStep)}
            >
              <CardContent className="flex items-center p-6">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#9b87f5] text-white mr-4">
                  {isCompleted ? (
                    <CheckCircle className="h-6 w-6" />
                  ) : (
                    <IconComponent className="h-6 w-6" />
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-1">
                    步骤 {index + 1}: {step.title}
                  </h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
                
                <div className="flex items-center">
                  {isCompleted ? (
                    <span className="text-green-600 font-medium">已完成</span>
                  ) : (
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <h4 className="font-semibold text-blue-900 mb-2">配置说明</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>账套管理</strong>：配置您的公司银行账户信息，用于后续的对账单处理和财务分析</li>
            <li>• <strong>系统账号管理</strong>：为您的团队成员创建账号，设置适当的权限和角色</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );

  const renderComplete = () => (
    <div className="w-full max-w-2xl mx-auto">
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-8">
          <CheckCircle className="h-24 w-24 text-green-500 mb-6" />
          <h2 className="text-3xl font-bold text-green-700 mb-4">设置完成！</h2>
          <p className="text-center text-gray-600 mb-6">
            恭喜您已完成所有必要的配置步骤。系统现在已准备就绪，您可以开始使用所有功能了。
          </p>
          <div className="text-sm text-gray-500 bg-gray-50 p-4 rounded-lg w-full">
            <h4 className="font-medium mb-2">您现在可以：</h4>
            <ul className="space-y-1">
              <li>• 上传和处理银行对账单</li>
              <li>• 管理费用分类和账单</li>
              <li>• 查看财务报表和分析</li>
              <li>• 管理团队成员和权限</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSystemAccounts = () => (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl text-center">系统账号管理</CardTitle>
          <div className="text-center text-sm text-gray-600">
            步骤 2/2: 设置用户账号和权限
          </div>
        </CardHeader>
        <CardContent className="text-center py-12">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">系统账号管理功能即将推出</h3>
          <p className="text-gray-600 mb-6">
            此功能正在开发中，目前您可以先完成账套管理配置
          </p>
          <div className="flex justify-center space-x-4">
            <Button 
              variant="outline" 
              onClick={() => setCurrentStep('overview')}
            >
              返回总览
            </Button>
            <Button 
              onClick={() => handleStepComplete('system-accounts')}
              className="bg-[#9b87f5] hover:bg-[#8671e0]"
            >
              跳过此步骤
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (currentStep === 'overview') {
    return renderOverview();
  }

  if (currentStep === 'complete') {
    return renderComplete();
  }

  if (currentStep === 'account-management') {
    return (
      <div className="w-full">
        <AccountSetupClient 
          onComplete={() => handleStepComplete('account-management')} 
        />
        <div className="flex justify-center mt-6">
          <Button 
            variant="outline" 
            onClick={() => setCurrentStep('overview')}
          >
            返回总览
          </Button>
        </div>
      </div>
    );
  }

  if (currentStep === 'system-accounts') {
    return renderSystemAccounts();
  }

  return null;
}; 