# Excel在线处理工具

这是一个基于Next.js、React、Tailwind CSS和shadcn/ui构建的在线Excel处理工具。该工具允许用户上传Excel文件，在线编辑数据，并下载修改后的文件。

## 功能特点

- 上传Excel文件（支持.xls和.xlsx格式）
- 在线查看和编辑Excel数据
- 保存编辑后的数据
- 下载修改后的Excel文件
- 响应式设计，适配各种设备

## 技术栈

- **前端框架**: [Next.js](https://nextjs.org/) 和 [React](https://reactjs.org/)
- **样式**: [Tailwind CSS](https://tailwindcss.com/)
- **UI组件**: [shadcn/ui](https://ui.shadcn.com/)
- **表格处理**: [TanStack Table](https://tanstack.com/table/)
- **Excel处理**: [SheetJS (xlsx)](https://sheetjs.com/)

## 快速开始

### 安装依赖

```bash
npm install
```

### 运行开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run export
```

### 运行生产版本

```bash
npm start
```

## 使用方法

1. 打开应用，看到上传界面
2. 拖拽Excel文件到上传区域，或点击"选择文件"按钮选择文件
3. 上传成功后，将显示Excel数据表格
4. 点击单元格可以编辑内容
5. 编辑完成后，点击"保存"按钮保存更改
6. 点击"下载"按钮可将编辑后的文件下载到本地
7. 如需上传新文件，点击表格下方的"上传新文件"链接

## 许可证

MIT

# AI Smart Cafe Web

这是AI Smart Cafe的Web前端项目。

## 开发环境要求

- Node.js 16.x
- npm 8.x 或更高版本

## 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

服务器将在 http://localhost:3000 上启动。

## 服务器部署

系统要求:
- CentOS 7
- Node.js 16.20.2 (与GLIBC 2.17兼容)
- PM2 (用于进程管理)

部署步骤:

1. 确保服务器已安装Node.js 16.x:

```bash
# 在服务器上运行
curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs
npm install -g pm2
```

2. 从本地开发环境部署:

```bash
# 在本地项目目录运行
./deploy.sh
```

或者手动部署:

```bash
# 构建项目
npm install
npm run build

# 将文件同步到服务器
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude '.next/cache' ./ root@your-server:/var/www/ai-smartcafe-web/

# 在服务器上安装依赖并启动
ssh root@your-server "cd /var/www/ai-smartcafe-web && npm install --production && PORT=3000 pm2 start npm --name 'ai-smartcafe-web' -- start"
```

## 注意事项

- 项目使用Next.js 14，与Node.js 16兼容
- 为确保与CentOS 7 (GLIBC 2.17)兼容，请不要升级到更高版本的Next.js
- 若需更新依赖，请先确认新版本与Node.js 16兼容
