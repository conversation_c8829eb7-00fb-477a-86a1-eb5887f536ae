package com.fuioupay.domain.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fuioupay.domain.model.enums.ModelType;
import com.fuioupay.domain.model.enums.ModelProvider;
import lombok.Data;

import java.util.Map;

@Data
public class ModelRequest {

    private String systemPrompt;

    private String prompt;

    private ModelType modelType;

    // 各模型特有参数
    private Map<String, Object> providerParams;

    @JsonIgnore
    public ModelProvider getProvider() {
        return modelType.getProvider();
    }
}
