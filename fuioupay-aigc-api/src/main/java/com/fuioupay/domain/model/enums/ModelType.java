package com.fuioupay.domain.model.enums;

import lombok.Getter;

public enum ModelType {

    // 模型大类_提供商
    // deepseek，官方
    DEEPSEEK_V3_OFFICIAL(ModelFamily.DEEPSEEK, ModelProvider.OFFICIAL),
    DEEPSEEK_R1_OFFICIAL(ModelFamily.DEEPSEEK, ModelProvider.OFFICIAL),
    // deepseek，火山
    DEEPSEEK_V3_VOLCANO(ModelFamily.DEEPSEEK, ModelProvider.VOLCANO),
    DEEPSEEK_R1_VOLCANO(ModelFamily.DEEPSEEK, ModelProvider.VOLCANO),
    // deepseek，阿里百炼
    DEEPSEEK_V3_ALIBAILIAN(ModelFamily.DEEPSEEK, ModelProvider.ALIBAILIAN),
    DEEPSEEK_R1_ALIBAILIAN(ModelFamily.DEEPSEEK, ModelProvider.ALIBAILIAN),
    // deepseek，腾讯元宝
    DEEPSEEK_V3_SILICON(ModelFamily.DEEPSEEK, ModelProvider.TENCENT),
    DEEPSEEK_R1_SILICON(ModelFamily.DEEPSEEK, ModelProvider.TENCENT),
    // doubao，火山
    DOUBAO_1_5_THINKING_PRO_250415(ModelFamily.DOUBAO, ModelProvider.OFFICIAL),
    // 千问，阿里百炼
    QWEN_ALIBAILIAN(ModelFamily.QWEN, ModelProvider.OFFICIAL),
    // chatgpt，官方
    CHATGPT_OFFICIAL(ModelFamily.CHATGPT, ModelProvider.OFFICIAL);

    @Getter
    private final ModelFamily family;
    @Getter
    private final ModelProvider provider;

    private ModelType(ModelFamily family, ModelProvider provider) {
        this.family = family;
        this.provider = provider;
    }

}
