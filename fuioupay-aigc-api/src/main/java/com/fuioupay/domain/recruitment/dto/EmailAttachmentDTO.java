package com.fuioupay.domain.recruitment.dto;

import com.fuioupay.domain.recruitment.enums.PositionType;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class EmailAttachmentDTO {

    // 附件名
    private String filename;
    // 附件大小
    private long size;
    // 附件内容
    private byte[] content;
    // 阿里云识别出mkdown文本内容
    private String text;
    // 阿里云识别是否成功
    private boolean isSuccess;
    // 附件对应主题
    private String subject;

    private PositionType positionType;

}
