package com.fuioupay.domain.recruitment.enums;

import cn.hutool.core.collection.ListUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import java.util.stream.Collectors;

/**
 * 职位类型枚举
 *
 * <AUTHOR>
 */
public enum PositionType {

    JAVA(ListUtil.toList("资深Java开发工程师", "Java高级开发工程师"),
            ";<EMAIL>;cheng<PERSON>@fuioupay.com", true),
    TEST(ListUtil.toList("高级软件测试工程师"), ";<EMAIL>", true),
    WEB(ListUtil.toList("前端资深开发工程师"), ";<EMAIL>;<EMAIL>", true),
    PRODUCT(ListUtil.toList("产品经理"), "", false),

    // 报错提示
    ERROR(ListUtil.toList("【岗位分析自动化流程报错了，请尽快联系开发查看！】"), "<EMAIL>", false),
    ;

    // 招聘岗位名称List = 邮件主题 = IPositionInfo
    @Getter
    private final List<String> name;
    // 简历解析抄送人
    @Getter
    private final String emailTo;
    @Getter
    private final Boolean isEffective;

    PositionType(List<String> name, String emailTo, Boolean isEffective) {
        this.name = name;
        this.emailTo = emailTo;
        this.isEffective = isEffective;
    }

    public static List<PositionType> getInstancesByEffective() {
        return Arrays.stream(PositionType.values()).filter(PositionType::getIsEffective).collect(Collectors.toList());
    }
}