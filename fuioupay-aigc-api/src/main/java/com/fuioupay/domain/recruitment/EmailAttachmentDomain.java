package com.fuioupay.domain.recruitment;


import com.fuioupay.domain.recruitment.dto.EmailAttachmentDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface EmailAttachmentDomain {

    /**
     * 返回附件内容
     * 只取pdf和word
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param to            发件人
     * @param subjectFilter 过滤主题
     * @return
     */
    List<EmailAttachmentDTO> downloadAttachments(LocalDateTime startTime, LocalDateTime endTime, String to,
            String... subjectFilter);

}

