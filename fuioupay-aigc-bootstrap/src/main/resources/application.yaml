server:
  port: 8091

spring:
  web:
    resources:
      static-locations: classpath:/static/
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
    naming:
      physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

#  mail:
#    host: mail.fuioupay.com
#    port: 25
#    username: <EMAIL>
#    password: 4}um2zqRxG][3gsX
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
