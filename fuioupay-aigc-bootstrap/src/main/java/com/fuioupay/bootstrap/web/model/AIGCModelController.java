package com.fuioupay.bootstrap.web.model;

import com.fuioupay.domain.model.req.ModelRequest;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 模型调用对外接口
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/models")
public class AIGCModelController {

    @Resource
    AIGCModelApplication aigcModelApplication;

    @PostMapping
    @ResponseBody
    public Mono<Object> invokeModel(@RequestBody ModelRequest request) {
        return aigcModelApplication.callModel(request.getModelType(), request);
    }

    @GetMapping(value = "/stream",
            produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<Object>> streamModel(@RequestBody ModelRequest request)
    {
        return aigcModelApplication.callModelStream(request.getModelType(), request).map(
                data -> ServerSentEvent.builder(data).build());
    }
}
