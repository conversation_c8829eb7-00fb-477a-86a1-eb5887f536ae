package com.fuioupay.bootstrap.web.model;

import com.fuioupay.domain.ModelInvoker;
import com.fuioupay.domain.model.enums.ModelType;
import com.fuioupay.domain.model.req.ModelRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 模型调用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AIGCModelApplication {

    @Resource
    ModelInvoker modelInvoker;

    public Mono<Object> callModel(ModelType modelType, ModelRequest request) {
        return modelInvoker.callModel(modelType, request);
    }

    public Flux<Object> callModelStream(ModelType modelType, ModelRequest request) {
        return modelInvoker.callModelStream(modelType, request).doOnError(e -> log.error("Stream error", e));
    }
}
