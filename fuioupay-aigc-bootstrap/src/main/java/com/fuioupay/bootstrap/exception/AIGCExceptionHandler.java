package com.fuioupay.bootstrap.exception;

import com.fuioupay.domain.AIGCException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class AIGCExceptionHandler {

    @ExceptionHandler(AIGCException.class)
    @ResponseBody
    public ResponseEntity<?> handleAIExamException(AIGCException e) {

        return new ResponseEntity<>(new ErrorResponse(e.getMessage(), e.getCode()), HttpStatus.BAD_REQUEST);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ErrorResponse {
        private String message;

        private Integer code;
    }
}
