package com.fuioupay.bootstrap.scheduler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.fuioupay.common.constants.DatePattern;
import com.fuioupay.common.utils.JacksonUtil;
import com.fuioupay.domain.AIGCException;
import com.fuioupay.domain.ModelInvoker;
import com.fuioupay.domain.model.enums.ModelType;
import com.fuioupay.domain.model.req.ModelRequest;
import com.fuioupay.domain.model.rsp.DeepSeekChatCompletionResponse;
import com.fuioupay.domain.recruitment.DocAdvanceDomain;
import com.fuioupay.domain.recruitment.EmailAttachmentDomain;
import com.fuioupay.domain.recruitment.dto.EmailAttachmentDTO;
import com.fuioupay.domain.recruitment.enums.PositionType;
import com.fuioupay.domain.recruitment.prompt.ResumeAnalysicPrompt;
import jakarta.annotation.Resource;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.parser.Parser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 邮箱简历收集
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ResumeCollectorScheduler {

    @Resource
    EmailAttachmentDomain emailAttachmentDomain;
    @Resource
    DocAdvanceDomain docAdvanceDomain;
    @Resource
    ModelInvoker modelInvoker;

    @Resource
    private JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String mailFrom;
    @Value("${spring.mail.mailTo:<EMAIL>}")
    private String mailTo;
    @Value("${spring.mail.mailCopy:<EMAIL>}")
    private String mailCopy;
    // 一次传几个简历给ai
    private static final Integer BATCHSIZE = 1;

    // @Scheduled(initialDelay = 5000)
    @Scheduled(cron = "0 0 1 * * ?")
    @Retryable(maxAttempts = 4,
               retryFor = {Exception.class, AIGCException.class},
               // 重试延迟和延迟倍数
               backoff = @Backoff(delay = 5000,
                                  multiplier = 3))
    public void scheduleDailyCollection() {
        log.info("开始执行简历收集定时任务...");
        List<EmailAttachmentDTO> attachmentInfos = emailAttachmentDomain.downloadAttachments(
                LocalDateTime.now().minusDays(1).minusHours(2), LocalDateTime.now(), null, "【BOSS直聘】");
        log.info("简历附件拿到了，共计 {} 份", attachmentInfos.size());

        // 过滤规则
        List<EmailAttachmentDTO> filterAttachmentInfos = attachmentInfos.stream().filter(a -> {
            if (CollectionUtils.isEmpty(PositionType.getInstancesByEffective())) {
                return false;
            }
            // ****PositionType放开哪些岗位筛选****
            return PositionType.getInstancesByEffective().stream().filter(pos -> {
                boolean con = pos.getName().stream().anyMatch(name -> Optional.ofNullable(a.getSubject()).orElse("")
                        .toUpperCase().contains(name.toUpperCase()));
                if (con) a.setPositionType(pos);
                return con;
            }).count() > 0;
        }).collect(Collectors.toList());

        /*
        // 临时测试使用
        // filterAttachmentInfos = filterAttachmentInfos.subList(0, Math.min(filterAttachmentInfos.size(), 20));
        */

        List<EmailAttachmentDTO> text = docAdvanceDomain.getText(filterAttachmentInfos);
        log.info("调用阿里云获取简历文本成功，共计 {} 份", text.size());

        Map<PositionType, List<EmailAttachmentDTO>> positionMap = text.stream().filter(
                dto -> dto.getPositionType() != null).collect(
                Collectors.groupingBy(EmailAttachmentDTO::getPositionType));
        log.info("简历按岗位分组，共计 {} 个岗位类型", positionMap.size());

        positionMap.forEach((positionType, positionDtos) -> {
            List<String> orderAnalysicList = new ArrayList<>();
            log.info("开始处理 {} 岗位的简历，共计 {} 份", positionType.getName(), positionDtos.size());
            for (int i = 0; i < positionDtos.size(); i += BATCHSIZE) {
                int endIndex = Math.min(i + BATCHSIZE, positionDtos.size());
                List<EmailAttachmentDTO> batch = positionDtos.subList(i, endIndex);
                log.info("开始处理 {} 岗位的第{}批简历，本批包含{}份", positionType.getName(), (i / BATCHSIZE) + 1,
                        batch.size());
                try {
                    // 同步等待每个批次的call完成
                    callSync(batch, positionType, orderAnalysicList);
                } catch (Exception e) {
                    log.error("处理简历批次失败: {}", e.getMessage(), e);
                }
            }

            // 所有批次处理完后，再调用排序
            callSummary(positionType, orderAnalysicList, positionDtos);
        });
    }

    @Recover
    public void recoverFromMaxRetryAttempts(Exception e) {
        log.error("简历收集定时任务执行失败，已达到最大重试次数，错误: {}", e.getMessage(), e);
        // 发送警报邮件通知管理员
        sendMailByAigcResponse(PositionType.ERROR, e.getMessage() + e, new LinkedHashSet<String>(),
                new LinkedHashSet<String>(), ListUtil.empty(), 0);
    }

    // 评分模型 - 同步版本
    private void callSync(List<EmailAttachmentDTO> text, PositionType positionType, List<String> orderAnalysicList) {
        String prompt = """
                候选人简历文本（多候选人用"------- 候选人简历内容---"分隔）。
                
                %s
                
                ===============
                
                用户问题：请您帮我评价以上 %s 份简历。
                """;

        String resumeContent = text.stream().map(dto -> "\n\n------- 候选人简历内容---\n" + Optional.ofNullable(
                dto.getSubject()).orElse("") + "\n\n" + dto.getText()).collect(Collectors.joining());

        prompt = String.format(prompt, resumeContent, text.size());

        ModelRequest request = new ModelRequest();
        request.setPrompt(prompt);
        request.setSystemPrompt(ResumeAnalysicPrompt.getInstance().getSystemSuggestionPromptForPosition(positionType));
        request.setProviderParams(new HashMap<>());

        log.info("callSync.开始调用ai模型，岗位类型：{}，入参： \n\n {} \n\n\n\n", positionType.getName(),
                JacksonUtil.toJson(request));

        try {
            String result = callModel(request);
            Optional.ofNullable(result).ifPresent(r -> {
                orderAnalysicList.add(r);
                log.info("批次分析完成，结果已添加到列表");
            });
        } catch (Exception e) {
            log.error("批次分析调用模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("批次分析失败", e);
        }
    }

    // 排名模型 - 同步版本
    private void callSummary(PositionType positionType, List<String> orderAnalysicList,
            List<EmailAttachmentDTO> attachList)
    {
        if (orderAnalysicList.isEmpty()) {
            log.warn("没有简历分析结果，跳过排名模型调用");
            return;
        }

        // 分批处理，每批最多20份简历
        final int batchSize = 20;
        StringBuilder finalResult = new StringBuilder();
        Set<String> matchNameList = new LinkedHashSet<>(), pendingNameList = new LinkedHashSet<>();

        IntStream.range(0, (orderAnalysicList.size() + batchSize - 1) / batchSize).forEach(batchIndex -> {
            int startIndex = batchIndex * batchSize;
            int endIndex = Math.min(startIndex + batchSize, orderAnalysicList.size());
            List<String> batchAnalysis = orderAnalysicList.subList(startIndex, endIndex);

            log.info("开始处理第{}批简历排名分析，本批包含{}份", batchIndex + 1, batchAnalysis.size());
            ModelRequest request = new ModelRequest();
            request.setPrompt(
                    String.join("\n\n--候选人分割线--\n\n", batchAnalysis) + "\n\n\n用户问题： 请您帮我评价以上 " +
                            batchAnalysis.size() + " 位候选人。");
            request.setSystemPrompt(ResumeAnalysicPrompt.getInstance().getSystemSummaryPromptForPosition(positionType));
            request.setProviderParams(new HashMap<>());

            log.info("callSummary.开始调用ai模型，岗位类型：{}，入参： \n\n {} \n\n\n\n", positionType.getName(),
                    JacksonUtil.toJson(request));

            try {
                String result = callModel(request);
                Optional.ofNullable(result).ifPresent(xmlStr -> {
                    try {
                        Document xmlDoc = Jsoup.parse(xmlStr.trim(), "", Parser.xmlParser());
                        String htmlContent = Objects.requireNonNull(xmlDoc.selectFirst("HtmlContent")).html();
                        String matchName = Objects.requireNonNull(xmlDoc.selectFirst("MatchName")).text();
                        String pendingName = Objects.requireNonNull(xmlDoc.selectFirst("PendingName")).text();
                        pendingNameList.addAll(Optional.ofNullable(Arrays.stream(pendingName.split(","))
                                        .filter(name -> StrUtil.isNotBlank(name)).collect(Collectors.toSet()))
                                .orElse(new LinkedHashSet<String>()));
                        matchNameList.addAll(Optional.ofNullable(Arrays.stream(matchName.split(","))
                                        .filter(name -> StrUtil.isNotBlank(name)).collect(Collectors.toSet()))
                                .orElse(new LinkedHashSet<String>()));
                        finalResult.append(htmlContent).append("\n\n<div style='color:#ccc;'>================ 完成第（")
                                .append(batchIndex + 1).append("）批候选人简历分析 ================</div>\n\n");
                    } catch (Exception e) {
                        e.printStackTrace();
                        finalResult.append(xmlStr).append("\n\n<div style='color:#ccc;'>================ 完成第（")
                                .append(batchIndex + 1).append("）批候选人简历分析 ================</div>\n\n");
                    }
                    log.info("第{}批简历排名分析完成", batchIndex + 1);
                });
            } catch (Exception e) {
                log.error("第{}批排名模型调用失败: {}", batchIndex + 1, e.getMessage(), e);
                throw new RuntimeException("排名分析失败", e);
            }
        });
        List<EmailAttachmentDTO> sendAttachList = getEmailAttachmentDTOS(attachList, matchNameList, pendingNameList);
        // 所有批次处理完成后，发送完整的分析结果
        if (!finalResult.isEmpty()) {
            sendMailByAigcResponse(positionType, finalResult.toString(), matchNameList, pendingNameList, sendAttachList,
                    attachList.size());
            log.info("所有批次的简历排名分析完成，邮件已发送");
        }
    }

    private List<EmailAttachmentDTO> getEmailAttachmentDTOS(List<EmailAttachmentDTO> attachList,
            Set<String> matchNameList, Set<String> pendingNameList)
    {
        // 先预处理matchNameList，按长度降序排序（避免短名字先匹配长名字的情况）
        List<String> sortedMatchNames = new ArrayList<>(matchNameList);
        sortedMatchNames.addAll(new ArrayList<>(pendingNameList));
        sortedMatchNames.sort((a, b) -> b.length() - a.length());

        // 获取匹配或者待定的附件列表
        List<EmailAttachmentDTO> sendAttachList = attachList.stream().filter(att -> {
            if (att.getSubject() == null) return false;

            // 按实际观察的分隔符拆分（您提供的例子中使用的是空格和竖线）
            String[] tokens = att.getSubject().split("[\\s\\|]+");

            return sortedMatchNames.stream().anyMatch(
                    name -> Arrays.stream(tokens).anyMatch(token -> token.trim().equalsIgnoreCase(name)));
        }).peek(att -> log.debug("匹配成功: {} -> {}", att.getSubject(), sortedMatchNames.stream().filter(
                name -> att.getSubject().toLowerCase().contains(name.toLowerCase())).findFirst().orElse(""))).toList();
        return sendAttachList;
    }

    /**
     * 公共方法：调用AI模型并获取结果
     *
     * @param request 模型请求参数
     * @return 模型返回的结果
     */
    private String callModel(ModelRequest request) {
        return modelInvoker.callModel(ModelType.DEEPSEEK_R1_VOLCANO, request).map(
                response -> JacksonUtil.parse(Optional.ofNullable(response).orElse("").toString(),
                        DeepSeekChatCompletionResponse.class)).map(response -> response.getChoices().get(0).getMessage()
                .getContent()).block();
    }

    /**
     * 发送分析结果给收件人
     */
    private void sendMailByAigcResponse(PositionType positionType, String contents, Set<String> matchNameList,
            Set<String> pendingNameList, List<EmailAttachmentDTO> sendAttachList, int totalAttachCount)
    {
        try {
            log.info("开始发送简历分析结果邮件... {}", JacksonUtil.toJson(contents));
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 设置发件人
            helper.setFrom(mailFrom);
            // 设置收件人
            String[] to = (mailTo + positionType.getEmailTo()).split(";");
            helper.setTo(to);
            String[] cc = mailCopy.split(";");
            helper.setCc(cc);

            // 设置邮件主题
            String subject = "【" + String.join("|", positionType.getName()) + "】 岗位简历分析结果（共计 " +
                    totalAttachCount + " 份简历）-" + LocalDateTime.now().format(
                    DateTimeFormatter.ofPattern(DatePattern.yyyy_MM_dd_HH_mm_ss));
            helper.setSubject(subject);
            // 设置邮件内容，使用HTML格式化
            String htmlBuilder = getHtmlBuilder(contents, matchNameList, pendingNameList);
            helper.setText(htmlBuilder, true);

            // 添加附件
            if (CollUtil.isNotEmpty(sendAttachList)) {
                for (EmailAttachmentDTO attachment : sendAttachList) {
                    helper.addAttachment(attachment.getFilename(), new ByteArrayResource(attachment.getContent()));
                }
                log.info("已添加 {} 个附件", sendAttachList.size());
            }

            // 发送邮件
            mailSender.send(message);
            log.info("简历分析结果邮件发送成功，收件人: to:{}，cc:{}， {}", Arrays.toString(to), Arrays.toString(cc),
                    htmlBuilder);
        } catch (MessagingException e) {
            log.error("发送邮件失败: {}", e.getMessage(), e);
        }
    }

    private String getHtmlBuilder(String contents, Set<String> matchNameList, Set<String> pendingNameList) {
        // 构建HTML
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("""
                <html>
                <head>
                  <style>
                    body {
                      font-family: "Segoe UI", Helvetica, Arial, sans-serif;
                      margin: 0;
                      padding: 0;
                      background-color: #f0f2f5;
                    }
                    .resume-card {
                      background-color: #ffffff;
                      padding: 12px 16px;
                      margin-bottom: 16px;
                      border-radius: 8px;
                      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                    }
                    .resume-heading {
                      color: #1877f2;
                      margin: 0 0 8px 0;
                      font-size: 17px;
                      font-weight: 600;
                    }
                    .resume-match-group {
                      margin-bottom: 8px;
                    }
                    .resume-match-label {
                      display: inline-block;
                      background-color: #e7f3ff;
                      color: #1877f2;
                      padding: 4px 8px;
                      border-radius: 6px;
                      font-size: 13px;
                      margin-right: 6px;
                      margin-bottom: 6px;
                    }
                    .resume-pending-label {
                      display: inline-block;
                      background-color: #f0f2f5;
                      color: #65676b;
                      padding: 4px 8px;
                      border-radius: 6px;
                      font-size: 13px;
                      margin-right: 6px;
                      margin-bottom: 6px;
                    }
                    .resume-name-list {
                      font-size: 15px;
                      color: #050505;
                    }
                    .resume-tip-text {
                      font-size: 13px;
                      color: #65676b;
                      margin-top: 12px;
                    }
                    .resume-dynamic-contents > div {
                      max-width: 100% !important;
                      width: 100% !important;
                      padding: 0 !important;
                      margin: 0 !important;
                    }
                    .resume-dynamic-contents {
                      text-align: left !important;
                    }
                  </style>
                </head>
                """);
        htmlBuilder.append("""
                <body>
                  <div class="resume-card">
                    <h2 class="resume-heading">当前邮件中（匹配/待定）的所有候选人姓名如下（%d）</h2>
                    <div class="resume-match-group">
                      <span class="resume-match-label">匹配（%d）</span>
                      <span class="resume-name-list">%s</span>
                    </div>
                    <div>
                      <span class="resume-pending-label">待定（%d）</span>
                      <span class="resume-name-list">%s</span>
                    </div>
                    <div class="resume-tip-text">若对照姓名查不到对应附件时，请联系HR手动转发附件</div>
                  </div>
                  <!-- 中间动态内容 -->
                  <div class="resume-dynamic-contents">
                    %s
                  </div>
                </body>
                """.formatted((matchNameList.size() + pendingNameList.size()), matchNameList.size(),
                String.join(" 、 ", matchNameList), pendingNameList.size(), String.join(" 、 ", pendingNameList),
                contents));
        htmlBuilder.append("</html>");
        return htmlBuilder.toString();
    }

}
