package com.fuioupay.bootstrap;



import java.io.*;
import java.lang.reflect.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;
import java.util.logging.Logger;

public class SimpleCodeLinkIndexUtil {
    private static final Logger log = Logger.getLogger(SimpleCodeLinkIndexUtil.class.getName());
    private final Map<String, String> classNameToPathMap = new HashMap<>();
    private final Map<String, String> xmlMapperMap = new HashMap<>();
    private final Set<String> processedClasses = new HashSet<>();
    private final Set<String> collectedFiles = new LinkedHashSet<>();

    // ----每个项目待修正。-----
    // 指定查找资源目录
    private static final String[] SOURCE_DIRS = {"src/main/java", "src/main/resources"};
    // 要查找的文件所在的根路径
    private static final String projectBasePath = "C:\\workspace\\fuioupay\\ccs-cloud\\";
    // 要查找的文件所在的包名
    private static final String basePackage = "com.fuiou.ccs";
    // 输出文件名
    private static final String outputFile = "prompt.txt";
    // 接口层查找映射关系
    private static final Map<String, String> INTERFACE_TO_IMPL_SUFFIX_MAP = new HashMap<>();
    // 递归深度限制 <= 3层，超过的请加入入口类targetClasses
    private static final int MAX_RECURSION_DEPTH = 3;

    static {
        // 初始化映射关系，用户可以根据需要修改或添加
        INTERFACE_TO_IMPL_SUFFIX_MAP.put("Service", "ServiceImpl");
        INTERFACE_TO_IMPL_SUFFIX_MAP.put("Domain", "DomainImpl");
    }

    public static void main(String[] args) {
        try {
            System.out.println("开始执行代码生成...");
            System.out.println("当前工作目录: " + System.getProperty("user.dir"));

            Class<?>[] targetClasses = {FuioupayAigcBootstrapApplicationTests.class};

            // 添加prompts
            String[] prompts = {"\n参考WorkOrderController中的实现结构。新增一个处理任务的逻辑，具体逻辑如下：" +
                    "\n1、入参date定义为非必填，业务中涉及到此参数必填的都改成非必填， 不填的话查询的日期就是所有日期" +
                    "\n" +
                    "\n注意：\n严格按照我代码的结构来逐个编写代码，你可以先思考下怎么做，然后写出完整的代码，中间不能有省略, 同时不要在代码中生成文件路径"};

            SimpleCodeLinkIndexUtil.linkAndExport(targetClasses, outputFile, prompts);
        } catch (Exception e) {
            log.severe("执行过程中发生异常: " + e);
            e.printStackTrace();
        }
    }

    public SimpleCodeLinkIndexUtil() {
        initFileMappings();
    }

    public void generate(Class<?>[] entryClasses, Path outputPath) throws Exception {
        try {
            System.out.println("开始生成文件，输出路径: " + outputPath);

            // 清空输出文件
            Files.write(outputPath, new byte[0], StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            System.out.println("已清空输出文件");

            // 处理入口类
            System.out.println("开始处理入口类，数量: " + entryClasses.length);
            for (Class<?> clazz : entryClasses) {
                System.out.println("正在处理类: " + clazz.getName());
                analyzeClass(clazz, 0);
            }

            // 写入收集到的文件内容
            System.out.println("收集到的文件数量: " + collectedFiles.size());
            for (String filePath : collectedFiles) {
                if (filePath.endsWith(".java") || filePath.endsWith(".xml")) {
                    System.out.println("正在写入文件: " + filePath);
                    writeFileContent(outputPath, filePath);
                }
            }
            System.out.println("文件生成完成");
        } catch (Exception e) {
            System.err.println("generate方法出现异常: " + e);
            e.printStackTrace();
            throw e;
        }
    }

    private void analyzeClass(Class<?> clazz, int depth) {
        String className = clazz.getName();

        if (!processedClasses.add(className) || !className.startsWith(basePackage)) {
            System.out.println("跳过类: " + className + " (已处理或不在基础包内)");
            return;
        }

        // 检查递归深度
        if (depth > MAX_RECURSION_DEPTH) {
            System.out.println("跳过类: " + className + " (超过最大递归深度)");
            return;
        }

        System.out.println("分析类: " + className + " (深度: " + depth + ")");

        // 添加当前类文件
        addClassFile(className);

        // 处理字段依赖
        for (Field field : clazz.getDeclaredFields()) {
            analyzeType(field.getGenericType(), depth + 1);
        }

        // 处理方法参数和返回类型
        for (Method method : clazz.getDeclaredMethods()) {
            analyzeType(method.getGenericReturnType(), depth + 1);
            for (Parameter param : method.getParameters()) {
                analyzeType(param.getParameterizedType(), depth + 1);
            }
        }

        // 处理构造器参数
        for (Constructor<?> constructor : clazz.getDeclaredConstructors()) {
            for (Parameter param : constructor.getParameters()) {
                analyzeType(param.getParameterizedType(), depth + 1);
            }
        }

        // 处理父类和接口
        analyzeType(clazz.getGenericSuperclass(), depth + 1);
        for (Type intf : clazz.getGenericInterfaces()) {
            analyzeType(intf, depth + 1);
        }

        // 接口实现类的处理
        if (clazz.isInterface() && className.startsWith(basePackage)) {
            String interfaceSimpleName = clazz.getSimpleName();
            Package pkg = clazz.getPackage();
            String packageNameString = (pkg != null) ? pkg.getName() : "";
            if (packageNameString.isEmpty()) {
                int lastDot = className.lastIndexOf('.');
                if (lastDot != -1) {
                    packageNameString = className.substring(0, lastDot);
                }
            }

            // 只对深度较浅的接口查找实现
            if (depth < MAX_RECURSION_DEPTH - 1) {
                for (Map.Entry<String, String> entry : INTERFACE_TO_IMPL_SUFFIX_MAP.entrySet()) {
                    String interfaceSuffix = entry.getKey();
                    String implSuffix = entry.getValue();

                    if (interfaceSimpleName.endsWith(interfaceSuffix)) {
                        System.out.println(
                                "检测到符合映射规则的接口: " + className + " (后缀: " + interfaceSuffix + ")");
                        String baseName = interfaceSimpleName.substring(0,
                                interfaceSimpleName.length() - interfaceSuffix.length());
                        String implSimpleName = baseName + implSuffix;

                        // 1. 尝试在 impl 子包下查找: com.example.package.impl.MyCustomImpl
                        String potentialImplNameInSubPackage = packageNameString + ".impl." + implSimpleName;
                        // 2. 尝试在相同包下查找: com.example.package.MyCustomImpl
                        String potentialImplNameInSamePackage = packageNameString + "." + implSimpleName;

                        String foundImplClassName = null;

                        if (classNameToPathMap.containsKey(potentialImplNameInSubPackage)) {
                            System.out.println("在 impl 子包中找到候选实现类名: " + potentialImplNameInSubPackage);
                            foundImplClassName = potentialImplNameInSubPackage;
                        } else if (classNameToPathMap.containsKey(potentialImplNameInSamePackage)) {
                            System.out.println("在相同包中找到候选实现类名: " + potentialImplNameInSamePackage);
                            foundImplClassName = potentialImplNameInSamePackage;
                        }

                        if (foundImplClassName != null) {
                            try {
                                System.out.println("准备分析实现类: " + foundImplClassName);
                                analyzeClass(Class.forName(foundImplClassName, true,
                                        Thread.currentThread().getContextClassLoader()), depth + 1);
                                // 假设一个接口只有一个主要的实现约定，找到后即跳出循环
                                break;
                            } catch (ClassNotFoundException e) {
                                System.err.println("警告: 无法通过 Class.forName 加载实现类 '" + foundImplClassName +
                                        "'. Error: " + e.getMessage());
                            } catch (Exception e) {
                                System.err.println(
                                        "分析实现类 '" + foundImplClassName + "' 时发生一般错误: " + e.getMessage());
                                e.printStackTrace(System.err);
                            }
                        } else {
                            System.out.println("未能在 '" + potentialImplNameInSubPackage + "' 或 '" +
                                    potentialImplNameInSamePackage + "' 中找到接口 '" + className + "' (后缀 " +
                                    interfaceSuffix + ") 对应的 '" + implSuffix + "' 实现类文件映射。");
                        }
                    }
                }
            }
        }

        // 处理Mapper XML文件
        if (className.endsWith("Mapper")) {
            addMapperXml(className);
        }
    }

    private void analyzeType(Type type, int depth) {
        if (type == null) {
            return;
        }

        // 超过最大递归深度则跳过
        if (depth > MAX_RECURSION_DEPTH) {
            return;
        }

        if (type instanceof Class) {
            Class<?> clazz = (Class<?>) type;
            if (clazz.getName().startsWith(basePackage)) {
                analyzeClass(clazz, depth);
            }
        } else if (type instanceof ParameterizedType) {
            ParameterizedType pType = (ParameterizedType) type;
            analyzeType(pType.getRawType(), depth);

            for (Type arg : pType.getActualTypeArguments()) {
                analyzeType(arg, depth + 1);
            }
        }
    }

    private void addClassFile(String className) {
        String filePath = classNameToPathMap.get(className);
        if (filePath != null && collectedFiles.add(filePath)) {
            System.out.println("添加类文件: " + filePath);
            log.info("Added class file: " + filePath);
        } else {
            System.out.println("未找到类文件映射: " + className);
        }
    }

    private void addMapperXml(String mapperClassName) {
        String xmlPath = xmlMapperMap.get(mapperClassName);
        if (xmlPath != null && collectedFiles.add(xmlPath)) {
            System.out.println("添加Mapper XML: " + xmlPath);
            log.info("Added Mapper XML: " + xmlPath);
        } else {
            System.out.println("未找到Mapper XML映射: " + mapperClassName);
        }
    }

    private void writeFileContent(Path outputPath, String filePath) throws IOException {
        try {
            // 添加文件路径标记
            Files.write(outputPath, ("\n\n===== FILE: " + filePath + " =====\n").getBytes(), StandardOpenOption.APPEND);

            // 写入文件内容
            List<String> lines = Files.readAllLines(Paths.get(filePath));
            Files.write(outputPath, lines, StandardOpenOption.APPEND);
            System.out.println("成功写入文件: " + filePath);
        } catch (Exception e) {
            System.err.println("写入文件失败: " + filePath + ", 错误: " + e.getMessage());
            throw e;
        }
    }

    private void initFileMappings() {
        try {
            System.out.println("初始化文件映射...");
            System.out.println("项目基础路径: " + projectBasePath);

            // 这里直接使用projectBasePath作为路径
            File projectRoot = new File(projectBasePath);
            System.out.println("项目根目录是否存在: " + projectRoot.exists());
            System.out.println("项目根目录是否为目录: " + projectRoot.isDirectory());

            if (!projectRoot.exists() || !projectRoot.isDirectory()) {
                System.err.println("项目根目录不存在或不是一个目录!");
                return;
            }

            // 查找所有模块的源代码目录
            List<File> sourceDirs = new ArrayList<>();
            findAllSourceDirs(projectRoot, sourceDirs);
            System.out.println("找到 " + sourceDirs.size() + " 个源代码目录");

            // 扫描所有找到的源代码目录
            for (File sourceDir : sourceDirs) {
                // 从源目录获取包路径
                String packagePath = getPackagePathFromSourceDir(sourceDir);
                System.out.println("扫描源代码目录: " + sourceDir.getAbsolutePath() + ", 基础包路径: " + packagePath);
                scanDirectory(sourceDir, packagePath);
            }

            System.out.println("文件映射初始化完成. 类映射数量: " + classNameToPathMap.size() + ", XML映射数量: " +
                    xmlMapperMap.size());
        } catch (Exception e) {
            System.err.println("初始化文件映射时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 递归查找所有模块的源代码目录
     */
    private void findAllSourceDirs(File dir, List<File> sourceDirs) {
        if (!dir.exists() || !dir.isDirectory() || dir.getName().equals("target")) {
            return;
        }

        // 检查当前目录是否是源代码目录
        for (String sourceDir : SOURCE_DIRS) {
            File potentialSourceDir = new File(dir, sourceDir);
            if (potentialSourceDir.exists() && potentialSourceDir.isDirectory()) {
                System.out.println("发现源代码目录: " + potentialSourceDir.getAbsolutePath());
                sourceDirs.add(potentialSourceDir);
            }
        }

        // 递归检查所有子目录
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    findAllSourceDirs(file, sourceDirs);
                }
            }
        }
    }

    /**
     * 从源代码目录路径推断基础包路径
     */
    private String getPackagePathFromSourceDir(File sourceDir) {
        String path = sourceDir.getAbsolutePath();
        // 检查是否包含java或resources目录
        if (path.endsWith("java") || path.endsWith("java\\") || path.endsWith("java/")) {
            // 返回空字符串，因为包路径将在scanDirectory中添加
            return "";
        } else if (path.endsWith("resources") || path.endsWith("resources\\") || path.endsWith("resources/")) {
            // 资源目录没有包路径
            return "";
        }
        return "";
    }

    private void scanDirectory(File dir, String packagePath) {
        if (!dir.exists() || dir.getName().equals("target")) {
            if (!dir.exists()) {
                System.out.println("目录不存在: " + dir.getAbsolutePath());
            }
            return;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            System.out.println("无法列出目录内容: " + dir.getAbsolutePath());
            return;
        }

        System.out.println(
                "扫描目录: " + dir.getAbsolutePath() + ", 包路径: " + packagePath + ", 文件数量: " + files.length);

        for (File file : files) {
            if (file.isDirectory()) {
                String newPackage = packagePath.isEmpty() ? file.getName() : packagePath + "." + file.getName();
                scanDirectory(file, newPackage);
            } else {
                processFile(file, packagePath);
            }
        }
    }

    private void processFile(File file, String packagePath) {
        String fileName = file.getName();
        String filePath = file.getAbsolutePath();

        if (fileName.endsWith(".java")) {
            String className = packagePath + "." + fileName.replace(".java", "");
            classNameToPathMap.put(className, filePath);
            System.out.println("添加Java类映射: " + className + " -> " + filePath);
        } else if (fileName.endsWith(".xml")) {
            processXmlFile(file, filePath);
        }
    }

    private void processXmlFile(File xmlFile, String filePath) {
        try (BufferedReader reader = new BufferedReader(new FileReader(xmlFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("<mapper")) {
                    Matcher matcher = Pattern.compile("namespace\\s*=\\s*\"([^\"]+)\"").matcher(line);
                    if (matcher.find()) {
                        String namespace = matcher.group(1);
                        if (namespace.startsWith(basePackage)) {
                            xmlMapperMap.put(namespace, filePath);
                        }
                    }
                    break;
                }
            }
        } catch (IOException e) {
            log.warning("Error processing XML file: " + filePath);
        }
    }

    public static void linkAndExport(Class<?>[] classes, String outputFile) {
        linkAndExport(classes, outputFile, null);
    }

    public static void linkAndExport(Class<?>[] classes, String outputFile, String[] prompts) {
        try {
            Path outputPath = Paths.get(outputFile);
            System.out.println("创建输出文件: " + outputPath.toAbsolutePath());

            SimpleCodeLinkIndexUtil util = new SimpleCodeLinkIndexUtil();
            util.generate(classes, outputPath);

            // 如果有prompts，添加到文件末尾
            if (prompts != null && prompts.length > 0) {
                System.out.println("添加prompts到文件末尾");
                Files.write(outputPath, "\n\n============================\n".getBytes(), StandardOpenOption.APPEND);
                for (String prompt : prompts) {
                    Files.write(outputPath, prompt.getBytes(), StandardOpenOption.APPEND);
                    Files.write(outputPath, "\n".getBytes(), StandardOpenOption.APPEND);
                }
            }

            System.out.println("代码链接索引生成完成，位置: " + outputPath.toAbsolutePath());
        } catch (Exception e) {
            System.err.println("生成代码链接索引失败，详细错误: ");
            e.printStackTrace();
            log.severe("Failed to generate code link index: " + e.getMessage());
        }
    }
}