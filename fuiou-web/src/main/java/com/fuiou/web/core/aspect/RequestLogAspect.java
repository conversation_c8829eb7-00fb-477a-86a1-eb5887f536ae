package com.fuiou.web.core.aspect;

import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.common.utils.WebUtil;
import com.fuiou.web.annotation.ExecutionLogoutputMethod;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * Spring boot 控制器 请求日志，方便代码调试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(value = "fuiou.log.enabled", havingValue = "true", matchIfMissing = true)
public class RequestLogAspect {

    private final static Logger LOGGER = LoggerFactory.getLogger(RequestLogAspect.class);

    /**
     * 拦截所有Controller
     */
    @Pointcut("execution(!static com.fuiou.common.api.ApiResult *(..)) " +
        "&& (@within(org.springframework.stereotype.Controller) " +
        "|| @within(org.springframework.web.bind.annotation.RestController))")
    public void logPointCut() {
    }

    /**
     * 打印请求日志及耗时时间
     */
    @Around("logPointCut()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        HttpServletRequest request = WebUtil.getRequest();
        String requestUrl = Objects.requireNonNull(request).getRequestURI();
        String requestMethod = request.getMethod();

        long startTime = System.currentTimeMillis();
        Object result = pjp.proceed();
        long endTime = System.currentTimeMillis();

        StringBuilder logBuilder = new StringBuilder(256);
        logBuilder.append("\n\n================  Request Start  ================\n");
        logBuilder.append("请求地址：{} -> {}\n");
        logBuilder.append("请求参数：{}\n");
        logBuilder.append("返回结果：{}\n");
        logBuilder.append("耗时时间：{}ms\n");
        logBuilder.append("================  Request End  ================\n");

        Object parameters = getParameters(pjp, request);
        String logResult = result == null ? "" : result.toString();
        if (result != null && logResult.contains("@")) {
            logResult = JacksonUtil.toJson(result);
        }
        if (logResult.length() > 500) {
            logResult = logResult.substring(0, 500) + "...";
        }
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        ExecutionLogoutputMethod annotation = method.getAnnotation(ExecutionLogoutputMethod.class);
        if (Objects.nonNull(annotation)) {
            logResult = "已加密";
            parameters = "已加密";
        }
        LOGGER.info(logBuilder.toString(), requestMethod, requestUrl, parameters, logResult, (endTime - startTime));
        return result;
    }

    public Object getParameters(ProceedingJoinPoint pjp, HttpServletRequest request) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();

        Map<String, Object> parameterMap = new HashMap<>(16);
        request.getParameterMap().forEach((k, v) ->
            parameterMap.put(k, String.join(",", v)));

        Object[] args = pjp.getArgs();
        for (int i = 0; i < args.length; i++) {
            Object value = args[i];
            Parameter parameter = parameters[i];

            PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
            if (pathVariable != null) {
                String name = pathVariable.value();
                name = StringUtils.isEmpty(name) ? parameter.getName() : name;
                parameterMap.put(name, value);
                continue;
            }

            RequestBody requestBody = parameter.getAnnotation(RequestBody.class);
            if (requestBody != null) {
                return parameterMap.size() == 0 ? value : Lists.newArrayList(parameterMap, value);
            }

            // 上传文件，记录文件名
            if (value instanceof MultipartFile) {
                MultipartFile multipartFile = (MultipartFile) value;
                String name = multipartFile.getName();
                String fileName = multipartFile.getOriginalFilename();
                parameterMap.put(name, fileName);
            } else if (value instanceof MultipartFile[]) {
                MultipartFile[] arr = (MultipartFile[]) value;
                if (arr.length == 0) {
                    continue;
                }
                String name = arr[0].getName();
                StringJoiner joiner = new StringJoiner(",");
                for (MultipartFile multipartFile : arr) {
                    joiner.add(multipartFile.getOriginalFilename());
                }
                parameterMap.put(name, joiner.toString());
            }

        }
        return parameterMap;
    }


}
