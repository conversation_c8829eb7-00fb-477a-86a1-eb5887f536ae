// package com.fuiou.web.core.processor;
//
// import org.springframework.boot.context.config.ConfigFileApplicationListener;
// import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
// import org.springframework.boot.context.event.ApplicationPreparedEvent;
// import org.springframework.context.ApplicationEvent;
// import org.springframework.context.event.SmartApplicationListener;
// import org.springframework.core.Ordered;
//
// /**
//  * 监听器
//  * 放在项目启动器加载，nacos登录加密无法使用，先暂停
//  *
//  * <AUTHOR>
//  */
// public class DecryptApplicationListener implements SmartApplicationListener, Ordered {
//
//     @Override
//     public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
//         return (ApplicationEnvironmentPreparedEvent.class.isAssignableFrom(eventType) ||
//             ApplicationPreparedEvent.class.isAssignableFrom(eventType));
//     }
//
//     /**
//      * Handle an application event.
//      *
//      * @param event the event to respond to
//      */
//     @Override
//     public void onApplicationEvent(ApplicationEvent event) {
//         if (event instanceof ApplicationEnvironmentPreparedEvent) {
//             new DecryptConvertUtil().performReplacementProperties(((ApplicationEnvironmentPreparedEvent) event).getEnvironment());
//         }
//     }
//
//     @Override
//     public int getOrder() {
//         /* 设置该监听器 在加载配置文件之后执行 */
//         return (ConfigFileApplicationListener.DEFAULT_ORDER + 1);
//     }
//
// }
