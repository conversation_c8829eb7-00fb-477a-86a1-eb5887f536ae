package com.fuiou.web.core.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;

import static com.fuiou.common.exception.enums.GlobalErrorCode.*;

/**
 * 全局异常处理器，将 Exception 翻译成 ApiResult + 对应的异常编号
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理 SpringMVC 请求参数缺失
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数，结果并未传递 xx 参数
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public ApiResult<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        LOGGER.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResult.fail(BAD_REQUEST.getCode(), String.format("请求参数缺失:%s", ex.getParameterName()));
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResult<?> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException ex) {
        LOGGER.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResult.fail(BAD_REQUEST.getCode(), "请求参数格式错误");
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ApiResult<?> httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException ex) {
        LOGGER.warn("[httpMessageNotReadableExceptionHandler]", ex);
        return ApiResult.fail(BAD_REQUEST.getCode(), "请求参数解析失败");
    }

    /**
     * 处理 SpringMVC 参数校验不正确
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResult<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
        LOGGER.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
        FieldError fieldError = ex.getBindingResult().getFieldError();
        assert fieldError != null; // 断言，避免告警
        return ApiResult.fail(BAD_REQUEST.getCode(), fieldError.getDefaultMessage());
    }

    /**
     * 处理 SpringMVC 参数绑定不正确，本质上也是通过 Validator 校验
     */
    @ExceptionHandler(BindException.class)
    public ApiResult<?> bindExceptionHandler(BindException ex) {
        LOGGER.warn("[handleBindException]", ex);
        FieldError fieldError = ex.getFieldError();
        assert fieldError != null; // 断言，避免告警
        return ApiResult.fail(BAD_REQUEST.getCode(), "请求参数格式错误");
    }

    /**
     * 处理 Validator 校验不通过产生的异常
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public ApiResult<?> constraintViolationExceptionHandler(ConstraintViolationException ex) {
        LOGGER.warn("[constraintViolationExceptionHandler]", ex);
        ConstraintViolation<?> constraintViolation = ex.getConstraintViolations().iterator().next();
        return ApiResult.fail(BAD_REQUEST.getCode(), constraintViolation.getMessage());
    }

    /**
     * 处理 Dubbo Consumer 本地参数校验时，抛出的 ValidationException 异常
     */
    @ExceptionHandler(value = ValidationException.class)
    public ApiResult<?> validationException(ValidationException ex) {
        LOGGER.warn("[constraintViolationExceptionHandler]", ex);
        // 无法拼接明细的错误信息，因为 Dubbo Consumer 抛出 ValidationException 异常时，是直接的字符串信息，且人类不可读
        return ApiResult.fail(BAD_REQUEST);
    }

    /**
     * 处理 SpringMVC 请求地址不存在
     * <p>
     * 注意，它需要设置如下两个配置项：
     * 1. spring.mvc.throw-exception-if-no-handler-found 为 true
     * 2. spring.mvc.static-path-pattern 为 /statics/**
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ApiResult<?> noHandlerFoundExceptionHandler(NoHandlerFoundException ex) {
        LOGGER.warn("[noHandlerFoundExceptionHandler]", ex);
        return ApiResult.fail(NOT_FOUND.getCode(), String.format("请求地址不存在:%s", ex.getRequestURL()));
    }

    /**
     * 处理 SpringMVC 请求方法不正确
     * <p>
     * 例如说，A 接口的方法为 GET 方式，结果请求方法为 POST 方式，导致不匹配
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ApiResult<?> httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException ex) {
        LOGGER.warn("[httpRequestMethodNotSupportedExceptionHandler]", ex);
        return ApiResult.fail(METHOD_NOT_ALLOWED.getCode(), String.format("请求方法不正确:%s", ex.getMessage()));
    }

    /**
     * 处理 json 解析异常
     */
    @ExceptionHandler(JsonProcessingException.class)
    public ApiResult<?> jsonProcessingExceptionHandler(JsonProcessingException ex) {
        LOGGER.warn("[jsonProcessingExceptionHandler]", ex);
        return ApiResult.fail(JSON_FORMAT_ERROR);
    }

    /**
     * 处理业务异常 ServiceException
     */
    @ExceptionHandler(value = ServiceException.class)
    public ApiResult<?> serviceExceptionHandler(ServiceException ex) {
        LOGGER.warn("[serviceExceptionHandler] code = {}, msg = {}", ex.getCode(), ex.getMessage());
        return ApiResult.fail(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理系统异常，兜底处理所有的一切
     */
    @ExceptionHandler(value = Exception.class)
    public ApiResult<?> defaultExceptionHandler(HttpServletRequest req, Throwable ex) {
        LOGGER.error("[defaultExceptionHandler]", ex);
        // TODO 兜底异常处理
        return ApiResult.fail(INTERNAL_SERVER_ERROR.getCode(), INTERNAL_SERVER_ERROR.getMsg());
    }

}
