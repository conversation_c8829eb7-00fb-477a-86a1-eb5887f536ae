package com.fuiou.web.config;

import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.common.utils.LocalDateUtil;
import com.fuiou.web.core.aspect.RequestLogAspect;
import com.fuiou.web.core.aspect.SensitiveAspect;
import com.fuiou.web.core.handler.GlobalExceptionHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.Filter;
import java.util.List;

/**
 * Web 通用配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableAspectJAutoProxy
public class WebAutoConfiguration implements WebMvcConfigurer {

    /**
     * 全局异常解析
     */
    @Bean
    @ConditionalOnMissingBean(GlobalExceptionHandler.class)
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

    /**
     * 请求日志
     */
    @Bean
    public RequestLogAspect requestLogAspect() {
        return new RequestLogAspect();
    }

    /**
     * 脱敏注解
     */
    @Bean
    public SensitiveAspect sensitiveAspect() {
        return new SensitiveAspect();
    }

    /**
     * 创建 CorsFilter Bean，解决跨域问题
     */
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilterBean() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOrigin("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 创建 UrlBasedCorsConfigurationSource 对象
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 对接口配置跨域设置
        source.registerCorsConfiguration("/**", config);
        return createFilterBean(new CorsFilter(source), Integer.MIN_VALUE);
    }

    /**
     * Jackson HttpMessageConverter 配置
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.forEach(converter -> {
            if (converter instanceof MappingJackson2HttpMessageConverter) {
                MappingJackson2HttpMessageConverter httpMessageConverter = (MappingJackson2HttpMessageConverter) converter;
                JacksonUtil.registerModule(httpMessageConverter.getObjectMapper());
            }
        });
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setDateFormatter(LocalDateUtil.DATE_FORMATTER);
        registrar.setDateTimeFormatter(LocalDateUtil.DATETIME_FORMATTER);
        registrar.setTimeFormatter(LocalDateUtil.TIME_FORMATTER);
        registrar.registerFormatters(registry);
    }

    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

}
