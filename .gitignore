# Created by .ignore support plugin (hsz.mobi)
### Example user template template
### Example user template

HELP.md
target/
logs/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**
!**/src/test/**

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

### VS Code ###
.vscode/

### 2022-07.25加入下面的 ###
*.class

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
#*.jar
*.war
*.ear

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# ---- Maven
target/
dependency-reduced-pom.xml

# ---- IntelliJ IDEA
*.iws
*.iml
*.ipr
.idea/

# ---- Eclipse
.classpath
.project
.settings/
.metadata/

# ---- Mac OS X
.DS_Store?
.DS_Store
Icon?
# Thumbnails
._*
# Files that might appear on external disk
.Spotlight-V100
.Trashes

# ---- Windows
# Windows image file caches
Thumbs.db
# Folder config file
Desktop.ini

.java-version
#--compile
bin/
tmp/
build/

.gradle/
.vscode/
.tmp

# --- JRebel
/**/rebel.xml
**/combined_files.txt
**/prompt.txt
**/CodeTest.java

# --- CodeLink
CodeLinkIndexUtil.java
SimpleCodeLinkIndexUtil.java
FileGeniusApplication.java
prompt.txt


