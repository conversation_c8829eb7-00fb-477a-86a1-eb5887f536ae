package com.fuiou.mail.config;

import com.fuiou.mail.config.properties.FuiouMailProperties;
import com.fuiou.mail.register.MultiMailServiceRegister;
import com.fuiou.mail.service.MailService;
import com.fuiou.mail.service.impl.MailServiceImpl;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSender;

/**
 * 邮件配置
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Configuration
@Import(MultiMailServiceRegister.class)
@EnableConfigurationProperties(FuiouMailProperties.class)
@AutoConfigureAfter(MailSenderAutoConfiguration.class)
public class MailConfiguration {

    @Bean
    @ConditionalOnBean({MailProperties.class, JavaMailSender.class})
    public MailService mailService(JavaMailSender mailSender, MailProperties mailProperties) {
        return new MailServiceImpl(mailSender, mailProperties);
    }

}
