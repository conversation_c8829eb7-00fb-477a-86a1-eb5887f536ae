package com.fuiou.mail.service.impl;

import com.fuiou.mail.service.MailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.util.FileCopyUtils;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class MailServiceImpl implements MailService {

    private final String from;

    private final JavaMailSender mailSender;

    private final MailProperties mailProperties;

    public MailServiceImpl(JavaMailSender mailSender, MailProperties mailProperties) {
        this.mailSender = mailSender;
        this.mailProperties = mailProperties;
        String username = mailProperties.getUsername();
        this.from = username;//username.contains("@") ? username : username + "@fuiou.com";
    }

    @Override
    public void sendSimpleMail(String to, String subject, String content, String... cc) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);
        message.setTo(StringUtils.split(to, ","));
        message.setSubject(subject);
        message.setText(content);
        if (cc != null && cc.length > 0) {
            message.setCc(cc);
        }
        mailSender.send(message);
    }

    @Override
    public void sendHtmlMail(String to, String subject, String content, String... cc) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        buildHelper(to, subject, content, message, cc);
        mailSender.send(message);
    }

    @Override
    public void sendAttachmentsMail(String to, String subject, String content, Map<String, ?> attachmentMap, String... cc) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = buildHelper(to, subject, content, message, cc);
        addAttachments(helper, attachmentMap);
        mailSender.send(message);
    }

    @Override
    public void sendAttachmentMail(String to, String subject, String content, String fileName, InputStream inputStream, String... cc) throws MessagingException {
        HashMap<String, InputStream> attachmentMap = new HashMap<>(2);
        attachmentMap.put(fileName, inputStream);
        sendAttachmentsMail(to, subject, content, attachmentMap, cc);
    }


    @Override
    public void sendAttachmentMail(String to, String subject, String content, String fileName, byte[] fileBody, String... cc) throws MessagingException {
        HashMap<String, byte[]> attachmentMap = new HashMap<>(2);
        attachmentMap.put(fileName, fileBody);
        sendAttachmentsMail(to, subject, content, attachmentMap, cc);
    }

    @Override
    public void sendResourceMail(String to, String subject, String content, String resourceId, InputStream inputStream, String... cc) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = buildHelper(to, subject, content, message, cc);
        try {
            byte[] bytes = FileCopyUtils.copyToByteArray(inputStream);
            helper.addInline(resourceId, new ByteArrayResource(bytes));
        } catch (IOException e) {
            throw new MessagingException(e.getMessage(), e);
        }
        mailSender.send(message);
    }

    @Override
    public void sendResourceMail(String to, String subject, String content, String resourceId, Resource resource, String... cc) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = buildHelper(to, subject, content, message, cc);
        helper.addInline(resourceId, resource);
        mailSender.send(message);
    }

    @Override
    public void sendResourceAttachmentMail(String to, String subject, String content, String resourceId, Resource resource, String attachmentName, InputStream attachment, String... cc) throws MessagingException {
        HashMap<String, InputStream> attachmentMap = new HashMap<>(2);
        attachmentMap.put(attachmentName, attachment);
        sendResourceAttachmentsMail(to, subject, content, resourceId, resource, attachmentMap, cc);
    }

    @Override
    public void sendResourceAttachmentMail(String to, String subject, String content, String resourceId, Resource resource, String attachmentName, byte[] attachment, String... cc) throws MessagingException {
        HashMap<String, byte[]> attachmentMap = new HashMap<>(2);
        attachmentMap.put(attachmentName, attachment);
        sendResourceAttachmentsMail(to, subject, content, resourceId, resource, attachmentMap, cc);
    }

    @Override
    public void sendResourceAttachmentsMail(String to, String subject, String content, String resourceId, Resource resource, Map<String, ?> attachmentMap, String... cc) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = buildHelper(to, subject, content, message, cc);
        helper.addInline(resourceId, resource);
        addAttachments(helper, attachmentMap);
        mailSender.send(message);
    }

    private MimeMessageHelper buildHelper(String to, String subject, String content, MimeMessage message, String... cc) throws MessagingException {
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(from);
        helper.setTo(StringUtils.split(to, ","));
        helper.setSubject(subject);
        helper.setText(content, true);
        if (cc != null && cc.length > 0) {
            helper.setCc(cc);
        }
        return helper;
    }

    private void addAttachments(MimeMessageHelper helper, Map<String, ?> attachmentMap) throws MessagingException {
        try {
            for (Map.Entry<String, ?> entry : attachmentMap.entrySet()) {
                String fileName = entry.getKey();
                Object fileBody = entry.getValue();
                byte[] bytes = fileBody instanceof InputStream ? FileCopyUtils.copyToByteArray((InputStream) fileBody) : (byte[]) fileBody;
                helper.addAttachment(fileName, new ByteArrayResource(bytes));
            }
        } catch (IOException e) {
            for (Object fileBody : attachmentMap.values()) {
                try {
                    if (fileBody instanceof InputStream) {
                        ((InputStream) fileBody).close();
                    }
                } catch (IOException ignored) { }
            }
            throw new MessagingException(e.getMessage(), e);
        }
    }

}
