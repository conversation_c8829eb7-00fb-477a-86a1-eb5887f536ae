package com.fuiou.job.listener;

import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务被增删改，暂停等操作监听器
 *
 * <AUTHOR>
 */
public class QuartzSchedulerListener implements SchedulerListener {

    private static Logger log = LoggerFactory.getLogger(QuartzSchedulerListener.class);

    @Override
    public void jobScheduled(Trigger trigger) {
        log.info("QuartzSchedulerListener.jobScheduled，新的任务被部署时调用 =  {}", trigger.getEndTime());
    }

    @Override
    public void jobUnscheduled(TriggerKey triggerKey) {
        log.info("QuartzSchedulerListener.jobUnscheduled，任务被卸载时调用 =  {}", triggerKey.getName());
    }

    @Override
    public void triggerFinalized(Trigger trigger) {
        log.info("QuartzSchedulerListener.jobScheduled，当一个触发器再也不会被触发时调用 =  {}", trigger.getEndTime());
    }

    @Override
    public void triggerPaused(TriggerKey triggerKey) {
        log.info("QuartzSchedulerListener.jobUnscheduled，一个任务或组被暂停时调用 =  {}", triggerKey.getName());
    }

    @Override
    public void triggersPaused(String triggerGroup) {
        log.info("QuartzSchedulerListener.triggersPaused，一个组被暂停时调用 =  {}", triggerGroup);
    }

    @Override
    public void triggerResumed(TriggerKey triggerKey) {
        log.info("QuartzSchedulerListener.triggerResumed，一个任务或组被恢复时调用  =  {}", triggerKey.getName());
    }

    @Override
    public void triggersResumed(String triggerGroup) {
        log.info("QuartzSchedulerListener.triggersResumed，一个组被恢复时调用 =  {}", triggerGroup);
    }

    @Override
    public void jobAdded(JobDetail jobDetail) {
        log.info("QuartzSchedulerListener.jobAdded，一个任务被添加时调用 =  {}", jobDetail.getKey());
    }

    @Override
    public void jobDeleted(JobKey jobKey) {
        log.info("QuartzSchedulerListener.jobDeleted，一个任务被删除时调用 =  {}", jobKey.getName());
    }

    @Override
    public void jobPaused(JobKey jobKey) {
        log.info("QuartzSchedulerListener.jobPaused，一个任务或组被暂停时调用 =  {}", jobKey.getName());
    }

    @Override
    public void jobsPaused(String jobGroup) {
        log.info("QuartzSchedulerListener.jobsPaused，一个组被暂停时调用 =  {}", jobGroup);
    }

    @Override
    public void jobResumed(JobKey jobKey) {
        log.info("QuartzSchedulerListener.jobResumed，一个任务或组被恢复时调用 =  {}", jobKey.getName());
    }

    @Override
    public void jobsResumed(String jobGroup) {
        log.info("QuartzSchedulerListener.jobsResumed，一个组被恢复时调用 =  {}", jobGroup);
    }

    @Override
    public void schedulerError(String msg, SchedulerException cause) {
        log.info("QuartzSchedulerListener.schedulerError，运行期间报错 =  {}", msg);
    }

    @Override
    public void schedulerInStandbyMode() {
        log.info("QuartzSchedulerListener.schedulerInStandbyMode，任务进入待机模式");
    }

    @Override
    public void schedulerStarted() {
        log.info("QuartzSchedulerListener.schedulerStarted，任务进入启动状态，待机恢复");
    }

    @Override
    public void schedulerStarting() {
        log.info("QuartzSchedulerListener.schedulerStarting，任务启动中");
    }

    @Override
    public void schedulerShutdown() {
        log.info("QuartzSchedulerListener.schedulerShutdown，任务被关闭");
    }

    @Override
    public void schedulerShuttingdown() {
        log.info("QuartzSchedulerListener.schedulerShuttingdown，任务关闭中");
    }

    @Override
    public void schedulingDataCleared() {
        log.info("QuartzSchedulerListener.schedulingDataCleared，任务数据被删除");
    }
}
