server:
  port: 8080
  tomcat:
    uri-encoding: UTF-8

spring:
  # 数据库配置
  datasource:
    # 数据库
    url: *****************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池-初始化大小
    initial-size: 10
    # 连接池-最大连接数
    max-active: 100
    # 最大等待时间
    max-wait: 60000
    # 连接池-最小空闲数
    min-idle: 10
    # 检测空闲连接
    test-while-idle: true
    # 最小空闲时间
    min-evictable-idle-time-millis: 300000
  # 定时器配置
  quartz:
    jdbc:
      initialize-schema: never
    scheduler-name: bootQuartzScheduler
    # 程序结束时会等待quartz相关的内容结束
    wait-for-jobs-to-complete-on-shutdown: true
    # QuartzScheduler启动时更新己存在的Job,不用每次修改targetObject后删除qrtz_job_details表对应记录
    overwrite-existing-jobs: true
    # 使用数据库存储
    job-store-type: jdbc
    # 初始化完成后自动启动调度程序
    autoStartup: true
    # 启动后，延迟多少秒之后运行
    startup-delay: 600
    properties:
      org:
        quartz:
          # 调度器配置
          scheduler:
            #调度标识名 集群中每一个实例都必须使用相同的名称
            instanceName: bootQuartzScheduler
            #ID设置为自动获取 每一个必须不同
            instanceId: AUTO
          # 存储配置
          jobStore:
            #数据保存方式为数据库持久化
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            #数据库代理类
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            #表的前缀，默认QRTZ_
            tablePrefix: t_qrtz_
            #是否加入集群
            isClustered: true
            # 信息保存时间 默认值60秒
            misfireThreshold: 60000
            #调度实例失效的检查时间间隔15秒
            clusterCheckinInterval: 15000
            #JobDataMaps是否都为String类型
            useProperties: false
            # 事务隔离级别
            txIsolationLevelReadCommitted: true
          # 线程池配置
          threadPool:
            #设置线程前缀
            threadNamePrefix: Boot_Job_Pool
            #设置线程的优先级（最大为java.lang.Thread.MAX_PRIORITY 10，最小为Thread.MIN_PRIORITY 1，默认为5）
            threadPriority: 5
            #指定线程数，至少为1（无默认值）(一般设置为1-100直接的整数合适)
            threadCount: 80
            #线程池的实现类
            class: org.quartz.simpl.SimpleThreadPool
