package com.fuiou.oss.core.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Minio参数配置类
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "fuiou.oss")
public class OssProperties {

	/**
	 * 对象存储名称
	 */
	private String name;

	/**
	 * 对象存储服务的端点
	 */
	private String endpoint;

	/**
	 * 访问 Key（用户名）
	 */
	private String accessKey;

	/**
	 * 秘钥（密码）
	 */
	private String secretKey;

    /**
     * 默认存储桶
     */
    private String bucket;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    @Override
    public String toString() {
        return "OssProperties{" +
            "name='" + name + '\'' +
            ", endpoint='" + endpoint + '\'' +
            ", accessKey='" + accessKey + '\'' +
            ", secretKey='" + secretKey + '\'' +
            ", bucket='" + bucket + '\'' +
            '}';
    }

}
