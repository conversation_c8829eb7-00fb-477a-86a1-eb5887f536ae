package com.fuiou.oss.core.service;

import com.fuiou.oss.core.model.OssFile;
import com.fuiou.oss.core.rule.DefaultFilePathRule;
import com.fuiou.oss.core.rule.FilePathRule;

import java.io.InputStream;
import java.util.Map;

/**
 * 对象存储服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OssService {

    default FilePathRule filePathRule() {
        return DefaultFilePathRule.getInstance();
    }

    /**
     * 获取当前配置存储桶名
     */
    String getBucket();

    /**
     * 创建存储桶
     *
     * @param bucket 存储桶名称
     * @return 是否成功
     */
    boolean createBucket(String bucket);

    /**
     * 删除 存储桶
     *
     * @param bucket 存储桶名称
     * @return 是否成功
     */
    boolean removeBucket(String bucket);

    /**
     * 存储桶是否存在
     *
     * @param bucket 存储桶名称
     * @return 是否存在
     */
    boolean existsBucket(String bucket);

    /**
     * 获取文件的信息
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @return Oss文件对象
     */
    OssFile statFile(String bucket, String objectName);

    /**
     * 获取文件的信息
     *
     * @param objectName 对象名称
     * @return Oss文件对象
     */
    default OssFile statFile(String objectName) {
        return statFile(getBucket(), objectName);
    }

    /**
     * 获取访问对象的外链地址
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param expiry     过期时间(小时) 最大为7天 超过7天则默认最大值
     * @return link外链
     */
    String getObjectUrl(String bucket, String objectName, Integer expiry);

    /**
     * 获取访问对象的外链地址
     *
     * @param objectName 对象名称
     * @param expiry     过期时间(小时) 最大为7天 超过7天则默认最大值
     * @return link外链
     */
    default String getObjectUrl(String objectName, Integer expiry) {
        return getObjectUrl(getBucket(), objectName, expiry);
    }

    /**
     * 构建文件链接
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @return 文件链接
     */
    String buildFileLink(String bucket, String objectName);

    /**
     * 构建文件链接
     *
     * @param objectName 对象名称
     * @return 文件链接
     */
    default String buildFileLink(String objectName) {
        return buildFileLink(getBucket(), objectName);
    }

    /**
     * 上传文件
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param file       文件流
     * @param headers    请求头
     */
    OssFile upload(String bucket, String objectName, InputStream file, String contentType, Map<String, String> headers);

    /**
     * 上传文件
     *
     * @param objectName 对象名称
     * @param file       文件流
     * @param headers    请求头
     */
    default OssFile upload(String objectName, InputStream file, String contentType, Map<String, String> headers) {
        return upload(getBucket(), objectName, file, contentType, headers);
    }

    /**
     * 上传文件
     *
     * @param bucket      存储桶名称
     * @param objectName  对象名称
     * @param contentType 内容类型
     * @param file        文件流
     */
    default OssFile upload(String bucket, String objectName, InputStream file, String contentType) {
        return upload(bucket, objectName, file, contentType, null);
    }

    /**
     * 上传文件
     *
     * @param objectName 对象名称
     * @param file       文件流
     */
    default OssFile upload(String objectName, InputStream file, String contentType) {
        return upload(getBucket(), objectName, file, contentType);
    }

    /**
     * 上传文件
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param file       文件流
     */
    default OssFile upload(String bucket, String objectName, InputStream file) {
        return upload(bucket, objectName, file, "application/octet-stream", null);
    }

    /**
     * 上传文件
     *
     * @param objectName 对象名称
     * @param file       文件流
     */
    default OssFile upload(String objectName, InputStream file) {
        return upload(getBucket(), objectName, file);
    }

    /**
     * 分块上传文件
     *
     * @param bucket      存储桶名称
     * @param objectName  对象名称
     * @param file        文件流
     * @param fileSize    文件大小
     * @param partSize    分块大小
     * @param partCount   分块总数
     * @param contentType 内容类型
     * @param headers     请求头
     * @return {@link OssFile}
     */
    OssFile multipartUpload(String bucket, String objectName, InputStream file, long fileSize, long partSize, int partCount,
                            String contentType, Map<String, String> headers);

    /**
     * 分块上传文件
     *
     * @param objectName  对象名称
     * @param file        文件流
     * @param fileSize    文件大小
     * @param partSize    分块大小
     * @param partCount   分块总数
     * @param contentType 内容类型
     * @param headers     请求头
     * @return {@link OssFile}
     */
    default OssFile multipartUpload(String objectName, InputStream file, long fileSize, long partSize, int partCount,
                                    String contentType, Map<String, String> headers) {
        return multipartUpload(getBucket(), objectName, file, fileSize, partSize, partCount, contentType, headers);
    }

    /**
     * 分块上传文件
     *
     * @param bucket      存储桶名称
     * @param objectName  对象名称
     * @param file        文件流
     * @param fileSize    文件大小
     * @param partSize    分块大小
     * @param partCount   分块总数
     * @param contentType 内容类型
     * @return {@link OssFile}
     */
    default OssFile multipartUpload(String bucket, String objectName, InputStream file, long fileSize, long partSize, int partCount, String contentType) {
        return multipartUpload(bucket, objectName, file, fileSize, partSize, partCount, contentType, null);
    }

    /**
     * 分块上传文件
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param file       文件流
     * @param fileSize   文件大小
     * @param partSize   分块大小
     * @param partCount  分块总数
     * @return {@link OssFile}
     */
    default OssFile multipartUpload(String bucket, String objectName, InputStream file, long fileSize, long partSize, int partCount) {
        return multipartUpload(bucket, objectName, file, fileSize, partSize, partCount, null, null);
    }

    /**
     * 分块上传文件
     *
     * @param objectName  对象名称
     * @param file        文件流
     * @param fileSize    文件大小
     * @param partSize    分块大小
     * @param partCount   分块总数
     * @param contentType 内容类型
     * @return {@link OssFile}
     */
    default OssFile multipartUpload(String objectName, InputStream file, long fileSize, long partSize, int partCount, String contentType) {
        return multipartUpload(getBucket(), objectName, file, fileSize, partSize, partCount, contentType);
    }

    /**
     * 分块上传文件
     *
     * @param objectName 对象名称
     * @param file       文件流
     * @param fileSize   文件大小
     * @param partSize   分块大小
     * @param partCount  分块总数
     * @return {@link OssFile}
     */
    default OssFile multipartUpload(String objectName, InputStream file, long fileSize, long partSize, int partCount) {
        return multipartUpload(getBucket(), objectName, file, fileSize, partSize, partCount);
    }

    /**
     * 下载文件
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     */
    InputStream download(String bucket, String objectName);

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     */
    default InputStream download(String objectName) {
        return download(getBucket(), objectName);
    }

    /**
     * 删除文件
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     */
    void remove(String bucket, String objectName);

    /**
     * 删除文件
     *
     * @param objectName 对象名称
     */
    default void remove(String objectName) {
        remove(getBucket(), objectName);
    }

    /**
     * 创建分区上传
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @return {@link OssFile}
     */
    OssFile createMultipartUpload(String bucket, String objectName, String contentType);

    /**
     * 创建分区上传
     *
     * @param objectName  对象名称
     * @param contentType 内容类型
     * @return {@link OssFile}
     */
    default OssFile createMultipartUpload(String objectName, String contentType) {
        return createMultipartUpload(getBucket(), objectName, contentType);
    }

    /**
     * 创建分区上传
     *
     * @param objectName 对象名称
     * @return {@link OssFile}
     */
    default OssFile createMultipartUpload(String objectName) {
        return createMultipartUpload(objectName, null);
    }

    /**
     * 上传文件块
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param uploadId   上传ID
     * @param data       文件数据必须是 InputStream、RandomAccessFile、byte[] 或 String
     * @param fileSize   文件大小
     * @param partNumber 分区数
     * @return 是否成功
     */
    boolean uploadPart(String bucket, String objectName, String uploadId, Object data, long fileSize, int partNumber);

    /**
     * 上传文件块
     *
     * @param objectName 对象名称
     * @param uploadId   上传ID
     * @param data       文件数据必须是 InputStream、RandomAccessFile、byte[] 或 String
     * @param fileSize   文件大小
     * @param partNumber 分区数
     * @return 是否成功
     */
    default boolean uploadPart(String objectName, String uploadId, Object data, long fileSize, int partNumber) {
        return uploadPart(getBucket(), objectName, uploadId, data, fileSize, partNumber);
    }

    /**
     * 完成分块上传
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param uploadId   上传ID
     * @return 是否成功
     */
    boolean completeMultipartUpload(String bucket, String objectName, String uploadId);

    /**
     * 完成分块上传
     *
     * @param objectName 对象名称
     * @param uploadId   上传ID
     * @return 是否成功
     */
    default boolean completeMultipartUpload(String objectName, String uploadId) {
        return completeMultipartUpload(getBucket(), objectName, uploadId);
    }

    /**
     * 终止分段上传
     *
     * @param bucket     存储桶名称
     * @param objectName 对象名称
     * @param uploadId   上传ID
     */
    void abortMultipartUpload(String bucket, String objectName, String uploadId);

    /**
     * 终止分段上传
     *
     * @param objectName 对象名称
     * @param uploadId   上传ID
     */
    default void abortMultipartUpload(String objectName, String uploadId) {
        abortMultipartUpload(getBucket(), objectName, uploadId);
    }

}
