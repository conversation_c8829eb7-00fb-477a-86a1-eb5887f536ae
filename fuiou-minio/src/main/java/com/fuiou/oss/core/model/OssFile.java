package com.fuiou.oss.core.model;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * oss 文件对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OssFile {

    /**
     * 上传ID
     */
    private String uploadId;

    /**
     * 存储桶
     */
    private String bucket;

    /**
     * 对象名称（文件路径）
     */
    private String objectName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件地址
     */
    private String link;

    /**
     * 文件大小
     */
    private long size;

    /**
     * 文件大小
     */
    private LocalDateTime lastModified;

    /**
     * 文件 contentType
     */
    private String contentType;

    /**
     * 头信息
     */
    private Map<String, List<String>> headers;

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public LocalDateTime getLastModified() {
        return lastModified;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Map<String, List<String>> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, List<String>> headers) {
        this.headers = headers;
    }

    @Override
    public String toString() {
        return "OssFile{" +
            "uploadId='" + uploadId + '\'' +
            ", bucket='" + bucket + '\'' +
            ", objectName='" + objectName + '\'' +
            ", originalName='" + originalName + '\'' +
            ", link='" + link + '\'' +
            ", size=" + size +
            ", lastModified=" + lastModified +
            ", contentType='" + contentType + '\'' +
            ", headers=" + headers +
            '}';
    }

}
