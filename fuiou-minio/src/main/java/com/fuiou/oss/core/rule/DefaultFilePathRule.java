package com.fuiou.oss.core.rule;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 默认文件名规则
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class DefaultFilePathRule implements FilePathRule {

    private static volatile DefaultFilePathRule INSTANCE;

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd/");

    public static DefaultFilePathRule getInstance() {
        DefaultFilePathRule instance = INSTANCE;
        if (instance == null) {
            synchronized (DefaultFilePathRule.class) {
                instance = INSTANCE;
                if (instance == null) {
                    instance = new DefaultFilePathRule();
                    INSTANCE = instance;
                }
            }
        }
        return instance;
    }

    @Override
    public String buildFilePath(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1) {
            throw new IllegalArgumentException("文件名格式错误：" + fileName);
        }
        String fileExtension = fileName.substring(dotIndex);
        String randomUUID = UUID.randomUUID().toString().replace("-", "");
        return LocalDate.now().format(DATE_FORMATTER) + randomUUID + fileExtension;
    }

}
