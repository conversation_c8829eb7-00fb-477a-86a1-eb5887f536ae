# 通用查询组件

通用查询组件，将`Query`对象转换成`QueryWrapper`对象，利用`QueryWrapper`将查询条件与数据库条件打通，返回查询结果



## Query对象定义

### @Query

- `alias`：默认表别名
- `naming`：字段名转数据库列名策略，默认`UNDERLINE_TO_CAMEL`下划线转驼峰
  - `NO_CHANGE`：不做任何改变，原样输出
  - `UPPER_CASE`：全部大写
  - `LOWER_CASE`：全部小写
  - `UNDERLINE_TO_CAMEL`：下划线转驼峰

### @QueryField

- `alias`：表别名
- `column`：数据库字段名
- `condition`：条件`com.fuiou.query.enums.ConditionEnum`
  - `EQ`：等于
  - `NE`：不等于
  - `GT`：大于
  - `GE`：大于等于
  - `LT`：小于
  - `LE`：小于等于
  - `BETWEEN`：在值区间`BETWEEN 值1 AND 值2`
  - `NOT_BETWEEN`：不在值区间`NOT BETWEEN 值1 AND 值2`
  - `LIKE`：模糊模糊查询`%值%`
  - `LIKE_LEFT`：左模糊查询`%值`
  - `LIKE_RIGHT`：右模糊查询`值%`
  - `IN`：在列表中`IN (...)`
  - `NOT_IN`：不在列表中`NOT IN (...)`
- `betweenField`：between条件的第二个字段（end）

### 示例

``` java
@Data
@Query(alias = "u")
public class Query {

    @QueryField(condition = ConditionEnum.EQ)
    private Integer id;
    
    @QueryField(alias = "d", condition = ConditionEnum.EQ)
    private Integer deptId;

    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    @QueryField(condition = ConditionEnum.BETWEEN, betweenField = "ageEnd")
    private Integer age;

    private Integer ageEnd;
    
    @Test
    public void getQueryWrapper() {
        QueryWrapper<Object> queryWrapper = QueryUtil.getQueryWrapper(queryObj);
        // TODO 最后得到的查询条件为
        // u.id = #{id} AND d.deptId = #{deptId} AND u.name LIKE #{name} AND u.age BETWEEN #{age} AND #{ageEnd})
        System.out.println(queryWrapper.getCustomSqlSegment());
    }
    
}
```





## 使用

```java
@GetMapping
@ApiOperation(value = "查询")
ApiResult<IPage<xxxVO>> page(xxxQuery query) {
    QueryWrapper<xxx> queryWrapper = QueryUtil.getQueryWrapper(query);
    IPage<xxx> page = sysClientService.page(getPage(), queryWrapper);
    return ApiResult.data(page.convert(xxxConvert.INSTANCE::toVO));
}
```





