package com.fuiou.query.annotation;

import com.fuiou.query.enums.ConditionEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 * 查询字段注解
 * <p>
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface QueryField {

    /**
     * 对应数据库字段，默认当前字段下划线命名
     */
    String column() default "";

    /**
     * 条件
     */
    ConditionEnum condition();

    /**
     * 别名
     */
    String alias() default "";

    /**
     * between 第二个字段（end）
     */
    String betweenField() default "";

}
