package com.fuiou.query.exception;

/**
 * <p>
 * 条件构造异常
 * <p>
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class QueryException extends RuntimeException {

    public QueryException(String message) {
        super(message);
    }

    public QueryException(String message, Throwable cause) {
        super(message, cause);
    }

    public QueryException(Throwable cause) {
        super(cause);
    }

}
