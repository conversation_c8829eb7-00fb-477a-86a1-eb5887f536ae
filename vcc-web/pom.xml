<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fuiou</groupId>
        <artifactId>vcc</artifactId>
        <version>0.0.1</version>
    </parent>
    <artifactId>vcc-web</artifactId>
    <packaging>war</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <druid.version>1.1.10</druid.version>
        <db2.version>10.1</db2.version>
        <quartz.version>1.8.5</quartz.version>
        <mybatis.version>2.3.1</mybatis.version>
    </properties>

    <profiles>
        <profile>
            <id>sit</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profiles.active>mchnt</profiles.active>
                <profiles.ip>************</profiles.ip>
            </properties>
        </profile>
        <profile>
            <id>prd132</id>
            <properties>
                <profiles.active>oln</profiles.active>
                <profiles.ip>***********</profiles.ip>
            </properties>
        </profile>
        <profile>
            <id>prd133</id>
            <properties>
                <profiles.active>oln</profiles.active>
                <profiles.ip>***********</profiles.ip>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>vcc-visa</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>

        <!-- MyBatis依赖 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis.version}</version>
        </dependency>

        <!-- druid -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-ds-open-${profiles.active}</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>


        <!--db2 依赖包 -->
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc</artifactId>
            <version>${db2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc_license_cu</artifactId>
            <version>${db2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>db2-jdk1.8-support</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--富友秘钥 依赖包 -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>keyLoader</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-cacheCenter</artifactId>
            <version>3.16</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-sk-cipher</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-sk-client</artifactId>
            <version>2.7</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.76</version>
        </dependency>

        <!-- dubbo-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.5.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.14</version>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>ROOT</finalName>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.txt</include>
                    <include>**/*.sql</include>
                    <include>**/*.ico</include>
                </includes>
                <!-- filtering作用即 设置为true代表打通pom.xml和 <directory>指定路径下的文件之间的通道。（该目录下一般都是配置文件yml或properties，也可以是txt等其他）
                    解析第一种：使得<directory>指定路径下的配置文件中以${}表达式定义的变量，可以从pom.xml文件中获取到对应标签变量去完成文本替换。
                    解析第二种：或者替换<directory>指定路径下的配置文件中以@profiles.active@表达式定义的变量 -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/*.p12</include>
                    <include>**/*.html</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
