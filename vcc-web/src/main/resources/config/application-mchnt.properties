FuiouKeyManager=http://kmp.fuiou.mchnt:29002/kms/key/sysKeyLoaderService?wsdl
platId=mtest
databaseName=webdb
spring.redis.host=redis.fuiou.mchnt
spring.redis.port=33333
spring.redis.password=FuMchntTest2018
spring.redis.database=0
spring.redis.timeout=1000
spring.redis.lettuce.pool.enabled=true
spring.redis.lettuce.pool.max-idle=16
spring.redis.lettuce.pool.max-wait=32
spring.redis.lettuce.pool.min-idle=8
#cert
#clientID=B2BWS_4_9_4477123
#buyerId=4477123
#proxyPoolId=testtokenGen
#multiplyProxyPoolId=testtokenGen
#vccAuthBankId=********
#vccAuthCompanyId=2222
#vccAuthRegionId=4
#vccAuthProcessorId=9
#vccOpenCloseUrl=https://cert.api.visa.com/vpa/v1/requisitionService
#vccHttpUserId=5HM0I1A3RA7KC4XK7K0721oOIhJuSQ0U1JbAb6sZHjAAF_jvE
#vccHttpPassword=7xVLtHh
#vccHttpP12=/sit/vcc_keyAndCertBundle.p12
#vccRateUrl=https://cert.api.visa.com/forexrates/v1/foreignexchangerates
#vccCvvUrl=https://cert.api.visa.com/vpa/v1/accountManagement/GetSecurityCode
#vccGetPaymentControllerUrl=https://cert.api.visa.com/vpa/v1/accountManagement/getPaymentControls
#vccGetAuthorizationDataUrl=https://cert.api.visa.com/cdsapi/commercial/v1/ob/authdata
#vccGetAuthorizationData2Url=https://cert.api.visa.com/vbs/dapi/v1/transactions/auth
#vccRegisteredUserDetails=https://cert.api.visa.com/CPRAPI/v1/registeredUserDetails
#vccGetTrxnDataUrl=https://cert.api.visa.com/cdsapi/commercial/v1/ob/trxndata
#sandbox
#clientID=9
#buyerId=4477123
#proxyPoolId=testtokenGen
#multiplyProxyPoolId=testtokenGen
#vccAuthBankId=9999
#vccAuthCompanyId=789452
#vccAuthRegionId=1
#vccAuthProcessorId=1
#vccOpenCloseUrl=https://sandbox.api.visa.com/vpa/v1/requisitionService
#vccHttpUserId=KFMCP6T4XEUI2ZNUN1KL21V2kNtDxbQuui_BY1VolVSCDHoP8
#vccHttpPassword=3VdQfpOt
#vccHttpP12=/dev/vcc_keyAndCertBundle.p12
#vccRateUrl=https://sandbox.api.visa.com/forexrates/v1/foreignexchangerates
#vccCvvUrl=https://sandbox.api.visa.com/vpa/v1/accountManagement/GetSecurityCode
#vccGetPaymentControllerUrl=https://sandbox.api.visa.com/vpa/v1/accountManagement/getPaymentControls
#vccGetAuthorizationDataUrl=https://sandbox.api.visa.com/cdsapi/commercial/v1/ob/authdata
#vccGetAuthorizationData2Url=https://sandbox.api.visa.com/vbs/dapi/v1/transactions/auth
#vccRegisteredUserDetails=https://sandbox.api.visa.com/CPRAPI/v1/registeredUserDetails
#vccGetTrxnDataUrl=https://sandbox.api.visa.com/cdsapi/commercial/v1/ob/trxndata
#vccGetTrxnV2DataUrl=https://sandbox.api.visa.com/vbs/dapi/v1/transaction_enhanced
#prd
clientID=B2BWS_4_9_********
buyerId=**********
proxyPoolId=Pool_0728
multiplyProxyPoolId=Pool_0728
vccAuthBankId=********
vccAuthCompanyId=**********
vccOpenCloseUrl=https://api.visa.com/vpa/v1/requisitionService
vccHttpUserId=WEC6WRKFESREFOVQJLV321L2MW7yO8zHN_jYl-ZHHkJE6x2qw
vccHttpPassword=UP48kwE4YOHTUS
vccHttpP12=/prd/vcc_keyAndCertBundle.p12
vccRateUrl=https://api.visa.com/forexrates/v1/foreignexchangerates
vccCvvUrl=https://api.visa.com/vpa/v1/accountManagement/GetSecurityCode
vccGetPaymentControllerUrl=https://api.visa.com/vpa/v1/accountManagement/getPaymentControls
vccGetAuthorizationDataUrl=https://api.visa.com/cdsapi/commercial/v1/ob/authdata
vccGetAuthorizationData2Url=https://api.visa.com/vbs/dapi/v1/transactions/auth
vccRegisteredUserDetails=https://api.visa.com/CPRAPI/v1/registeredUserDetails
vccGetTrxnDataUrl=https://api.visa.com/cdsapi/commercial/v1/ob/trxndata
vccGetTrxnV2DataUrl=https://api.visa.com/v1/transactions
#vccGetTrxnV2DataUrl=https://api.visa.com/vbs/dapi/v1/transaction_enhanced
skipVerifySign=1
zk.address=zookeeper://zk3.fuiou.mchnt:2181?backup=zk1.fuiou.mchnt:2181,zk2.fuiou.mchnt:2181
keyPlat=mpay
rsaPub=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCbNOX6MzVhRZMCl3m510rr0JR2biOOo4bOgeiJlZKqlehUiPf1iSp46aZrh+LLqPuwaCTXSTX1MUNAj31W69II7vnnZ08Wt+k7cquP9cbucESTOe8blcDH1f8m8ClcbSJe5kvwqS9uBj8CfNQkNZxdbCoc5DlYggtcZ6RXckRtCwIDAQAB
spring.rabbitmq.host=rabbitmq.fuiou.mchnt
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin
nodeIp=************
dubbo.file=dubbo-consumer-mchnt.xml
