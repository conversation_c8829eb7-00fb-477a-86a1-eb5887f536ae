package com.fuiou.vcc.constant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FuiouDataCheck {
    boolean isMust() default false;

    int maxLength();

    int minLength() default 0;

    boolean isInt() default false;

    String filedDesc();

    String pattern() default "";
}
