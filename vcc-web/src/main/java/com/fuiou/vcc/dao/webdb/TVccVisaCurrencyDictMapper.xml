<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.vcc.dao.webdb.TVccVisaCurrencyDictMapper">
    <resultMap id="BaseResultMap" type="com.fuiou.vcc.web.dto.webdb.TVccVisaCurrencyDict">
        <id column="ROW_ID" jdbcType="INTEGER" property="rowId"/>
        <result column="ISO_CODE" jdbcType="CHAR" property="isoCode"/>
        <result column="THREE_WORD" jdbcType="CHAR" property="threeWord"/>
        <result column="MIN_UNIT" jdbcType="INTEGER" property="minUnit"/>
        <result column="CURRENCY_DESC" jdbcType="VARCHAR" property="currencyDesc"/>
    </resultMap>
    <sql id="Base_Column_List">
        ROW_ID
        , ISO_CODE, THREE_WORD,MIN_UNIT,CURRENCY_DESC
    </sql>
    <select id="getIsoCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VCC_VISA_CURRENCY_DICT
        where THREE_WORD = #{threeWord}
    </select>
    <select id="getCurrency" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VCC_VISA_CURRENCY_DICT
        where ISO_CODE = #{isoCode}
    </select>
    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VCC_VISA_CURRENCY_DICT
        with ur
    </select>

</mapper>