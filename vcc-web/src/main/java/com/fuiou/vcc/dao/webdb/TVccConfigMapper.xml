<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.vcc.dao.webdb.TVccConfigMapper">

    <resultMap id="BaseResultMap" type="com.fuiou.vcc.web.dto.webdb.TVccConfig">
        <result column="row_id" property="rowId"/>
        <result column="func_no" property="funcNo"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_value" property="paramValue"/>
        <result column="comments" property="comments"/>
        <result column="row_st" property="rowSt"/>
        <result column="reserved1" property="reserved1"/>
        <result column="reserved2" property="reserved2"/>
        <result column="row_crt_ts" property="rowCrtTs"/>
        <result column="rec_upd_ts" property="recUpdTs"/>
    </resultMap>

    <sql id="Base_Column_List">
        row_id,
                func_no,
                param_key,
                param_value,
                comments,
                row_st,
                reserved1,
                reserved2,
                row_crt_ts,
                rec_upd_ts
    </sql>


    <select id="loadList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_vcc_config
        WHERE func_no = #{funcNo} and param_key=#{paramKey} and row_st='1'
    </select>

    <select id="loadListByFuncNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_vcc_config
        WHERE func_no = #{funcNo} and row_st='1'
    </select>
</mapper>