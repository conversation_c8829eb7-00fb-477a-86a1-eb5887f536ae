package com.fuiou.vcc.utils;


import cn.hutool.json.JSONUtil;
import com.fuiou.fsp.soa.data.MchntKeyData;
import com.fuiou.sk.cipher.FuiouRsaCipher;
import com.fuiou.sk.service.FuiouSkServiceWrapper;
import com.fuiou.vcc.api.exception.FuiouException;
import com.fuiou.vcc.constant.ResultCode;
import com.fuiou.vcc.web.dto.request.BaseReqData;
import com.fuiou.vcc.web.dto.response.BaseRspData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 签名util
 *
 * <AUTHOR>
 */
@Slf4j
public class SignUtil {
    public static <T> String genSignStr(T reqData) throws FuiouException {
        try {
            StringBuffer rspBuffer = new StringBuffer();
            List<Field> fields = new ArrayList<Field>();
            genFields(reqData.getClass(), fields);
            Map<String, String> treeMap = new TreeMap<String, String>();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getName().equals("signature")) {
                    continue;
                }
                if (field.get(reqData) == null || StringUtils.isBlank(String.valueOf(field.get(reqData))) || !(field.get(reqData) instanceof String)) {
                    continue;
                }
                treeMap.put(field.getName(), String.valueOf(field.get(reqData)));
            }
            for (String key : treeMap.keySet()) {
                if (StringUtils.isNotBlank(treeMap.get(key))) {
                    rspBuffer.append(treeMap.get(key));
                    rspBuffer.append("|");
                }
            }
            String rspStr = rspBuffer.toString().substring(0, rspBuffer.toString().length() - 1);
            log.info("生成签名明文:" + rspStr);
            return rspStr;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            throw new FuiouException(ResultCode.SYSTEM_UNKNOWN_ERROR.getCode(), "生成验签明文失败");
        }
    }

    public static void genFields(Class<?> clazz, List<Field> fieldList) {
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            if (!containsFieldName(fieldList, field)) {
                fieldList.add(field);
            }
        }
        if (!clazz.equals(BaseReqData.class) && !clazz.equals(BaseRspData.class)) {
            genFields(clazz.getSuperclass(), fieldList);
        }
    }

    private static boolean containsFieldName(List<Field> fieldList, Field field) {
        for (Field fieldExist : fieldList) {
            if (fieldExist.getName().equals(field.getName())) {
                return true;
            }
        }
        return false;
    }

    public static <T extends BaseReqData> void verifySign(T reqData, String signature) throws FuiouException {
        String signStr = genSignStr(reqData);
        try {
            String pubkey = getPubKey(reqData.getMchntCd());
            log.info("local RSA source : " + signStr);
            log.info("rsa公钥 keyValue: " + pubkey);
            if (!FuiouRsaCipher.verifySignBySha2(pubkey, signStr, signature)) {
                throw new FuiouException(ResultCode.SIGN_VERIFY_ERROR.getCode(), ResultCode.SIGN_VERIFY_ERROR.getMsg());
            }
        } catch (FuiouException e) {
            log.error("[ 异常 ]", e);
            throw e;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            throw new FuiouException(ResultCode.KEY_CONFIG_ERROR.getCode(), ResultCode.KEY_CONFIG_ERROR.getMsg());
        }


    }

    public static void verifySign(String signStr, String clientId, String signature) throws FuiouException {
        try {
            String pubKey = getPubKey(clientId);
            if (!FuiouRsaCipher.verifySign(pubKey, signStr, signature)) {
                throw new FuiouException(ResultCode.SIGN_VERIFY_ERROR.getCode(), ResultCode.SIGN_VERIFY_ERROR.getMsg());
            }
        } catch (FuiouException e) {
            log.error("[ 异常 ]", e);
            throw e;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            throw new FuiouException(ResultCode.KEY_CONFIG_ERROR.getCode(), ResultCode.KEY_CONFIG_ERROR.getMsg());
        }

    }

    private static String getPubKey(String mchntCd) {
        MchntKeyData keyData = FuiouSkServiceWrapper.getCachedMchntKey(mchntCd);
        return keyData.getRsaMchntPubKey();
    }

    private static String getPriKey(String mchntCd) {
        MchntKeyData keyData = FuiouSkServiceWrapper.getCachedMchntKey(mchntCd);
        log.info("签名数据=" + mchntCd + "======" + JSONUtil.toJsonStr(keyData));
        return keyData.getRsaFuiouPriKey();
    }


    public static <T extends BaseRspData> String genRspSignature(T rspData) {
        try {
            String signStr = genSignStr(rspData);
            MchntKeyData data = FuiouSkServiceWrapper.getCachedMchntKey(rspData.getMchntCd());
            return FuiouRsaCipher.sign2Base64BySha2(data.getRsaFuiouPriKey(), signStr);
        } catch (FuiouException e) {
            log.error("[ 异常 ]", e);
            return e.getMessage();
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            return "";
        }
    }


    public static <T extends BaseRspData> String genRspSignature(String signStr, String clientId) {

        try {
            MchntKeyData data;
            if ("testId1".equals(clientId)) {
                data = FuiouSkServiceWrapper.getCachedMchntKey("0002900F1004443");
            } else {
                data = FuiouSkServiceWrapper.getCachedMchntKey(clientId);
            }
            return FuiouRsaCipher.sign2Base64(data.getRsaFuiouPriKey(), signStr);
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            return "";
        }
    }


    /**
     * map生成签名字符串
     *
     * @param map
     * @return
     */
    public static String genFzgSignStr(Map<String, Object> map) {
        try {
            StringBuffer rspBuffer = new StringBuffer();
            Map<String, Object> treeMap = new TreeMap<String, Object>();
            treeMap.putAll(map);
            for (String key : treeMap.keySet()) {
                if (key.equals("list") || key.equals("signature")) {
                    continue;
                }
                if (treeMap.get(key) == null || treeMap.get(key) instanceof List || StringUtils.isBlank(String.valueOf(treeMap.get(key)))) {
                    rspBuffer.append("|");
                    continue;
                }
                rspBuffer.append(treeMap.get(key));
                rspBuffer.append("|");
            }
            String rspStr = rspBuffer.toString().substring(0, rspBuffer.toString().length() - 1);
            System.out.println("生成签名明文:" + rspStr);

            return rspStr;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
        }
        return "";
    }

    /**
     * 对象生成签名字符串
     *
     * @param reqData
     * @return
     */
    public static <T> String genFzgSignStr(T reqData) {
        try {
            StringBuffer rspBuffer = new StringBuffer();
            Field[] fields = reqData.getClass().getDeclaredFields();
            Map<String, String> treeMap = new TreeMap<String, String>();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getName().equals("list") || field.getName().equals("sign")) {
                    continue;
                }
                if (field.get(reqData) == null || field.get(reqData) instanceof List || StringUtils.isBlank(String.valueOf(field.get(reqData)))) {
                    rspBuffer.append("|");
                    continue;
                }
                treeMap.put(field.getName(), (String) field.get(reqData));
            }
            for (String key : treeMap.keySet()) {
                if (StringUtils.isNotBlank(treeMap.get(key))) {
                    rspBuffer.append(treeMap.get(key));
                    rspBuffer.append("|");
                }
            }
            String rspStr = rspBuffer.toString().substring(0, rspBuffer.toString().length() - 1);
            System.out.println("生成签名明文:" + rspStr);

            return rspStr;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
        }
        return "";
    }

    /**
     * 对象生成签名字符串
     *
     * @param reqData
     * @return
     */
    public static <T> String genSignStrWithoutBaseReq(T reqData) {
        try {
            StringBuffer rspBuffer = new StringBuffer();
            Field[] fields = reqData.getClass().getDeclaredFields();
            Map<String, String> treeMap = new TreeMap<String, String>();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getName().equals("list") || field.getName().equals("signature")) {
                    continue;
                }
                if (field.get(reqData) == null || field.get(reqData) instanceof List || StringUtils.isBlank(String.valueOf(field.get(reqData)))) {
                    continue;
                }
                treeMap.put(field.getName(), (String) field.get(reqData));
            }
            for (String key : treeMap.keySet()) {
                if (StringUtils.isNotBlank(treeMap.get(key))) {
                    rspBuffer.append(treeMap.get(key));
                    rspBuffer.append("|");
                }
            }
            String rspStr = rspBuffer.toString().substring(0, rspBuffer.toString().length() - 1);
            System.out.println("生成签名明文:" + rspStr);

            return rspStr;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
        }
        return "";
    }

    public static void main(String[] args) {
        String prikey = "MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQCleZ/WWlI+MwaENt1OK1ykGMdP6hdca/14BfMnXxTo/8RelqDSkPuccYsn//v87YBejNYmL6CvsamLipXZNgpxvLjy3doJguFaN9L8HOe1E0YXNMgrSFiXMRyeE9lmSWt4Fe8c9TfhaGL+NqA8SKT/C3k/9J/L/PNQzEqzrJBpm3cVC+/EnNzMnVzVsfOmJWHgVB5YaBL8wnQjVTcuQiRXLP/AnFnIunx1SOvRyYBxWOFgu8GFspWgZqjAcCLKT9TS0uD10q7o11ekTkYRk7PfEPgKJ+Xa175dIpHcOZTdQO5ZMGT/6rXD+WSeYisPGGOW2cTa109Te0y44lCK7fJFAgMBAAECggEBAIxFfKQVbrBBSt3bMGCqS17jjlmFBAaZmIUc7hFK/YvB/LF+GJhGxLPKYH8o9XBj2DTOSF6YcytcfG/Iq9w0fkgKBfIC9GippOR4fAaxbg3GZ90WJjTioA6SWEL8aobV6B8k4Mx4ZsVSWtBKeCyCHDQDguYfNTKTm6K7evuyZbzO5HhKq0kP5+HQF7YphArBykRsS2pFjG+LgGeFpXVsdUljnLqa/go2gqLEkqF/BcL2fROA7VkS3QcG0WkgslugqDp9tS668+vdFFA4IXtuvPd6Skp6IQ2A9dv4EWS21wV8JOggY8UJ4YtDywE66fUNjrQBwKJm3bEL5r8AYppsfKUCgYEA+bPT4WdqR3OkLCZ+kZxW62bm+r/GLB22/ibaBJNSubtpQkhrEEEpMqJDyVpkqCjrNPDPhggGZOUJ/xURmcfEkiePKg0IsuRgqZCeE9QPfOnt/U9zg2U+fm+ESKPY6nl5ABqBjPR1pOBe5h3IOfWOA9ub1FE/+LRueJi5Ok7JDHsCgYEAqaX+UIvJ5ebwLdSw060TL+RTZ2MejjGPs+xCvEGmbSOT+PoP99P1ChktNX9OusqffIN4WGbbcgYXY1PtLypVmuFRMvqcv7VK8beP6eVfbBUuB9OSg1W5hwRQMkcbwz+pj9MiagjvBkH0z4QsRie9enI+aUk25pdDrwpfgB/FoD8CgYEAo8X2ahhR9Js8SljVGtvXhn3vcPbnG3hB1V/WDroxv+/Tkc29quOSCcuzehT3f/OWkRqAggAxcWtnqw4+hQYpP1MC6ymxUuPHIm/fvlGP9vXXShUaRkvZOUQbFymf0+noGtFHtxN/NayTkYpnENylUJJxGkhQFOcCrcY9dqjF5JECgYEAgsHnd4uXDTVnr9t8g1qmLEavJkPWnECFA2e5tEJhlUNT3RZYUmszNhpbpx09wGlGbgEjM/frckJRqoRYjv7xRlQecs2JHZYNcqtKKDxbxQG6Hdwr1ECxo+hmK6p1MpOSDMHuh43lNYyGtZ+pRFWDDKqbgiklQKwcRgEXxLg4aZ8CgYEA40iB/BgTHiesRcjj+nj1gog+/FwzL254lf+j1tfa7Ms2tf4L4hSo2rcDymSXnsxZFdZn3L8lKJg8jpxoNYihs9Jlr70FJDrSgOJ9WRq84ARwU/pnBR4H+9Y/u3Z5tKV8Mx5Xj4Rv0bdygltgaD8qlKbkJ7FfhmgmIAMYFNP7qAk=";

        try {
            String rsaPri = FuiouRsaCipher.sign2Base64BySha2(prikey, "1111|openAccount|USD|********|0002900F0345178|********|2019-08-24T14:15:22Z|********|1.0.0");
            System.out.println(rsaPri);
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
        }

    }

}
