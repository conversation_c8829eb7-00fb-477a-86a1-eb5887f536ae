package com.fuiou.vcc.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisUtil {
    private static final String VCC_CHECK_DUPLICATE_REQ_SSN = "VCC_CHECK_DUPLICATE_REQ_SSN";
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 判断请求流水号是否重复
     * 如果key不存在则保存后返回true,说明获得锁;
     * 如果key存在则保存后返回false,
     *
     * @param mchntCd
     * @param reqSsn
     * @return
     */
    public boolean checkNeverUsedReqSsn(String mchntCd, String reqSsn) {
        String key = StrUtil.concat(true, VCC_CHECK_DUPLICATE_REQ_SSN, mchntCd, StrUtil.DASHED, reqSsn);
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, VCC_CHECK_DUPLICATE_REQ_SSN
                    , 1, TimeUnit.DAYS));
        } catch (Exception e) {
            log.error("{},{},{}checkNeverUsedReqSsn异常", mchntCd, reqSsn, e.getMessage());
            return true;
        }
    }


}
