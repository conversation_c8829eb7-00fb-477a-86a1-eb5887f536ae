package com.fuiou.vcc.utils;

import cn.hutool.core.collection.CollUtil;
import com.fuiou.vcc.dao.webdb.TVccConfigMapper;
import com.fuiou.vcc.web.dto.webdb.TVccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class ConfigUtil {
    @Resource
    private TVccConfigMapper tVccConfigMapper;

    private static final String MCHNT_AUTH_NOTIFY_URL = "AUTH_NOTIFY_URL";


    public TVccConfig getVccConfig(String functionNo, String mchntCd) {
        List<TVccConfig> tVccConfigs = tVccConfigMapper.loadList(functionNo, mchntCd);
        if (CollUtil.isEmpty(tVccConfigs)) {
            return null;
        }
        return tVccConfigs.get(0);
    }

    public TVccConfig getOnlyOneNotifyMchntCd() {
        List<TVccConfig> tVccConfigs = tVccConfigMapper.loadListByFuncNo(MCHNT_AUTH_NOTIFY_URL);
        if (CollUtil.isEmpty(tVccConfigs)) {
            return null;
        }
        return tVccConfigs.get(0);
    }
}

