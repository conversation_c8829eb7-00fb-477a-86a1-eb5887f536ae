package com.fuiou.vcc.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.utils.RabbitMQConfig;
import com.fuiou.vcc.dao.webdb.TVccHttpLogMapper;
import com.fuiou.vcc.visa.utils.AmqpProducerUtil;
import com.fuiou.vcc.web.dto.request.BaseReqData;
import com.fuiou.vcc.web.dto.webdb.TVccHttpLog;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class AmqpUtil {
    @Resource
    private AmqpProducerUtil amqpProducerUtil;

    @Resource
    private TVccHttpLogMapper httpLogMapper;

    public void createCommercialLog(String url, String reqJson, String rspData, String ip, String duration) {
        TVccHttpLog vccHttpLog = null;
        // 发送延时消息
        try {
            BaseReqData baseReqData = JSONUtil.toBean(reqJson, BaseReqData.class);
            vccHttpLog = new TVccHttpLog();
            vccHttpLog.setMchntCd(baseReqData.getMchntCd());
            vccHttpLog.setReserved1(baseReqData.getCode());
            vccHttpLog.setReqUrl(url);
            vccHttpLog.setDuration(duration);
            vccHttpLog.setUuid(MDC.get("uuid"));
            vccHttpLog.setReqIp(ip);
            vccHttpLog.setRequestBody(reqJson);
            vccHttpLog.setResponseBody(rspData);
            vccHttpLog.setRowCrtTs(new Date());
            amqpProducerUtil.createCommercialLog(vccHttpLog);
        } catch (Exception e) {
            log.error("http记录失败,丢失~,{}", JSONUtil.toJsonStr(vccHttpLog));
        }

    }


    @RabbitListener(queues = {RabbitMQConfig.ORDER_QUEUE_NAME, RabbitMQConfig.NOTIFY_QUEUE_NAME})
    public void handleHttpMessage(String logJson) {
        log.info("接收到log消息：" + StrUtil.removeAllLineBreaks(logJson));
        try {
            TVccHttpLog vccHttpLog = JSONUtil.toBean(logJson, TVccHttpLog.class);
            vccHttpLog.setRequestBody(StrUtil.subPreGbk(StrUtil.removeAllLineBreaks(vccHttpLog.getRequestBody()), 2000, false));
            vccHttpLog.setResponseBody(StrUtil.subPreGbk(StrUtil.removeAllLineBreaks(vccHttpLog.getResponseBody()), 2000, false));
            httpLogMapper.insert(vccHttpLog);
            log.info("接收到log入库,uuid为{}", vccHttpLog.getUuid());
        } catch (Exception e) {
            log.error("vccLog 记库", e);
        }
    }


    public void createNotifyLog(String url, String mchntCd, String reqJson, String rspData, String ip, String duration) {
        TVccHttpLog vccHttpLog = null;
        // 发送延时消息
        try {
            vccHttpLog = new TVccHttpLog();
            vccHttpLog.setMchntCd(mchntCd);
            vccHttpLog.setReserved1("notify");
            vccHttpLog.setReqUrl(url);
            vccHttpLog.setDuration(duration);
            vccHttpLog.setUuid(MDC.get("uuid"));
            vccHttpLog.setReqIp(ip);
            vccHttpLog.setRequestBody(reqJson);
            vccHttpLog.setResponseBody(rspData);
            vccHttpLog.setRowCrtTs(new Date());
            amqpProducerUtil.createNotifyLog(vccHttpLog);
        } catch (Exception e) {
            log.error("notify记录失败,丢失~,{}", JSONUtil.toJsonStr(vccHttpLog));
        }

    }

}
