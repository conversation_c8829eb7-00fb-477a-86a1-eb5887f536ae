package com.fuiou.vcc.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.fuiou.fms.soa.data.MailData;
import com.fuiou.fms.soa.data.MailRspData;
import com.fuiou.fms.soa.service.IFmsService;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public class EmailUtil {
    private static IFmsService emailService = (IFmsService) SpringUtil.getBean("fmsService");

    public static void sendHtml(String rcv, String subject, String html) {
        try {
            MailData mail = getMailData(rcv, subject, html);
            MailRspData rsp = emailService.sendHtmlMail(mail);
            log.info("邮件是否推送成功: " + (rsp != null && rsp.isRspSucc()));
        } catch (Exception e) {
            log.info("邮件推送异常: " + e.getMessage());
        }
    }

    private static MailData getMailData(String rcv, String subject, String html) {
        MailData mail = new MailData();
        mail.setPlatId("cps");
        mail.setCharset("UTF-8");
        mail.setAsyncFlag(true);
        mail.setMailUserName("<EMAIL>");
        mail.setToMailList(Arrays.asList(rcv.split(",")));
        mail.setMailSubject(subject);
        mail.setMailText(html);
        return mail;
    }
}
