package com.fuiou.vcc.interceptor;


import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.exception.FuiouException;
import com.fuiou.vcc.constant.ResultCode;
import com.fuiou.vcc.web.dto.ResultVo;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice(basePackages = {"com.fuiou.vcc.web"})
public class ControllerResponseAdvice implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        // response是ResultVo类型，或者注释了NotControllerResponseAdvice都不进行包装
        return !(methodParameter.getParameterType().isAssignableFrom(ResultVo.class)
                || methodParameter.hasMethodAnnotation(NotControllerResponseAdvice.class));
    }

    @Override
    public Object beforeBodyWrite(Object data, MethodParameter returnType, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest request, ServerHttpResponse response) {
        // String类型不能直接包装
        if (returnType.getGenericParameterType().equals(String.class)) {
            try {
                // 将数据包装在ResultVo里后转换为json串进行返回
                return JSONUtil.toJsonPrettyStr(new ResultVo(JSONUtil.parseObj(data)));
            } catch (JSONException jsonException) {
                return JSONUtil.toJsonPrettyStr(new ResultVo(MapUtil.of("data", data)));
            } catch (Exception e) {
                throw new FuiouException(ResultCode.RESPONSE_PACK_ERROR.getCode(), e.getMessage());
            }

        }
        // 否则直接包装成ResultVo返回FuiouException
        return new ResultVo(data);
    }
}
