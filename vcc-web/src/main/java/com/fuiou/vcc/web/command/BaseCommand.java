package com.fuiou.vcc.web.command;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.exception.FuiouException;
import com.fuiou.vcc.api.utils.DateUtil;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.constant.ResultCode;
import com.fuiou.vcc.utils.FuiouDataCheckUtil;
import com.fuiou.vcc.utils.RedisUtil;
import com.fuiou.vcc.utils.SignUtil;
import com.fuiou.vcc.web.dto.request.BaseReqData;
import com.fuiou.vcc.web.dto.response.BaseRspData;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;

public abstract class BaseCommand<T extends BaseReqData> {
    @Resource
    private RedisUtil redisUtil;

    public String exe(String json) throws FuiouException {
        T reqData = JSONUtil.toBean(json, getTClass());
        StaticPropertiesUtil.staticMchntTheadLocal.set(reqData.getMchntCd());
        FuiouDataCheckUtil.check(reqData);
        checkSpecField(reqData);
        if (StrUtil.equals("1", StaticPropertiesUtil.staticSkipVerifySign)) {
            SignUtil.verifySign(reqData, reqData.getSignature());
        }
        BaseRspData apiResponseDto = execute(reqData);
        apiResponseDto.setSignature(SignUtil.genRspSignature(apiResponseDto));
        return JSONUtil.toJsonStr(apiResponseDto);
    }

    protected abstract BaseRspData execute(T reqData) throws FuiouException;


    public Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public void assignCommonResp(BaseReqData reqData, BaseRspData rspData) {
        rspData.setRspCode(ResultCode.SUCCESS.getCode());
        rspData.setRspDesc(ResultCode.SUCCESS.getMsg());
        rspData.setReqSsn(reqData.getReqSsn());
        rspData.setReqDate(reqData.getReqDate());
        rspData.setMchntCd(reqData.getMchntCd());
    }

    private void checkSpecField(BaseReqData reqData) throws FuiouException {
        if (!StrUtil.equals(DateUtil.getCurrentDate(), reqData.getReqDate())) {
            throw new FuiouException(ResultCode.CLIENT_REQUEST_BODY_CHECK_ERROR.getCode(), "请求日期不是当前日期");
        }
        if (!redisUtil.checkNeverUsedReqSsn(reqData.getMchntCd(), reqData.getReqSsn())) {
            throw new FuiouException(ResultCode.CLIENT_REQUEST_BODY_CHECK_ERROR.getCode(), "交易流水当日不能重复");
        }
    }
}
