package com.fuiou.vcc.web.dto.request;


import com.fuiou.vcc.constant.FuiouDataCheck;
import com.fuiou.vcc.visa.constants.Constants;
import lombok.Data;

/**
 * Created by renzw on 2020/10/16.
 */
@Data
public class TrxnQryReqData extends BaseReqData {
    @FuiouDataCheck(filedDesc = "卡号", maxLength = 19)
    private String accountNo;
    @FuiouDataCheck(filedDesc = "开始日期", maxLength = 12, pattern = Constants.REGEX_YYYYMMDD, isMust = true)
    private String startDate;
    @FuiouDataCheck(filedDesc = "结束日期", maxLength = 12, pattern = Constants.REGEX_YYYYMMDD, isMust = true)
    private String endDate;

    private String startIndex;


}
