package com.fuiou.vcc.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.fuiou.vcc.interceptor.NotControllerResponseAdvice;
import com.fuiou.vcc.web.command.CommandInvoker;
import com.fuiou.vcc.web.service.VisaNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Optional;

@Slf4j
@RestController
public class VisaVccController {
    @Resource
    private CommandInvoker commandInvoker;
    @Resource
    private VisaNotifyService visaNotifyService;

    @PostMapping("commercial")
    public String commercial(@NotBlank @RequestBody String reqJson) {
        log.info("开始执行,请求报文:" + StrUtil.removeAllLineBreaks(reqJson));
        String rspData = commandInvoker.execute(reqJson);
        log.info("响应报文:" + StrUtil.removeAllLineBreaks(rspData));
        return rspData;
    }

    @PostMapping("auth/notify")
    @NotControllerResponseAdvice
    public ResponseEntity<String> visaAuthNotify(@RequestBody String reqJson) {
        log.info("visa授权通知开始，请求参数:" + StrUtil.removeAllLineBreaks(reqJson));
        HttpResponse httpResponse = visaNotifyService.buildNotifyMchntMsg(reqJson);
        log.info("visa授权通知结束");
        return ResponseEntity.status(Optional.ofNullable(httpResponse).map(HttpResponse::getStatus).orElse(HttpStatus.BAD_REQUEST.value()))
                .contentType(MediaType.APPLICATION_JSON).body(new JSONObject().set("message", "success").toString());
    }


}
