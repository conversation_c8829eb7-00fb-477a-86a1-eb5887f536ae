package com.fuiou.vcc.web.dto.request;

import com.fuiou.vcc.constant.FuiouDataCheck;
import com.fuiou.vcc.visa.constants.Constants;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统公共参数
 */
@Data
public class BaseReqData implements Serializable {
    @FuiouDataCheck(filedDesc = "接口版本号", maxLength = 16, isMust = true)
    protected String ver;

    @FuiouDataCheck(filedDesc = "商户号", maxLength = 15, isMust = true)
    protected String mchntCd;
    @FuiouDataCheck(filedDesc = "接口代码", maxLength = 32, isMust = true)
    protected String code;

    @FuiouDataCheck(filedDesc = "交易流水", maxLength = 30, isMust = true)
    protected String reqSsn;
    @FuiouDataCheck(filedDesc = "交易日期", pattern = Constants.REGEX_YYYYMMDD, maxLength = 8, isMust = true)
    protected String reqDate;
    @FuiouDataCheck(filedDesc = "签名", maxLength = 2048, isMust = true)
    protected String signature;
}
