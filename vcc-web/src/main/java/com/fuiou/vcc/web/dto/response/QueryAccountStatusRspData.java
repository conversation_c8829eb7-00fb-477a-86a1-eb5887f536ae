package com.fuiou.vcc.web.dto.response;

import lombok.Data;

import java.util.List;

/**
 * Created by renzw on 2019/12/18.
 */
@Data
public class QueryAccountStatusRspData extends BaseRspData {
    private String currency;
    private String amt;
    private String accountNo;
    private String expiryDate;
    private String securityCode;
    private String startDate;
    private String endDate;
    private String accountStatus; //01有效，02已使用，03已销卡
    private String useAmt = "0";

    private String limitAmt = "0";
    private String limitAuthCount;
    private List<TransDetail> transList;

    @Data
    public static class TransDetail {
        private String transType;
        private String transAmt;
        private String transCurrency;
        private String transTime;
        private String transDesc;
    }


}
