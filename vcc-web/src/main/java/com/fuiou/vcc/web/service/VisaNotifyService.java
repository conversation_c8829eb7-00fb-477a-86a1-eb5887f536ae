package com.fuiou.vcc.web.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.utils.AmqpUtil;
import com.fuiou.vcc.utils.ConfigUtil;
import com.fuiou.vcc.web.dto.webdb.TVccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public class VisaNotifyService {
    @Resource
    private ConfigUtil configUtil;
    @Resource
    private AmqpUtil amqpUtil;

    public HttpResponse buildNotifyMchntMsg(String reqJson) {
        TVccConfig onlyOneNotifyMchntCd = configUtil.getOnlyOneNotifyMchntCd();
        if (Objects.isNull(onlyOneNotifyMchntCd)) {
            return null;
        }
        HttpResponse httpResponse = null;
        long startTime = System.currentTimeMillis();
        try {
            httpResponse = HttpRequest.post(onlyOneNotifyMchntCd.getParamValue()).timeout(1000 * 60).body(reqJson).execute();

        } catch (Exception e) {
            log.error("buildNotifyMchntMsg err,{}", e.getMessage());
        } finally {
            long diffTime = System.currentTimeMillis() - startTime;
            amqpUtil.createNotifyLog(onlyOneNotifyMchntCd.getParamValue(), onlyOneNotifyMchntCd.getParamKey(), reqJson, httpResponse.body(), StaticPropertiesUtil.staticNodeIp, String.valueOf(diffTime));
            return httpResponse;
        }

    }
}
