package com.fuiou.vcc.web.command;

import com.fuiou.vcc.api.data.request.ChannelGetTrxnDatatReqData;
import com.fuiou.vcc.api.data.response.ChannelGetTrxnDatatRspData;
import com.fuiou.vcc.api.exception.FuiouException;
import com.fuiou.vcc.visa.constants.Constants;
import com.fuiou.vcc.visa.service.VisaChannelService;
import com.fuiou.vcc.web.dto.request.TrxnQryReqData;
import com.fuiou.vcc.web.dto.response.BaseRspData;
import com.fuiou.vcc.web.dto.response.TrxnQryRspData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("trxnQuery")
public class TrxnQryCommand extends BaseCommand<TrxnQryReqData> {
    @Resource
    private VisaChannelService visaChannelService;

    @Override
    protected BaseRspData execute(TrxnQryReqData reqData) throws FuiouException {
        log.info("----TrxnQryCommand execute start------");
        TrxnQryRspData trxnQryRspData = new TrxnQryRspData();
        assignCommonResp(reqData, trxnQryRspData);
        checkSpecField(reqData);
        ChannelGetTrxnDatatRspData trxnData = visaChannelService.getTrxnData(genChannelReqData(reqData));
        if (!Constants.CHANNEL_RSP_CODE_SUCCESS.equals(trxnData.getRspCode())) {
            trxnQryRspData.setRspCode(trxnData.getRspCode());
            trxnQryRspData.setRspDesc(trxnData.getRspDesc());
            return trxnQryRspData;
        }
        trxnQryRspData.setRspEntities(trxnData.getRspEntities());
        trxnQryRspData.setResponseMetadata(trxnData.getResponseMetadata());
        return trxnQryRspData;
    }

    private void checkSpecField(TrxnQryReqData reqData) throws FuiouException {

    }

    private ChannelGetTrxnDatatReqData genChannelReqData(TrxnQryReqData reqData) {
        ChannelGetTrxnDatatReqData trxnDatatReqData = new ChannelGetTrxnDatatReqData();
        trxnDatatReqData.setEndDate(reqData.getEndDate());
        trxnDatatReqData.setStartDate(reqData.getStartDate());
        trxnDatatReqData.setStartIndex(reqData.getStartIndex());
        return trxnDatatReqData;
    }
}
