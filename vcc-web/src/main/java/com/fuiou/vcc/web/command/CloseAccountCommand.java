package com.fuiou.vcc.web.command;

import com.fuiou.vcc.api.constants.ChannelConstants;
import com.fuiou.vcc.api.data.request.ChannelCloseAccountReqData;
import com.fuiou.vcc.api.data.response.ChannelBaseRspData;
import com.fuiou.vcc.api.exception.FuiouException;
import com.fuiou.vcc.visa.constants.Constants;
import com.fuiou.vcc.visa.service.VisaChannelService;
import com.fuiou.vcc.web.dto.request.CloseAccountReqData;
import com.fuiou.vcc.web.dto.response.BaseRspData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("closeAccount")
public class CloseAccountCommand extends BaseCommand<CloseAccountReqData> {
    @Resource
    private VisaChannelService visaChannelService;

    @Override
    protected BaseRspData execute(CloseAccountReqData reqData) throws FuiouException {
        log.info("----CloseAccountCommand execute start------");
        checkSpecField(reqData);
        ChannelBaseRspData rspData = visaChannelService.closeAccount(genCloseAccountReqData(reqData));
        BaseRspData baseRspData = new BaseRspData();
        assignCommonResp(reqData, baseRspData);
        if (!Constants.CHANNEL_RSP_CODE_SUCCESS.equals(rspData.getRspCode())) {
            baseRspData.setRspCode(rspData.getRspCode());
            baseRspData.setRspDesc(rspData.getRspDesc());
        }
        baseRspData.setResSsn(rspData.getResSsn());
        return baseRspData;
    }

    private void checkSpecField(CloseAccountReqData reqData) throws FuiouException {

    }

    private ChannelCloseAccountReqData genCloseAccountReqData(CloseAccountReqData reqData) {
        ChannelCloseAccountReqData channelCloseAccountReqData = new ChannelCloseAccountReqData();
        channelCloseAccountReqData.setCardModel(ChannelConstants.CARD_MODEL_MULTIPLY);
        channelCloseAccountReqData.setCardNo(reqData.getAccountNo());
        return channelCloseAccountReqData;
    }
}
