package com.fuiou.vcc.web.dto;

import com.fuiou.vcc.constant.ResultCode;
import com.fuiou.vcc.constant.StatusCode;
import lombok.Data;

@Data
public class ResultVo {
    // 状态码
    private String code;

    // 状态信息
    private String msg;

    // 返回对象
    private Object data;

    // 手动设置返回vo
    public ResultVo(String code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ResultVo(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    // 默认返回成功状态码，数据对象
    public ResultVo(Object data) {
        this.code = ResultCode.ACCEPT_SUCCESS.getCode();
        this.msg = ResultCode.ACCEPT_SUCCESS.getMsg();
        this.data = data;
    }

    // 返回指定状态码，数据对象
    public ResultVo(StatusCode statusCode, Object data) {
        this.code = statusCode.getCode();
        this.msg = statusCode.getMsg();
        this.data = data;
    }

    // 只返回状态码
    public ResultVo(StatusCode statusCode) {
        this.code = statusCode.getCode();
        this.msg = statusCode.getMsg();
        this.data = null;
    }
}
