package com.fuiou.vcc.web.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fuiou.vcc.constant.FuiouDataCheck;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloseAccountReqData extends BaseReqData {
    @FuiouDataCheck(filedDesc = "卡号", maxLength = 16, isMust = true)
    private String accountNo;


}
