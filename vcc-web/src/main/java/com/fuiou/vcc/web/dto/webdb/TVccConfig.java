package com.fuiou.vcc.web.dto.webdb;

import lombok.Data;

import java.util.Date;

@Data
public class TVccConfig {
    /**
     * 主键id
     */
    private Integer rowId;

    /**
     * 功能编号
     */
    private String funcNo;

    /**
     * 配置名称
     */
    private String paramKey;

    /**
     * 配置内容
     */
    private String paramValue;

    /**
     * 描述
     */
    private String comments;

    /**
     * 行状态。1为有效
     */
    private String rowSt;

    /**
     * 保留1
     */
    private String reserved1;

    /**
     * 保留2
     */
    private String reserved2;

    /**
     * 创建时间
     */
    private Date rowCrtTs;

    /**
     * 更新时间
     */
    private Date recUpdTs;
}
