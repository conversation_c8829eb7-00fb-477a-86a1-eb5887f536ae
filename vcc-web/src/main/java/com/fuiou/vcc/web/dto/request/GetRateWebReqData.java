package com.fuiou.vcc.web.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fuiou.vcc.constant.FuiouDataCheck;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRateWebReqData extends BaseReqData {
    @FuiouDataCheck(filedDesc = "原币种", maxLength = 3, isMust = true)
    private String sourceCurrency;
    @FuiouDataCheck(filedDesc = "目标币种", maxLength = 3, isMust = true)
    private String targetCurrency;
}
