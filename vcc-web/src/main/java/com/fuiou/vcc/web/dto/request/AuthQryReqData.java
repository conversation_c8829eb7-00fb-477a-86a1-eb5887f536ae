package com.fuiou.vcc.web.dto.request;


import com.fuiou.vcc.constant.FuiouDataCheck;
import com.fuiou.vcc.visa.constants.Constants;
import lombok.Data;

/**
 * Created by renzw on 2020/10/16.
 */
@Data
public class AuthQryReqData extends BaseReqData {
    @FuiouDataCheck(filedDesc = "卡号", maxLength = 19)
    private String accountNo;
    @FuiouDataCheck(filedDesc = "开始日期", maxLength = 8, pattern = Constants.REGEX_YYYYMMDD, isMust = true)
    private String startDate;
    @FuiouDataCheck(filedDesc = "结束日期", maxLength = 8, pattern = Constants.REGEX_YYYYMMDD, isMust = true)
    private String endDate;
    @FuiouDataCheck(filedDesc = "页码", maxLength = 8, isInt = true)
    private String pageNum;
    @FuiouDataCheck(filedDesc = "页面大小", maxLength = 3, isInt = true)
    private String pageSize;


    @FuiouDataCheck(filedDesc = "消息类型", maxLength = 200)
    private String msgType;

    @FuiouDataCheck(filedDesc = "授权类别代码", maxLength = 100)
    private String authCatgCd;

}
