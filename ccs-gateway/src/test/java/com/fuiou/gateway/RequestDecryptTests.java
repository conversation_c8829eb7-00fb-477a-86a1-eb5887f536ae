package com.fuiou.gateway;

import com.fuiou.crypto.service.impl.RSACryptoServiceImpl;
import com.fuiou.crypto.utils.AESCryptoUtil;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.rsa.crypto.KeyStoreKeyFactory;
import org.springframework.util.AntPathMatcher;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import java.security.KeyPair;

/**
 * 请求解密测试
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class RequestDecryptTests {

    /**
     * aes 秘钥进行 rsa 加密给到前端
     */
    @Test
    public void rsaEncrypt() {
        KeyPair keyPair = keyPair();
        RSACryptoServiceImpl rsaCryptoService = new RSACryptoServiceImpl(keyPair);
        String encrypt = rsaCryptoService.encryptToString("VcCnCarCVCA2jG9y");
        System.out.println(encrypt);
    }

    /**
     * aes 秘钥进行 rsa 加密给到前端
     */
    @Test
    public void rsaDecrypt() {
        KeyPair keyPair = keyPair();
        RSACryptoServiceImpl rsaCryptoService = new RSACryptoServiceImpl(keyPair);
        String encryptMessage = "XdKQzFIMiJVoF9RaOXHe03hvNNtgCO1GOcKq9OXCdgyNXqkLLU/WW4Q6NGo6ulM4iiJH2Vu/6mgm6/kRxJND/UDVhDyv86JxADPpKcJ+fcefZ3UnN4agynxV71i/NtXpeBdevRf20Rf9u90+XoMMlZT/zhF9gIBJM0cWqh/0rEy6JKkXjSU3GfuAVxxVg8y4aI4u5pSJQDQBptqvclc0Y5Rw3Y6Z/2ri+XzBDbyoLlxK1pudMv9ortutfVxFNzCZVdScsDeV+wWzZES7Y/uPtenJjibsZPFU3QuGmF6K+hTkV4c+XvHAapcKYOkcu3O7wDWNM4bbhnNpvNvwHMhmAw==";
        String decrypt = rsaCryptoService.decryptToString(encryptMessage);
        System.out.println(decrypt);
    }

    @Test
    public void aesEncrypt() {
    }

    @Test
    public void aesDecrypt() {

    }

    @Test
    public void test() {
        try {
            String encryptData = "VGZV4KP3raB4VIn432PnsEWF/QlVxXxdMhE5ZDlX/mdvrDwKj09uEBovWi77N3mJ7qyQYa7Yi1GLQab5VQhurwPrDZyk6xp7wlezVQwkkIPBbUqQhW9Bo+L+m4liIAHlzbWwswfN0db8Kr6JIXpqTFTVedMWMj/ndMt2zkBVh0eZy8AM66NqQKnXyOLuSOM6RGzlP3r7YYHgJDeFyoEsxI8PK9Ib7Wlf9f+mL5vXOi6YNV5QghKu28ZgZTLKhd8cJmosxa8EgB9ruhNfS8mC3f+EKRl5KsdBDfkCu16QiyhYCzniZoZGG3qcEAZS5oK0d7Ba2dY09XbmxHne9RduWH8L0Af1ZghNPXEWQqxGl0ZE8QnLwV5GGfQHlzDJ5ylbRh93S3PA6VRoW85JUuyOPk8bGvI1RpzKWtxq1SmzirdD1hJG/oNGzhr/IMh/afLRQwYIxW0xz2QbD+4KsH4vb8ajbIs5c/TNCGhtMRr9RYOmXsnx9IE/Bg7FVp6bCh3FElppxuiK0HI0VVR3RhWw7ByhwReppzTMLUxvvYd3Mi/zltoQkvWwwBnPr0WRdKWwLmm1oDusn/vKg8RZMzVlBE9Jw9dzft70bSv2hDGPfRfq/pc9xEU62ZKa6cnVdqhEaRDGlqP8MObj80XXMhXM3vjwlbfW86U15+knW7lgbup3EqEcokUkgl4V8yrrDSCz";
            System.out.println("结果："+AESCryptoUtil.decryptToString("tr6uf7iuk3tr6uf7", "bca5GlFMtp7rjj7w", encryptData));
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
    }

    private KeyPair keyPair() {
        ClassPathResource ksFile = new ClassPathResource("gateway.jks");
        KeyStoreKeyFactory keyStoreKeyFactory = new KeyStoreKeyFactory(ksFile, "klEVpbmC".toCharArray());
        return keyStoreKeyFactory.getKeyPair("gateway", "WzOtau9R".toCharArray());
    }

}
