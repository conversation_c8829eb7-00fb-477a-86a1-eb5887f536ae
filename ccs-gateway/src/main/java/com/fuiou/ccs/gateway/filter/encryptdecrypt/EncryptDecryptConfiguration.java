package com.fuiou.ccs.gateway.filter.encryptdecrypt;

import com.fuiou.ccs.gateway.utils.UrlEnabler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.KeyPair;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21 17:17
 */
@Configuration
@ConditionalOnProperty(name = "fuiou.crypto.enabled", havingValue = "true")
public class EncryptDecryptConfiguration {

    private static Logger logger = LoggerFactory.getLogger(EncryptDecryptConfiguration.class);

    private final KeyPair rsaKeyPair;

    public EncryptDecryptConfiguration(KeyPair rsaKeyPair) {
        this.rsaKeyPair = rsaKeyPair;
    }

    @Bean("encryptDecryptProperties")
    public EncryptDecryptProperties properties() {
        return new EncryptDecryptProperties();
    }

    @Bean("encryptDecryptEnabler")
    public UrlEnabler urlEnabler() {
        UrlEnabler e = null;
        e = new UrlEnabler(properties().getClientMap());
        e.setTargetFilterName("encryptDecryptFilter");
        return e;
    }

    @Bean
    public EncryptDecryptFilter encryptDecryptFilter() {
        EncryptDecryptFilter f = new EncryptDecryptFilter(rsaKeyPair);
        f.setUrlEnabler(urlEnabler());
        return f;
    }

}
