package com.fuiou.ccs.gateway.security.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 跳过的url配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RefreshScope
@ConfigurationProperties(prefix = "fuiou.security")
public class AuthProperties {

    /**
     * 跳过的url
     */
    private List<String> skipUrls;

    /**
     * 是否并发登录
     */
    private boolean isConcurrentLogin = false;

    public List<String> getSkipUrls() {
        return Optional.ofNullable(skipUrls).orElse(new ArrayList<>(1));
    }

    public void setSkipUrls(List<String> skipUrls) {
        this.skipUrls = skipUrls;
    }

    public boolean isConcurrentLogin() {
        return isConcurrentLogin;
    }

    public void setConcurrentLogin(boolean concurrentLogin) {
        isConcurrentLogin = concurrentLogin;
    }

    @Override
    public String toString() {
        return "AuthProperties{"
                + "skipUrls=" + skipUrls
                + ", isConcurrentLogin=" + isConcurrentLogin
                + '}';
    }

}
