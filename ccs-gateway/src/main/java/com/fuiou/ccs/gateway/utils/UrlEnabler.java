package com.fuiou.ccs.gateway.utils;

import com.fuiou.ccs.gateway.constants.GatewayConstants;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class UrlEnabler {
    private static Logger logger = LoggerFactory.getLogger(UrlEnabler.class);

    /**
     * default matcher
     */
    private UrlMatcher matcher = new UrlMatcher();

    /**
     * default urlResult
     */
    private Map<String, Boolean> urlResult = Maps.newLinkedHashMap();

    private Map<String, Map<String, Boolean>> urlResultMap = Maps.newHashMap();

    private Map<String, UrlMatcher> urlMatcherMap = Maps.newHashMap();

    private String targetFilterName = "not set";

    public UrlEnabler(List<String> enablerPathList) {
        if (enablerPathList == null || enablerPathList.size() < 1) {
            throw new RuntimeException("pathList is null");
        }
        putEnablerPathListToMatcher(enablerPathList, this.matcher, this.urlResult);
        urlMatcherMap.put(GatewayConstants.DEFAULT_CLIENTID, matcher);
        urlResultMap.put(GatewayConstants.DEFAULT_CLIENTID, urlResult);
    }

    public UrlEnabler(Map<String, List<String>> clientMap) {
        if (clientMap == null || clientMap.size() < 1) {
            throw new RuntimeException("clientMap is null");
        }
        // 先获取default下的pathList
        List<String> defalutEnablerPathList = clientMap.get(GatewayConstants.DEFAULT_CLIENTID);
        UrlMatcher defalutUrlMatcher = new UrlMatcher();
        Map<String, Boolean> defalutUrlResult = Maps.newLinkedHashMap();
        putEnablerPathListToMatcher(defalutEnablerPathList, defalutUrlMatcher, defalutUrlResult);
        urlMatcherMap.put(GatewayConstants.DEFAULT_CLIENTID, defalutUrlMatcher);
        urlResultMap.put(GatewayConstants.DEFAULT_CLIENTID, defalutUrlResult);
        this.matcher = defalutUrlMatcher;
        this.urlResult = defalutUrlResult;
        for (Map.Entry<String, List<String>> client : clientMap.entrySet()) {
            String clientId = client.getKey();
            List<String> enablerPathList = client.getValue();
            if (clientId.equalsIgnoreCase(GatewayConstants.DEFAULT_CLIENTID)) {
                continue;
            }
            UrlMatcher urlMatcher = new UrlMatcher();
            Map<String, Boolean> urlResultTemp = Maps.newLinkedHashMap();
            putEnablerPathListToMatcher(enablerPathList, urlMatcher, urlResultTemp);
            urlMatcher.merge(defalutUrlMatcher);
            for (Map.Entry<String, Boolean> entry : defalutUrlResult.entrySet()) {
                if (urlResultTemp.containsKey(entry.getKey())) {
                    continue;
                }
                urlResultTemp.put(entry.getKey(), entry.getValue());
            }
            urlMatcherMap.put(clientId, urlMatcher);
            urlResultMap.put(clientId, urlResultTemp);
        }
    }

    /**
     * 追加pathList, 重复项覆盖已有的
     *
     * @param appendPathList
     */
    public UrlEnabler appendPathList(List<String> appendPathList) {
        if (appendPathList == null || appendPathList.size() < 1) {
            return this;
        }
        putEnablerPathListToMatcher(appendPathList, this.matcher, this.urlResult);
        urlMatcherMap.put(GatewayConstants.DEFAULT_CLIENTID, matcher);
        urlResultMap.put(GatewayConstants.DEFAULT_CLIENTID, urlResult);
        return this;
    }

    public UrlEnabler appendClientMap(Map<String, List<String>> appendClientMap) {
        if (appendClientMap == null || appendClientMap.size() < 1) {
            return this;
        }
        for (Map.Entry<String, List<String>> client : appendClientMap.entrySet()) {
            String clientId = client.getKey();
            List<String> appendPathList = client.getValue();
            UrlMatcher urlMatcher = urlMatcherMap.get(clientId);
            Map<String, Boolean> urlResultTemp = urlResultMap.get(clientId);
            putEnablerPathListToMatcher(appendPathList, urlMatcher, urlResultTemp);
            urlMatcherMap.put(clientId, urlMatcher);
            urlResultMap.put(clientId, urlResultTemp);
        }
        return this;
    }

    private void putEnablerPathListToMatcher(List<String> enablerPathList, UrlMatcher matcher, Map<String, Boolean> urlResult) {
        for (String path : enablerPathList) {
            String[] tmp = path.split("->");
            if (tmp.length != 2) {
                logger.warn(String.format("%s format is incorrect, check whether url contains '->', please update accordingly, it will not be support in future...", path));
                tmp = new String[2];
                tmp[0] = path;
                tmp[1] = "true";
//				throw new RuntimeException(String.format("%s format is incorrect, check whether url contains ->", path));
            }
            String url = tmp[0].trim();
            String enabled = tmp[1].trim();
            if (!"true".equals(enabled) && !"false".equals(enabled)) {
                throw new RuntimeException(String.format("%s format is incorrect, check whether url contains ->", path));
            }
            urlResult.put(url, Boolean.valueOf(enabled));
            matcher.putPath(url);
        }
    }

    public boolean enabled(String url) {
        return enabled(url, GatewayConstants.DEFAULT_CLIENTID);
    }

    public boolean enabled(String url, String clientId) {
        UrlMatcher urlMatcher = urlMatcherMap.getOrDefault(clientId, this.matcher);
        String matchedPattern = urlMatcher.match(url);
        if (StringUtils.isBlank(matchedPattern)) {
            logger.debug("filter name is [{}], return false; clientId is [{}], url is [{}] matched pattern is [{}]", targetFilterName, clientId, url, matchedPattern);
            return false;
        }
        Map<String, Boolean> urlResultTemp = urlResultMap.getOrDefault(clientId, this.urlResult);
        if (!urlResultTemp.containsKey(matchedPattern)) {
            throw new RuntimeException(String.format("cannot find matchedPattern %s in map。。。", matchedPattern));
        }
        Boolean isEnabled = urlResultTemp.get(matchedPattern);
        logger.debug("filter name is [{}], get from map is [{}] ,clientId is [{}], url is [{}] matched pattern is [{}]", targetFilterName, isEnabled, clientId, url, matchedPattern);
        return isEnabled;
    }

    public void setTargetFilterName(String targetFilterName) {
        this.targetFilterName = targetFilterName;
    }
}
