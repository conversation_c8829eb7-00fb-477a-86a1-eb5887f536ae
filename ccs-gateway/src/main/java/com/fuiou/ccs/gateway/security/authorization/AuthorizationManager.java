package com.fuiou.ccs.gateway.security.authorization;

import com.fuiou.ccs.gateway.constants.GatewayConstants;
import com.fuiou.ccs.gateway.security.properties.AuthProperties;
import com.fuiou.common.constants.AuthConstants;
import com.fuiou.common.domain.AuthUser;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.redis.util.RedisUtil;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 认证管理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@EnableConfigurationProperties({AuthProperties.class})
public class AuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthorizationManager.class);

    final RedisUtil redisUtil;
    final PathMatcher pathMatcher;
    final AuthProperties authProperties;

    public AuthorizationManager(RedisUtil redisUtil, AuthProperties authProperties) {
        this.redisUtil = redisUtil;
        this.authProperties = authProperties;
        this.pathMatcher = new AntPathMatcher();
    }

    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext authorizationContext) {
        ServerHttpRequest request = authorizationContext.getExchange().getRequest();

        // 判断是否跳过此url
        String path = request.getURI().getPath();
        List<String> skipUrls = authProperties.getSkipUrls();
        if (isSkip(path, skipUrls)) {
            return Mono.just(new AuthorizationDecision(true));
        }

//        // 对要跨域的预检请求直接放行
//        if (request.getMethod() == HttpMethod.OPTIONS) {
//            return Mono.just(new AuthorizationDecision(true));
//        }
        // 验证是否有代理认证通过标识
        String agentAuthSuccess = request.getHeaders().getFirst(AuthConstants.AGENT_AUTH_SUCCESS);
        if ("true".equals(agentAuthSuccess)) {
            return Mono.just(new AuthorizationDecision(true));
        }

        // 校验认证用户
        AuthUser authUser = getAuthUser(request);
        checkAuthUser(authUser);

        // 默认添加管理员权限（用管理员权限直接跳过）
        List<String> authorities = new ArrayList<>();
        authorities.add(AuthConstants.AUTHORITY_PREFIX + GatewayConstants.ADMIN_ROLE_CODE);

        // urlPrem -> [roles...]
        Map<String, List<String>> urlPermMap = redisUtil.hEntries(AuthConstants.AUTH_PERMISSION_RUL_RULES_KEY);
        if (MapUtils.isNotEmpty(urlPermMap)) {
            String method = request.getMethodValue();
            String restfulPath = method + ":" + path;
            for (Map.Entry<String, List<String>> entry : urlPermMap.entrySet()) {
                String urlPerm = entry.getKey();
                // 匹配urlPerm，添加需要验证的角色
                if (pathMatcher.match(urlPerm, restfulPath)) {
                    authorities.addAll(entry.getValue());
                }
            }
        }
        return authentication
                .filter(Authentication::isAuthenticated)
                .flatMapIterable(Authentication::getAuthorities)
                .map(GrantedAuthority::getAuthority)
                .any(authorities::contains)
                .map(AuthorizationDecision::new)
                .defaultIfEmpty(new AuthorizationDecision(false));
    }

    /**
     * 是否跳过此url
     *
     * @param path     当前路径
     * @param skipUrls 跳过的url
     * @return 是否
     */
    private boolean isSkip(String path, List<String> skipUrls) {
        if (CollectionUtils.isEmpty(skipUrls)) {
            return false;
        }
        for (String skipUrl : skipUrls) {
            if (pathMatcher.match(skipUrl, path)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验用户
     *
     * @throws OAuth2AuthenticationException
     */
    private void checkAuthUser(AuthUser authUser) {
        if (authUser == null) {
            OAuth2Error oAuth2Error = new OAuth2Error("invalid_token", "账号未登录", null);
            throw new OAuth2AuthenticationException(oAuth2Error);
        }

        // 允许并发登录
        if (authProperties.isConcurrentLogin()) {
            return;
        }

        String clientId = authUser.getClientId();
        String userId = authUser.getUserId();
        String jti = authUser.getJti();
        String jwtRedisKey = AuthUtil.getJwtRedisKey(clientId, userId);
        String redisJti = null;
        try {
            redisJti = redisUtil.get(jwtRedisKey);
        } catch (Exception e) {
            LOGGER.warn(e.getMessage(), e);
            OAuth2Error oAuth2Error = new OAuth2Error("invalid_token", "认证服务异常", null);
            throw new OAuth2AuthenticationException(oAuth2Error);
        }

        if (!Objects.equals(jti, redisJti)) {
            OAuth2Error oAuth2Error = new OAuth2Error("invalid_token", "账号已在其他地方登录", null);
            throw new OAuth2AuthenticationException(oAuth2Error);
        }
    }

    /**
     * 获取认证用户
     */
    private AuthUser getAuthUser(ServerHttpRequest request) {
        String authUserHeader = request.getHeaders().getFirst(AuthConstants.AUTH_USER_HEADER);
        return AuthUtil.getUser(authUserHeader);
    }

}
