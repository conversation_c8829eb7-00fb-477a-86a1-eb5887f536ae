package com.fuiou.ccs.gateway.security;

import com.fuiou.common.api.ApiResult;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import com.fuiou.ccs.gateway.utils.WebfluxUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 认证失败处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class RestAuthenticationEntryPoint implements ServerAuthenticationEntryPoint {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestAuthenticationEntryPoint.class);

    @Override
    public Mono<Void> commence(ServerWebExchange exchange, AuthenticationException authException) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        LOGGER.error("Responding with unauthorized error. Message:{}, url:{}", authException.getMessage(), request.getPath());
        ApiResult<Void> result;
        if (authException.getMessage() == null) {
            result = ApiResult.fail(GlobalErrorCode.UNAUTHORIZED);
        } else {
            result = ApiResult.fail(GlobalErrorCode.UNAUTHORIZED.getCode(), authException.getMessage());
        }
        return WebfluxUtil.writeJsonResponse(response, HttpStatus.OK, result);
    }

}
