fuiou:
  # 加密配置
  crypto:
    enabled: true
    # ras 加密
    rsa:
      # 生成： keytool -genkey -alias gateway -keyalg RSA -keypass Vg1xtCr1 -keystore D:/gateway.jks -storepass AQwRZ7Bg
      # 获取公钥：需要openssl软件 http://slproweb.com/download/Win64OpenSSL_Light-1_1_1k.exe 安装后需要配环境变量
      # 命令：keytool -list -rfc --keystore gateway.jks | openssl x509 -inform pem -pubkey
      key-alias: gateway
      key-password: Vg1xtCr1
      key-store: gateway.jks
      key-store-password: AQwRZ7Bg
    aes:
      iv-parameter: bca5GlFMtp7rjj7w
