spring:
  cloud:
    nacos:
      server-addr: 10.0.16.51:8848
      username: nacos
      password: VDDGnYjdMPNIyBkw
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d
    gateway:
      #      filter:
      #        remove-hop-by-hop:
      #          headers:
      #            - agent_auth_success
      # 与 Spring Cloud 注册中心的集成，对应 DiscoveryLocatorProperties 类
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins:
              - "https://gdpt.fuiou.com"
            exposedHeaders: "*"
            allowCredentials: true
            allowedHeaders:
              - Origin
              - Accept
              - X-Requested-With
              - Content-Type
              - Authorization
              - X-XSRF-Token
              - X-Auth-Token
              - X-Secret-Key
              - X-Ca-Timestamp
              - X-Ca-Nonce
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE

  security:
    oauth2:
      resourceserver:
        jwt:
          public-key-location: classpath:jwt-public-prod.key
  #          jwk-set-uri: 'http://localhost:8001/fuiou-auth/rsa/public_key'

fuiou:
  gateway:
    # 自定义路由
    dynamic-route:
      enabled: false
