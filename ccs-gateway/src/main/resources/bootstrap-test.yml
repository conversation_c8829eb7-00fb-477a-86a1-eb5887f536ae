spring:
  cloud:
    nacos:
      server-addr: 192.168.8.204:9848
      username: nacos
      password: 1qazse4
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: dd4a6df7-e29f-48ee-a551-fd695921bd2c
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: dd4a6df7-e29f-48ee-a551-fd695921bd2c

    gateway:
      #      filter:
      #        remove-hop-by-hop:
      #          headers:
      #            - agent_auth_success
      # 与 Spring Cloud 注册中心的集成，对应 DiscoveryLocatorProperties 类
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            exposedHeaders: "*"
            allowCredentials: true
            allowedHeaders:
              - Origin
              - Accept
              - X-Requested-With
              - Content-Type
              - Authorization
              - X-XSRF-Token
              - X-Auth-Token
              - X-Secret-Key
              - X-Ca-Timestamp
              - X-Ca-Nonce
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE

  security:
    oauth2:
      resourceserver:
        jwt:
          public-key-location: classpath:jwt-public.key
  #          jwk-set-uri: 'http://localhost:8001/fuiou-auth/rsa/public_key'

fuiou:
  gateway:
    # 自定义路由
    dynamic-route:
      enabled: true
