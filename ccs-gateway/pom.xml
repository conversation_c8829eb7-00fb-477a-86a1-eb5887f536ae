<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ccs-gateway</artifactId>
    <parent>
        <artifactId>ccs-cloud</artifactId>
        <groupId>com.fuiou.ccs</groupId>
        <version>1.0.0</version>
    </parent>

    <dependencies>
        <!-- fuiou cloud dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-cloud-common</artifactId>
            <version>${fuiou-cloud-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fuiou</groupId>
                    <artifactId>fuiou-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- fuiou crypto dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-crypto</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>

        <!-- spring cloud gateway dependency -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>

        <!-- alibaba sentinel gateway dependency -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
        </dependency>

        <!-- spring security oauth2 dependency -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-resource-server</artifactId>
            <version>5.2.15.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-security-oauth2-core</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-security-web</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
            <version>5.2.15.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-security-oauth2-core</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>spring-security-oauth2-core</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-web</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-oauth2-core</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-core</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>

        <!-- spring boot dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>ccs-gateway</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>