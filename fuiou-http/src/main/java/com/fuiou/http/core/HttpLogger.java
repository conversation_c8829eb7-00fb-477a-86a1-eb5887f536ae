package com.fuiou.http.core;

import okhttp3.logging.HttpLoggingInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * OkHttp logger, Slf4j and console log.
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public enum HttpLogger implements HttpLoggingInterceptor.Logger {

	/**
	 * http 日志：Slf4j
	 */
	Slf4j() {
        private final Logger LOGGER = LoggerFactory.getLogger(HttpLogger.class);
		@Override
		public void log(String message) {
            LOGGER.info(message);
		}
	},

	/**
	 * http 日志：Console
	 */
	Console() {
		@Override
		public void log(String message) {
			// 统一添加前缀，方便在茫茫日志中查看
			System.out.print("ConsoleLogger: ");
			System.out.println(message);
		}
	};

}
