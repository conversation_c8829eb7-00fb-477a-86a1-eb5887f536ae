package com.fuiou.http.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * http 测试
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class HttpUtilTests {

    @Test
    public void getForObject() {
        String url = "https://www.baidu.com/";
        String html = HttpUtil.getForObject(url, String.class);
        Assertions.assertNotNull(html);
    }

    @Test
    public void getForList() {

    }

    @Test
    public void postForList() {
       /* String url = "http://localhost:8123/cadre-score/deal/score/list";
        ApiResult result = HttpUtil.postForObjectByForm(url, ApiResult.class);
        System.out.println(result);*/
    }

    @Test
    public void postForObject() {

    }

    @Test
    public void getForResult() {

    }

    @Test
    public void postForResult() {

    }


}
