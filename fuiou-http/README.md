# HTTP 调用组件

根据`okhttp3` 封装的 Http 调用组件，核心类：`HttpUtil`



## 核心方法

- 自定义请求

  ```java
  Exchange exchange(String url, HttpMethod httpMethod, Map<String, String> header, Map<String, Object> params)
      
  Exchange exchangeByJson(String url, HttpMethod httpMethod, Map<String, String> header, Object bodyJson)
  ```

- GET请求，返回对象

  ``` java
  T getForObject(String url, Class<T> resultType, Object... uriVariables)
      
  T getForObject(String url, Map<String, Object> params, Class<T> resultType)
      
  T getForObject(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
  ```

- GET请求，返回集合

  ```java
  List<T> getForList(String url, Class<T> resultType, Object... uriVariables)
      
  List<T> getForList(String url, Map<String, Object> params, Class<T> resultType)
      
  List<T> getForList(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
  ```

- POST表单请求

  ```java
  // 返回对象
  T postForObjectByForm(String url, Map<String, Object> params, Class<T> resultType)
  T postForObjectByForm(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
      
  // 返回集合
  List<T> postForListByForm(String url, Map<String, Object> params, Class<T> resultType)
  List<T> postForListByForm(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
  ```

- POST Json请求

  ``` java
  // 返回对象
  T postForObject(String url, Map<String, Object> params, Class<T> resultType)
  T postForObject(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
      
  // 返回集合
  List<T> postForList(String url, Map<String, Object> params, Class<T> resultType)
  List<T> postForList(String url, Map<String, String> header, Map<String, Object> params, Class<T> resultType)
  ```

  

