package com.fuiou.crypto.error;

import com.fuiou.common.exception.IErrorCode;

/**
 * 加解密错误编号
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public enum CryptoErrorCode implements IErrorCode {

    /**
     * 加密失败
     */
    ENCRYPT_FAIL(600, "加密失败"),
    /**
     * 解密失败
     */
    DECRYPT_FAIL(601, "解密失败"),
    /**
     * 签名/秘钥校验失败
     */
    SECRET_KEY_MISS(602, "签名缺失"),
    /**
     * 签名/秘钥校验失败
     */
    SECRET_KEY_VERIFY_FAIL(603, "签名校验失败"),
    ;

    final int code;

    final String msg;

    CryptoErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}
