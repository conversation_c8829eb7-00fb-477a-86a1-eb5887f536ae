package com.fuiou.crypto.utils;

import com.fuiou.crypto.constants.CryptoConstants;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * RSA 加解密工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class RSACryptoUtil {

    /**
     * 创建加密器
     *
     * @param publicKey 公钥
     * @return 加密器
     */
    public static Cipher createEncryptCipher(PublicKey publicKey) {
        return CryptoUtil.createEncryptCipher(CryptoConstants.RSA, publicKey);
    }

    /**
     * 创建解密器
     *
     * @param privateKey 私钥
     * @return 解密器
     */
    public static Cipher createDecryptCipher(PrivateKey privateKey) {
        return CryptoUtil.createDecryptCipher(CryptoConstants.RSA, privateKey);
    }

    /**
     * 加密
     */
    public static byte[] encrypt(PublicKey publicKey, String secretMessage)
        throws BadPaddingException, IllegalBlockSizeException {
        Cipher encryptCipher = createEncryptCipher(publicKey);
        return CryptoUtil.encrypt(encryptCipher, secretMessage);
    }

    /**
     * 解密
     */
    public static byte[] decrypt(PrivateKey privateKey, String encryptMessage)
        throws BadPaddingException, IllegalBlockSizeException {
        Cipher decryptCipher = createDecryptCipher(privateKey);
        return CryptoUtil.decrypt(decryptCipher, encryptMessage);
    }

    /**
     * 加密
     */
    public static String encryptToString(PublicKey publicKey, String secretMessage)
        throws BadPaddingException, IllegalBlockSizeException {
        byte[] encryptedMessageBytes = encrypt(publicKey, secretMessage);
        return new String(encryptedMessageBytes, StandardCharsets.UTF_8);
    }

    /**
     * 解密
     */
    public static String decryptToString(PrivateKey privateKey, String encryptMessage)
        throws BadPaddingException, IllegalBlockSizeException {
        byte[] decryptMessageBytes = decrypt(privateKey, encryptMessage);
        return new String(decryptMessageBytes, StandardCharsets.UTF_8);
    }

}
