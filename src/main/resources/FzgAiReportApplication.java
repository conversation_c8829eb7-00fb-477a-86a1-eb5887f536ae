package com.fuioupay.bootstrap;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAspectJAutoProxy
@EnableScheduling
@EnableRetry
@SpringBootApplication(scanBasePackages = "com.fuioupay")
public class FzgAiReportApplication {

    public static void main(String[] args) {
        SpringApplication.run(FzgAiReportApplication.class, args);
    }

}
