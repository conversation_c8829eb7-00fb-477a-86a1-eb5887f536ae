<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FZG AI Report - AI模型测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        select, textarea, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }

        textarea {
            height: 100px;
            resize: vertical;
        }

        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .response {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
            min-height: 100px;
            white-space: pre-wrap;
        }

        .loading {
            color: #007bff;
            font-style: italic;
        }

        .error {
            color: #dc3545;
        }

        .success {
            color: #28a745;
        }

        #structured {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        #status {
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🤖 FZG AI Report 测试页面</h1>

    <form id="aiForm">
        <div class="form-group">
            <label for="modelType">模型类型:</label>
            <select id="modelType" name="modelType">
                <option value="DEEPSEEK">DeepSeek深度思考模型</option>
                <option value="DEEPSEEK_V3">DEEPSEEK</option>
                <option value="DOUBAO">豆包</option>
            </select>
        </div>

        <div class="form-group">
            <label for="prompt">用户问题:</label>
            <textarea id="prompt" name="prompt" placeholder="请输入您的问题..." required></textarea>
        </div>

        <div class="form-group">
            <label for="systemPrompt">系统提示 (可选):</label>
            <textarea id="systemPrompt" name="systemPrompt" placeholder="例如：你是一个友好的AI助手"></textarea>
        </div>

        <div class="form-group">
            <label for="lastConversationId">上次对话ID (可选):</label>
            <input type="text" id="lastConversationId" name="lastConversationId" placeholder="用于获取历史对话上下文">
        </div>

        <div class="form-group">
            <button type="button" onclick="callAI(false)">发送请求</button>
            <button type="button" onclick="callAI(true)">流式请求</button>
            <button type="button" onclick="clearResponse()">清空响应</button>
        </div>
    </form>

    <div class="form-group">
        <label>AI响应:</label>
        <div id="response" class="response">等待响应...</div>
    </div>

    <div class="form-group">
        <label>结构化数据:</label>
        <div id="structured" class="response">-</div>
    </div>

    <div class="form-group">
        <label>对话ID:</label>
        <div id="conversationId" class="response">-</div>
    </div>

    <div class="form-group">
        <label>响应状态:</label>
        <div id="status" class="response">-</div>
    </div>
</div>

<script>
    let currentConversationId = '';

    async function callAI(isStream) {
        const form = document.getElementById('aiForm');
        const responseDiv = document.getElementById('response');
        const conversationDiv = document.getElementById('conversationId');
        const buttons = document.querySelectorAll('button');

        // 禁用按钮
        buttons.forEach(btn => btn.disabled = true);

        // 获取表单数据
        const formData = new FormData(form);
        const data = {
            modelType: formData.get('modelType'),
            prompt: formData.get('prompt'),
            systemPrompt: formData.get('systemPrompt') || undefined,
            lastConversationId: formData.get('lastConversationId') || undefined,
            isSteamed: isStream
        };

        try {
            responseDiv.className = 'response loading';
            responseDiv.textContent = '正在请求AI响应...';

            const url = isStream ? '/api/models/stream' : '/api/models';

            if (isStream) {
                await handleStreamResponse(url, data, responseDiv, conversationDiv);
            } else {
                await handleNormalResponse(url, data, responseDiv, conversationDiv);
            }

        } catch (error) {
            responseDiv.className = 'response error';
            responseDiv.textContent = '请求失败: ' + error.message;
        } finally {
            // 重新启用按钮
            buttons.forEach(btn => btn.disabled = false);
        }
    }

    async function handleNormalResponse(url, data, responseDiv, conversationDiv) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        responseDiv.className = 'response success';
        responseDiv.textContent = result.assistant || '无响应内容';

        // 显示结构化数据
        const structuredDiv = document.getElementById('structured');
        if (result.structured) {
            structuredDiv.textContent = typeof result.structured === 'string' ? result.structured : JSON.stringify(result.structured, null, 2);
        } else {
            structuredDiv.textContent = '-';
        }

        // 显示对话ID
        if (result.conversationId) {
            currentConversationId = result.conversationId;
            conversationDiv.textContent = currentConversationId;
        }
    }

    async function handleStreamResponse(url, data, responseDiv, conversationDiv) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        responseDiv.className = 'response success';
        responseDiv.textContent = '';

        const structuredDiv = document.getElementById('structured');
        const statusDiv = document.getElementById('status');
        structuredDiv.textContent = '';
        statusDiv.textContent = '流式响应中...';

        let buffer = '';

        while (true) {
            const {done, value} = await reader.read();

            if (done) break;

            buffer += decoder.decode(value, {stream: true});
            const lines = buffer.split('\n');

            // 保留最后一行（可能不完整）
            buffer = lines.pop() || '';

            for (const line of lines) {
                if (line.trim().startsWith('data:')) {
                    try {
                        const jsonStr = line.substring(line.indexOf('data:') + 5).trim();
                        if (jsonStr === '[DONE]') {
                            statusDiv.textContent = '响应完成';
                            break;
                        }

                        const jsonData = JSON.parse(jsonStr);

                        // 处理assistant字段（AI回复内容）
                        if (jsonData.assistant && jsonData.assistant !== '[DONE]') {
                            responseDiv.textContent += jsonData.assistant;
                        }

                        // 处理structured字段（结构化数据）
                        if (jsonData.structured) {
                            structuredDiv.textContent += jsonData.structured;
                        }

                        // 处理conversationId
                        if (jsonData.conversationId && !currentConversationId) {
                            currentConversationId = jsonData.conversationId;
                            conversationDiv.textContent = currentConversationId;
                        }

                        // 处理isFinished状态
                        if (jsonData.isFinished !== undefined) {
                            statusDiv.textContent = jsonData.isFinished ? '响应完成' : '流式响应中...';
                        }

                    } catch (e) {
                        console.warn('解析流式数据失败:', e, '原始数据:', line);
                    }
                }
            }
        }

        // 处理剩余的buffer
        if (buffer.trim()) {
            try {
                if (buffer.trim().startsWith('data:')) {
                    const jsonStr = buffer.substring(buffer.indexOf('data:') + 5).trim();
                    if (jsonStr !== '[DONE]') {
                        const jsonData = JSON.parse(jsonStr);
                        if (jsonData.assistant && jsonData.assistant !== '[DONE]') {
                            responseDiv.textContent += jsonData.assistant;
                        }
                        if (jsonData.structured) {
                            structuredDiv.textContent += jsonData.structured;
                        }
                    }
                }
            } catch (e) {
                console.warn('解析最后的buffer失败:', e);
            }
        }
    }

    function clearResponse() {
        document.getElementById('response').textContent = '等待响应...';
        document.getElementById('response').className = 'response';
        document.getElementById('structured').textContent = '-';
        document.getElementById('conversationId').textContent = '-';
        document.getElementById('status').textContent = '-';
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 设置默认问题
        document.getElementById('prompt').value = '你好，请介绍一下你自己';
        document.getElementById('systemPrompt').value = '你是一个友好的AI助手';
    });
</script>
</body>
</html>
