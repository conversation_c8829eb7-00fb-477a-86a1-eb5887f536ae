package com.fuiou.fzg.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fuiou.fzg.constant.SystemPromptConstant;
import com.fuiou.fzg.model.ConversationHistory;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 对话历史管理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConversationHistoryService {

    // redis key
    private static final String CONVERSATION_KEY_PREFIX = "conversation:";
    // redis消息过期时间，30天
    private static final long CONVERSATION_EXPIRE_DAYS = 30;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存对话历史
     *
     * @param conversationId         对话ID
     * @param userMessage            用户消息
     * @param assistantMessage       AI回复
     * @param previousConversationId 上一次对话ID
     * @param modelType              模型类型
     */
    public void saveConversationHistory(String conversationId, String userMessage, String assistantMessage,
            String previousConversationId, String modelType)
    {
        try {
            ConversationHistory history = new ConversationHistory();
            history.setConversationId(conversationId);
            history.setUserMessage(userMessage);
            history.setAssistantMessage(assistantMessage);
            history.setPreviousConversationId(previousConversationId);
            history.setModelType(modelType);
            history.setCreateTime(LocalDateTime.now());

            String key = CONVERSATION_KEY_PREFIX + conversationId;
            redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(history), CONVERSATION_EXPIRE_DAYS, TimeUnit.DAYS);

            log.debug("保存对话历史成功: {}", conversationId);
        } catch (Exception e) {
            log.error("保存对话历史失败: {}", conversationId, e);
        }
    }

    /**
     * 获取对话历史链
     *
     * @param conversationId 对话ID
     * @return 历史消息列表（按时间顺序）
     */
    public List<ChatMessage> getConversationHistory(String conversationId, String systemPrompt, String prompt) {
        List<ChatMessage> messages = new ArrayList<>();
        // 添加系统提示词
        messages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM)
                .content(SystemPromptConstant.getSystemPrompt(systemPrompt)).build());
        try {
            // 添加历史对话
            if (StrUtil.isNotBlank(conversationId)) {
                for (ConversationHistory history : getHistoryChain(conversationId)) {
                    if (StrUtil.isNotBlank(history.getUserMessage())) {
                        messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(history.getUserMessage())
                                .build());
                    }
                    if (StrUtil.isNotBlank(history.getAssistantMessage())) {
                        messages.add(ChatMessage.builder().role(ChatMessageRole.ASSISTANT)
                                .content(history.getAssistantMessage()).build());
                    }
                }
            }
            // 添加当前用户问题
            messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(prompt).build());
            return messages;
        } catch (Exception e) {
            log.error("获取对话历史失败: {}", conversationId, e);
        }
        return messages;
    }

    /**
     * 递归获取完整的对话历史链
     *
     * @param conversationId 对话ID
     * @return 对话历史链
     */
    private List<ConversationHistory> getHistoryChain(String conversationId) {
        List<ConversationHistory> historyChain = new ArrayList<>();
        String currentId = conversationId;
        int maxDepth = 50; // 防止无限递归
        int depth = 0;

        while (StrUtil.isNotBlank(currentId) && depth < maxDepth) {
            ConversationHistory history = getConversationById(currentId);
            if (history == null) {
                break;
            }
            historyChain.add(0, history); // 插入到列表开头，保持时间顺序
            currentId = history.getPreviousConversationId();
            depth++;
        }
        // 这里集合最后要倒序返回，最新的在最底下
        Collections.reverse(historyChain);
        return historyChain;
    }

    /**
     * 根据ID获取对话历史
     *
     * @param conversationId 对话ID
     * @return 对话历史
     */
    private ConversationHistory getConversationById(String conversationId) {
        try {
            String key = CONVERSATION_KEY_PREFIX + conversationId;
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                return JSONUtil.toBean(value.toString(), ConversationHistory.class);
            }
        } catch (Exception e) {
            log.error("获取对话记录失败: {}", conversationId, e);
        }

        return null;
    }

}
