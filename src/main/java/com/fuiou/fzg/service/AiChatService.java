package com.fuiou.fzg.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONUtil;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import com.fuiou.fzg.param.AiReportParam;
import com.fuiou.fzg.vo.AiReportVo;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AiChatService {

    @Value("${ai.api-key}")
    private String apiKey;
    @Value("${ai.timeout:120}")
    private int timeout;
    @Value("${ai.connect-timeout:60}")
    private int connectTimeout;
    @Value("${ai.retry-times:3}")
    private int retryTimes;

    @Resource
    private ConversationHistoryService conversationHistoryService;
    private ArkService arkService;

    @PostConstruct
    public void init() {
        // 并发连接池
        ConnectionPool connectionPool = new ConnectionPool(100, 1, TimeUnit.SECONDS);
        Dispatcher dispatcher = new Dispatcher();
        this.arkService = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool).apiKey(apiKey)
                .timeout(Duration.ofSeconds(timeout)).connectTimeout(Duration.ofSeconds(connectTimeout)).retryTimes(
                        retryTimes).build();
        log.info("火山引擎AI服务初始化完成");
    }

    /**
     * 非流式调用AI模型
     */
    public Mono<AiReportVo> chatCompletion(AiReportParam param) {
        return Mono.fromCallable(() -> {
            try {
                String response = arkService.createChatCompletion(getChatCompletionRequest(param)).getChoices().get(0)
                        .getMessage().getContent()
                        .toString();
                String conversationId = IdUtil.fastSimpleUUID();
                saveConversationHistory(param, conversationId, response);
                return buildResponse(response, conversationId);
            } catch (Exception e) {
                log.error("AI调用失败", e);
                throw new RuntimeException("AI调用失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 流式调用AI模型
     */
    public Flux<AiReportVo> chatCompletionStream(AiReportParam param) {
        return Flux.create(sink -> {
            try {
                StringBuilder responseBuilder = new StringBuilder();
                String conversationId = IdUtil.fastSimpleUUID();

                arkService.streamChatCompletion(getChatCompletionRequest(param))
                        .doOnError(Throwable::printStackTrace)
                        .subscribe(choice -> {
                            if (!choice.getChoices().isEmpty()) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                if (StrUtil.isNotBlank(content)) {
                                    responseBuilder.append(content);
                                    AiReportVo vo = new AiReportVo();
                                    vo.setAssistant(content);
                                    vo.setStructured("");
                                    vo.setConversationId(conversationId);
                                    sink.next(vo);
                                }
                            }
                        }, sink::error, () -> {
                            // 流结束时保存完整对话历史
                            saveConversationHistory(param, conversationId, responseBuilder.toString());
                            // 发送结束标记
                            AiReportVo doneVo = new AiReportVo();
                            doneVo.setAssistant("[DONE]");
                            doneVo.setConversationId(conversationId);
                            sink.next(doneVo);
                            sink.complete();
                        });
            } catch (Exception e) {
                log.error("AI流式调用初始化失败", e);
                sink.error(new RuntimeException("AI流式调用初始化失败: " + e.getMessage(), e));
            }
        });
    }

    private ChatCompletionRequest getChatCompletionRequest(AiReportParam param) {
        List<ChatMessage> messages = getHistoryMessages(param);
        return ChatCompletionRequest.builder().model(param.getModelType().getModelId()).messages(messages).build();
    }

    /**
     * 获取历史对话消息
     */
    private List<ChatMessage> getHistoryMessages(AiReportParam param) {
        if (StrUtil.isNotBlank(param.getLastConversationId())) {
            return conversationHistoryService.getConversationHistory(param.getLastConversationId(), param.getPrompt(),
                    param.getSystemPrompt());
        }
        return new ArrayList<>();
    }

    /**
     * 保存对话历史
     */
    private void saveConversationHistory(AiReportParam param, String conversationId, String response) {
        try {
            conversationHistoryService.saveConversationHistory(conversationId, param.getPrompt(), response,
                    param.getLastConversationId(), param.getModelType().getModelId());
        } catch (Exception e) {
            log.error("保存对话历史失败", e);
        }
    }

    /**
     * 构建响应对象
     * 使用hutool工具处理
     */
    private AiReportVo buildResponse(String response, String conversationId) {
        AiReportVo vo = new AiReportVo();
        vo.setAssistant(StrUtil.trim(response));
        vo.setConversationId(conversationId);
        // todo 这里要改下，回头要在提示词中增加结构化数据解析
        try {
            if (StrUtil.isNotBlank(response)) {
                String trimmed = StrUtil.trim(response);
                if ((trimmed.startsWith("{") && trimmed.endsWith("}")) || (trimmed.startsWith("[") && trimmed.endsWith(
                        "]")))
                {
                    vo.setStructured(JSONUtil.formatJsonStr(trimmed));
                }
            }
        } catch (Exception e) {
            log.debug("响应不是有效的JSON格式，跳过结构化处理");
        }
        return vo;
    }
}
