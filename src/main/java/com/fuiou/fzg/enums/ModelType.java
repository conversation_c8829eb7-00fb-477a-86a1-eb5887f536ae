package com.fuiou.fzg.enums;

/**
 * AI模型类型枚举
 *
 * <AUTHOR>
 */
public enum ModelType {
    
    /**
     * DeepSeek模型
     */
    DEEPSEEK("deepseek-r1-250528", "DeepSeek模型"),
    
    /**
     * 豆包模型
     */
    DOUBAO("doubao-seed-1-6-250615", "豆包模型"),
    
    /**
     * GPT模型
     */
    GPT("gpt-3.5-turbo", "GPT模型");
    
    private final String modelId;
    private final String description;
    
    ModelType(String modelId, String description) {
        this.modelId = modelId;
        this.description = description;
    }
    
    public String getModelId() {
        return modelId;
    }
    
    public String getDescription() {
        return description;
    }
}
