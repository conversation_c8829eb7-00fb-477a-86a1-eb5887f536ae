package com.fuiou.fzg.param;

import com.fuiou.fzg.enums.ModelType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AiReportParam implements Serializable {

    private static final long serialVersionUID = 1L;
    // 模型类型
    @NotNull(message = "模型类型不能为空")
    private ModelType modelType = ModelType.DEEPSEEK_V3;
    // 是否流式输出，默认非流式
    private Boolean isSteamed = false;
    // 用户问题
    @NotBlank(message = "用户问题不能为空")
    private String prompt;
    // 用户提供的参考数据
    private String systemPrompt;
    // 上次对话ID，用于获取历史对话
    private String lastConversationId;

}
