package com.fuiou.fzg.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 流式XML解析器
 * 实时解析XML流，只返回标签内的内容，不返回标签本身
 * 
 * <AUTHOR>
 */
@Slf4j
public class StreamXmlParser {
    
    private StringBuilder buffer = new StringBuilder();
    private StringBuilder contentBuffer = new StringBuilder();
    private StringBuilder structBuffer = new StringBuilder();
    
    // 解析状态
    private boolean insideContentTag = false;
    private boolean insideStructTag = false;
    private boolean contentCompleted = false;
    private boolean structCompleted = false;
    
    // 已发送的内容长度
    private int sentContentLength = 0;
    private int sentStructLength = 0;
    
    /**
     * 处理新接收的数据块
     * @param chunk 新数据块
     * @return 新提取的content内容，如果没有则返回空字符串
     */
    public String processChunk(String chunk) {
        if (StrUtil.isBlank(chunk)) {
            return "";
        }
        
        buffer.append(chunk);
        return parseCurrentBuffer();
    }
    
    /**
     * 解析当前缓冲区内容
     */
    private String parseCurrentBuffer() {
        String fullContent = buffer.toString();
        String newContent = "";
        
        // 逐字符解析
        for (int i = 0; i < fullContent.length(); i++) {
            char c = fullContent.charAt(i);
            
            // 检查是否进入content标签
            if (!insideContentTag && !contentCompleted && isAtPosition(fullContent, i, "<content>")) {
                insideContentTag = true;
                i += "<content>".length() - 1; // 跳过标签
                log.debug("进入content标签");
                continue;
            }
            
            // 检查是否离开content标签
            if (insideContentTag && !contentCompleted && isAtPosition(fullContent, i, "</content>")) {
                insideContentTag = false;
                contentCompleted = true;
                i += "</content>".length() - 1; // 跳过标签
                log.debug("离开content标签");
                continue;
            }
            
            // 检查是否进入struct标签
            if (!insideStructTag && !structCompleted && isAtPosition(fullContent, i, "<struct>")) {
                insideStructTag = true;
                i += "<struct>".length() - 1; // 跳过标签
                log.debug("进入struct标签");
                continue;
            }
            
            // 检查是否离开struct标签
            if (insideStructTag && !structCompleted && isAtPosition(fullContent, i, "</struct>")) {
                insideStructTag = false;
                structCompleted = true;
                i += "</struct>".length() - 1; // 跳过标签
                log.debug("离开struct标签");
                continue;
            }
            
            // 收集content内容
            if (insideContentTag) {
                contentBuffer.append(c);
            }
            
            // 收集struct内容
            if (insideStructTag) {
                structBuffer.append(c);
            }
        }
        
        // 返回新的content内容
        String currentContent = contentBuffer.toString();
        if (currentContent.length() > sentContentLength) {
            newContent = currentContent.substring(sentContentLength);
            sentContentLength = currentContent.length();
        }
        
        return newContent;
    }
    
    /**
     * 检查指定位置是否匹配目标字符串
     */
    private boolean isAtPosition(String text, int position, String target) {
        if (position + target.length() > text.length()) {
            return false;
        }
        return text.substring(position, position + target.length()).equals(target);
    }
    
    /**
     * 获取完整的content内容
     */
    public String getCompleteContent() {
        return contentBuffer.toString();
    }
    
    /**
     * 获取完整的struct内容
     */
    public String getCompleteStruct() {
        return structBuffer.toString();
    }
    
    /**
     * 检查content是否解析完成
     */
    public boolean isContentCompleted() {
        return contentCompleted;
    }
    
    /**
     * 检查struct是否解析完成
     */
    public boolean isStructCompleted() {
        return structCompleted;
    }
    
    /**
     * 重置解析器状态
     */
    public void reset() {
        buffer.setLength(0);
        contentBuffer.setLength(0);
        structBuffer.setLength(0);
        insideContentTag = false;
        insideStructTag = false;
        contentCompleted = false;
        structCompleted = false;
        sentContentLength = 0;
    }
    
    /**
     * 获取解析状态信息（用于调试）
     */
    public String getStatus() {
        return String.format("Content: %s, Struct: %s, InContent: %s, InStruct: %s", 
                contentCompleted, structCompleted, insideContentTag, insideStructTag);
    }
}
