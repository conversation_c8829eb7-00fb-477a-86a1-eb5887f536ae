package com.fuiou.fzg.controller;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.fuiou.fzg.param.AiReportParam;
import com.fuiou.fzg.service.AiChatService;
import com.fuiou.fzg.vo.AiReportVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * AI模型调用对外接口
 * 提供统一的AI模型调用入口
 *
 * <AUTHOR>
 */
@Valid
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/models")
public class FzgAiModelController {

    @Resource
    private AiChatService aiChatService;

    /**
     * 非流式调用AI模型
     */
    @PostMapping
    @ResponseBody
    public Mono<ResponseEntity<AiReportVo>> invokeModel(@Validated @RequestBody AiReportParam request,
            HttpServletRequest httpRequest)
    {
        String clientIp = ServletUtil.getClientIP(httpRequest);
        log.info("接收到AI模型调用请求: {} (客户端IP: {})", JSONUtil.toJsonStr(request), clientIp);

        return aiChatService.chatCompletion(request).map(ResponseEntity::ok).onErrorReturn(
                ResponseEntity.internalServerError().build());
    }

    /**
     * 流式调用AI模型
     */
    @PostMapping(value = "/stream",
                 produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<AiReportVo>> streamModel(@Validated @RequestBody AiReportParam request,
            HttpServletRequest httpRequest)
    {
        String clientIp = ServletUtil.getClientIP(httpRequest);
        log.info("接收到AI模型流式调用请求: {} (客户端IP: {})", JSONUtil.toJsonStr(request), clientIp);

        return aiChatService.chatCompletionStream(request).map(data -> ServerSentEvent.builder(data).build());
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<String>> health() {
        return Mono.just(ResponseEntity.ok("AI模型服务运行正常"));
    }
}
