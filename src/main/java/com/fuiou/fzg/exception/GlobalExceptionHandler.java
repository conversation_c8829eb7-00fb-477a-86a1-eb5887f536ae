package com.fuiou.fzg.exception;

import com.fuiou.fzg.vo.AiReportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<AiReportVo> handleValidationException(Exception e) {
        log.error("参数验证失败", e);
        
        AiReportVo errorVo = new AiReportVo();
        errorVo.setAssistant("参数验证失败: " + e.getMessage());
        
        return ResponseEntity.badRequest().body(errorVo);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<AiReportVo> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("非法参数", e);
        
        AiReportVo errorVo = new AiReportVo();
        errorVo.setAssistant("参数错误: " + e.getMessage());
        
        return ResponseEntity.badRequest().body(errorVo);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<AiReportVo> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        
        AiReportVo errorVo = new AiReportVo();
        errorVo.setAssistant("服务异常: " + e.getMessage());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorVo);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<AiReportVo> handleException(Exception e) {
        log.error("未知异常", e);
        
        AiReportVo errorVo = new AiReportVo();
        errorVo.setAssistant("系统异常，请稍后重试");
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorVo);
    }
}
