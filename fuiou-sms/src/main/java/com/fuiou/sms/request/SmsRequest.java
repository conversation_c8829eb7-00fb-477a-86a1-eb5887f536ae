package com.fuiou.sms.request;

import com.fuiou.common.utils.RandomUtil;
import com.fuiou.sms.config.SmsProperties;
import com.fuiou.sms.util.MD5;

import java.util.Objects;

/**
 * 短信请求对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class SmsRequest {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 内容
     */
    private String content;

    /**
     * 随机字符串
     */
    private String randomParam;

    /**
     * MD5校验字符串
     */
    private String md5Str;

    /**
     * 各应用系统Id
     */
    private String platId;

    /**
     * 各应用系统对应其内部操作员的loginId，可不填
     */
    private String loginId;

    /**
     * 业务代码，用于区分业务
     */
    private String busiCd;

    /**
     * 发送频率限制，每手机号每业务间隔时间，默认10秒
     */
    private Integer frequency = 10;

    public SmsRequest(String mobile, String content, String busiCd, SmsProperties smsProperties) {
        this(mobile, content, smsProperties.getPlatId(), busiCd, smsProperties.getFrequency(), smsProperties.getSalt(), null);
    }

    public SmsRequest(String mobile, String content, String platId, String busiCd, Integer frequency, String salt) {
        this(mobile, content, platId, busiCd, frequency, salt, null);
    }

    public SmsRequest(String mobile, String content, String platId, String busiCd, Integer frequency, String salt, String loginId) {
        this.mobile = mobile;
        this.content = content;
        this.platId = platId;
        this.loginId = loginId;
        this.busiCd = busiCd;
        this.frequency = frequency;
        String randomParam = System.currentTimeMillis() + RandomUtil.randomString(6);
        this.randomParam = randomParam;
        this.md5Str = MD5.MD5Encode(mobile + platId  +busiCd + randomParam + salt);
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRandomParam() {
        return randomParam;
    }

    public void setRandomParam(String randomParam) {
        this.randomParam = randomParam;
    }

    public String getMd5Str() {
        return md5Str;
    }

    public void setMd5Str(String md5Str) {
        this.md5Str = md5Str;
    }

    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getBusiCd() {
        return busiCd;
    }

    public void setBusiCd(String busiCd) {
        this.busiCd = busiCd;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SmsRequest that = (SmsRequest) o;
        return Objects.equals(mobile, that.mobile) && Objects.equals(content, that.content) && Objects.equals(randomParam, that.randomParam) && Objects.equals(md5Str, that.md5Str) && Objects.equals(platId, that.platId) && Objects.equals(loginId, that.loginId) && Objects.equals(busiCd, that.busiCd) && Objects.equals(frequency, that.frequency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mobile, content, randomParam, md5Str, platId, loginId, busiCd, frequency);
    }

    @Override
    public String toString() {
        return "SmsRequest{" +
            "mobile='" + mobile + '\'' +
            ", content='" + content + '\'' +
            ", randomParam='" + randomParam + '\'' +
            ", md5Str='" + md5Str + '\'' +
            ", platId='" + platId + '\'' +
            ", loginId='" + loginId + '\'' +
            ", busiCd='" + busiCd + '\'' +
            ", frequency=" + frequency +
            '}';
    }

}
