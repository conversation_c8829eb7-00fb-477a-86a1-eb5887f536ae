<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.0.xsd"
>

	<!-- 扫描路径下@Service，由spring的context父容器进行初始化以保证事务的增强处理 -->
	<context:component-scan base-package="com.fuiou.dipsMq"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Component" />
	</context:component-scan>

	<import resource="applicationContext-mq.xml"/>
	<import resource="redis.xml"/>
	<import resource="dubbo.xml"/>



	<!--	wosdb begin-->
	<bean id="dataSourceWOSDB" class="com.fuiou.support.FuiouPooledDataSource"
		  destroy-method="close">
		<property name="platId" value="${platId}" />
		<property name="databaseName" value="wosdb" />
	</bean>
	<bean id="sqlSessionFactoryWOSDB" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="mapperLocations">
			<list>
				<value>classpath:/com/fuiou/dipsMq/persist/wosdb/mapper/*.xml</value>
			</list>
		</property>
		<property name="dataSource" ref="dataSourceWOSDB" />
		<property name="plugins">
			<list>
				<bean
						class="com.github.miemiedev.mybatis.paginator.OffsetLimitInterceptor">
					<property name="dialectClass"
							  value="com.github.miemiedev.mybatis.paginator.dialect.MySQLDialect"></property>
				</bean>
			</list>
		</property>
	</bean>
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.fuiou.dipsMq.persist.wosdb.mapper" />
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryWOSDB" />
	</bean>
	<!--wosdb end-->


</beans>