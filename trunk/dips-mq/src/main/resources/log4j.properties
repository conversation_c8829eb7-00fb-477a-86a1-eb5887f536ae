
log4j.rootLogger = INFO,fileout,console

log4j.logger.mpayLogger=DEBUG,fileout,console

log4j.appender.fileout=org.apache.log4j.DailyRollingFileAppender
log4j.appender.fileout.Threshold=DEBUG 
log4j.appender.fileout.File=${log4j.appender.fileout.File}
log4j.appender.fileout.Append=true
log4j.appender.fileout.DatePattern='.'yyyyMMdd
log4j.appender.fileout.layout=org.apache.log4j.PatternLayout 
log4j.appender.fileout.layout.ConversionPattern=[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%5p] [uuid=%X{uuid}] %m%n
log4j.appender.file.encoding=UTF-8

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%5p] [uuid=%X{uuid}] %m%n
log4j.appender.console.Threshold=DEBUG 


log4j.logger.org.springframework.jdbc=DEBUG
log4j.logger.org.springframework=INFO
log4j.logger.org.apache.commons=INFO
log4j.logger.net.sf.ehcache=ERROR
log4j.logger.org.apache.ibatis=DEBUG
log4j.logger.org.mybatis.spring=DEBUG
log4j.logger.com.fuiou= DEBUG


