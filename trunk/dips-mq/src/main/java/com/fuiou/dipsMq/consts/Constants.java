package com.fuiou.dipsMq.consts;

import com.fuiou.dipsMq.utils.ConfigReader;
import com.fuiou.mqtt.framework.utils.LogWriter;

import java.net.InetAddress;
import java.util.UUID;


public class Constants {


 public static final String ZXT_ORDER_NOTIFY_URL=ConfigReader.getDefaultConfig("zxt_order_notify_url");

 /***
  * @Description 判断当前是否测试环境模式
  * @Date 2020/8/27 10:21
  **/
 public static final boolean IS_SIT_MODE = "sit".equalsIgnoreCase(ConfigReader.getDefaultConfig("dev_mode"));

 public static Boolean STARTEND = false;


 public static final String ZXT_INS_CD = ConfigReader.getDefaultConfig("zxt_ins_cd");//装修通机构号
 public static final String ZXT_ORDER_PREFIX = "ZXT";//装修通订单号前缀


 public static   String local_ip;

 static {
  try {
   local_ip = InetAddress.getLocalHost().getHostAddress();
  } catch (Exception e) {
   LogWriter.error("获取本机ip发生异常",e);
   local_ip= UUID.randomUUID().toString().replaceAll("-","");
  }
 }

}
