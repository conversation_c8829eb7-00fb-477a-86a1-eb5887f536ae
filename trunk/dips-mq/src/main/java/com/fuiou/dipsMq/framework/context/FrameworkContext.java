package com.fuiou.dipsMq.framework.context;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 加载spring上下文
 * 
 * <AUTHOR>
 * 
 */
@Component("frameworkContext")
public class FrameworkContext implements ApplicationContextAware {
	
	public FrameworkContext() {
	}

	public void setApplicationContext(ApplicationContext arg0) throws BeansException {
		applicationContext = arg0;
	}

	public static ApplicationContext getApplicationContext() {
		return applicationContext;
	}
	
	

	private static ApplicationContext applicationContext;
}
