package com.fuiou.dipsMq.consts;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class OrderPayConsts {

	public static final String[] SPECIAL_ORDERPAY = {"WXZL","WECHAT","ALIPAY","<PERSON><PERSON><PERSON>","<PERSON>ES<PERSON>A<PERSON>","<PERSON>IONPAY","J<PERSON><PERSON>","FWC","WX<PERSON>","WXPF","ALPA","ALPF","YQK","YFK","APPLEPAY","INSTAL","LETPAY",OrderPayConsts.UNIONPAY_MPAY,OrderTypeConst.DIGICCY,InstalTypeEnum.UNION_INSTAL_PY68.getCode(),InstalTypeEnum.UNION_INSTAL_PY69.getCode(), InstalTypeEnum.ALIPAY_INSTAL_PY70.getCode(),InstalTypeEnum.ALIPAY_INSTAL_PY71.getCode(),OrderTypeConst.CARD_BRUSH_CONSUME, OrderTypeConst.CARD_BRUSH_REFUND, OrderTypeConst.CARD_BRUSH_CANCEL, OrderTypeConst.JDBT, OrderPayConsts.NUCCPAY,OrderTypeConst.WALLETPAY};
	public static final String WXZL = "WXZL";
	public static final String[] WECHAT = {"WECHAT","JSAPI","LETPAY"};
	public static final String[] ALIPAY = {"ALIPAY","FWC"};
	public static final String ALKB = "ALKB";
	public static final String ALIPAY_TYPE = "ALIPAY";
	public static final String BESTPAY = "BESTPAY";
	public static final String UNIONPAY = "UNIONPAY";
	public static final String JSAPI = "JSAPI";
	public static final String FWC = "FWC";
	public static final String LETPAY = "LETPAY";
	public static final String WXPA = "WXPA";
	public static final String WXPF = "WXPF";
	public static final String ALPA = "ALPA";
	public static final String ALPF = "ALPF";
	public static final String YQK = "YQK";
	public static final String YFK = "YFK";
	public static final String CZK = "CZK";
	public static final String APPLEPAY = "APPLEPAY";
	public static final String [] WMP_SUPPORT_ORDER_TYPE = {"WECHAT","JSAPI","ALIPAY","FWC","ALKB","BESTPAY","UNIONPAY",YFK,YQK, OrderTypeConst.CashOrderType.CASH.getValue(),"INSTAL", OrderTypeConst.CARD_BRUSH_CONSUME, OrderTypeConst.CARD_BRUSH_REFUND, OrderTypeConst.CARD_BRUSH_CANCEL, OrderTypeConst.JDBT, OrderPayConsts.NUCCPAY, OrderTypeConst.DIGICCY, OrderTypeConst.DYPAY};

	/***
	 * <AUTHOR> 
	 * @Description   银联小程序订单类型
	 * @Date 2020/7/16 10:48 
	**/
	public static final String UNIONPAY_MPAY = "MPAY";

	/**
	 * 银联卡分期支付(走rut的模式)
	 */
	public static final String INSTAL = "INSTAL";





	/**
	 * 花呗分期支付
	 */
	public static final String ANT = "ANT";
	
	/**
	 * 方法注释： 是否是特殊支付类型
	 * 修改内容： 新增
	 * @return
	 */
	public static boolean isSpeOrderPay(String orderPay) {
		return Arrays.asList(SPECIAL_ORDERPAY).contains(orderPay);
    }
	
//	public static void getTypeByOrderType(String orderType,String transState,RefreshData data){







	//花呗分期
	public static String huabeiPayTypeDesc(String addOrderType,String payType,String payTypeDesc){
		if(!"ANT".equals(addOrderType)){
			return payTypeDesc;
		}
		if("02".equals(payType)){
			payTypeDesc="支付宝花呗";
		}
		if("19".equals(payType)){
			payTypeDesc="支付宝退款";
		}
		if("16".equals(payType)){
			payTypeDesc="口碑花呗";
		}
		if("25".equals(payType)){
			payTypeDesc="口碑退款";
		}
		return payTypeDesc;

	}

	//转换订单类型
	public static String getOrderType(String orderType){
		if(FWC.equals(orderType) || ALKB.equals(orderType) ){
			orderType = ALIPAY_TYPE;
		}
		if("LETPAY".equals(orderType) || JSAPI.equals(orderType) || WXZL.equals(orderType)){
			orderType= "WECHAT";
		}
		if(UNIONPAY_MPAY.equals(orderType)){
			orderType = UNIONPAY ;
		}
		return orderType;
	}


	/**
	 * @Description:  被扫支付宝分期订单类型，包括花呗分期和信用卡分期
	 * @Author: 程军
	 * @Date: 2023/2/14 14:10
	 */
	public static final Set<String> ALIPAY_INSTALL_TPAY_ORDER_TYPES =new HashSet<>(Arrays.asList( ALIPAY));

	/**
	 * @Description:  台卡支付宝分期订单类型，包括花呗分期和信用卡分期
	 * @Author: 程军
	 * @Date: 2023/2/14 14:10
	 */
	public static final Set<String> ALIPAY_INSTALL_DECCA_ORDER_TYPES =new HashSet<>(Arrays.asList( FWC));


	/**
	 * @Description:  支付宝分期订单类型，包括花呗分期和信用卡分期
	 * @Author: 程军
	 * @Date: 2023/2/14 14:10
	 */
	public static final Set<String> ALIPAY_INSTALL_ALL_ORDER_TYPES =new HashSet<>();
	static {
		ALIPAY_INSTALL_ALL_ORDER_TYPES.addAll(ALIPAY_INSTALL_TPAY_ORDER_TYPES);
		ALIPAY_INSTALL_ALL_ORDER_TYPES.addAll(ALIPAY_INSTALL_DECCA_ORDER_TYPES);
	}

	/**
	 * 网联二维码
	 */
	public static final String NUCCPAY = "NUCCPAY";
}
