package com.fuiou.dipsMq.utils;

import com.fuiou.dipsMq.consts.Constants;
import com.fuiou.mqtt.framework.utils.LogWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


@Component
public class RedisHandler<T, V> {

	@SuppressWarnings("rawtypes")
    @Autowired
	private RedisTemplate write1RedisTemplate;
	
    @SuppressWarnings("rawtypes")
    @Autowired
	private RedisTemplate write2RedisTemplate;

    @SuppressWarnings("rawtypes")
    @Autowired
	private RedisTemplate readRedisTemplate;
	
	
	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * 
	 * @param key
	 *            缓存的键值
	 * @param value
	 *            缓存的值
	 * @return 缓存的对象
	 */
	@SuppressWarnings({"unchecked" })
	public void setCacheObject(String key, T value) {
		try{
			write1RedisTemplate.opsForValue().set(key, value);
		}catch(Exception e){
			LogWriter.error(this,"异常",e);
		}
		if( !Constants.IS_SIT_MODE) {
			try{
				write2RedisTemplate.opsForValue().set(key, value);
			}catch(Exception e){
							LogWriter.error(this,"异常",e);
			}
		}
	}



	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 *
	 * @param key
	 *            缓存的键值
	 * @param value
	 *            缓存的值
	 * @return 缓存的对象
	 */
	@SuppressWarnings({"unchecked" })
	public void setCacheObjectTimeOut(String key, T value,long timeout) {
		try{
			write1RedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
		}catch(Exception e){
						LogWriter.error(this,"异常",e);
		}
		if( !Constants.IS_SIT_MODE) {
			try{
				write2RedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
			}catch(Exception e){
							LogWriter.error(this,"异常",e);
			}
		}
	}


	
	@SuppressWarnings("unchecked")
	public void leftPush(String key, T value) {
		try{
			write1RedisTemplate.opsForList().leftPush(key, value);
		}catch(Exception e){
						LogWriter.error(this,"异常",e);
		}
		if( !Constants.IS_SIT_MODE) {
			try{
				write2RedisTemplate.opsForList().leftPush(key, value);
			}catch(Exception e){
							LogWriter.error(this,"异常",e);
			}
		}
	}
	
	@SuppressWarnings("unchecked")
	public void remove(String key, T value) {
		try{
			write1RedisTemplate.opsForList().remove(key, 0,value);
		}catch(Exception e){
						LogWriter.error(this,"异常",e);
		}
		if( !Constants.IS_SIT_MODE) {
			try{
				write2RedisTemplate.opsForList().remove(key, 0,value);
			}catch(Exception e){
							LogWriter.error(this,"异常",e);
			}
		}
	}
	
	
	/**
	 * 获得缓存的基本对象。
	 * 
	 * @param key
	 *            缓存键值
	 * @param operation
	 * @return 缓存键值对应的数据
	 */
	@SuppressWarnings({ "hiding", "unchecked" })
	public <T> T getCacheObject(String key) {
		ValueOperations<String, T> operation = readRedisTemplate.opsForValue();
		return operation.get(key);
	}
	
	@SuppressWarnings({ "hiding", "unchecked" })
	public <T> T getCacheObjectList(String key) {
		ListOperations<String, V> operation = readRedisTemplate.opsForList();
		return (T) operation.range(key, 0, -1);		
	}
	
	
	@SuppressWarnings("unchecked")
	public void deleteCacheObject(String key) {
		try{
			write1RedisTemplate.delete(key);
		}catch(Exception e){
						LogWriter.error(this,"异常",e);
		}
		if( !Constants.IS_SIT_MODE) {
			try{
				write2RedisTemplate.delete(key);
			}catch(Exception e){
							LogWriter.error(this,"异常",e);
			}
		}
	}
	

	@SuppressWarnings("rawtypes")
	public void setWrite1RedisTemplate(RedisTemplate write1RedisTemplate) {
		this.write1RedisTemplate = write1RedisTemplate;
	}

	@SuppressWarnings("rawtypes")
	public void setWrite2RedisTemplate(RedisTemplate write2RedisTemplate) {
		this.write2RedisTemplate = write2RedisTemplate;
	}

	@SuppressWarnings("rawtypes")
	public void setReadRedisTemplate(RedisTemplate readRedisTemplate) {
		this.readRedisTemplate = readRedisTemplate;
	}
	
}
