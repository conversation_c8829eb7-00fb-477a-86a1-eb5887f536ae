package com.fuiou.dipsMq.consts;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dipsMq.utils.StringUtil;
import com.fuiou.mqtt.framework.utils.LogWriter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description //交易类型枚举
 * @Date 2020-04-09 18:00:14
 **/
public enum OrderBusiCdEnum {
    /***
     * <AUTHOR>
     * @Description 微信收款
     * @Date 2020/4/9 21:29
     **/
    WECHAT("TX09", "微信收款", Arrays.asList(OrderPayConsts.WECHAT), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 微信退款
     * @Date 2020/4/9 21:29
     **/
    WECHAT_REFUND("TX51", "微信退款", Arrays.asList(OrderPayConsts.WECHAT), OrderTypeConst.TradeType.NEGATIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝收款
     * @Date 2020/4/9 21:29
     **/
    ALIPAY("TX15", "支付宝收款", Arrays.asList(OrderPayConsts.ALIPAY), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝退款
     * @Date 2020/4/9 21:29
     **/
    ALIPAY_REFUND("TX51", "支付宝退款", Arrays.asList(OrderPayConsts.ALIPAY), OrderTypeConst.TradeType.NEGATIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 预付卡收款
     * @Date 2020/4/9 21:29
     **/
    YFK("Y104", "预付卡收款", Arrays.asList(OrderPayConsts.YFK), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 预付卡退款
     * @Date 2020/4/9 21:29
     **/
    YFK_REFUND("Y105", "预付卡退款", Arrays.asList(OrderPayConsts.YFK), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 园区卡收款
     * @Date 2020/4/9 21:29
     **/
    YQK("Y113", "园区卡收款", Arrays.asList(OrderPayConsts.YQK), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 园区卡退款
     * @Date 2020/4/9 21:29
     **/
    YQK_REFUND("TX18", "园区卡退款", Arrays.asList(OrderPayConsts.YFK), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 苹果支付收款
     * @Date 2020/4/9 21:29
     **/
    APPLEPAY("PY34", "苹果支付收款", Arrays.asList(OrderPayConsts.APPLEPAY), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 苹果支付退款
     * @Date 2020/4/9 21:29
     **/
    APPLEPAY_REFUND("TX18", "苹果支付退款", Arrays.asList(OrderPayConsts.APPLEPAY), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 现金收款
     * @Date 2020/4/9 21:29
     **/
    CASH("TX86", "现金收款", Arrays.asList(OrderTypeConst.CashOrderType.CASH.getValue()), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 现金退款
     * @Date 2020/4/9 21:29
     **/
    CASHTK_REFUND("CX86", "现金退款", Arrays.asList(OrderTypeConst.CashOrderType.CASHTK.getValue()), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 银联收款
     * @Date 2020/4/9 21:29
     **/
    UNIONPAY("TX27", "银联收款", Arrays.asList(OrderPayConsts.UNIONPAY), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 银联退款
     * @Date 2020/4/9 21:29
     **/
    UNIONPAY_REFUND("TX51", "银联退款", Arrays.asList(OrderPayConsts.UNIONPAY), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 翼支付收款
     * @Date 2020/4/9 21:29
     **/
    BESTPAY("TX42", "翼支付收款", Arrays.asList(OrderPayConsts.BESTPAY), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 翼支付退款
     * @Date 2020/4/9 21:29
     **/
    BESTPAY_REFUND("TX51", "翼支付退款", Arrays.asList(OrderPayConsts.BESTPAY), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 微信预授权
     * @Date 2020/4/9 21:29
     **/
    WXPA("TX90", "微信预授权", Arrays.asList(OrderPayConsts.WXPA), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝预授权
     * @Date 2020/4/9 21:29
     **/
    ALPA("TX92", "支付宝预授权", Arrays.asList(OrderPayConsts.ALPA), OrderTypeConst.TradeType.POSITIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 微信预授权完成
     * @Date 2020/4/9 21:29
     **/
    WXPF("TX91", "微信预授权完成", Arrays.asList(OrderPayConsts.WXPF), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝预授权完成
     * @Date 2020/4/9 21:29
     **/
    ALPF("TX93", "支付宝预授权完成", Arrays.asList(OrderPayConsts.ALPF), OrderTypeConst.TradeType.POSITIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 微信预授权撤销
     * @Date 2020/4/9 21:29
     **/
    WXPC("CX90", "微信预授权撤销", Arrays.asList(OrderTypeConst.PRE_AUTH_WX_CANCEL), OrderTypeConst.TradeType.NEGATIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝预授权撤销
     * @Date 2020/4/9 21:29
     **/
    ALPC("CX92", "支付宝预授权撤销", Arrays.asList(OrderTypeConst.PRE_AUTH_AL_CANCEL), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 微信预授权完成撤销
     * @Date 2020/4/9 21:29
     **/
    WXPFC("CX91", "微信预授权完成撤销", Arrays.asList(OrderTypeConst.PRE_AUTH_WX_FINISH_CANCEL), OrderTypeConst.TradeType.NEGATIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 支付宝预授权完成撤销
     * @Date 2020/4/9 21:29
     **/
    ALPFC("CX93", "支付宝预授权完成撤销", Arrays.asList(OrderTypeConst.PRE_AUTH_AL_FINISH_CANCEL), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 阿里口碑收款
     * @Date 2020/4/9 21:29
     **/
    ALKB("TX46", "阿里口碑收款", Arrays.asList(OrderPayConsts.ALKB), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 阿里口碑退款
     * @Date 2020/4/9 21:29
     **/
    ALKB_REFUND("TX48", "阿里口碑退款", Arrays.asList(OrderPayConsts.ALKB), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 银联无卡分期收款
     * @Date 2020/4/9 21:29
     **/
    INSTAL("TX56", "银联无卡分期收款", Arrays.asList(OrderPayConsts.INSTAL), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 银联无卡分期退款
     * @Date 2020/4/9 21:29
     **/
    INSTAL_REFUND("TX57", "银联无卡分期退款", Arrays.asList(OrderPayConsts.INSTAL), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 微信直连收款
     * @Date 2020/4/9 21:29
     **/
    WXZL("TX45", "微信直连收款", Arrays.asList(OrderPayConsts.WXZL), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 微信直连退款
     * @Date 2020/4/9 21:29
     **/
    WXZL_REFUND("TX51", "微信直连退款", Arrays.asList(OrderPayConsts.WXZL), OrderTypeConst.TradeType.NEGATIVE.getValue()),

    /***
     * <AUTHOR>
     * @Description 银联小程序收款
     * @Date 2020/4/9 21:29
     **/
    MPAY("PY88", "银联小程序收款", Arrays.asList(OrderPayConsts.UNIONPAY_MPAY), OrderTypeConst.TradeType.POSITIVE.getValue()),
    /***
     * <AUTHOR>
     * @Description 银联小程序退款
     * @Date 2020/4/9 21:29
     **/
    MPAY_REFUND("TX18", "银联小程序退款", Arrays.asList(OrderPayConsts.UNIONPAY_MPAY), OrderTypeConst.TradeType.NEGATIVE.getValue());


    /***
     * <AUTHOR>
     * @Description 根据订单类型和交易类型获取业务代码
     * @Date 2020/4/9 19:00
     * @param orderType
     * @param tradeType
     * @return com.fuiou.terminals.consts.OrderBusiCdEnum
     **/
    public static OrderBusiCdEnum getInstance(String orderType, String tradeType) {
        LogWriter.info(String.format("交易类型枚举 根据订单状态和交易类型获取业务代码，orderType=%s,tradeType=%s",orderType,tradeType));
        for (OrderBusiCdEnum orderBusiCdEnum : OrderBusiCdEnum.values()) {
            if (orderBusiCdEnum.getOrderTypes().contains(orderType) && orderBusiCdEnum.getTradeType().equals(tradeType)) {
                LogWriter.info(String.format("交易类型枚举 根据订单状态和交易类型获取业务代码，orderType=%s,tradeType=%s,orderBusiCdEnum=%s",orderType,tradeType, JSONObject.toJSONString(orderBusiCdEnum)));
                return orderBusiCdEnum;
            }
        }
        return null;
    }

    /**
     * <AUTHOR> 
     * @Description   根据业务代码和交易类型获取订单类型 
     * @Date 2020/10/15 10:55 
     * @param busiCd 业务代码
    * @param tradeType 正反交易类型
     * @return java.lang.String
    **/
    public static String getOrderType(String busiCd, String tradeType) {
        LogWriter.info(String.format("交易类型枚举 根据业务代码和交易类型获取订单类型，busiCd=%s,tradeType=%s",busiCd,tradeType));
        for (OrderBusiCdEnum orderBusiCdEnum : OrderBusiCdEnum.values()) {
            if (orderBusiCdEnum.getBusiCd().equals(busiCd) && orderBusiCdEnum.getTradeType().equals(tradeType)) {
                LogWriter.info(String.format("交易类型枚举 根据业务代码和交易类型获取订单类型，busiCd=%s,tradeType=%s,orderBusiCdEnum=%s",busiCd,tradeType, JSONObject.toJSONString(orderBusiCdEnum)));
                return orderBusiCdEnum.getOrderTypes().get(0);
            }
        }
        return busiCd;
    }

    /**
     * 通过busiCd获取正、反交易
     * @param busiCd
     * @return
     */
    public static String convertTradeType(String busiCd) {
        LogWriter.info(String.format("将swt订单交易类型转换为tpay订单类型begin, swtOrderType=%s", busiCd));
        for (OrderBusiCdEnum orderBusiCdEnum : OrderBusiCdEnum.values()) {
            if (orderBusiCdEnum.getBusiCd().equals(StringUtil.trim(busiCd))) {
                LogWriter.info( String.format("将swt订单交易类型转换为tpay订单类型end, swtbusiCd=%s, TradeType=%s", busiCd, orderBusiCdEnum.getTradeType()));
                return orderBusiCdEnum.getTradeType();
            }
        }
        return "";
    }


    /***
     * <AUTHOR>
     * @Description   业务代码
     * @Date 2020/4/10 14:40
    **/
    private String busiCd;
    /***
     * <AUTHOR>
     * @Description 业务代码
     * @Date 2020/4/9 21:27
     **/
    private String name;
    /***
     * <AUTHOR>
     * @Description 订单类型
     * @Date 2020/4/9 21:27
     **/
    private List<String> orderTypes;
    /***
     * <AUTHOR>
     * @Description 交易类型, 正交易反交易
     * @Date 2020/4/9 21:27
     * @see OrderTypeConst.TradeType
     **/
    private String tradeType;

    OrderBusiCdEnum(String busiCd, String name, List<String> orderTypes, String tradeType) {
        this.busiCd = busiCd;
        this.name = name;
        this.orderTypes = orderTypes;
        this.tradeType = tradeType;
    }

    public String getBusiCd() {
        return busiCd;
    }

    public String getName() {
        return name;
    }

    public List<String> getOrderTypes() {
        return orderTypes;
    }

    public String getTradeType() {
        return tradeType;
    }


}
