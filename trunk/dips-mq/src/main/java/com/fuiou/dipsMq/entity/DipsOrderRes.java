package com.fuiou.dipsMq.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 回调装修通订单接口返回参数
 * @create 2025-05-27 17:08
 **/
public class DipsOrderRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码，成功为：0000
     *
     * @Author: Joker
     * @Date: 2025/5/21 22:33
     */
    private String code;
    /**
     * 结果描述
     *
     * @Author: Joker
     * @Date: 2025/5/21 22:33
     */
    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
