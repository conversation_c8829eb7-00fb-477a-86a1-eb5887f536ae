package com.fuiou.dipsMq.utils;

import com.fuiou.mqtt.framework.utils.LogWriter;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * 
 * <AUTHOR>
 *
 */
public class ConfigReader {
	public static String CONFIG_ROOR;
	public static final String PROP_FUIOU = "fuiou";
	private static Map<String,String> keyMap = new HashMap<String,String>();
	private static Map<String,Properties> propMap=new HashMap<String,Properties>();
	
	static{
		CONFIG_ROOR = ConfigReader.class.getResource("/").getFile();
		scheduleRefreshConfig();
	}
	
	 private static void scheduleRefreshConfig(){
		 new Thread(new Runnable() {
			@Override
			public void run() {
				 try {
					 while(true){
						loadProp(PROP_FUIOU);
						TimeUnit.MINUTES.sleep(1);
					 }
				} catch (Exception e) {
					 LogWriter.error("异常 Exception",e);
					//do nothing
				}
			}
		}).start();
	 }
	
	
	
	public static boolean isRestart(){
		return "1".equals(getDefaultConfig("is_restart"));
	}
	
	/**获取fuiou.properties中的键值
	 * @param propName
	 * @param key
	 */
	public static String getDefaultConfig(String key) {
		if(!propMap.containsKey(PROP_FUIOU)) 
			loadProp(PROP_FUIOU);		
		Properties prop=(Properties) propMap.get(PROP_FUIOU);
		String value = prop.getProperty(key);
		if(value==null || value.equals("")){
			if(keyMap.get(key)!=null){
				value = keyMap.get(key);
			}
		}
		return value;
	}
	


	

	/**
	 * @param propName
	 */
	private static void loadProp(String propName) {
		Properties prop=new Properties();
		FileInputStream fis=null;
		try {
			 fis = new FileInputStream(CONFIG_ROOR+propName+".properties");
			prop.load(fis);
		} catch (Exception e) {
			LogWriter.error("异常 Exception Read "+propName+" fail:"+e.getMessage(),e);
			throw new RuntimeException("Read "+propName+" fail:"+e.getMessage(),e);
		}finally{
			//优化解决linuxToo many open filesBUG
				try {
					if(fis!=null)
					fis.close();
				} catch (IOException e) {
					LogWriter.error("异常 IOException",e);
				}
		}
		propMap.put(propName,prop);
	}

	public static String getDefaultConfig(String key, String defalutValue) {
		String result = getDefaultConfig(key);
		if(StringUtil.isEmpty(result))
		{
			return defalutValue;
		}
		return result;
	}
}
