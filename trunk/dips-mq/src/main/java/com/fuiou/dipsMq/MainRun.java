package com.fuiou.dipsMq;


import com.fuiou.dipsMq.consts.Constants;
import com.fuiou.mqtt.framework.utils.LogWriter;
import com.fuiou.sk.cipher.data.CipherCharset;
import com.fuiou.dipsMq.framework.context.ApplicationContextKeeper;
import org.apache.log4j.PropertyConfigurator;
import org.springframework.context.support.ClassPathXmlApplicationContext;


public class MainRun {

    public static void main(String[] args) {
       //设置rsa编码为gbk
        CipherCharset.initGBK();
        start();
    }

    public static void start() {
        try {
            // 日志初始化
            initLog4j();
			// Spring初始化
            initSpring();
            //启动标记
            Constants.STARTEND = true;
//


        } catch (Exception e) {
            LogWriter.error("启动发生异常 Exception", e);
        }
    }


    /**
     * Spring初始化
     */
    public static void initSpring() {
        LogWriter.info("开始初始化Spring开始-START");
        ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext();
        context.setConfigLocation("config/applicationContext.xml");
        context.getEnvironment().setActiveProfiles("app_active");
        context.refresh();
        ApplicationContextKeeper.init(context);
        LogWriter.info("开始初始化Spring结束-END");
    }


    /**
     * 初始化log4J
     */
    private static void initLog4j() {
        LogWriter.info("开始初始化log4J日志开始-START");
        String path = MainRun.class.getResource("/").getPath() + "log4j.properties";
        System.out.println("log4J路径：" + path);
        PropertyConfigurator.configure(MainRun.class.getResource("/").getPath() + "log4j.properties");
        LogWriter.info("开始初始化log4J日志结束-END");
    }
}
