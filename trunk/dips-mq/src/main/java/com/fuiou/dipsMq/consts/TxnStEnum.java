package com.fuiou.dipsMq.consts;
import com.fuiou.mqtt.framework.utils.LogWriter;
import org.apache.commons.lang3.StringUtils;
/**
 * 注        释：交易状态
 * 修改时间：2017年10月30日 下午2:39:10
 * 创建时间：2017年10月30日 下午2:39:10
 * <AUTHOR> E-mail:<EMAIL> 
 */
public enum TxnStEnum {
//	public static final String;PAY_STATUS_00 ="00";//未支付状态
//	public static final String PAY_STATUS_01 ="01";//已支付
//	public static final String PAY_STATUS_02 ="02";//支付失败
//	public static final String PAY_STATUS_03 ="03";//已退款
//	public static final String PAY_STATUS_04 ="04";//支付中
//	public static final String PAY_STATUS_05 ="05";//已撤销
//	public static final String PAY_STATUS_06 ="06";//已退货
//	public static final String PAY_STATUS_07 ="07";//超时
//
//	public static final String PAY_STATUS_PREAUTH_FINISH ="11";//预授权完成
//	/**
//	 * 预授权完成退款
//	 */
//	public static final String PAY_STATUS_PREAUTH_FINISH_CANCEL ="12";
	FAILED_PAY("0", "失败","02"),
	SUCCESS_PAY("1", "成功","01"),
	CANCEL_PAY("2", "已撤销","05"),
	REFUND_PAY("3", "已退款","03"),
	REVERSAL_PAY("4", "状态不明","00"),
	HANDLE_PAY("5", "处理中","04"),
	FINISH_PAY("6", "已完成","11");
	private TxnStEnum(String code, String desc,String status) {
		this.code = code;
		this.desc = desc;
		this.status = status;
	}
	private String code;
	private String desc;
	public String status;
	public String getCode() {
		return code;
	}
	public String getDesc() {
		return desc;
	}
	public String getStatus() {return status;}

	/**
	 * 方法注释： 返回交易状态
	 * 修改内容： 新增
	 * 修改时间： 2017年11月22日 下午5:10:54
	 * <AUTHOR>
	 * @param srcTxnSt
	 * @param destTxnSt
	 * @param reversalInd
	 * @param cancelInd
	 * @return
	 * srcTxnSt:1 destTxnSt:1 reversalInd:0 cancelInd:0 kbpsRspCd:0000 busiCd:TX02 issInsCdD:0801040000
	 */
	public static TxnStEnum getTxnStEnum(String srcTxnSt, String destTxnSt, String reversalInd,
										 String cancelInd, String kbpsRspCd, String busiCd, String issInsCdD,String tradeType) {
		LogWriter.info("转换支付页状态入参：srcTxnSt:" + srcTxnSt + " destTxnSt:" + destTxnSt + " reversalInd:" + reversalInd
				+ " cancelInd:" + cancelInd + " kbpsRspCd:" + kbpsRspCd + " busiCd:" + busiCd + " issInsCdD:" + issInsCdD);
		// 成功交易
		if (TxnStateConsts.SUCCESS_SRC_TXN_ST.equals(srcTxnSt)
				&& TxnStateConsts.SUCCESS_DEST_TXN_ST.equals(destTxnSt)) {
			// 已撤销
			if (TxnStateConsts.CANCEL_IND_ST.equals(cancelInd)) {
				return TxnStEnum.CANCEL_PAY;
			}
			if (TxnStateConsts.FINISH_INS_ST.equals(cancelInd)) {
				return TxnStEnum.FINISH_PAY;
			}
			// 已退款
			if (TxnStateConsts.REFUND_CANCEL_TXN_ST.equals(cancelInd)) {
				return TxnStEnum.REFUND_PAY;
			}
			if (TxnStateConsts.SUCCESS_REVERSAL_IND_ST.equals(reversalInd)
					&& TxnStateConsts.SUCCESS_CANCEL_IND_ST.equals(cancelInd) && TxnStateConsts.SUCCESS_KBPS_RSP_CD.equals(kbpsRspCd)) {
				if("01".equals(tradeType)){
					//反交易退款成功
					return TxnStEnum.REFUND_PAY;
				}
				return TxnStEnum.SUCCESS_PAY;
			}
		}
		if(StringUtils.isBlank(srcTxnSt) && StringUtils.isBlank(destTxnSt)){
			return TxnStEnum.HANDLE_PAY;
		}
		if (TxnStateConsts.BUSI_TP.isCardTxnOrder(busiCd)) {
			return TxnStEnum.FAILED_PAY;
		}
		//银联无卡分期退款
		if("TX57".equals(busiCd) && TxnStateConsts.UNKNOWN_STATUS_KBPS_RSP_CD.contains(StringUtils.trim(kbpsRspCd))){
			return TxnStEnum.REVERSAL_PAY;
		}

		//主要指微信、支付宝、银行卡等交易，txn.cancel_ind=9 or txn.kbps_rsp_cd in ('1096', '1098', '1009')
		if (!TxnTypeEnum.isBailianTxn(busiCd) && !TxnTypeEnum.isReverseTxn(busiCd) && !TxnTypeEnum.isWildCardTxn(busiCd, issInsCdD) && !TxnTypeEnum.isYibaoTxn(busiCd) &&
				!TxnTypeEnum.isInstalmentdTxn(busiCd) &&
				(cancelInd.equals(TxnStateConsts.REFUND_BEIN_TXN_ST) || TxnStateConsts.UNKNOWN_STATUS_KBPS_RSP_CD.contains(StringUtils.trim(kbpsRspCd)))
				) {
			return TxnStEnum.REVERSAL_PAY;
		}
		if(TxnTypeEnum.isRenbaoTxn(busiCd) && cancelInd.equals(TxnStateConsts.REFUND_BEIN_TXN_ST)){
			return TxnStEnum.FAILED_PAY;
		}
		// 失败交易
		return TxnStEnum.FAILED_PAY;
	}
}
