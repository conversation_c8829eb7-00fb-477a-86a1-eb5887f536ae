package com.fuiou.dipsMq.persist.wosdb.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 注   释：t_txn_log
 * 修改时间：2017年9月1日 下午2:40:22
 * 创建时间：2017年9月1日 下午2:40:22
 * <AUTHOR> E-mail:<EMAIL> 
 */
public class TxnLogBean {
	private long rowId;
	
	private String kbpsSrcSettleDt  ;
	private String srcModuleCd  ;
	private String kbpsTraceNo  ;
	private String subTxnSeq  ;
	private String settleCircle  ;
	private String rlatKbpsSrcSettleDt  ;
	private String rlatSrcModuleCd  ;
	private String rlatKbpsTraceNo  ;
	private String rlatSubTxnSeq  ;   
	private String firstKbpsSrcSettleDt  ;  
	private String firstSrcModuleCd  ;
	private String firstKbpsTraceNo  ;   
	private String firstSubTxnSeq  ;   
	private String busiEndInd  ;    
	private String postTxnSeq  ;    
	private String srcInsCd  ;    
	private String srcSsn  ;
	private String srcTxnDt  ;    
	private String destInsCd  ;    
	private String destSsn  ;
	private String srcRegionCd  ;    
	private String destRegionCd  ;    
	private String kbpsBusiAcnt  ;    
	private String kbpsBusiAcntR  ;   
	private String busiCd  ;
	private String txnMd  ;
	private String txnCd  ;
	private String txnIndex  ;
	private String specKey  ;
	private String txnCatalog  ;
	private String srcChnl  ;
	private String destChnl  ;
	private String cardBinD  ;    
	private String cardAttrD  ;    
	private String issInsCdD  ;   
	private String cardBinC  ;    
	private String cardAttrC  ;    
	private String issInsCdC  ;   
	private String srcPriAcntNo  ;   
	private String srcPriAcntNoR  ;  
	private String destPriAcntNo  ;   
	private String destPriAcntNoR  ;  
	private String srcTxnAmt  ;    
	private String destTxnAmt  ;    
	private String amtAlgoCd  ;    
	private String srcTxnSt  ;
	private String destTxnSt  ;
	private String advProcSt  ;
	private String destRspCd  ;
	private String destRspSsn  ;
	private String acqRspCd  ;
	private String acqAuthRspCd  ;
	private String kbpsRspCd  ;
	private String reversalInd  ;
	private String cancelInd  ;
	private String toTmLen  ;
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date toTs;
	private String toTxnCd  ;
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date txnRcvTs;
	@JSONField(format="yyyy-MM-dd HH:mm:ss")
	private Date txnFinTs;
	private String kbpsSettleInd  ;
	private String capitalDir  ;
	private String adjCapitalMd  ;
	private String srcSettleDt  ;
	private String destSettleDt  ;
	private String kbpsDestSettleDt  ;
	private String kbpsSrcMmdd  ;
	private String kbpsDestMmdd  ;    
	private String acqInsCd  ;    
	private String fwdInsCd  ;    
	private String rcvInsCd  ;    
	private String srcMchntTp  ;    
	private String srcMchntCd  ;    
	private String srcOrderNo  ;    
	private String srcMchntTxnDt  ;   
	private String srcMchntTxnTm  ;   
	private String orderDt  ;
	private String billMonth  ;
	private String payMd  ;
	private String destMchntTp  ;    
	private String destMchntCd  ;    
	private String destOrderNo  ;    
	private String srcTermId  ;    
	private String destTermId  ;    
	private String custmrTp  ;
	private String custmrNo  ;
	private String custmrNoTp  ;    
	private String goodsCd  ;
	private String goodsNum  ;
	private String goodsPrice  ;
	private String origSrcMchntCd  ;   
	private String origSrcOrderNo  ;   
	private String origOrderDt  ;    
	private String origSrcInsCd  ;   
	private String origSrcSsn  ;    
	private String origLocDt  ;    
	private String msgTp  ;
	private String procCd  ;
	private String txnDtTm  ;    
	private String settleConvRt  ;    
	private String locTm  ;
	private String locDt  ;
	private String expireDt  ;
	private String posEntryMdCd  ;   
	private String cardSeqId  ;    
	private String posCondCd  ;    
	private String posPinCapCd  ;   
	private String txnFeeAmt;    
	private String track2Data  ;    
	private String track3Data  ;    
	private String retriRefNo  ;    
	private String authRspCd  ;    
	private String cardAcptrNmLoc  ;   
	private String addnRspDat  ;    
	private String addnPrivData  ;    
	private String txnCurrCd  ;    
	private String secCtrlInf  ;    
	private String addnAmt  ;
	private String icFlds  ;
	private String addnTxnInf  ;    
	private String pbocData  ;
	private String detailInqrData  ;    
	private String msgRsnCd  ;    
	private String addnPosInf  ;    
	private String idNo  ;
	private String origMsgTp  ;    
	private String origSysTraNo  ;   
	private String origTxnDtTm  ;
	private Date origTxnDt;
	private String origTraceNo  ;
	private String origAcqInsCd  ;   
	private String origFwdInsCd  ;   
	private String debitAcntNo  ;    
	private String creditAcntNo  ;    
	private String cupsRes  ;
	private String acqInsRes  ;    
	private String issInsRes  ;    
	private String mac  ; 
	private String cupHead  ;
	private String resrvData1  ;    
	private String resrvData2  ;    
	private String icFldsIndex  ;    
	private String resrvData3  ;    
	private String resrvData4  ;    
	private String resrvData5  ;    
	private String rspIcFlds  ;    
	private String srcSsnNew  ;    
	private String destSsnNew  ;    
	private String outOrderNo  ;    
	private String resrvData6  ;    
	private String resrvData7  ;    
	private String resrvData8  ;    
	private String resrvData9  ;    
	private String resrvData10  ;
	
	private String tableName;  //表名

	private String amt;// swt退款金额
	/**
	* @Description:  原单累计退款金额
	* @Author: 程军
	* @Date: 2024/2/2 15:54
	*/
	private String srcGoodsNum;

	private String loadDt;


	public String getLoadDt() {
		return loadDt;
	}

	public void setLoadDt(String loadDt) {
		this.loadDt = loadDt;
	}

	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public long getRowId() {
		return rowId;
	}
	public void setRowId(long rowId) {
		this.rowId = rowId;
	}
	public String getKbpsSrcSettleDt() {
		return kbpsSrcSettleDt;
	}
	public void setKbpsSrcSettleDt(String kbpsSrcSettleDt) {
		this.kbpsSrcSettleDt = kbpsSrcSettleDt;
	}
	public String getSrcModuleCd() {
		return srcModuleCd;
	}
	public void setSrcModuleCd(String srcModuleCd) {
		this.srcModuleCd = srcModuleCd;
	}
	public String getKbpsTraceNo() {
		return kbpsTraceNo;
	}
	public void setKbpsTraceNo(String kbpsTraceNo) {
		this.kbpsTraceNo = kbpsTraceNo;
	}
	public String getSubTxnSeq() {
		return subTxnSeq;
	}
	public void setSubTxnSeq(String subTxnSeq) {
		this.subTxnSeq = subTxnSeq;
	}
	public String getSettleCircle() {
		return settleCircle;
	}
	public void setSettleCircle(String settleCircle) {
		this.settleCircle = settleCircle;
	}
	public String getRlatKbpsSrcSettleDt() {
		return rlatKbpsSrcSettleDt;
	}
	public void setRlatKbpsSrcSettleDt(String rlatKbpsSrcSettleDt) {
		this.rlatKbpsSrcSettleDt = rlatKbpsSrcSettleDt;
	}
	public String getRlatSrcModuleCd() {
		return rlatSrcModuleCd;
	}
	public void setRlatSrcModuleCd(String rlatSrcModuleCd) {
		this.rlatSrcModuleCd = rlatSrcModuleCd;
	}
	public String getRlatKbpsTraceNo() {
		return rlatKbpsTraceNo;
	}
	public void setRlatKbpsTraceNo(String rlatKbpsTraceNo) {
		this.rlatKbpsTraceNo = rlatKbpsTraceNo;
	}
	public String getRlatSubTxnSeq() {
		return rlatSubTxnSeq;
	}
	public void setRlatSubTxnSeq(String rlatSubTxnSeq) {
		this.rlatSubTxnSeq = rlatSubTxnSeq;
	}
	public String getFirstKbpsSrcSettleDt() {
		return firstKbpsSrcSettleDt;
	}
	public void setFirstKbpsSrcSettleDt(String firstKbpsSrcSettleDt) {
		this.firstKbpsSrcSettleDt = firstKbpsSrcSettleDt;
	}
	public String getFirstSrcModuleCd() {
		return firstSrcModuleCd;
	}
	public void setFirstSrcModuleCd(String firstSrcModuleCd) {
		this.firstSrcModuleCd = firstSrcModuleCd;
	}
	public String getFirstKbpsTraceNo() {
		return firstKbpsTraceNo;
	}
	public void setFirstKbpsTraceNo(String firstKbpsTraceNo) {
		this.firstKbpsTraceNo = firstKbpsTraceNo;
	}
	public String getFirstSubTxnSeq() {
		return firstSubTxnSeq;
	}
	public void setFirstSubTxnSeq(String firstSubTxnSeq) {
		this.firstSubTxnSeq = firstSubTxnSeq;
	}
	public String getBusiEndInd() {
		return busiEndInd;
	}
	public void setBusiEndInd(String busiEndInd) {
		this.busiEndInd = busiEndInd;
	}
	public String getPostTxnSeq() {
		return postTxnSeq;
	}
	public void setPostTxnSeq(String postTxnSeq) {
		this.postTxnSeq = postTxnSeq;
	}
	public String getSrcInsCd() {
		return srcInsCd;
	}
	public void setSrcInsCd(String srcInsCd) {
		this.srcInsCd = srcInsCd;
	}
	public String getSrcSsn() {
		return srcSsn;
	}
	public void setSrcSsn(String srcSsn) {
		this.srcSsn = srcSsn;
	}
	public String getSrcTxnDt() {
		return srcTxnDt;
	}
	public void setSrcTxnDt(String srcTxnDt) {
		this.srcTxnDt = srcTxnDt;
	}
	public String getDestInsCd() {
		return destInsCd;
	}
	public void setDestInsCd(String destInsCd) {
		this.destInsCd = destInsCd;
	}
	public String getDestSsn() {
		return destSsn;
	}
	public void setDestSsn(String destSsn) {
		this.destSsn = destSsn;
	}
	public String getSrcRegionCd() {
		return srcRegionCd;
	}
	public void setSrcRegionCd(String srcRegionCd) {
		this.srcRegionCd = srcRegionCd;
	}
	public String getDestRegionCd() {
		return destRegionCd;
	}
	public void setDestRegionCd(String destRegionCd) {
		this.destRegionCd = destRegionCd;
	}
	public String getKbpsBusiAcnt() {
		return kbpsBusiAcnt;
	}
	public void setKbpsBusiAcnt(String kbpsBusiAcnt) {
		this.kbpsBusiAcnt = kbpsBusiAcnt;
	}
	public String getKbpsBusiAcntR() {
		return kbpsBusiAcntR;
	}
	public void setKbpsBusiAcntR(String kbpsBusiAcntR) {
		this.kbpsBusiAcntR = kbpsBusiAcntR;
	}
	public String getBusiCd() {
		return busiCd;
	}
	public void setBusiCd(String busiCd) {
		this.busiCd = busiCd;
	}
	public String getTxnMd() {
		return txnMd;
	}
	public void setTxnMd(String txnMd) {
		this.txnMd = txnMd;
	}
	public String getTxnCd() {
		return txnCd;
	}
	public void setTxnCd(String txnCd) {
		this.txnCd = txnCd;
	}
	public String getTxnIndex() {
		return txnIndex;
	}
	public void setTxnIndex(String txnIndex) {
		this.txnIndex = txnIndex;
	}
	public String getSpecKey() {
		return specKey;
	}
	public void setSpecKey(String specKey) {
		this.specKey = specKey;
	}
	public String getTxnCatalog() {
		return txnCatalog;
	}
	public void setTxnCatalog(String txnCatalog) {
		this.txnCatalog = txnCatalog;
	}
	public String getSrcChnl() {
		return srcChnl;
	}
	public void setSrcChnl(String srcChnl) {
		this.srcChnl = srcChnl;
	}
	public String getDestChnl() {
		return destChnl;
	}
	public void setDestChnl(String destChnl) {
		this.destChnl = destChnl;
	}
	public String getCardBinD() {
		return cardBinD;
	}
	public void setCardBinD(String cardBinD) {
		this.cardBinD = cardBinD;
	}
	public String getCardAttrD() {
		return cardAttrD;
	}
	public void setCardAttrD(String cardAttrD) {
		this.cardAttrD = cardAttrD;
	}
	public String getIssInsCdD() {
		return issInsCdD;
	}
	public void setIssInsCdD(String issInsCdD) {
		this.issInsCdD = issInsCdD;
	}
	public String getCardBinC() {
		return cardBinC;
	}
	public void setCardBinC(String cardBinC) {
		this.cardBinC = cardBinC;
	}
	public String getCardAttrC() {
		return cardAttrC;
	}
	public void setCardAttrC(String cardAttrC) {
		this.cardAttrC = cardAttrC;
	}
	public String getIssInsCdC() {
		return issInsCdC;
	}
	public void setIssInsCdC(String issInsCdC) {
		this.issInsCdC = issInsCdC;
	}
	public String getSrcPriAcntNo() {
		return srcPriAcntNo;
	}
	public void setSrcPriAcntNo(String srcPriAcntNo) {
		this.srcPriAcntNo = srcPriAcntNo;
	}
	public String getSrcPriAcntNoR() {
		return srcPriAcntNoR;
	}
	public void setSrcPriAcntNoR(String srcPriAcntNoR) {
		this.srcPriAcntNoR = srcPriAcntNoR;
	}
	public String getDestPriAcntNo() {
		return destPriAcntNo;
	}
	public void setDestPriAcntNo(String destPriAcntNo) {
		this.destPriAcntNo = destPriAcntNo;
	}
	public String getDestPriAcntNoR() {
		return destPriAcntNoR;
	}
	public void setDestPriAcntNoR(String destPriAcntNoR) {
		this.destPriAcntNoR = destPriAcntNoR;
	}
	public String getSrcTxnAmt() {
		return srcTxnAmt;
	}
	public void setSrcTxnAmt(String srcTxnAmt) {
		this.srcTxnAmt = srcTxnAmt;
	}
	public String getDestTxnAmt() {
		return destTxnAmt;
	}
	public void setDestTxnAmt(String destTxnAmt) {
		this.destTxnAmt = destTxnAmt;
	}
	public String getAmtAlgoCd() {
		return amtAlgoCd;
	}
	public void setAmtAlgoCd(String amtAlgoCd) {
		this.amtAlgoCd = amtAlgoCd;
	}
	public String getSrcTxnSt() {
		return srcTxnSt;
	}
	public void setSrcTxnSt(String srcTxnSt) {
		this.srcTxnSt = srcTxnSt;
	}
	public String getDestTxnSt() {
		return destTxnSt;
	}
	public void setDestTxnSt(String destTxnSt) {
		this.destTxnSt = destTxnSt;
	}
	public String getAdvProcSt() {
		return advProcSt;
	}
	public void setAdvProcSt(String advProcSt) {
		this.advProcSt = advProcSt;
	}
	public String getDestRspCd() {
		return destRspCd;
	}
	public void setDestRspCd(String destRspCd) {
		this.destRspCd = destRspCd;
	}
	public String getDestRspSsn() {
		return destRspSsn;
	}
	public void setDestRspSsn(String destRspSsn) {
		this.destRspSsn = destRspSsn;
	}
	public String getAcqRspCd() {
		return acqRspCd;
	}
	public void setAcqRspCd(String acqRspCd) {
		this.acqRspCd = acqRspCd;
	}
	public String getAcqAuthRspCd() {
		return acqAuthRspCd;
	}
	public void setAcqAuthRspCd(String acqAuthRspCd) {
		this.acqAuthRspCd = acqAuthRspCd;
	}
	public String getKbpsRspCd() {
		return kbpsRspCd;
	}
	public void setKbpsRspCd(String kbpsRspCd) {
		this.kbpsRspCd = kbpsRspCd;
	}
	public String getReversalInd() {
		return reversalInd;
	}
	public void setReversalInd(String reversalInd) {
		this.reversalInd = reversalInd;
	}
	public String getCancelInd() {
		return cancelInd;
	}
	public void setCancelInd(String cancelInd) {
		this.cancelInd = cancelInd;
	}
	public String getToTmLen() {
		return toTmLen;
	}
	public void setToTmLen(String toTmLen) {
		this.toTmLen = toTmLen;
	}
	
	public Date getToTs() {
		return toTs;
	}
	public String getToTxnCd() {
		return toTxnCd;
	}
	public void setToTxnCd(String toTxnCd) {
		this.toTxnCd = toTxnCd;
	}
	public Date getTxnRcvTs() {
		return txnRcvTs;
	}
	public void setTxnRcvTs(Date txnRcvTs) {
		this.txnRcvTs = txnRcvTs;
	}
	public Date getTxnFinTs() {
		return txnFinTs;
	}
	public void setTxnFinTs(Date txnFinTs) {
		this.txnFinTs = txnFinTs;
	}
	public void setToTs(Date toTs) {
		this.toTs = toTs;
	}
	public String getKbpsSettleInd() {
		return kbpsSettleInd;
	}
	public void setKbpsSettleInd(String kbpsSettleInd) {
		this.kbpsSettleInd = kbpsSettleInd;
	}
	public String getCapitalDir() {
		return capitalDir;
	}
	public void setCapitalDir(String capitalDir) {
		this.capitalDir = capitalDir;
	}
	public String getAdjCapitalMd() {
		return adjCapitalMd;
	}
	public void setAdjCapitalMd(String adjCapitalMd) {
		this.adjCapitalMd = adjCapitalMd;
	}
	public String getSrcSettleDt() {
		return srcSettleDt;
	}
	public void setSrcSettleDt(String srcSettleDt) {
		this.srcSettleDt = srcSettleDt;
	}
	public String getDestSettleDt() {
		return destSettleDt;
	}
	public void setDestSettleDt(String destSettleDt) {
		this.destSettleDt = destSettleDt;
	}
	public String getKbpsDestSettleDt() {
		return kbpsDestSettleDt;
	}
	public void setKbpsDestSettleDt(String kbpsDestSettleDt) {
		this.kbpsDestSettleDt = kbpsDestSettleDt;
	}
	public String getKbpsSrcMmdd() {
		return kbpsSrcMmdd;
	}
	public void setKbpsSrcMmdd(String kbpsSrcMmdd) {
		this.kbpsSrcMmdd = kbpsSrcMmdd;
	}
	public String getKbpsDestMmdd() {
		return kbpsDestMmdd;
	}
	public void setKbpsDestMmdd(String kbpsDestMmdd) {
		this.kbpsDestMmdd = kbpsDestMmdd;
	}
	public String getAcqInsCd() {
		return acqInsCd;
	}
	public void setAcqInsCd(String acqInsCd) {
		this.acqInsCd = acqInsCd;
	}
	public String getFwdInsCd() {
		return fwdInsCd;
	}
	public void setFwdInsCd(String fwdInsCd) {
		this.fwdInsCd = fwdInsCd;
	}
	public String getRcvInsCd() {
		return rcvInsCd;
	}
	public void setRcvInsCd(String rcvInsCd) {
		this.rcvInsCd = rcvInsCd;
	}
	public String getSrcMchntTp() {
		return srcMchntTp;
	}
	public void setSrcMchntTp(String srcMchntTp) {
		this.srcMchntTp = srcMchntTp;
	}
	public String getSrcMchntCd() {
		return srcMchntCd;
	}
	public void setSrcMchntCd(String srcMchntCd) {
		this.srcMchntCd = srcMchntCd;
	}
	public String getSrcOrderNo() {
		return srcOrderNo;
	}
	public void setSrcOrderNo(String srcOrderNo) {
		this.srcOrderNo = srcOrderNo;
	}
	public String getSrcMchntTxnDt() {
		return srcMchntTxnDt;
	}
	public void setSrcMchntTxnDt(String srcMchntTxnDt) {
		this.srcMchntTxnDt = srcMchntTxnDt;
	}
	public String getSrcMchntTxnTm() {
		return srcMchntTxnTm;
	}
	public void setSrcMchntTxnTm(String srcMchntTxnTm) {
		this.srcMchntTxnTm = srcMchntTxnTm;
	}
	public String getOrderDt() {
		return orderDt;
	}
	public void setOrderDt(String orderDt) {
		this.orderDt = orderDt;
	}
	public String getBillMonth() {
		return billMonth;
	}
	public void setBillMonth(String billMonth) {
		this.billMonth = billMonth;
	}
	public String getPayMd() {
		return payMd;
	}
	public void setPayMd(String payMd) {
		this.payMd = payMd;
	}
	public String getDestMchntTp() {
		return destMchntTp;
	}
	public void setDestMchntTp(String destMchntTp) {
		this.destMchntTp = destMchntTp;
	}
	public String getDestMchntCd() {
		return destMchntCd;
	}
	public void setDestMchntCd(String destMchntCd) {
		this.destMchntCd = destMchntCd;
	}
	public String getDestOrderNo() {
		return destOrderNo;
	}
	public void setDestOrderNo(String destOrderNo) {
		this.destOrderNo = destOrderNo;
	}
	public String getSrcTermId() {
		return srcTermId;
	}
	public void setSrcTermId(String srcTermId) {
		this.srcTermId = srcTermId;
	}
	public String getDestTermId() {
		return destTermId;
	}
	public void setDestTermId(String destTermId) {
		this.destTermId = destTermId;
	}
	public String getCustmrTp() {
		return custmrTp;
	}
	public void setCustmrTp(String custmrTp) {
		this.custmrTp = custmrTp;
	}
	public String getCustmrNo() {
		return custmrNo;
	}
	public void setCustmrNo(String custmrNo) {
		this.custmrNo = custmrNo;
	}
	public String getCustmrNoTp() {
		return custmrNoTp;
	}
	public void setCustmrNoTp(String custmrNoTp) {
		this.custmrNoTp = custmrNoTp;
	}
	public String getGoodsCd() {
		return goodsCd;
	}
	public void setGoodsCd(String goodsCd) {
		this.goodsCd = goodsCd;
	}
	public String getGoodsNum() {
		return goodsNum;
	}
	public void setGoodsNum(String goodsNum) {
		this.goodsNum = goodsNum;
	}
	public String getGoodsPrice() {
		return goodsPrice;
	}
	public void setGoodsPrice(String goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public String getOrigSrcMchntCd() {
		return origSrcMchntCd;
	}
	public void setOrigSrcMchntCd(String origSrcMchntCd) {
		this.origSrcMchntCd = origSrcMchntCd;
	}
	public String getOrigSrcOrderNo() {
		return origSrcOrderNo;
	}
	public void setOrigSrcOrderNo(String origSrcOrderNo) {
		this.origSrcOrderNo = origSrcOrderNo;
	}
	public String getOrigOrderDt() {
		return origOrderDt;
	}
	public void setOrigOrderDt(String origOrderDt) {
		this.origOrderDt = origOrderDt;
	}
	public String getOrigSrcInsCd() {
		return origSrcInsCd;
	}
	public void setOrigSrcInsCd(String origSrcInsCd) {
		this.origSrcInsCd = origSrcInsCd;
	}
	public String getOrigSrcSsn() {
		return origSrcSsn;
	}
	public void setOrigSrcSsn(String origSrcSsn) {
		this.origSrcSsn = origSrcSsn;
	}
	public String getOrigLocDt() {
		return origLocDt;
	}
	public void setOrigLocDt(String origLocDt) {
		this.origLocDt = origLocDt;
	}
	public String getMsgTp() {
		return msgTp;
	}
	public void setMsgTp(String msgTp) {
		this.msgTp = msgTp;
	}
	public String getProcCd() {
		return procCd;
	}
	public void setProcCd(String procCd) {
		this.procCd = procCd;
	}
	public String getTxnDtTm() {
		return txnDtTm;
	}
	public void setTxnDtTm(String txnDtTm) {
		this.txnDtTm = txnDtTm;
	}
	public String getSettleConvRt() {
		return settleConvRt;
	}
	public void setSettleConvRt(String settleConvRt) {
		this.settleConvRt = settleConvRt;
	}
	public String getLocTm() {
		return locTm;
	}
	public void setLocTm(String locTm) {
		this.locTm = locTm;
	}
	public String getLocDt() {
		return locDt;
	}
	public void setLocDt(String locDt) {
		this.locDt = locDt;
	}
	public String getExpireDt() {
		return expireDt;
	}
	public void setExpireDt(String expireDt) {
		this.expireDt = expireDt;
	}
	public String getPosEntryMdCd() {
		return posEntryMdCd;
	}
	public void setPosEntryMdCd(String posEntryMdCd) {
		this.posEntryMdCd = posEntryMdCd;
	}
	public String getCardSeqId() {
		return cardSeqId;
	}
	public void setCardSeqId(String cardSeqId) {
		this.cardSeqId = cardSeqId;
	}
	public String getPosCondCd() {
		return posCondCd;
	}
	public void setPosCondCd(String posCondCd) {
		this.posCondCd = posCondCd;
	}
	public String getPosPinCapCd() {
		return posPinCapCd;
	}
	public void setPosPinCapCd(String posPinCapCd) {
		this.posPinCapCd = posPinCapCd;
	}
	public String getTxnFeeAmt() {
		return txnFeeAmt;
	}
	public void setTxnFeeAmt(String txnFeeAmt) {
		this.txnFeeAmt = txnFeeAmt;
	}
	public String getTrack2Data() {
		return track2Data;
	}
	public void setTrack2Data(String track2Data) {
		this.track2Data = track2Data;
	}
	public String getTrack3Data() {
		return track3Data;
	}
	public void setTrack3Data(String track3Data) {
		this.track3Data = track3Data;
	}
	public String getRetriRefNo() {
		return retriRefNo;
	}
	public void setRetriRefNo(String retriRefNo) {
		this.retriRefNo = retriRefNo;
	}
	public String getAuthRspCd() {
		return authRspCd;
	}
	public void setAuthRspCd(String authRspCd) {
		this.authRspCd = authRspCd;
	}
	public String getCardAcptrNmLoc() {
		return cardAcptrNmLoc;
	}
	public void setCardAcptrNmLoc(String cardAcptrNmLoc) {
		this.cardAcptrNmLoc = cardAcptrNmLoc;
	}
	public String getAddnRspDat() {
		return addnRspDat;
	}
	public void setAddnRspDat(String addnRspDat) {
		this.addnRspDat = addnRspDat;
	}
	public String getAddnPrivData() {
		return addnPrivData;
	}
	public void setAddnPrivData(String addnPrivData) {
		this.addnPrivData = addnPrivData;
	}
	public String getTxnCurrCd() {
		return txnCurrCd;
	}
	public void setTxnCurrCd(String txnCurrCd) {
		this.txnCurrCd = txnCurrCd;
	}
	public String getSecCtrlInf() {
		return secCtrlInf;
	}
	public void setSecCtrlInf(String secCtrlInf) {
		this.secCtrlInf = secCtrlInf;
	}
	public String getAddnAmt() {
		return addnAmt;
	}
	public void setAddnAmt(String addnAmt) {
		this.addnAmt = addnAmt;
	}
	public String getIcFlds() {
		return icFlds;
	}
	public void setIcFlds(String icFlds) {
		this.icFlds = icFlds;
	}
	public String getAddnTxnInf() {
		return addnTxnInf;
	}
	public void setAddnTxnInf(String addnTxnInf) {
		this.addnTxnInf = addnTxnInf;
	}
	public String getPbocData() {
		return pbocData;
	}
	public void setPbocData(String pbocData) {
		this.pbocData = pbocData;
	}
	public String getDetailInqrData() {
		return detailInqrData;
	}
	public void setDetailInqrData(String detailInqrData) {
		this.detailInqrData = detailInqrData;
	}
	public String getMsgRsnCd() {
		return msgRsnCd;
	}
	public void setMsgRsnCd(String msgRsnCd) {
		this.msgRsnCd = msgRsnCd;
	}
	public String getAddnPosInf() {
		return addnPosInf;
	}
	public void setAddnPosInf(String addnPosInf) {
		this.addnPosInf = addnPosInf;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	public String getOrigMsgTp() {
		return origMsgTp;
	}
	public void setOrigMsgTp(String origMsgTp) {
		this.origMsgTp = origMsgTp;
	}
	public String getOrigSysTraNo() {
		return origSysTraNo;
	}
	public void setOrigSysTraNo(String origSysTraNo) {
		this.origSysTraNo = origSysTraNo;
	}
	public String getOrigTxnDtTm() {
		return origTxnDtTm;
	}
	public void setOrigTxnDtTm(String origTxnDtTm) {
		this.origTxnDtTm = origTxnDtTm;
	}
	public String getOrigAcqInsCd() {
		return origAcqInsCd;
	}
	public void setOrigAcqInsCd(String origAcqInsCd) {
		this.origAcqInsCd = origAcqInsCd;
	}
	public String getOrigFwdInsCd() {
		return origFwdInsCd;
	}
	public void setOrigFwdInsCd(String origFwdInsCd) {
		this.origFwdInsCd = origFwdInsCd;
	}
	public String getDebitAcntNo() {
		return debitAcntNo;
	}
	public void setDebitAcntNo(String debitAcntNo) {
		this.debitAcntNo = debitAcntNo;
	}
	public String getCreditAcntNo() {
		return creditAcntNo;
	}
	public void setCreditAcntNo(String creditAcntNo) {
		this.creditAcntNo = creditAcntNo;
	}
	public String getCupsRes() {
		return cupsRes;
	}
	public void setCupsRes(String cupsRes) {
		this.cupsRes = cupsRes;
	}
	public String getAcqInsRes() {
		return acqInsRes;
	}
	public void setAcqInsRes(String acqInsRes) {
		this.acqInsRes = acqInsRes;
	}
	public String getIssInsRes() {
		return issInsRes;
	}
	public void setIssInsRes(String issInsRes) {
		this.issInsRes = issInsRes;
	}
	public String getMac() {
		return mac;
	}
	public void setMac(String mac) {
		this.mac = mac;
	}
	public String getCupHead() {
		return cupHead;
	}
	public void setCupHead(String cupHead) {
		this.cupHead = cupHead;
	}
	public String getResrvData1() {
		return resrvData1;
	}
	public void setResrvData1(String resrvData1) {
		this.resrvData1 = resrvData1;
	}
	public String getResrvData2() {
		return resrvData2;
	}
	public void setResrvData2(String resrvData2) {
		this.resrvData2 = resrvData2;
	}
	public String getIcFldsIndex() {
		return icFldsIndex;
	}
	public void setIcFldsIndex(String icFldsIndex) {
		this.icFldsIndex = icFldsIndex;
	}
	public String getResrvData3() {
		return resrvData3;
	}
	public void setResrvData3(String resrvData3) {
		this.resrvData3 = resrvData3;
	}
	public String getResrvData4() {
		return resrvData4;
	}
	public void setResrvData4(String resrvData4) {
		this.resrvData4 = resrvData4;
	}
	public String getResrvData5() {
		return resrvData5;
	}
	public void setResrvData5(String resrvData5) {
		this.resrvData5 = resrvData5;
	}
	public String getRspIcFlds() {
		return rspIcFlds;
	}
	public void setRspIcFlds(String rspIcFlds) {
		this.rspIcFlds = rspIcFlds;
	}
	public String getSrcSsnNew() {
		return srcSsnNew;
	}
	public void setSrcSsnNew(String srcSsnNew) {
		this.srcSsnNew = srcSsnNew;
	}
	public String getDestSsnNew() {
		return destSsnNew;
	}
	public void setDestSsnNew(String destSsnNew) {
		this.destSsnNew = destSsnNew;
	}
	public String getOutOrderNo() {
		return outOrderNo;
	}
	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}
	public String getResrvData6() {
		return resrvData6;
	}
	public void setResrvData6(String resrvData6) {
		this.resrvData6 = resrvData6;
	}
	public String getResrvData7() {
		return resrvData7;
	}
	public void setResrvData7(String resrvData7) {
		this.resrvData7 = resrvData7;
	}
	public String getResrvData8() {
		return resrvData8;
	}
	public void setResrvData8(String resrvData8) {
		this.resrvData8 = resrvData8;
	}
	public String getResrvData9() {
		return resrvData9;
	}
	public void setResrvData9(String resrvData9) {
		this.resrvData9 = resrvData9;
	}
	public String getResrvData10() {
		return resrvData10;
	}
	public void setResrvData10(String resrvData10) {
		this.resrvData10 = resrvData10;
	}
	public Date getOrigTxnDt() {
		return origTxnDt;
	}
	public void setOrigTxnDt(Date origTxnDt) {
		this.origTxnDt = origTxnDt;
	}
	public String getOrigTraceNo() {
		return origTraceNo;
	}
	public void setOrigTraceNo(String origTraceNo) {
		this.origTraceNo = origTraceNo;
	}

	public String getAmt() {
		return amt;
	}

	public void setAmt(String amt) {
		this.amt = amt;
	}

	public String getSrcGoodsNum() {
		return srcGoodsNum == null ? "" : srcGoodsNum.trim();
	}

	public void setSrcGoodsNum(String srcGoodsNum) {
		this.srcGoodsNum = srcGoodsNum;
	}
}
