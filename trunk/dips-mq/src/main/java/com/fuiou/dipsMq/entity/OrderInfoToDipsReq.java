package com.fuiou.dipsMq.entity;

import java.io.Serializable;

/**
 * 推送订单信息到装修通入参
 *
 * <AUTHOR>
 * @create 2025-05-27 11:01
 **/
public class OrderInfoToDipsReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private String order_no; //订单号

    private String mchnt_cd; //商户号

    private String trade_dt; //支付日期

    private String order_type;

    private String order_amt; //订单金额

    private String coupon_amt; //优惠金额

    private String acual_amt; //实收金额

    private String trade_type; //正反交易类型  1 正交易 2 反交易

    private String timestamp; //发送时间  格式yyyyMMddHHmmss

    private String pay_state; //支付状态

    private String resp_code; //交易应答码

    private String resp_msg; //交易描述

    private String txn_time; //交易时间

    private String pay_time; //支付完成时间  正交易传递

    private String src_order_no; //源订单号

    private String total_refund_amt; //退款总金额

    private String channel_order_id; //条码流水

    private String transaction_id; //渠道流水号

    private String fy_trace_no; //富友跟踪号

    private String fy_term_id; //富友终端号

    private String src_trade_dt;//原单清算日期

    private String src_fy_trace_no;//原单参考号

    private String openId;

    /**
     * 1. 获取所有 post 内容，不包括字节类型参数，如文件、字节流，剔除 sign 字段，剔除值为空的参数；
     * 2. 按照第一个字符的键值 ASCII 码递增排序（字母升序排序），如果遇到相同字符则按照第二个字符的键值 ASCII 码递增排序，以此类推；
     * 3. 将排序后的参数与其对应值，组合成 参数=参数值 的格式，并且把这些参数用 & 字符连接起来，此时生成的字符串为待签名字符串。
     */

    private String sign;

    public String getAcual_amt() {
        return acual_amt;
    }

    public void setAcual_amt(String acual_amt) {
        this.acual_amt = acual_amt;
    }

    public String getFy_trace_no() {
        return fy_trace_no;
    }

    public void setFy_trace_no(String fy_trace_no) {
        this.fy_trace_no = fy_trace_no;
    }

    public String getTxn_time() {
        return txn_time;
    }

    public void setTxn_time(String txn_time) {
        this.txn_time = txn_time;
    }

    public String getCoupon_amt() {
        return coupon_amt;
    }

    public void setCoupon_amt(String coupon_amt) {
        this.coupon_amt = coupon_amt;
    }

    public String getChannel_order_id() {
        return channel_order_id;
    }

    public void setChannel_order_id(String channel_order_id) {
        this.channel_order_id = channel_order_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getSrc_order_no() {
        return src_order_no;
    }

    public void setSrc_order_no(String src_order_no) {
        this.src_order_no = src_order_no;
    }

    public String getTotal_refund_amt() {
        return total_refund_amt;
    }

    public void setTotal_refund_amt(String total_refund_amt) {
        this.total_refund_amt = total_refund_amt;
    }

    public String getResp_code() {
        return resp_code;
    }

    public void setResp_code(String resp_code) {
        this.resp_code = resp_code;
    }

    public String getResp_msg() {
        return resp_msg;
    }

    public void setResp_msg(String resp_msg) {
        this.resp_msg = resp_msg;
    }

    public String getOrder_type() {
        return order_type;
    }

    public void setOrder_type(String order_type) {
        this.order_type = order_type;
    }

    public String getPay_time() {
        return pay_time;
    }

    public void setPay_time(String pay_time) {
        this.pay_time = pay_time;
    }

    public String getPay_state() {
        return pay_state;
    }

    public void setPay_state(String pay_state) {
        this.pay_state = pay_state;
    }

    public String getOrder_no() {
        return order_no;
    }

    public void setOrder_no(String order_no) {
        this.order_no = order_no;
    }

    public String getMchnt_cd() {
        return mchnt_cd;
    }

    public void setMchnt_cd(String mchnt_cd) {
        this.mchnt_cd = mchnt_cd;
    }

    public String getTrade_dt() {
        return trade_dt;
    }

    public void setTrade_dt(String trade_dt) {
        this.trade_dt = trade_dt;
    }

    public String getOrder_amt() {
        return order_amt;
    }

    public void setOrder_amt(String order_amt) {
        this.order_amt = order_amt;
    }

    public String getTrade_type() {
        return trade_type;
    }

    public void setTrade_type(String trade_type) {
        this.trade_type = trade_type;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getFy_term_id() {
        return fy_term_id;
    }

    public void setFy_term_id(String fy_term_id) {
        this.fy_term_id = fy_term_id;
    }

    public String getSrc_trade_dt() {
        return src_trade_dt;
    }

    public void setSrc_trade_dt(String src_trade_dt) {
        this.src_trade_dt = src_trade_dt;
    }

    public String getSrc_fy_trace_no() {
        return src_fy_trace_no;
    }

    public void setSrc_fy_trace_no(String src_fy_trace_no) {
        this.src_fy_trace_no = src_fy_trace_no;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }


}
