package com.fuiou.dipsMq.framework.context;

import org.springframework.context.ApplicationContext;

public class ApplicationContextKeeper {

	private static ApplicationContext appCtx = null;

	public static ApplicationContext getAppCtx() {
		return appCtx;
	}
	public static void init(ApplicationContext ctxVal) {
		appCtx = ctxVal;
	}


	/**
	 * 获取含类型信息的bean
	 * @param <T>
	 * @param beanName
	 * @param clazz
	 * @return
	 */
	public static <T>  T getBean(String beanName,Class<T>clazz){
		T bean=appCtx.getBean(beanName,clazz);
		return bean;
	}
	/**
	 * 获取原型bean
	 * @param beanName
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getBean(String beanName){
		return (T) appCtx.getBean(beanName);
	}

	/**
	 * 获取原型bean
	 * @return
	 */
	public static <T> T getBean(Class<T>clazz){
		return (T) appCtx.getBean(clazz);
	}
}
