package com.fuiou.dipsMq.utils;

import com.fuiou.mqtt.framework.utils.LogWriter;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * @description 日期处理工具类
 * @createTime 2014年10月29日15:00:21
 * @version 1.0.0
 *
 */
public class DateUtils {
	/**
	 * 没有分隔符的日期格式
	 */
	public static final String FULL_TIME_WITH_OUT_SEPARATOR = "yyyyMMddHHmmss";
	/**
	 * 最为常见的日期格式
	 */
	public static final String FULL_TIME_WITH_COMMON_SEPARATOR = "yyyy-MM-dd HH:mm:ss";
	
	public static final String FORMAT_DATE = "yyyyMMdd";
	
	/**
	 * yyyyMMddHHmmss 字符串regex
	 */
	public static final String FORMAT_DATETIME_REGEX = "[1-3]{1}(0|9|1|2)[0-9]{2}(([0][1-9]{1})|([1][0-2]{1}))(([0-2][0-9]{1})|([3][0-1]{1}))(([0-1]{1}[0-9]{1})|([2]{1}[0-3]{1}))(([0-5]{1}[0-9]{1}){2})";
	

	/**
	 * @description 取得当前日期的String。
	 * @return String 返回值为日期,格式自定义，需要符合标准，参考Java Doc “SimpleDateFormat”
	 */
	public static String getCurrentDate(String aFormat) {
		String tDate = new SimpleDateFormat(aFormat).format(new Date(
				System.currentTimeMillis()));
		return tDate;
	}
	
	/**
	 * 获得当前时间格式yyyyMMdd
	 * @return
	 */
	public static String getCurrentDate() {
		return DateUtils.getCurrentDate("yyyyMMdd");
	}

	/**
	 * 获得当前时间格式HHmmss
	 * @return
	 */
	public static String getCurrentTime() {
		return DateUtils.getCurrentDate("HHmmss");
	}

	/**
	 * 获得当前时间格式yyyyMMddHHmmss
	 * @return
	 */
	public static String getCurrentDateTime() {
		return DateUtils.getCurrentDate("yyyyMMddHHmmss");
	}

	public static String getMillisecondDateTime() {
		return DateUtils.getCurrentDate("yyyy-MM-dd: HH:mm:ss SSS");
	}

	/**
	 * @description 根据传入的日期及格式进行格式化时间的字符串，默认不传获取yyyyMMddHHmmss日期格式
	 */
	public static String getDateFormat(Date d, String... format) {
		String f = "yyyyMMddHHmmss";
		if (format.length != 0) {
			f = format[0];
		}
		return new SimpleDateFormat(f).format(d);
	}

	/**
	 * 将源日期字符串按照指定的格式转成目标日期字符串
	 * 
	 * @param sourceDateStr
	 *            源日期字符串
	 * @param sourceFormat
	 *            源日期字符串的格式
	 * @param distFormat
	 *            目标日期字符串的格式
	 * @return 格式化的日期字符串
	 */
	public static String getDateFormat(String sourceDateStr,
			String sourceFormat, String distFormat) {
		// 目标日期字符串
		String distDateStr = "";

		if (StringUtils.isEmpty(sourceFormat)) {
			sourceFormat = FULL_TIME_WITH_OUT_SEPARATOR;
		}
		if (StringUtils.isEmpty(distFormat)) {
			distFormat = FULL_TIME_WITH_COMMON_SEPARATOR;
		}

		SimpleDateFormat sdf = new SimpleDateFormat(sourceFormat);
		try {
			Date distDate = sdf.parse(sourceDateStr);

			sdf = null;
			sdf = new SimpleDateFormat(distFormat);
			distDateStr = sdf.format(distDate);

		} catch (ParseException e) {
			LogWriter.error("日期格式化出错", e);
		}

		return distDateStr;
	}

	public static String addDays(String t1, int days) {
		DateFormat format = new SimpleDateFormat("yyyyMMdd");
		Date d1 = null;
		try {
			d1 = format.parse(t1);
		} catch (ParseException e) {
			return "";
		}
		Calendar c1 = Calendar.getInstance();
		c1.setTime(d1);
		c1.add(Calendar.DAY_OF_MONTH, days);
		return format.format(c1.getTime());
	}

	public static String getLastDayOfPreMon(String t1) {
		t1 = t1.substring(0, 6) + "01";
		return addDays(t1, -1);
	}

	public static String getPreMon(String t1) {
		return getLastDayOfPreMon(t1).substring(4, 6);
	}

	public static String subMin(int min) {
		DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		Calendar c1 = Calendar.getInstance();
		c1.add(Calendar.MINUTE, -1 * min);
		return format.format(c1.getTime());
	}

	/**
	 * @description 增加日期天数
	 * @param date
	 *            日期
	 * @param days
	 *            天数
	 * @return String
	 */
	public static String DateAddDays(String date, int days) {
		DateFormat df = new SimpleDateFormat("yyyyMMdd");
		try {
			Date d1 = df.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d1);
			cal.add(Calendar.DATE, days);
			return df.format(cal.getTime());
		} catch (Exception e) {
			LogWriter.error("日期增加出错，默认当天");
			return df.format(new Date());
		}
	}

	public static String DateAddMonth(String date, int month) {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		try {
			Date d1 = df.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d1);
			cal.add(Calendar.MONTH, month);
			return df.format(cal.getTime());
		} catch (Exception e) {
			LogWriter.error("出错，默认当天");
			return df.format(new Date());
		}
	}


	public static String dateAddMonth(String date, int month, String pattern) {
		if (StringUtil.isEmpty(pattern)) {
			pattern = "yyyy-MM-dd";
		}
		DateFormat df = new SimpleDateFormat(pattern);
		try {
			Date d1 = df.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d1);
			cal.add(Calendar.MONTH, month);
			return df.format(cal.getTime());
		} catch (Exception e) {
			LogWriter.error("出错，默认当天");
			return df.format(new Date());
		}
	}

	public static String getHalfDate() {
		GregorianCalendar gr = new GregorianCalendar();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		gr.set(GregorianCalendar.YEAR, GregorianCalendar.MONDAY - 6,
				GregorianCalendar.DATE);
		// 半年前的时间
		String di = null;
		di = sdf.format(gr.getTime());
		return di;
	}

	
	
	/**
	 * 当前季度的开始时间，即2012-01-1 00:00:00
	 * 
	 * @return
	 */
	public static String getCurrentQuarterStartTime() {
		Calendar c = Calendar.getInstance();
		int currentMonth = c.get(Calendar.MONTH) + 1;
		String now = null;
		SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			if (currentMonth >= 1 && currentMonth <= 3)
				c.set(Calendar.MONTH, 0);
			else if (currentMonth >= 4 && currentMonth <= 6)
				c.set(Calendar.MONTH, 3);
			else if (currentMonth >= 7 && currentMonth <= 9)
				c.set(Calendar.MONTH, 4);
			else if (currentMonth >= 10 && currentMonth <= 12)
				c.set(Calendar.MONTH, 9);
			c.set(Calendar.DATE, 1);
			now = shortSdf.format(c.getTime());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return now;
	}
	
	public static int daysBetweenDates2(Date newDate, Date oldDate)
	{
		int days = 0;
		Calendar calo = Calendar.getInstance();
		Calendar caln = Calendar.getInstance();
		calo.setTime(oldDate);
		caln.setTime(newDate);
		int oday = calo.get(Calendar.DAY_OF_YEAR);
		int nyear = caln.get(Calendar.YEAR);
		int oyear = calo.get(Calendar.YEAR);
		while (nyear > oyear)
		{
			calo.set(Calendar.MONTH, 11);
			calo.set(Calendar.DATE, 31);
			days = days + calo.get(Calendar.DAY_OF_YEAR);
			oyear = oyear + 1;
			calo.set(Calendar.YEAR, oyear);
		}
		int nday = caln.get(Calendar.DAY_OF_YEAR);
		days = days + nday - oday;

		return days;
	}
	
	//20151216 计算天数差
	public static int daysBetweenDates(Date newDate, Date oldDate)
	{
		int days = 0;
        if(newDate.compareTo(oldDate)>=0){
        	days=daysBetweenDates2(newDate,oldDate);
        }else{
        	days=-daysBetweenDates2(oldDate,newDate);
        }
		return days;
	}
	
	
	/**
	 * 
	 * Description: 格式化交易日期
	 * 
	 * @param txDateString
	 * @param formateStr
	 * @return :
	 */
	public static Date getFormatTxDate(String txDateString, String formateStr) {

		SimpleDateFormat format = new SimpleDateFormat(formateStr);
		Date date = null;
		try {
			date = format.parse(txDateString);
		} catch (ParseException e) {
			return null;
		}
		return date;
	}
	
	/**
	 * @description 获取当前系统时间
	 * @return 当前系统时间
	 */
	public static Date getNowDate() {
		Date currentTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		ParsePosition pos = new ParsePosition(0);
		Date currentTime_2 = formatter.parse(dateString, pos);
		return currentTime_2;
	}
	
	/**
	 * 获取-n天前的时间
	 * @param today
	 * @param n
	 * @return
	 */
	public static Date getSomeDaysLater(Date today, int n) {
		Date currentTime = new Date();//获取当前时间    
        return new Date(today.getTime() + 24L*3600*1000*n);
	}
	
	public static String getFormatString(Date date, String formateStr) {
		String tDate = new SimpleDateFormat(formateStr).format(date);
		return tDate;
	}
	
	/**
	 * @description 按指定格式获取指定时间字符串
	 * @param date
	 *            待转换日期
	 * @param patterns
	 *            时间格式 e.g yyyy-MM-dd HH:mm:ss
	 * @return 返回指定格式指定时间字符串
	 */
	public static String getDateStr(Date date, String patterns) {
		if (date == null) {
			date = new Date();
		}
		SimpleDateFormat formatter = new SimpleDateFormat(patterns);
		String dateString = formatter.format(date);
		return dateString;
	}
	
	/**
	 * @description 将待转换字符串日期根据指定格式转换为日期类型, 日期字符串涵盖的日期格式需要与指定的格式一致
	 * @param strDate
	 *            待转换字符串
	 * @param patterns
	 *            日期转换格式
	 * @return 返回指定字符串日期根据指定格式转换后的日期类型对象;
	 */
	public static Date getStrDate(String strDate, String patterns) {
		if (StringUtils.isEmpty(strDate)) {
			return new Date();
		}
		SimpleDateFormat formatter = new SimpleDateFormat(patterns);
		ParsePosition pos = new ParsePosition(0);
		Date strtodate = formatter.parse(strDate, pos);
		return strtodate;
	}
	
	/**获取指定日期的前(后)n个小时**/
	public static Date getPrexHourTime(Date d, int n) {
		Calendar c = Calendar.getInstance();
		c.setTime(d);
		c.add(Calendar.HOUR, n);
		return c.getTime();
	}
	
	public static Date getNextMinsDate(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, 1);
        return cal.getTime();
    }
	
	/**
	 * 获取当月第一天
	 * @return
	 */
	public static String getFirstCurrentMonthDay(){
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar c = Calendar.getInstance();    
		c.add(Calendar.MONTH, 0);
		c.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天 
		String first = format.format(c.getTime());
		return first;
	}
	
	/**
	 * 获取当月最后一天
	 * @return
	 */
	public static String getLastCurrentMonthDay(){
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		 Calendar ca = Calendar.getInstance();    
		 ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));  
		 String last = format.format(ca.getTime());
		 return last;
	}
	
	/**
	 * 获得当前是一年中的第几周
	 * @return
	 */
	public static int getCurrenWeekNum(){
		 Calendar c=Calendar.getInstance();
	        int i = c.get(Calendar.WEEK_OF_YEAR);
	       return i;
	}
	
	public static String getCurrentDateFor14(){
		return getCurrentDate("yyyyMMddHHmmss");
	}
	
	/**
	 * 获取当前时间的前一天
	 * @return
	 */
	public static String getCurrenDayBefore() {
        Calendar c = Calendar.getInstance();  
        c.setTime(new Date());  
        int day = c.get(Calendar.DATE);  
        c.set(Calendar.DATE, day - 1);  
        return new SimpleDateFormat("yyyyMMdd").format(c.getTime());  
    }
	
//	public static void main(String[] args) {
//		System.out.println(getCurrenDayBefore());
//	}
	
	/**
	 * 方法注释： 根据字符日期及模板获取日期
	 * 修改内容： 新增
	 * 修改时间： 2017年8月29日 上午11:57:05
	 * @param date
	 * @param format
	 * @return
	 */
	public static Date getDateByFormatDt(String date, String format){
		if (StringUtils.isNotEmpty(date) && StringUtils.isNotEmpty(format)) {
			try {
				SimpleDateFormat fmt = new SimpleDateFormat(format);
				return fmt.parse(date);
			} catch (ParseException e) {
				e.printStackTrace();
				return null;
			}
		}
		return null;
	}
	
	/**
	 * 方法注释： 获取两个日期相差几日
	 * 修改内容： 新增
	 * 修改时间： 2017年9月26日 下午2:52:06
	 * @param startDate
	 * @param enDate
	 * @return -1 时间范围不正确
	 */
	public static int getTimeDifferenceDay(Date startDate, Date enDate){
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(startDate);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(enDate);
		int day1= cal1.get(Calendar.DAY_OF_YEAR);
		int day2 = cal2.get(Calendar.DAY_OF_YEAR);
		int year1 = cal1.get(Calendar.YEAR);
		int year2 = cal2.get(Calendar.YEAR);
		if(year1 != year2) {
			int timeDistance = 0 ;
			for(int i = year1 ; i < year2 ; i ++) {
				timeDistance += 365;
				if(i%4==0 && i%100!=0 || i%400==0) { // 闰年
					timeDistance += 1;
				}
			}
			return timeDistance + (day2-day1);
		}
		return day2-day1;
	}
	
	/**
	 * 方法注释： 验证yyyyMMddHHmmss日志是否为正确类型
	 * 修改内容： 新增
	 * 修改时间： 2017年9月26日 上午11:08:41
	 * @param datetime
	 * @return
	 */
	public static boolean isFormatDatetime(String datetime){
		if (StringUtils.isNotBlank(datetime)
				&& datetime.matches(FORMAT_DATETIME_REGEX)) {
			return true;
		}
		return false;
	}
	
	//获取年
	public static String getCurrentYear(){
		Date currentTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy");
		String dateString = formatter.format(currentTime);
		return dateString;
	}

	/**
	 * @description 按指定格式获取指定时间字符串
	 * @param date
	 *            待转换日期
	 * @param patterns
	 *            时间格式 e.g yyyy-MM-dd HH:mm:ss
	 * @return 返回指定格式指定时间字符串
	 */
	public static String formatDateStrByGMT(Date date, String patterns) {
		if (date == null) {
			date = new Date();
		}
		SimpleDateFormat formatter = new SimpleDateFormat(patterns);
		formatter.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
		String dateString = formatter.format(date);
		return dateString;
	}

	public static Date formatDateByGMT(String strDate, String patterns) {
		if (org.apache.commons.lang3.StringUtils.isBlank(strDate)) {
			return new Date();
		}
		SimpleDateFormat formatter = new SimpleDateFormat(patterns);
		formatter.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
		ParsePosition pos = new ParsePosition(0);
		Date strtodate = formatter.parse(strDate, pos);
		return strtodate;
	}
	
}
