package com.fuiou.dipsMq.consts;

import org.springframework.util.StringUtils;

import java.util.*;

public class OrderTypeConst {
	public static final String HAND_BURSH_START_WITH = "SS"; //手刷开头 ，用于订单类型的判断

	public static final String BRUSHCARD = "BRUSHCARD";

	public static final String SWT_BALANCE = "SS01"; //余额查询

	public static final String PLACE_ORDER = "SS02"; //下单-消费

	public static final String CONSUME = "SS03"; //消费

	public static final String CONSUME_CANCEL = "SS04"; //消费撤销

	public static final String RETURN_GOODS = "SS05"; //退货

	public static List<String> orderTypeList = new ArrayList<String>();

	public static final String PRE_AUTH_WX = "WXPA"; //微信预授权

	public static final String PRE_AUTH_AL = "ALPA"; //支付宝预授权

	public static final String PRE_AUTH_WX_FINISH = "WXPF"; //微信预授权完成

	public static final String PRE_AUTH_AL_FINISH = "ALPF"; //支付宝预授权完成

	public static final String PRE_AUTH_WX_CANCEL = "WXPC"; //微信预授权撤销

	public static final String PRE_AUTH_AL_CANCEL = "ALPC"; //支付宝预授权撤销
	/**
	 *微信预授权完成退款
	 */
	public static final String PRE_AUTH_WX_FINISH_CANCEL = "WXPFC";

	/**
	 *支付宝预授权完成退款
	 */
	public static final String PRE_AUTH_AL_FINISH_CANCEL = "ALPFC";

	public static final String ALIPAY = "ALIPAY";
	public static final String WECHAT = "WECHAT";
	public static final String BESTPAY = "BESTPAY";
	public static final String UNIONPAY = "UNIONPAY";
	public static final String WXZL = "WXZL";
	public static final String ALKB = "ALKB";
	/**
	 * <AUTHOR>
	 * @Date 2021/6/2 10:27
	 * @Description 数字货币订单类型
	 **/
	public static final String DIGICCY = "DIGICCY";

	/**
	 * <AUTHOR>
	 * @Description 京东白条
	 * @Date 2023/12/04
	 */
	public static final String JDBT = "JDBT";

	/**
	 * <AUTHOR>
	 * @Description 预付卡
	 * @Date 2024/11/15
	 */
	public static final String YFK = "YFK";


	/**
	 * <AUTHOR>
	 * @Date 2022/3/30
	 * @Description java刷卡交易--走 终端刷卡服务 的交易
	 */
	public static final String CARD_BRUSH_CONSUME="CARD01";//刷卡消费
	public static final String CARD_BRUSH_REFUND="CARD02";//刷卡退货/退款
	public static final String CARD_BRUSH_CANCEL="CARD02";//消费撤销或退货/退款（退货撤销保持一致）

	/**
	 * 所有支付类型
	 */
	public static final String ALL_ORDER_TYPE = "****";


	static{
		orderTypeList.add(PLACE_ORDER);
		orderTypeList.add(CONSUME);
		orderTypeList.add(CONSUME_CANCEL);
		orderTypeList.add(RETURN_GOODS);
	}

	public static final boolean isPreOrder(String orderType){
		if(StringUtils.isEmpty(orderType)){
			return false;
		}
		if(PRE_AUTH_WX.equals(orderType)
				|| PRE_AUTH_AL.equals(orderType)
				|| PRE_AUTH_WX_FINISH.equals(orderType)
				|| PRE_AUTH_AL_FINISH.equals(orderType)
				|| PRE_AUTH_WX_CANCEL.equals(orderType)
				|| PRE_AUTH_AL_CANCEL.equals(orderType)
				|| PRE_AUTH_WX_FINISH_CANCEL.equals(orderType)
				|| PRE_AUTH_AL_FINISH_CANCEL.equals(orderType)
		){
			return true;
		}
		return false;
	}


	/**
	 * 现金支付
	 */
	public enum CashOrderType {
		/**
		 * CASH-现金支付正交易
		 */
		CASH("CASH"),
		/**
		 * CASHTK-现金支付反交易
		 */
		CASHTK("CASHTK");
		private String value;
		public String getValue() {
			return value;
		}
		private CashOrderType(String value) {
			this.value = value;
		}
	}

	/**
	 * 园区卡、预付卡
	 */
	public enum PrepaidCardType {
		/**
		 * YQK-园区卡
		 */
		YQK("YQK"),
		/**
		 * YFK-预付卡
		 */
		YFK("YFK"),
		/**
		 * CZK-储值卡
		 */
		CZK("CZK");
		private String value;
		public String getValue() {
			return value;
		}
		private PrepaidCardType(String value) {
			this.value = value;
		}
	}

	/**
	 * 交易类型,正交易反交易
	 */
	public enum TradeType {
		/**
		 * 00-正交易
		 */
		POSITIVE("00"),
		/**
		 * 01-反交易
		 */
		NEGATIVE("01");
		private String value;
		public String getValue() {
			return value;
		}
		private TradeType(String value) {
			this.value = value;
		}
	}


	public static final Set<String> ALIPAY_ALL =new HashSet<>(Arrays.asList( "ALIPAY","FWC","ANT",PRE_AUTH_AL));

	/**
	 * @Description: 支持保存付款卡号的订单类型
	 * @Author: 程军
	 * @Date: 2024/3/15 15:32
	 */
	public static final Set<String> SUPPORT_SAVE_PAYER_CARD_NO_ORDER_TYPES =new HashSet<>(Arrays.asList( OrderPayConsts.UNIONPAY, OrderPayConsts.INSTAL));


	/**
	 * @Description: 抖音支付
	 * @Author: 程军
	 * @Date: 2025/2/13 16:06
	 */
	public static final String DYPAY = "DYPAY";

	/**
	 * <AUTHOR>
	 * @Date 2025-04-22 17:20:56
	 * @Description 富友钱包订单类型
	 **/
	public static final String WALLETPAY = "WalletPay";

	//退款busiCd
	public static List<String> REVERSE_CODE_FIX_REFUND = new ArrayList<>();

	static {
		REVERSE_CODE_FIX_REFUND.add("CX02");//消费撤销
		REVERSE_CODE_FIX_REFUND.add("CX00");//预授权撤销
		REVERSE_CODE_FIX_REFUND.add("CX01");//预授权完成撤销
		REVERSE_CODE_FIX_REFUND.add("CX16");//消费分期撤销
		REVERSE_CODE_FIX_REFUND.add("CX68");//DCC消费撤销
		REVERSE_CODE_FIX_REFUND.add("CX69");//DCC预授权完成撤销
		REVERSE_CODE_FIX_REFUND.add("CX70");//EDC消费撤销
		REVERSE_CODE_FIX_REFUND.add("CX71");//EDC预授权完成撤销
		REVERSE_CODE_FIX_REFUND.add("TX01");//预授权完成
		REVERSE_CODE_FIX_REFUND.add("TX03");//退款
		REVERSE_CODE_FIX_REFUND.add("TX51");
		REVERSE_CODE_FIX_REFUND.add("CX86");//现金退款
	}

}
