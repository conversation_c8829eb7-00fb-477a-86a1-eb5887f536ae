# 1. 日志自定义注解，

``

```
    @LogAnnotation(value = "客户管理", methodName = "保存")
    public int insert(DipsCustomer record)throws MySQLIntegrityConstraintViolationException {
        return dipsCustomerMapper.insert(record);
    }
```

# 2. controller层接口权限校验,



1.  接口token通过http header参数token传递，避免侵入业务对象实体
2. controller层增加注解`@RequiresPermissions(value = "customer/save",name = "客户管理-保存")`
  1. 如果接口开放式接口，即不需要权限校验，配置参数,  permissio_open = true，示例 `@RequiresPermissions(value = "customer/detail",name = "客户管理-详情",  permissio_open = true)`


``

```
    @PostMapping("save")
    @ResponseBody
    @RequiresPermissions(value = "customer/save",name = "客户管理-保存")
    @LogAnnotation(value = "客户管理", methodName = "保存")
    public ResponseEntity save(@Valid @RequestBody DipsCustomer customer) throws Exception {
        int result = dipsCustomerService.insert(customer);
        return  ResponseEntityFactory.ok(customer);
    }
```



# 3.Controller层已配置统一异常处理，默认统一格式返回异常，一般不需要单独捕获异常



# 4. 示例demo

com.fuiou.dips.controller.CustomerController

## 4.1 客户详情：

​	请求url:http://localhost:8080/customer/detail/6

​	请求参数：无

​	响应报文：

```
{
    "code": "0000",
    "data": {
        "createTime": 1746521453000,
        "customerName": "string",
        "customerSource": "string",
        "mchntCd": "1",
        "phone": "string",
        "remark": "string",
        "reserved2": "",
        "reserved3": "",
        "rowId": 6,
        "storeId": "string",
        "updateTime": 1746521453000
    },
    "msg": "成功"
}
```

## 4.2 客户-新增：

​	请求url:http://localhost:8080/customer/save

​	请求参数：

```
{
        "createTime": 1746521453000,
        "customerName": "string",
        "mchntCd": "string",
        "phone": "13554255130",
        "remark": "string",
        "customer_source":"string",
        "reserved1": "string",
        "reserved2": "string",
        "reserved3": "string",
        "storeId": "string",
        "updateTime": "2025-03-30"
    }
```

​	响应报文：

```
{
    "code": "0000",
    "msg": "成功"
}
```

## 4.3 客户-列表分页查询：

​	请求url:http://localhost:8080/customer/list

​	请求参数：

```
{
        "mchntCd": "string",
       	"limit":1,
        "page":1
    }
```

​	响应报文：

```
{
    "code": "0000",
    "data": {
        "list": [
            {
                "createTime": 1746521453000,
                "customerName": "string",
                "customerSource": "123",
                "mchntCd": "string",
                "phone": "13554255130",
                "remark": "string",
                "reserved1": "string",
                "reserved2": "string",
                "reserved3": "string",
                "rowId": 9,
                "storeId": "string",
                "updateTime": 1746521453000
            }
        ],
        "total": 6
    },
    "msg": "成功"
}
```

