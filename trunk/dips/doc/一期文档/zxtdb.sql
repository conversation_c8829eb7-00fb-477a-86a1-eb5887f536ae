
CREATE TABLE t_dips_mchnt_cfg (
                                  row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  open_status char(1) NOT NULL DEFAULT '0' COMMENT '开通状态 1 开启 0 未开启',
                                  operator varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
                                  mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                  reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                  reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                  reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                  PRIMARY KEY (row_id),
                                  UNIQUE KEY idx_t_dips_mchnt_cfg_1 (mchnt_cd),
                                  KEY idx_t_dips_mchnt_cfg_2 (update_time)
) ENGINE = InnoDB COMMENT = '装修通商户配置表'



CREATE TABLE t_dips_project (
                                row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                project_name varchar(200) NOT NULL DEFAULT '' COMMENT '项目名称',
                                mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                store_id varchar(20) NOT NULL DEFAULT '' COMMENT '门店id',
                                project_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '项目总金额，单位分',
                                project_st char(1) NOT NULL DEFAULT '0' COMMENT '项目进度 1进行中 2 已关闭 9 已完成 ',
                                current_stage_no varchar(32) NOT NULL DEFAULT '' COMMENT '当前阶段编号',
                                lock_flag char(1) NOT NULL DEFAULT '0' COMMENT '锁定标记 0 未锁定 1锁定',
                                remark varchar(1000) NOT NULL DEFAULT '' COMMENT '项目备注',
                                create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                creator_login_id varchar(50) not null default '' comment '创建人login_id',
                                reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                PRIMARY KEY (row_id),
                                UNIQUE KEY idx_t_dips_project_1 (project_no),
                                KEY idx_t_dips_project_2 (mchnt_cd),
                                KEY idx_t_dips_project_3 (update_time)
) ENGINE = InnoDB COMMENT = '装修通项目表'


alter table t_dips_project
    add    ;




CREATE TABLE t_dips_project_customer (
                                         row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                         mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                         phone varchar(50) NOT NULL DEFAULT '' COMMENT '手机号（商户号加手机号确定客户唯一）',
                                         create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                         reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                         reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                         reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                         PRIMARY KEY (row_id),
                                         UNIQUE KEY idx_t_dips_project_customer_1 (project_no,mchnt_cd,phone),
                                         KEY idx_t_dips_project_customer_2 (mchnt_cd),
                                         KEY idx_t_dips_project_customer_3 (update_time),
                                         KEY idx_t_dips_project_customer_4 (project_no)
) ENGINE = InnoDB COMMENT = '装修通项目客户表'




CREATE TABLE t_dips_project_employee (
                                         row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                         mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                         employee_login_id varchar(50) NOT NULL DEFAULT '' COMMENT '用户ID，对应t_mchnt_acnt_login_inf  的login_id',
                                         create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                         reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                         reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                         reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                         PRIMARY KEY (row_id),
                                         UNIQUE KEY idx_t_dips_project_employee_1 (project_no,mchnt_cd,employee_login_id),
                                         KEY idx_t_dips_project_employee_2 (mchnt_cd),
                                         KEY idx_t_dips_project_employee_3 (update_time),
                                         KEY idx_t_dips_project_employee_4 (project_no)
) ENGINE = InnoDB COMMENT = '装修通项目成员表'



CREATE TABLE t_dips_project_stage (
                                      row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                      mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                      stage_no varchar(32) NOT NULL DEFAULT '' COMMENT '阶段编号',
                                      stage_order int(10) NOT NULL DEFAULT 1 COMMENT '阶段顺序',
                                      stage_name varchar(100) NOT NULL DEFAULT '' COMMENT '阶段名称',
                                      stage_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '阶段应收金额，单位分',
                                      stage_actual_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '阶段已收款金额，单位分',
                                      refund_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '阶段退款总金额，单位分',
                                      stage_st char(1) NOT NULL DEFAULT '0' COMMENT '阶段进度 0 未开始 1进行中 9 已完成收款',
                                      lock_flag char(1) NOT NULL DEFAULT '0' COMMENT '锁定标记 0 未锁定 1锁定',
                                      create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                      reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                      reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                      reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                      is_delete  TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，默认0-正常，1-已删除',
                                      PRIMARY KEY (row_id),
                                      UNIQUE KEY idx_t_dips_project_stage_1 (project_no,mchnt_cd,stage_no),
                                      KEY idx_t_dips_project_stage_2 (mchnt_cd),
                                      KEY idx_t_dips_project_stage_3 (update_time),
                                      KEY idx_t_dips_project_stage_4 (project_no)
) ENGINE = InnoDB COMMENT = '装修通项目阶段付款表'


CREATE TABLE t_dips_project_stage_change (
                                             row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             log_no varchar(32) NOT NULL DEFAULT '' COMMENT '记录流水号',
                                             project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                             mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                             change_content varchar(500) NOT NULL DEFAULT '' COMMENT '变更内容',
                                             create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                             reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                             reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                             reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                             PRIMARY KEY (row_id),
                                             UNIQUE KEY idx_t_dips_project_stage_change_1 (log_no),
                                             KEY idx_t_dips_project_stage_change_2 (mchnt_cd),
                                             KEY idx_t_dips_project_stage_change_3 (update_time),
                                             KEY idx_t_dips_project_stage_change_4 (project_no)
) ENGINE = InnoDB COMMENT = '装修通项目阶段付款变更记录表'



CREATE TABLE t_dips_txn_log (
                                row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                order_no varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
                                trade_dt varchar(8) NOT NULL DEFAULT '' COMMENT '支付日期 yyyyMMdd',
                                project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                stage_no varchar(32) NOT NULL DEFAULT '' COMMENT '阶段编号',
                                store_id varchar(20) NOT NULL DEFAULT '' COMMENT '门店id',
                                order_type varchar(20) NOT NULL DEFAULT '' COMMENT 'JSAPI:微信主扫，FWC:支付宝主扫',
                                trade_type char(1) NOT NULL DEFAULT '1' COMMENT '正反交易类型  1 正交易 2 反交易',
                                order_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '订单金额，单位分',
                                fee_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '手续费，单位分',
                                coupon_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '优惠金额，单位分',
                                pay_state char(2) NOT NULL DEFAULT '00' COMMENT '00:未支付状态,01:已支付02:支付失败,03:已退款,04:支付中,05:已撤销,06:已退货,07:超时,11:预授权完成,12:预授权完成退款',
                                refund_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '已退款金额，单位分',
                                src_order_no varchar(50) NOT NULL DEFAULT '' COMMENT '原订单号，退款订单需记录',
                                resp_code varchar(20) NOT NULL DEFAULT '' COMMENT '应答码',
                                resp_msg varchar(100) NOT NULL DEFAULT '' COMMENT '应答描述',
                                fy_term_id varchar(20) NOT NULL DEFAULT '' COMMENT '终端号',
                                fy_fettle_dt varchar(8) NOT NULL DEFAULT '' COMMENT '清算日期 yyyyMMdd',
                                fy_trace_no varchar(20) NOT NULL DEFAULT '' COMMENT '跟踪号',
                                channel_order_id varchar(32) NOT NULL DEFAULT '' COMMENT '条码流水',
                                transaction_id varchar(32) NOT NULL DEFAULT '' COMMENT '渠道订单号',
                                lock_flag char(1) NOT NULL DEFAULT '0' COMMENT '锁定标记 0 未锁定 1锁定',
                                goods_des varchar(1000) NOT NULL DEFAULT '' COMMENT '商品描述',
                                open_id varchar(100) NOT NULL DEFAULT '' COMMENT 'openid',
                                login_id varchar(50) NOT NULL DEFAULT '' COMMENT '订单创建人loginId',
                                create_user_mobile varchar(20) NOT NULL DEFAULT '' COMMENT '订单创建人手机号',
                                create_user_name varchar(200) NOT NULL DEFAULT '' COMMENT '订单创建人姓名',
                                create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                pay_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '支付完成时间',
                                reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                reserved4 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved5 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved6 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved7 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved8 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved9 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                reserved10 varchar(20) NOT NULL DEFAULT '' COMMENT '备注',
                                PRIMARY KEY (row_id),
                                UNIQUE KEY idx_t_dips_txn_log_1 (mchnt_cd,order_no),
                                KEY idx_t_dips_txn_log_2 (mchnt_cd),
                                KEY idx_t_dips_txn_log_3 (update_time),
                                KEY idx_t_dips_txn_log_4 (pay_time),
                                KEY idx_t_dips_txn_log_5 (order_no),
                                KEY idx_t_dips_txn_log_6 (project_no),
                                KEY idx_t_dips_txn_log_7 (src_order_no),
                                KEY idx_t_dips_txn_log_8 (trade_dt)
) ENGINE = InnoDB COMMENT = '装修通交易订单表'



CREATE TABLE t_dips_qrcode_inf (
                                   row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   qrcode_token varchar(64) NOT NULL DEFAULT '' COMMENT '收款码token',
                                   project_no varchar(32) NOT NULL DEFAULT '' COMMENT '项目编号',
                                   stage_no varchar(32) NOT NULL DEFAULT '' COMMENT '阶段编号',
                                   mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                   qrcode_type char(1) NOT NULL DEFAULT '' COMMENT '类型 ： 1 收款码 2 分享小卡片 3 刷卡支付 4 业主自助缴费',
                                   order_state char(1) NOT NULL DEFAULT '0' COMMENT '0 初始 1 完成 2 失效',
                                   order_no varchar(50) NOT NULL DEFAULT '' COMMENT '交易订单号',
                                   order_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '订单金额，单位分',
                                   notify_url varchar(256) NOT NULL DEFAULT '' COMMENT '回调地址',
                                   create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                   expire_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失效时间',
                                   addn_inf varchar(100) NOT NULL DEFAULT '' COMMENT '附加数据',
                                   goods_des varchar(200) NOT NULL DEFAULT '' COMMENT '商品描述',
                                   lock_flag char(1) NOT NULL DEFAULT '0' COMMENT '锁定标记 0 未锁定 1锁定',
                                   reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                   reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                   reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                   PRIMARY KEY (row_id),
                                   UNIQUE KEY idx_t_dips_qrcode_inf_1 (qrcode_token),
                                   KEY idx_t_dips_qrcode_inf_2 (mchnt_cd),
                                   KEY idx_t_dips_qrcode_inf_3 (update_time),
                                   KEY idx_t_dips_qrcode_inf_4 (project_no)
) ENGINE = InnoDB COMMENT = '装修通下单信息表'


CREATE TABLE t_dips_user_inf (
                                 row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 login_id varchar(100) NOT NULL DEFAULT '' COMMENT '用户账号，wmp登录账号或手机号',
                                 user_type char(2) NOT NULL DEFAULT '' COMMENT '用户类型 01 商户 02 客户',
                                 open_id varchar(100) NOT NULL DEFAULT '' COMMENT 'openid',
                                 create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                 last_login_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间',
                                 reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                 reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                 reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                 PRIMARY KEY (row_id),
                                 UNIQUE KEY idx_t_dips_user_inf_1 (login_id, user_type),
                                 KEY idx_t_dips_user_inf_2 (open_id),
                                 KEY idx_t_dips_user_inf_3 (update_time)
) ENGINE = InnoDB COMMENT = '装修通用户表'



CREATE TABLE t_dips_customer (
                                 row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                 customer_name varchar(100) NOT NULL DEFAULT '' COMMENT '客户姓名',
                                 phone varchar(50) NOT NULL DEFAULT '' COMMENT '手机号（商户号加手机号确定客户唯一）',
                                 store_id varchar(20) NOT NULL DEFAULT '' COMMENT '门店id',
                                 customer_source varchar(200) NOT NULL DEFAULT '' COMMENT '客户来源',
                                 remark varchar(1000) NOT NULL DEFAULT '' COMMENT '客户备注',
                                 create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                 follow_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '跟进时间',
                                 reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                 reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                 reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                 login_id  varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人，登录LOGIN_ID',
                                 PRIMARY KEY (row_id),
                                 UNIQUE KEY idx_t_dips_mchnt_cfg_1 (mchnt_cd, phone),
                                 KEY idx_t_dips_customer_2 (mchnt_cd),
                                 KEY idx_t_dips_customer_3 (update_time),
                                 KEY idx_t_dips_customer_4 (phone)
) ENGINE = InnoDB COMMENT = '装修通客户表'



CREATE TABLE t_dips_customer_follow (
                                        row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        log_no varchar(32) NOT NULL DEFAULT '' COMMENT '记录编号',
                                        mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                        phone varchar(50) NOT NULL DEFAULT '' COMMENT '手机号（商户号加手机号确定客户唯一）',
                                        remark varchar(500) NOT NULL DEFAULT '' COMMENT '跟进记录',
                                        creater varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
                                        follow_man varchar(50) NOT NULL DEFAULT '' COMMENT '跟进人',
                                        create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间（最后操作时间）',
                                        reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                        reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                        reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                        PRIMARY KEY (row_id),
                                        UNIQUE KEY idx_t_dips_customer_follow_1 (log_no, mchnt_cd, phone),
                                        KEY idx_t_dips_customer_follow_2 (mchnt_cd),
                                        KEY idx_t_dips_customer_follow_3 (update_time)
) ENGINE = InnoDB COMMENT = '装修通客户跟进记录表'



CREATE TABLE t_dips_msg_inf (
                                row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                msg_no varchar(32) NOT NULL DEFAULT '' COMMENT '消息流水号',
                                msg_title varchar(100) NOT NULL DEFAULT '' COMMENT '消息标题',
                                msg_type char(2) NOT NULL DEFAULT '' COMMENT '消息类型 01 支付成功通知 02 项目金额变动通知 03 退款通知',
                                mchnt_cd char(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                order_amt decimal(16, 0) NOT NULL DEFAULT 0 COMMENT '订单金额，单位分',
                                operator varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
                                msg_content varchar(1000) NOT NULL DEFAULT '' COMMENT '消息内容',
                                create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                PRIMARY KEY (row_id),
                                UNIQUE KEY idx_t_dips_msg_inf_1 (msg_no),
                                KEY idx_t_dips_msg_inf_2 (mchnt_cd),
                                KEY idx_t_dips_msg_inf_3 (update_time)
) ENGINE = InnoDB COMMENT = '装修通消息表'


CREATE TABLE t_dips_msg_user (
                                 row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 msg_no varchar(32) NOT NULL DEFAULT '' COMMENT '消息流水号',
                                 login_id varchar(100) NOT NULL DEFAULT '' COMMENT '用户账号，wmp登录账号或手机号',
                                 user_type char(2) NOT NULL DEFAULT '' COMMENT '用户类型 01 商户 02 客户',
                                 read_flag char(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0 未读 1 已读',
                                 create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                 reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                 reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                 reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                 PRIMARY KEY (row_id),
                                 UNIQUE KEY idx_t_dips_msg_user_1 (msg_no,login_id,user_type),
                                 KEY idx_t_dips_msg_user_2 (update_time),
                                 KEY idx_t_dips_msg_user_3 (login_id)
) ENGINE = InnoDB COMMENT = '装修通消息用户表'

CREATE TABLE t_dips_mchnt_term_inf (
                                       row_id bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       mchnt_cd varchar(15) NOT NULL DEFAULT '' COMMENT '商户号',
                                       store_id varchar(20) NOT NULL DEFAULT '' COMMENT '门店号',
                                       term_id varchar(20) NOT NULL DEFAULT '' COMMENT '终端号',
                                       term_model varchar(20) NOT NULL DEFAULT '' COMMENT '终端型号',
                                       term_type varchar(20) NOT NULL DEFAULT '' COMMENT '终端大类',
                                       term_sn varchar(30) NOT NULL DEFAULT '' COMMENT '终端序列号',
                                       order_no varchar(50) NOT NULL DEFAULT '' COMMENT '最后交易订单号',
                                       create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                       reserved1 varchar(20) NOT NULL DEFAULT '' COMMENT '备注1',
                                       reserved2 varchar(60) NOT NULL DEFAULT '' COMMENT '备注2',
                                       reserved3 varchar(512) NOT NULL DEFAULT '' COMMENT '备注3',
                                       PRIMARY KEY (row_id),
                                       UNIQUE KEY idx_t_dips_mchnt_term_inf_1 (mchnt_cd,term_id,store_id),
                                       KEY idx_t_dips_mchnt_term_inf_2 (update_time)
) ENGINE = InnoDB COMMENT = '商户门店交易终端信息'



