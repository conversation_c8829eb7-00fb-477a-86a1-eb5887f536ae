<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.0.xsd   
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context-4.0.xsd   
		http://www.springframework.org/schema/aop 
		http://www.springframework.org/schema/aop/spring-aop-4.0.xsd">

	<!-- 扫描路径下@Service，由spring的context父容器进行初始化以保证事务的增强处理 -->
	<context:component-scan base-package="com.fuiou.dips"
							use-default-filters="false">
		<context:include-filter type="annotation"
								expression="org.springframework.stereotype.Service" />
		<context:include-filter type="annotation"
								expression="org.springframework.stereotype.Component" />
	</context:component-scan>

	<aop:aspectj-autoproxy></aop:aspectj-autoproxy>


	<import resource="db.xml"/>
	<import resource="dubbo.xml"/>
	<import resource="redis.xml"/>

	
	
</beans>