package com.fuiou.dips.enums;

/**
 * 项目枚举类
 *
 * <AUTHOR>
 */
public interface ProjectEnum {

    /**
     * 项目阶段，初始阶段排序值
     */
    int INIT_STAGE_ORDER = 1;

    /**
     * 项目状态
     */
    enum ProjectStEnum {
        ONGOING("1", "进行中"),
        CLOSED("2", "已关闭"),
        COMPLETED("9", "已完成");

        final String state;
        final String msg;

        ProjectStEnum(String state, String msg) {
            this.state = state;
            this.msg = msg;
        }

        public String getState() {
            return state;
        }

        public String getMsg() {
            return msg;
        }
    }

    /**
     * 阶段状态
     */
    enum StageStEnum {
        NON_START("0", "未开始"),
        ONGOING("1", "进行中"),
        COMPLETED("9", "已完成");

        final String state;
        final String msg;

        StageStEnum(String state, String msg) {
            this.state = state;
            this.msg = msg;
        }

        public String getState() {
            return state;
        }

        public String getMsg() {
            return msg;
        }
    }

    /**
     * 付款状态 0 未收款 1 已收款
     */
    enum PaymentStatusEnum {
        NON_PAY(0, "未收款"),
        PAYED(1, "已收款");

        final Integer state;
        final String msg;

        PaymentStatusEnum(Integer state, String msg) {
            this.state = state;
            this.msg = msg;
        }

        public Integer getState() {
            return state;
        }

        public String getMsg() {
            return msg;
        }

    }

}
