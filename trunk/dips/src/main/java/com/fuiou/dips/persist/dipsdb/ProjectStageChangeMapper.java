package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.ProjectStageChange;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目阶段变更记录
 *
 * <AUTHOR>
 * @description 针对表【t_dips_project_stage_change(装修通项目阶段付款变更记录表)】的数据库操作Mapper
 */
public interface ProjectStageChangeMapper {

    int insert(ProjectStageChange record);

    List<ProjectStageChange> selectByProjectNoAndMchntCd(@Param("projectNo") String projectNo,
            @Param("mchntCd") String mchntCd);
}
