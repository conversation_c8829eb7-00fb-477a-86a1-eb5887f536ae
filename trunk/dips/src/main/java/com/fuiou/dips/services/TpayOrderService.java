package com.fuiou.dips.services;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.convert.TpayOrderConvertMapper;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.framework.context.ApplicationContextKeeper;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.*;
import com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo;
import com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfoDTO;
import com.fuiou.dips.persist.beans.tpay.TxnData5Bean;
import com.fuiou.dips.persist.tpaydb.TerminalsOrderInfoMapper;
import com.fuiou.dips.utils.DateUtils;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.StringUtil;
import com.fuiou.dips.utils.TpayParseCashCouponUtil;
import com.fuiou.tpaytransfer.soa.data.TransferData;
import com.fuiou.tpaytransfer.soa.services.TxnLogTransferExecutor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/***
 *tpay 订单表对象
 * @Author: Joker
 * @Date: 2025/5/19 16:25
 */

@Service
public class TpayOrderService {
    private static final Logger log = LoggerFactory.getLogger(TpayOrderService.class);

    @Resource
    private TxnLogTransferExecutor txnLogExecutor;

    @Resource
    private TerminalsOrderInfoMapper terminalsOrderInfoMapper;
    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private ProjectCustomerService projectCustomerService;
    @Resource
    private CustomerService customerService;
    @Resource
    private TpayOrderConvertMapper tpayOrderConvertMapper;


    @LogAnnotation("tpay订单信息转移wosdb")
    public void transfer(String mchntCd, String orderNo, String settleDt) {

        try {

            TransferData transData = terminalsOrderToTransferData(mchntCd, orderNo, settleDt);
            LogWriter.info(String.format("转移wosdb 日表begin=%s", JSONObject.toJSONString(transData)));
            if (transData == null) {
                LogWriter.info("转移wosdb 日表end,参数转换为空");
                return;
            }
            txnLogExecutor.transfer(transData);
        } catch (Exception e) {
            LogWriter.error(this, "tpay订单信息转移wosdb发生异常", e);
        }
    }

    private TransferData terminalsOrderToTransferData(String mchntCd, String orderNo, String settleDt) {
        if (StringUtil.isBlank(mchntCd) || StringUtil.isBlank(orderNo)) {
            LogWriter.info(String.format("转移wosdb 商户号与订单号不能为空"));
            return null;
        }

        TransferData data = new TransferData();
        data.setPlatId("dips");
        data.setMerchantCode(mchntCd);
        data.setOrderNo(orderNo);
        if (StringUtils.isNotBlank(settleDt)) {
            data.setFySettleDt(settleDt);
        }
        return data;
    }


    @LogAnnotation("tpay订单信息新增")
    public int insert(TerminalsOrderInfo record) {
        if (record == null) {
            log.info("tpay订单信息新增失败,参数为空");
            return 0;
        }
        if (OrderTypeEnum.UNKNOW.getOrderType().equals(record.getOrderType())) {
            LogWriter.info(String.format("tpay订单信息新增失败 订单类型未知，不需要同步"));
            return 0;
        }
        int insert = terminalsOrderInfoMapper.insert(record);
        if (insert > 0) {
            ApplicationContextKeeper.getBean(this.getClass()).transfer(record.getMerchantCode(), record.getMchntOrderNo(), record.getFySettleDt());
        }
        return insert;

    }

    @LogAnnotation("tpay订单信息更新")
    public int update(TerminalsOrderInfoDTO record) {
        if (record == null) {
            log.info("tpay订单信息更新失败,参数为空");
            return 0;
        }
        if (OrderTypeEnum.UNKNOW.getOrderType().equals(record.getOrderType())) {
            LogWriter.info(String.format("tpay订单信息更新失败 订单类型未知，不需要同步"));
            return 0;
        }
        int update = terminalsOrderInfoMapper.update(record);
        if (update > 0) {
            ApplicationContextKeeper.getBean(this.getClass()).transfer(record.getMerchantCode(), record.getMchntOrderNo(), record.getFySettleDt());
        }
        return update;
    }

    /**
     * @param txnLog            :
     * @param termCacheData
     * @param insMchntCacheData
     * @return: com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo
     * @Author: Joker
     * @Date: 2025/5/19 16:12
     */

    @LogAnnotation("转换tpay订单表对象")
    public TerminalsOrderInfo covertTerminalsOrderInfo(TxnLog txnLog, TermCacheData termCacheData, InsMchntCacheData insMchntCacheData) {
        if (txnLog == null) {
            log.info("转换tpay订单表对象失败,参数txnLog为空");
            return null;
        }
        if (termCacheData == null) {
            log.info("转换tpay订单表对象失败,参数termCacheData为空");
            return null;
        }
        TerminalsOrderInfo terminalsOrderInfo = new TerminalsOrderInfo();
        terminalsOrderInfo.setMchntOrderNo(txnLog.getOrderNo());
        terminalsOrderInfo.setTradeDt(txnLog.getTradeDt());
        terminalsOrderInfo.setMerchantCode(txnLog.getMchntCd());
        terminalsOrderInfo.setOrderType(txnLog.getOrderType());
        terminalsOrderInfo.setTradeType(txnLog.getTradeType());
        terminalsOrderInfo.setOrderAmt(txnLog.getOrderAmt() == null ? null : txnLog.getOrderAmt().toPlainString());
        terminalsOrderInfo.setCouponFee(txnLog.getCouponAmt() == null ? null : txnLog.getCouponAmt().toPlainString());
        terminalsOrderInfo.setPayState(txnLog.getPayState());
        terminalsOrderInfo.setRefundAmt(txnLog.getRefundAmt() == null ? null : txnLog.getRefundAmt().toPlainString());
        terminalsOrderInfo.setSrcMchntOrderNo(txnLog.getSrcOrderNo());
        terminalsOrderInfo.setErrorCode(txnLog.getRespCode());
        terminalsOrderInfo.setErrorMsg(txnLog.getRespMsg());
        terminalsOrderInfo.setFyTermId(txnLog.getFyTermId());
        terminalsOrderInfo.setFySettleDt(txnLog.getFyFettleDt());
        terminalsOrderInfo.setFyTraceNo(txnLog.getFyTraceNo());
        terminalsOrderInfo.setChannelOrderId(txnLog.getChannelOrderId());
        terminalsOrderInfo.setTransactionId(txnLog.getTransactionId());
        terminalsOrderInfo.setReserve1(txnLog.getOpenId());
        terminalsOrderInfo.setPayTime(txnLog.getPayTime());
        terminalsOrderInfo.setTxnData2(txnLog.getOrderNo());
        terminalsOrderInfo.setSysId(Constant.ZXT_TPAY_ORDER_SYS_ID);
        terminalsOrderInfo.setTxnData5(new TxnData5Bean(ServerNodeTypeEnum.jq.getCode(), IsOpenEnum.YES.getCode()).toDbStr());
        terminalsOrderInfo.setReserve7(covertTerminalsOrderInfoReserve7(txnLog.getMchntCd(), txnLog.getProjectNo(), txnLog.getStageNo()));
        terminalsOrderInfo.setTmModel(termCacheData.getTmModel());
        terminalsOrderInfo.setTmSn(termCacheData.getTmSerialNo());
        terminalsOrderInfo.setTmType(termCacheData.getTmType());
        terminalsOrderInfo.setShopId(termCacheData.getStoreId());
        terminalsOrderInfo.setShopName(termCacheData.getShopName());
        terminalsOrderInfo.setMerchantName(StringUtil.isBlank(termCacheData.getTmNameCn())?StringUtil.trimToEmpty(insMchntCacheData.getInsNmJcCn()):termCacheData.getTmNameCn());
        return terminalsOrderInfo;

    }

    @LogAnnotation("转换项目阶段备注信息")
    public String covertTerminalsOrderInfoReserve7(String mchntCd, String projectNo, String stageNo) {
        ProjectStage projectStage = projectStageService.queryProjectStage(projectNo, mchntCd, stageNo);
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(projectNo, mchntCd, null);
        log.info("项目阶段详情projectStage={},项目客户信息projectCustomer={}", JSONObject.toJSONString(projectStage), JSONObject.toJSONString(projectCustomer));
        if (projectStage == null && projectCustomer == null) {
            log.info("项目阶段详情或者项目阶段客户信息为空");
            return null;
        }
        log.info("项目阶段详情或者项目阶段客户信息为空");
        Customer customer = customerService.selectByMchntCdAndPhone(mchntCd, projectCustomer.getPhone());
        log.info("客户信息customer={}", JSONObject.toJSONString(customer));
        if (customer == null) {
            log.info("客户信息customer为空");
            return null;
        }
        return String.format("%s %s\n%s", customer.getCustomerName(), StringUtil.secretStr(projectCustomer.getPhone(), 3, 4), projectStage.getStageName());
    }

    public static void main(String[] args) {
        System.out.println(StringUtil.secretStr("13512346543", 3, 4));
    }

    @LogAnnotation("tpay订单信息-详情")
    public TerminalsOrderInfoDTO queryOrder(String mchntCd, String orderNo) {

        TerminalsOrderInfo order = terminalsOrderInfoMapper.queryOrder(mchntCd, orderNo);
        if (order == null) {
            throw new FUException(ResponseCodeEnum.ORDER_NON_EXIST);
        }
        TerminalsOrderInfoDTO result = tpayOrderConvertMapper.covertTerminalsOrderInfo(order);
        return result;

    }

    public void update(TxnLog txnLogOrder, OrderResultNoticData resultData) {
        try {
            TerminalsOrderInfoDTO queryOrder = queryOrder(resultData.getMchnt_cd(), resultData.getMchnt_order_no());
            if (queryOrder == null) {
                LogWriter.info("tpay订单信息不存在");
                return;
            }

            updateOrderStatus(resultData, queryOrder);
        } catch (Exception e) {
            log.error("tpay订单信息更新失败", e);
        }
    }

    private int updateOrderStatus(OrderResultNoticData resultData, TerminalsOrderInfoDTO queryOrder) throws Exception {
        LogWriter.info("--------------OrderService orderResultNotice----------解析返回数据:" + resultData.getMchnt_order_no());
        //查询订单是否存在
        if (queryOrder == null || OrderStatusEnum.INIT_STATUS.getCode().equals(queryOrder.getPayState())) {
            LogWriter.info(this, "订单不存在或者订单状态非00");
            return 0;
        }
        if (Constant.WPOS_SUCCESS_CODE.equals(resultData.getResult_code())) {
            //支付成功
            queryOrder.setPayState(OrderStatusEnum.PAY_SUCCESS.getCode());//成功
            queryOrder.setPayTime(DateUtils.parseDate(resultData.getTxn_fin_ts(), "yyyyMMddHHmmss"));//支付完成时间
            queryOrder.setTransactionId(resultData.getTransaction_id());
            //如果回调过来的channel_order_id不为空才需要更新
            if (StringUtil.isNotEmpty(resultData.getReserved_channel_order_id())) {
                queryOrder.setChannelOrderId(resultData.getReserved_channel_order_id());
            }
            queryOrder.setFySettleDt(resultData.getReserved_fy_settle_dt());
            queryOrder.setFundBillList(resultData.getReserved_fund_bill_list());
            // 设置实收金额和优惠金额
            TpayParseCashCouponUtil.setRealAmtCouponFee(queryOrder, resultData);

            //转换微信朋友圈代金卷信息(精简)
            queryOrder.setReservedPromotionDetail(TpayParseCashCouponUtil.parseCashCoupon(resultData.getReserved_promotion_detail()));
            // 2020-02-10 17:23:47  程军   台卡回调计算代金券优惠金额
            TpayParseCashCouponUtil.calculateWechatFee(queryOrder, resultData.getOrder_amt(), resultData.getReserved_settlement_amt());

        } else {
            queryOrder.setPayState(OrderStatusEnum.PAY_FAIL.getCode());//失败
        }
        LogWriter.info("---------------------------come here");

        queryOrder.setUpdTime(new Date());
        queryOrder.setErrorMsg(resultData.getResult_code() + ":" + resultData.getResult_msg());

        //超长不存库
        TpayParseCashCouponUtil.checkPromotionDetailLenth(queryOrder);

        int updateResult = update(queryOrder);

        return updateResult;
    }


}
