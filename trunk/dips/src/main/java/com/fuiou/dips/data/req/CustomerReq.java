package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.AtLeastOneNotBlank;
import com.fuiou.dips.valid.ChineseCharLength;
import com.fuiou.dips.valid.ValidPhone;
import com.fuiou.dips.valid.group.CreateGroup;
import com.fuiou.dips.valid.group.UpdateGroup;

import java.io.Serializable;

/**
 * 客户信息请求
 *
 * <AUTHOR>
 */
@AtLeastOneNotBlank(groups = CreateGroup.class,
                    fields = {"rowId", "customerName", "phone"},
                    message = "参数不能为空！")
public class CustomerReq implements Serializable {

    /**
     * 客户id
     * 更新必填
     */
    private Long rowId;
    /**
     * 客户姓名
     */
    @ChineseCharLength(min = 2, max = 20, message = "客户姓名必须在2到20个中文字符之间", groups = {CreateGroup.class, UpdateGroup.class})
    private String customerName;

    /**
     * 手机号（商户号加手机号确定客户唯一）
     */
    @ValidPhone(message = "手机号格式")
    private String phone;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 客户备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;

    public Long getRowId() {
        return rowId;
    }

    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    /**
     * 客户姓名
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * 客户姓名
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * 手机号（商户号加手机号确定客户唯一）
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机号（商户号加手机号确定客户唯一）
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 门店id
     */
    public String getStoreId() {
        return storeId;
    }

    /**
     * 门店id
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    /**
     * 客户来源
     */
    public String getCustomerSource() {
        return customerSource;
    }

    /**
     * 客户来源
     */
    public void setCustomerSource(String customerSource) {
        this.customerSource = customerSource;
    }

    /**
     * 客户备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 客户备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

}
