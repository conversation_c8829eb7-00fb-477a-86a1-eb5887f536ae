package com.fuiou.dips.data.req;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 消息查询
 */
public class PageMsgListReq extends PageReqBase implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否已读 0 未读 1 已读 ,不送查所有
     */
    @Pattern(regexp = "^$|^[01]$", message = "readFlag 必须为0 或 1")
    private String readFlag;

    public String getReadFlag() {
        return readFlag;
    }

    public void setReadFlag(String readFlag) {
        this.readFlag = readFlag;
    }
}
