package com.fuiou.dips.framework.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fuiou.dips.framework.annotation.ExcelValid;
import com.fuiou.dips.framework.annotation.ExcelValidationType;
import com.fuiou.dips.framework.annotation.ExcelValids;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 待处理导入（V2版本 - 使用简化注解）
 * 展示如何使用新的简化注解，自动生成错误消息
 *
 * <AUTHOR>
 */
public class ProjectDataImportExcelV2 implements Serializable {

    private static final long serialVersionUID = 1L;

    // 商户信息 - 简单的字符串长度校验
    @ExcelValid(
        type = ExcelValidationType.STRING_LENGTH,
        index = 0,
        fieldName = "商户号",  // 指定显示名称
        maxLength = 20,
        maxZhLength = 20
    )
    @ExcelProperty(value = "商户号", index = 0)
    private String mchntCd;

    // 所属门店 - 使用默认字段名映射
    @ExcelValid(
        type = ExcelValidationType.STRING_LENGTH,
        index = 1,
        maxLength = 200,
        maxZhLength = 200
    )
    @ExcelProperty(value = "所属门店", index = 1)
    private String storeName;

    @ExcelProperty(value = "项目创建日期", index = 2)
    private Date createTime;

    // 客户信息 - 自动生成消息
    @ExcelValid(
        type = ExcelValidationType.STRING_LENGTH,
        index = 3,
        maxLength = 60,
        maxZhLength = 60
    )
    @ExcelProperty(value = "客户姓名", index = 3)
    private String customerName;

    // 手机号 - 使用正则表达式校验
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.STRING_LENGTH,
            index = 4,
            maxLength = 16,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.REGEX_PATTERN,
            index = 4,
            pattern = "^1[3-9]\\d{9}$",
            order = 2
        )
    })
    @ExcelProperty(value = "客户手机号", index = 4)
    private String phone;

    // 项目金额 - 数值转换和范围校验
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.LONG_PARSE,
            index = 5,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.NUMBER_RANGE,
            index = 5,
            minValue = 0,
            maxValue = 999999999L,
            order = 2
        )
    })
    @ExcelProperty(value = "项目总金额", index = 5)
    private Long projectAmt;

    @ExcelValid(
        type = ExcelValidationType.STRING_LENGTH,
        index = 6,
        maxLength = 30,
        maxZhLength = 30
    )
    @ExcelProperty(value = "老板登录账号", index = 6)
    private String loginId;

    // 项目状态 - 枚举值校验
    @ExcelValid(
        type = ExcelValidationType.ENUM_VALUE,
        index = 7,
        allowedValues = {"待处理", "进行中", "已完成", "已取消"}
    )
    @ExcelProperty(value = "项目状态", index = 7)
    private String projectSt;

    // 阶段顺序 - 多重校验，自动生成消息
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.NOT_NULL,
            index = 8,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.INTEGER_PARSE,
            index = 8,
            order = 2
        ),
        @ExcelValid(
            type = ExcelValidationType.STOP_ANALYZE,
            index = 8,
            order = 3
        )
    })
    @ExcelProperty(value = "阶段顺序", index = 8)
    private Integer stageOrder;

    // 阶段名称 - 复杂的多重校验
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.NOT_NULL,
            index = 9,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.STRING_LENGTH,
            index = 9,
            maxLength = 100,
            maxZhLength = 100,
            order = 2
        ),
        @ExcelValid(
            type = ExcelValidationType.STOP_ANALYZE,
            index = 9,
            order = 3
        )
    })
    @ExcelProperty(value = "阶段名称", index = 9)
    private String stageName;

    // 阶段应收金额
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.LONG_PARSE,
            index = 10,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.NOT_NULL,
            index = 10,
            order = 2
        ),
        @ExcelValid(
            type = ExcelValidationType.NUMBER_RANGE,
            index = 10,
            minValue = 0,
            order = 3
        ),
        @ExcelValid(
            type = ExcelValidationType.STOP_ANALYZE,
            index = 10,
            order = 4
        )
    })
    @ExcelProperty(value = "阶段应收金额", index = 10)
    private Long stageAmt;

    // 阶段实收金额
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.LONG_PARSE,
            index = 11,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.NOT_NULL,
            index = 11,
            order = 2
        ),
        @ExcelValid(
            type = ExcelValidationType.NUMBER_RANGE,
            index = 11,
            minValue = 0,
            order = 3
        )
    })
    @ExcelProperty(value = "阶段实收金额", index = 11)
    private Long stageActualAmt;

    // 阶段状态
    @ExcelValids({
        @ExcelValid(
            type = ExcelValidationType.NOT_NULL,
            index = 12,
            order = 1
        ),
        @ExcelValid(
            type = ExcelValidationType.ENUM_VALUE,
            index = 12,
            allowedValues = {"未开始", "进行中", "已完成"},
            order = 2
        )
    })
    @ExcelProperty(value = "阶段状态", index = 12)
    private String stageSt;

    // 阶段信息，自定义解析组装该类
    private List<ProjectStageDataImportExcel> stages;

    // 省略 equals、hashCode 和 getter/setter 方法...
    // 这些方法与原来的实现保持一致

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ProjectDataImportExcelV2 that = (ProjectDataImportExcelV2) o;
        return Objects.equals(mchntCd, that.mchntCd) && Objects.equals(storeName, that.storeName) && Objects.equals(
                createTime, that.createTime) && Objects.equals(customerName, that.customerName) && Objects.equals(phone,
                that.phone) && Objects.equals(projectAmt, that.projectAmt) && Objects.equals(loginId, that.loginId) &&
               Objects.equals(projectSt, that.projectSt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mchntCd, storeName, createTime, customerName, phone, projectAmt, loginId, projectSt);
    }

    // Getter 和 Setter 方法
    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getProjectAmt() {
        return projectAmt;
    }

    public void setProjectAmt(Long projectAmt) {
        this.projectAmt = projectAmt;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getProjectSt() {
        return projectSt;
    }

    public void setProjectSt(String projectSt) {
        this.projectSt = projectSt;
    }

    public List<ProjectStageDataImportExcel> getStages() {
        return stages;
    }

    public void setStages(List<ProjectStageDataImportExcel> stages) {
        this.stages = stages;
    }

    public Integer getStageOrder() {
        return stageOrder;
    }

    public void setStageOrder(Integer stageOrder) {
        this.stageOrder = stageOrder;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public Long getStageAmt() {
        return stageAmt;
    }

    public void setStageAmt(Long stageAmt) {
        this.stageAmt = stageAmt;
    }

    public Long getStageActualAmt() {
        return stageActualAmt;
    }

    public void setStageActualAmt(Long stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public String getStageSt() {
        return stageSt;
    }

    public void setStageSt(String stageSt) {
        this.stageSt = stageSt;
    }
}
