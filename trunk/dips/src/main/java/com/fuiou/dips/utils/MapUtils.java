package com.fuiou.dips.utils;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.persist.beans.MchntAcntLoginInfBean;
import org.apache.commons.beanutils.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MapUtils {


    public static <T> T mapToEntity(Map<String, Object> map, Class<T> clazz) {
        try {
            T instance = clazz.newInstance();
            // 将下划线转为驼峰
            Map<String, Object> camelCaseMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String camelCaseName = toCamelCase(entry.getKey());
                camelCaseMap.put(camelCaseName, entry.getValue());
            }
            BeanUtils.populate(instance, camelCaseMap);
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("转换失败", e);
        }
    }

    public static <T> List<T> mapToEntityList(List<Map<String, Object>> resultList, Class<T> clazz) {
        return resultList.stream().map(map -> mapToEntity(map, clazz)).collect(Collectors.toList());
    }

    private static String toCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 先全部转为小写
        input = input.toLowerCase();

        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;

        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);

            if (currentChar == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpper = false;
                } else {
                    result.append(currentChar);
                }
            }
        }

        return result.toString();
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("LOGIN_ID", "123");
        map.put("NAME", "张三");
        map.put("user_id", "333");
        System.out.println(JsonUtil.bean2Json(MapUtils.mapToEntity(map, MchntAcntLoginInfBean.class)));
    }
}
