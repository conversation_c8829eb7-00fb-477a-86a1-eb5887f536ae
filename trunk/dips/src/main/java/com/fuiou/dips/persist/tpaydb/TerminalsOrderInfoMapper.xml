<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.dips.persist.tpaydb.TerminalsOrderInfoMapper">

    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="merchantCode" column="merchant_code" jdbcType="CHAR"/>
            <result property="merchantName" column="merchant_name" jdbcType="VARCHAR"/>
            <result property="mchntOrderNo" column="mchnt_order_no" jdbcType="VARCHAR"/>
            <result property="orderAmt" column="order_amt" jdbcType="INTEGER"/>
            <result property="refundAmt" column="refund_amt" jdbcType="INTEGER"/>
            <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
            <result property="payState" column="pay_state" jdbcType="CHAR"/>
            <result property="crtTime" column="crt_time" jdbcType="TIMESTAMP"/>
            <result property="updTime" column="upd_time" jdbcType="TIMESTAMP"/>
            <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
            <result property="channelOrderId" column="channel_order_id" jdbcType="VARCHAR"/>
            <result property="transactionId" column="transaction_id" jdbcType="VARCHAR"/>
            <result property="fyTermId" column="fy_term_id" jdbcType="VARCHAR"/>
            <result property="termVersion" column="term_version" jdbcType="VARCHAR"/>
            <result property="couponFee" column="coupon_fee" jdbcType="INTEGER"/>
            <result property="simNo" column="sim_no" jdbcType="VARCHAR"/>
            <result property="lockFlag" column="lock_flag" jdbcType="CHAR"/>
            <result property="reserve1" column="reserve1" jdbcType="VARCHAR"/>
            <result property="reserve2" column="reserve2" jdbcType="VARCHAR"/>
            <result property="reserve3" column="reserve3" jdbcType="VARCHAR"/>
            <result property="authCode" column="auth_code" jdbcType="VARCHAR"/>
            <result property="fyTraceNo" column="fy_trace_no" jdbcType="VARCHAR"/>
            <result property="fySettleDt" column="fy_settle_dt" jdbcType="CHAR"/>
            <result property="refundOrderNo" column="refund_order_no" jdbcType="VARCHAR"/>
            <result property="fundBillList" column="fund_bill_list" jdbcType="VARCHAR"/>
            <result property="undiscAmt" column="undisc_amt" jdbcType="INTEGER"/>
            <result property="receiptAmount" column="receipt_amount" jdbcType="INTEGER"/>
            <result property="merchantFund" column="merchant_fund" jdbcType="INTEGER"/>
            <result property="channelFund" column="channel_fund" jdbcType="INTEGER"/>
            <result property="reqRefundAmt" column="req_refund_amt" jdbcType="INTEGER"/>
            <result property="tradeDt" column="trade_dt" jdbcType="CHAR"/>
            <result property="tradeType" column="trade_type" jdbcType="CHAR"/>
            <result property="refundId" column="refund_id" jdbcType="VARCHAR"/>
            <result property="shopId" column="shop_id" jdbcType="VARCHAR"/>
            <result property="termMchntCd" column="term_mchnt_cd" jdbcType="VARCHAR"/>
            <result property="errorMsg" column="error_msg" jdbcType="VARCHAR"/>
            <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
            <result property="queryTimes" column="query_times" jdbcType="INTEGER"/>
            <result property="sysId" column="sys_id" jdbcType="VARCHAR"/>
            <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
            <result property="cardNo" column="card_no" jdbcType="VARCHAR"/>
            <result property="noticeUrl" column="notice_url" jdbcType="VARCHAR"/>
            <result property="srcMchntOrderNo" column="src_mchnt_order_no" jdbcType="VARCHAR"/>
            <result property="posSsn" column="pos_ssn" jdbcType="VARCHAR"/>
            <result property="checkCardNo" column="check_card_no" jdbcType="VARCHAR"/>
            <result property="markPrint" column="mark_print" jdbcType="VARCHAR"/>
            <result property="addOrderType" column="add_order_type" jdbcType="VARCHAR"/>
            <result property="reserve4" column="reserve4" jdbcType="VARCHAR"/>
            <result property="reserve5" column="reserve5" jdbcType="VARCHAR"/>
            <result property="reserve6" column="reserve6" jdbcType="VARCHAR"/>
            <result property="reserve7" column="reserve7" jdbcType="VARCHAR"/>
            <result property="reserve8" column="reserve8" jdbcType="VARCHAR"/>
            <result property="reserve9" column="reserve9" jdbcType="VARCHAR"/>
            <result property="reserve10" column="reserve10" jdbcType="VARCHAR"/>
            <result property="tmType" column="tm_type" jdbcType="VARCHAR"/>
            <result property="tmSn" column="tm_sn" jdbcType="VARCHAR"/>
            <result property="tmModel" column="tm_model" jdbcType="VARCHAR"/>
            <result property="reservedPromotionDetail" column="reserved_promotion_detail" jdbcType="VARCHAR"/>
            <result property="txnData1" column="txn_data1" jdbcType="VARCHAR"/>
            <result property="txnData2" column="txn_data2" jdbcType="VARCHAR"/>
            <result property="txnData3" column="txn_data3" jdbcType="VARCHAR"/>
            <result property="txnData4" column="txn_data4" jdbcType="VARCHAR"/>
            <result property="txnData5" column="txn_data5" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,merchant_code,merchant_name,
        mchnt_order_no,order_amt,refund_amt,
        order_type,pay_state,crt_time,
        upd_time,pay_time,channel_order_id,
        transaction_id,fy_term_id,term_version,
        coupon_fee,sim_no,lock_flag,
        reserve1,reserve2,reserve3,
        auth_code,fy_trace_no,fy_settle_dt,
        refund_order_no,fund_bill_list,undisc_amt,
        receipt_amount,merchant_fund,channel_fund,
        req_refund_amt,trade_dt,trade_type,
        refund_id,shop_id,term_mchnt_cd,
        error_msg,shop_name,query_times,
        sys_id,error_code,card_no,
        notice_url,src_mchnt_order_no,pos_ssn,
        check_card_no,mark_print,add_order_type,
        reserve4,reserve5,reserve6,
        reserve7,reserve8,reserve9,
        reserve10,tm_type,tm_sn,
        tm_model,reserved_promotion_detail,txn_data1,
        txn_data2,txn_data3,txn_data4,
        txn_data5
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_terminals_order_info
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <select id="queryOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_terminals_order_info
        where  merchant_code = #{merchantCode,jdbcType=CHAR} and mchnt_order_no=#{mchntOrderNo,jdbcType=VARCHAR}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo" useGeneratedKeys="true">
        insert into t_terminals_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="merchantCode != null">merchant_code,</if>
                <if test="merchantName != null">merchant_name,</if>
                <if test="mchntOrderNo != null">mchnt_order_no,</if>
                <if test="orderAmt != null">order_amt,</if>
                <if test="refundAmt != null">refund_amt,</if>
                <if test="orderType != null">order_type,</if>
                <if test="payState != null">pay_state,</if>
                <if test="crtTime != null">crt_time,</if>
                <if test="updTime != null">upd_time,</if>
                <if test="payTime != null">pay_time,</if>
                <if test="channelOrderId != null">channel_order_id,</if>
                <if test="transactionId != null">transaction_id,</if>
                <if test="fyTermId != null">fy_term_id,</if>
                <if test="termVersion != null">term_version,</if>
                <if test="couponFee != null">coupon_fee,</if>
                <if test="simNo != null">sim_no,</if>
                <if test="lockFlag != null">lock_flag,</if>
                <if test="reserve1 != null">reserve1,</if>
                <if test="reserve2 != null">reserve2,</if>
                <if test="reserve3 != null">reserve3,</if>
                <if test="authCode != null">auth_code,</if>
                <if test="fyTraceNo != null">fy_trace_no,</if>
                <if test="fySettleDt != null">fy_settle_dt,</if>
                <if test="refundOrderNo != null">refund_order_no,</if>
                <if test="fundBillList != null">fund_bill_list,</if>
                <if test="undiscAmt != null">undisc_amt,</if>
                <if test="receiptAmount != null">receipt_amount,</if>
                <if test="merchantFund != null">merchant_fund,</if>
                <if test="channelFund != null">channel_fund,</if>
                <if test="reqRefundAmt != null">req_refund_amt,</if>
                <if test="tradeDt != null">trade_dt,</if>
                <if test="tradeType != null">trade_type,</if>
                <if test="refundId != null">refund_id,</if>
                <if test="shopId != null">shop_id,</if>
                <if test="termMchntCd != null">term_mchnt_cd,</if>
                <if test="errorMsg != null">error_msg,</if>
                <if test="shopName != null">shop_name,</if>
                <if test="queryTimes != null">query_times,</if>
                <if test="sysId != null">sys_id,</if>
                <if test="errorCode != null">error_code,</if>
                <if test="cardNo != null">card_no,</if>
                <if test="noticeUrl != null">notice_url,</if>
                <if test="srcMchntOrderNo != null">src_mchnt_order_no,</if>
                <if test="posSsn != null">pos_ssn,</if>
                <if test="checkCardNo != null">check_card_no,</if>
                <if test="markPrint != null">mark_print,</if>
                <if test="addOrderType != null">add_order_type,</if>
                <if test="reserve4 != null">reserve4,</if>
                <if test="reserve5 != null">reserve5,</if>
                <if test="reserve6 != null">reserve6,</if>
                <if test="reserve7 != null">reserve7,</if>
                <if test="reserve8 != null">reserve8,</if>
                <if test="reserve9 != null">reserve9,</if>
                <if test="reserve10 != null">reserve10,</if>
                <if test="tmType != null">tm_type,</if>
                <if test="tmSn != null">tm_sn,</if>
                <if test="tmModel != null">tm_model,</if>
                <if test="reservedPromotionDetail != null">reserved_promotion_detail,</if>
                <if test="txnData1 != null">txn_data1,</if>
                <if test="txnData2 != null">txn_data2,</if>
                <if test="txnData3 != null">txn_data3,</if>
                <if test="txnData4 != null">txn_data4,</if>
                <if test="txnData5 != null">txn_data5,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="merchantCode != null">#{merchantCode,jdbcType=CHAR},</if>
                <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
                <if test="mchntOrderNo != null">#{mchntOrderNo,jdbcType=VARCHAR},</if>
                <if test="orderAmt != null">#{orderAmt,jdbcType=INTEGER},</if>
                <if test="refundAmt != null">#{refundAmt,jdbcType=INTEGER},</if>
                <if test="orderType != null">#{orderType,jdbcType=VARCHAR},</if>
                <if test="payState != null">#{payState,jdbcType=CHAR},</if>
                <if test="crtTime != null">#{crtTime,jdbcType=TIMESTAMP},</if>
                <if test="updTime != null">#{updTime,jdbcType=TIMESTAMP},</if>
                <if test="payTime != null">#{payTime,jdbcType=TIMESTAMP},</if>
                <if test="channelOrderId != null">#{channelOrderId,jdbcType=VARCHAR},</if>
                <if test="transactionId != null">#{transactionId,jdbcType=VARCHAR},</if>
                <if test="fyTermId != null">#{fyTermId,jdbcType=VARCHAR},</if>
                <if test="termVersion != null">#{termVersion,jdbcType=VARCHAR},</if>
                <if test="couponFee != null">#{couponFee,jdbcType=INTEGER},</if>
                <if test="simNo != null">#{simNo,jdbcType=VARCHAR},</if>
                <if test="lockFlag != null">#{lockFlag,jdbcType=CHAR},</if>
                <if test="reserve1 != null">#{reserve1,jdbcType=VARCHAR},</if>
                <if test="reserve2 != null">#{reserve2,jdbcType=VARCHAR},</if>
                <if test="reserve3 != null">#{reserve3,jdbcType=VARCHAR},</if>
                <if test="authCode != null">#{authCode,jdbcType=VARCHAR},</if>
                <if test="fyTraceNo != null">#{fyTraceNo,jdbcType=VARCHAR},</if>
                <if test="fySettleDt != null">#{fySettleDt,jdbcType=CHAR},</if>
                <if test="refundOrderNo != null">#{refundOrderNo,jdbcType=VARCHAR},</if>
                <if test="fundBillList != null">#{fundBillList,jdbcType=VARCHAR},</if>
                <if test="undiscAmt != null">#{undiscAmt,jdbcType=INTEGER},</if>
                <if test="receiptAmount != null">#{receiptAmount,jdbcType=INTEGER},</if>
                <if test="merchantFund != null">#{merchantFund,jdbcType=INTEGER},</if>
                <if test="channelFund != null">#{channelFund,jdbcType=INTEGER},</if>
                <if test="reqRefundAmt != null">#{reqRefundAmt,jdbcType=INTEGER},</if>
                <if test="tradeDt != null">#{tradeDt,jdbcType=CHAR},</if>
                <if test="tradeType != null">#{tradeType,jdbcType=CHAR},</if>
                <if test="refundId != null">#{refundId,jdbcType=VARCHAR},</if>
                <if test="shopId != null">#{shopId,jdbcType=VARCHAR},</if>
                <if test="termMchntCd != null">#{termMchntCd,jdbcType=VARCHAR},</if>
                <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
                <if test="shopName != null">#{shopName,jdbcType=VARCHAR},</if>
                <if test="queryTimes != null">#{queryTimes,jdbcType=INTEGER},</if>
                <if test="sysId != null">#{sysId,jdbcType=VARCHAR},</if>
                <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
                <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
                <if test="noticeUrl != null">#{noticeUrl,jdbcType=VARCHAR},</if>
                <if test="srcMchntOrderNo != null">#{srcMchntOrderNo,jdbcType=VARCHAR},</if>
                <if test="posSsn != null">#{posSsn,jdbcType=VARCHAR},</if>
                <if test="checkCardNo != null">#{checkCardNo,jdbcType=VARCHAR},</if>
                <if test="markPrint != null">#{markPrint,jdbcType=VARCHAR},</if>
                <if test="addOrderType != null">#{addOrderType,jdbcType=VARCHAR},</if>
                <if test="reserve4 != null">#{reserve4,jdbcType=VARCHAR},</if>
                <if test="reserve5 != null">#{reserve5,jdbcType=VARCHAR},</if>
                <if test="reserve6 != null">#{reserve6,jdbcType=VARCHAR},</if>
                <if test="reserve7 != null">#{reserve7,jdbcType=VARCHAR},</if>
                <if test="reserve8 != null">#{reserve8,jdbcType=VARCHAR},</if>
                <if test="reserve9 != null">#{reserve9,jdbcType=VARCHAR},</if>
                <if test="reserve10 != null">#{reserve10,jdbcType=VARCHAR},</if>
                <if test="tmType != null">#{tmType,jdbcType=VARCHAR},</if>
                <if test="tmSn != null">#{tmSn,jdbcType=VARCHAR},</if>
                <if test="tmModel != null">#{tmModel,jdbcType=VARCHAR},</if>
                <if test="reservedPromotionDetail != null">#{reservedPromotionDetail,jdbcType=VARCHAR},</if>
                <if test="txnData1 != null">#{txnData1,jdbcType=VARCHAR},</if>
                <if test="txnData2 != null">#{txnData2,jdbcType=VARCHAR},</if>
                <if test="txnData3 != null">#{txnData3,jdbcType=VARCHAR},</if>
                <if test="txnData4 != null">#{txnData4,jdbcType=VARCHAR},</if>
                <if test="txnData5 != null">#{txnData5,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="update" parameterType="com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfoDTO">
        update t_terminals_order_info
        <set>

            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>

            <if test="orderAmt != null">
                order_amt = #{orderAmt,jdbcType=INTEGER},
            </if>
            <if test="refundAmt != null">
                refund_amt = #{refundAmt,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="payState != null">
                pay_state = #{payState,jdbcType=CHAR},
            </if>

            <if test="updTime != null">
                upd_time = #{updTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelOrderId != null">
                channel_order_id = #{channelOrderId,jdbcType=VARCHAR},
            </if>
            <if test="transactionId != null">
                transaction_id = #{transactionId,jdbcType=VARCHAR},
            </if>
            <if test="fyTermId != null">
                fy_term_id = #{fyTermId,jdbcType=VARCHAR},
            </if>
            <if test="termVersion != null">
                term_version = #{termVersion,jdbcType=VARCHAR},
            </if>
            <if test="couponFee != null">
                coupon_fee = #{couponFee,jdbcType=INTEGER},
            </if>
            <if test="simNo != null">
                sim_no = #{simNo,jdbcType=VARCHAR},
            </if>
            <if test="lockFlag != null">
                lock_flag = #{lockFlag,jdbcType=CHAR},
            </if>
            <if test="reserve1 != null">
                reserve1 = #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null">
                reserve2 = #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="reserve3 != null">
                reserve3 = #{reserve3,jdbcType=VARCHAR},
            </if>
            <if test="authCode != null">
                auth_code = #{authCode,jdbcType=VARCHAR},
            </if>
            <if test="fyTraceNo != null">
                fy_trace_no = #{fyTraceNo,jdbcType=VARCHAR},
            </if>
            <if test="fySettleDt != null">
                fy_settle_dt = #{fySettleDt,jdbcType=CHAR},
            </if>
            <if test="refundOrderNo != null">
                refund_order_no = #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="fundBillList != null">
                fund_bill_list = #{fundBillList,jdbcType=VARCHAR},
            </if>
            <if test="undiscAmt != null">
                undisc_amt = #{undiscAmt,jdbcType=INTEGER},
            </if>
            <if test="receiptAmount != null">
                receipt_amount = #{receiptAmount,jdbcType=INTEGER},
            </if>
            <if test="merchantFund != null">
                merchant_fund = #{merchantFund,jdbcType=INTEGER},
            </if>
            <if test="channelFund != null">
                channel_fund = #{channelFund,jdbcType=INTEGER},
            </if>
            <if test="reqRefundAmt != null">
                req_refund_amt = #{reqRefundAmt,jdbcType=INTEGER},
            </if>
            <if test="tradeDt != null">
                trade_dt = #{tradeDt,jdbcType=CHAR},
            </if>

            <if test="refundId != null">
                refund_id = #{refundId,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=VARCHAR},
            </if>
            <if test="termMchntCd != null">
                term_mchnt_cd = #{termMchntCd,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                shop_name = #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="queryTimes != null">
                query_times = #{queryTimes,jdbcType=INTEGER},
            </if>
            <if test="sysId != null">
                sys_id = #{sysId,jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="noticeUrl != null">
                notice_url = #{noticeUrl,jdbcType=VARCHAR},
            </if>
            <if test="srcMchntOrderNo != null">
                src_mchnt_order_no = #{srcMchntOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="posSsn != null">
                pos_ssn = #{posSsn,jdbcType=VARCHAR},
            </if>
            <if test="checkCardNo != null">
                check_card_no = #{checkCardNo,jdbcType=VARCHAR},
            </if>
            <if test="markPrint != null">
                mark_print = #{markPrint,jdbcType=VARCHAR},
            </if>
            <if test="addOrderType != null">
                add_order_type = #{addOrderType,jdbcType=VARCHAR},
            </if>
            <if test="reserve4 != null">
                reserve4 = #{reserve4,jdbcType=VARCHAR},
            </if>
            <if test="reserve5 != null">
                reserve5 = #{reserve5,jdbcType=VARCHAR},
            </if>
            <if test="reserve6 != null">
                reserve6 = #{reserve6,jdbcType=VARCHAR},
            </if>
            <if test="reserve7 != null">
                reserve7 = #{reserve7,jdbcType=VARCHAR},
            </if>
            <if test="reserve8 != null">
                reserve8 = #{reserve8,jdbcType=VARCHAR},
            </if>
            <if test="reserve9 != null">
                reserve9 = #{reserve9,jdbcType=VARCHAR},
            </if>
            <if test="reserve10 != null">
                reserve10 = #{reserve10,jdbcType=VARCHAR},
            </if>
            <if test="tmType != null">
                tm_type = #{tmType,jdbcType=VARCHAR},
            </if>
            <if test="tmSn != null">
                tm_sn = #{tmSn,jdbcType=VARCHAR},
            </if>
            <if test="tmModel != null">
                tm_model = #{tmModel,jdbcType=VARCHAR},
            </if>
            <if test="reservedPromotionDetail != null">
                reserved_promotion_detail = #{reservedPromotionDetail,jdbcType=VARCHAR},
            </if>
            <if test="txnData1 != null">
                txn_data1 = #{txnData1,jdbcType=VARCHAR},
            </if>
            <if test="txnData2 != null">
                txn_data2 = #{txnData2,jdbcType=VARCHAR},
            </if>
            <if test="txnData3 != null">
                txn_data3 = #{txnData3,jdbcType=VARCHAR},
            </if>
            <if test="txnData4 != null">
                txn_data4 = #{txnData4,jdbcType=VARCHAR},
            </if>
            <if test="txnData5 != null">
                txn_data5 = #{txnData5,jdbcType=VARCHAR},
            </if>
        </set>
        where merchant_code = #{merchantCode,jdbcType=CHAR} and mchnt_order_no=#{mchntOrderNo,jdbcType=VARCHAR}

        and order_amt = #{oldOrderAmt,jdbcType=INTEGER}
        <if test="oldCouponFee != null">
            and coupon_fee = #{oldCouponFee,jdbcType=INTEGER}
        </if>
        <if test="oldReserve10 != null">
            and reserve10 = #{oldReserve10,jdbcType=VARCHAR}
        </if>
        <if test="oldrefundAmt != null">
            and refund_amt = #{refundAmt,jdbcType=INTEGER}
        </if>

        <if test="oldStatus != null">
            and pay_state = #{oldStatus,jdbcType=VARCHAR}
        </if>

        <if test="lockFlag != null">
            and lock_flag = #{lockFlag,jdbcType=VARCHAR}
        </if>
    </update>

</mapper>
