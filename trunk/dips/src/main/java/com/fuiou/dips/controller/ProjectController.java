package com.fuiou.dips.controller;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.req.ProjectReq;
import com.fuiou.dips.data.req.StoreSearchPageReq;
import com.fuiou.dips.data.resp.*;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.services.ProjectService;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.valid.group.CreateGroup;
import com.fuiou.dips.valid.group.QueryGroup;
import com.fuiou.dips.valid.group.UpdateGroup;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 项目管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
public class ProjectController {

    @Resource
    private ProjectService projectService;

    // 新增项目
    @PostMapping("/add")
    @ResponseBody
    public ResponseEntity<String> add(@Validated(CreateGroup.class) @RequestBody ProjectReq projectReq) throws Exception
    {
        LogWriter.info("开始执行项目保存,请求报文:" + JsonUtil.bean2Json(projectReq));
        String result = projectService.add(projectReq);
        return ResponseEntityFactory.ok(result);
    }

    // 门店列表
    @PostMapping("/stores")
    @ResponseBody
    public ResponseEntity<PageRespBase<StoreInfoResp>> stores(
            @Validated(QueryGroup.class) @RequestBody StoreSearchPageReq storePageReq)
    {
        LogWriter.info("开始执行门店列表查询,请求报文:" + JsonUtil.bean2Json(storePageReq));
        PageRespBase<StoreInfoResp> pageList = projectService.getStoresByMchntCd(storePageReq);
        return pageList != null ? ResponseEntityFactory.ok(pageList) : ResponseEntityFactory.fail(
                ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    // 检查门店台卡绑定关系
    @Deprecated
    @GetMapping("/check")
    @ResponseBody
    public ResponseEntity check(String storeId) {
        LogWriter.info("开始执行项目列表查询,请求报文:" + storeId);
        projectService.checkStoreByMchntCd(LoginConstat.getLoginToken().getMchntInfo().getMchntCd(), storeId);
        return ResponseEntityFactory.ok();
    }

    // 员工列表
    @GetMapping("/emps")
    @ResponseBody
    public ResponseEntity<List<EmpInfoResp>> list() {
        List<EmpInfoResp> list = projectService.getEmpInfos(LoginConstat.getLoginToken().getMchntInfo().getMchntCd());
        return ResponseEntityFactory.ok(list);
    }

    // 项目详情
    @GetMapping("/detail")
    @ResponseBody
    public ResponseEntity<ProjectResp> detail(String projectNo) throws Exception
    {
        LogWriter.info("开始执行项目详情查询,请求报文:" + projectNo);
        FUApiAssert.isNotBlank(ResponseCodeEnum.PARAM_ERROR, projectNo);
        ProjectResp detail = projectService.getDetailByProjectNoAndMchntCd(projectNo,
                LoginConstat.getLoginToken().getMchntInfo().getMchntCd());
        return detail != null ? ResponseEntityFactory.ok(detail) : ResponseEntityFactory.fail(
                ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    // 项目编辑，根据项目编号+商户号判断，可编辑内容：门店（未收款可切换门店）、客户手机号、项目成员（可增减）、备注，客户姓名无法编辑
    @PostMapping("/edit")
    @ResponseBody
    public ResponseEntity edit(@Validated(UpdateGroup.class) @RequestBody ProjectReq projectReq) throws Exception {
        LogWriter.info("开始执行项目编辑,请求报文:" + JsonUtil.bean2Json(projectReq));
        int result = projectService.edit(projectReq);
        return result > 0 ? ResponseEntityFactory.ok() : ResponseEntityFactory.fail(ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    // 状态查询，根据项目编号+商户号，查询项目状态，返回收款状态（已收款1，未收款0）和项目状态（进行中1，已关闭2，已完成9）
    @RequestMapping("/status")
    @ResponseBody
    public ResponseEntity<ProjectResp> status(@Validated(QueryGroup.class) @RequestBody ProjectReq projectReq)
            throws Exception
    {
        LogWriter.info("开始执行项目状态查询,请求报文:" + JsonUtil.bean2Json(projectReq));
        return ResponseEntityFactory.ok(
                projectService.getProjectStatus(projectReq.getProjectNo(), projectReq.getMchntCd()));
    }

    // 关闭项目，根据项目编号+商户号，将项目状态修改为已关闭，关闭前先判断状态，非进行中，就报错提示
    @PostMapping("/close")
    @ResponseBody
    public ResponseEntity close(@Validated(QueryGroup.class) @RequestBody ProjectReq projectReq) throws Exception {
        LogWriter.info("开始执行项目关闭,请求报文:" + JsonUtil.bean2Json(projectReq));
        int result = projectService.closeProject(projectReq.getProjectNo(), projectReq.getMchntCd());
        return result > 0 ? ResponseEntityFactory.ok() : ResponseEntityFactory.fail(ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

}
