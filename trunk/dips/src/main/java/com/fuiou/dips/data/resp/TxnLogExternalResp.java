package com.fuiou.dips.data.resp;

import com.fuiou.dips.data.entity.SignBaseEntry;
import com.fuiou.dips.utils.SignSort;

/***
 * 交易订单外部调用查询响应对象
 * @Author: Joker
 * @Date: 2025/5/21 11:25
 */

public class TxnLogExternalResp implements SignBaseEntry {


    /**
     * 订单金额（单位：分）
     */
    private String orderAmt;


    /**
     * 订单号
     */
    private String mchntOrderNo;

    /**
     * 商户号
     */
    private String mchntCd;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 阶段名称
     */
    private String stageName;




    /**
     * 项目编号
     */
    private String projectNo;


    /**
     * 阶段编号
     */
    private String stageNo;


    /**
     * 过期时间 格式yyyy-MM-dd HH:mm
     */
    private String expireTime;
    /**
     * 创建时间 格式yyyy-MM-dd HH:mm:ss
     */
    private String createTime;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 客户手机号
     */
    private String customerPhone;

    public String getOrderAmt() {

        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getMchntOrderNo() {

        return mchntOrderNo == null ? null : mchntOrderNo.trim();
    }

    public void setMchntOrderNo(String mchntOrderNo) {
        this.mchntOrderNo = mchntOrderNo;
    }

    public String getMchntCd() {

        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getProjectName() {

        return projectName == null ? null : projectName.trim();
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getStageName() {

        return stageName == null ? null : stageName.trim();
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }



    public String getProjectNo() {

        return projectNo == null ? null : projectNo.trim();
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getStageNo() {

        return stageNo == null ? null : stageNo.trim();
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public String getExpireTime() {

        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getCreateTime() {

        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCustomerName() {

        return customerName == null ? null : customerName.trim();
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {

        return customerPhone == null ? null : customerPhone.trim();
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    @Override
    public String toSignStr() {
        return SignSort.generateSignContent(this);
    }


}
