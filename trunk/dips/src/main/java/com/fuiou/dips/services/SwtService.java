package com.fuiou.dips.services;

import cn.hutool.core.date.DateUtil;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.swt.KbpsSocket;
import com.fuiou.dips.swt.constant.SwtContants;
import com.fuiou.dips.swt.data.KbpsDataBean;
import com.fuiou.dips.utils.MD5Util;
import com.fuiou.dips.utils.StringUtil;
import com.fuiou.ssn.service.SsnGenerator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;

@Service
public class SwtService {

    private static final Logger log = LoggerFactory.getLogger(SwtService.class);
    @Resource
    private MchntService mchntService;
    @Resource
    private TpayOrderService tpayOrderService;

    /**
     * 方法注释： 走 swt 支付
     * 修改内容： 新增
     * 修改时间： 2017年11月27日 上午11:16:51
     *
     * @param dataBean
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public KbpsDataBean cashPay(TxnLog dataBean) throws Exception {
        if (null == dataBean) {
            return null;
        }
//        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(dataBean.getMchntCd());
//        if(insMchntCacheData == null){
//            LogWriter.info(this, "商户信息不存在");
//            return null;
//        }
        KbpsDataBean data = createDataBean(SwtContants.BUSI_CONSTS.CASH_TRADE_BUSI_CD, SwtContants.SRC_INS_CD_SWT);
        setValueByTxnLog(dataBean, data, getChnlOrderId(""));

        data.setMac(MD5Util.encode((data.getBody() + SwtContants.secretInsKey), MD5Util.GBK).substring(0, 16));
        return KbpsSocket.send(data);
    }

    private void setValueByTxnLog(TxnLog dataBean, KbpsDataBean data, String chnlOrderId) throws UnsupportedEncodingException {
        data.setSrcMchntCd(dataBean.getMchntCd());
        data.setSrcTermId(dataBean.getFyTermId());
        data.setAddnTxnInf(chnlOrderId);
        data.setTxnAmt(dataBean.getOrderAmt().toPlainString());
        data.setOrderNo(String.format("%s%s", Constant.SWT_TX86_ORDER_NO_PREFIX,dataBean.getOrderNo()));
//        String rmk = tpayOrderService.covertTerminalsOrderInfoReserve7(dataBean.getMchntCd(), dataBean.getProjectNo(), dataBean.getStageNo());
        String rmk = tpayOrderService.covertTerminalsOrderInfoReserve7(dataBean.getMchntCd(), dataBean.getProjectNo(), dataBean.getStageNo());
        data.setTrackData(String.format("%s%s%s",StringUtil.leftPad(String.valueOf(rmk.getBytes("GBK").length), 3, "0"),rmk, StringUtil.repeat( " ",200-rmk.getBytes("GBK").length)));
//        if(!"0003050F6370795".equals(StringUtils.trimToEmpty(dataBean.getMchntCd()))) {
////        String rmk = "joker first 135****5130";
//            String rmk = tpayOrderService.covertTerminalsOrderInfoReserve7(dataBean.getMchntCd(), dataBean.getProjectNo(), dataBean.getStageNo());
//            int length = rmk.getBytes("GBK").length;
//            rmk = StringUtils.isBlank(rmk) ? "" : String.format("%s%s", rmk, StringUtils.repeat(" ", 200 - length));
//            rmk = String.format("%s%s", StringUtils.leftPad(String.valueOf(length), 3, "0"), rmk);
//            log.info("组装swt支付rmk前：rmk={},length={},gbk_byte.length={}", rmk, rmk.length(), rmk.getBytes("GBK").length);
//            data.setTrackData(rmk);
//        }
//        if(StringUtils.isNotBlank(req.getExOrderNo())) {
//            data.setRspData(String.format("%s%s", StringUtils.leftPad(String.valueOf(req.getExOrderNo().length()), 3, "0"), req.getExOrderNo()));
//        }

    }

    /**
     * 方法注释： 获取chnlOrderId
     * 修改内容： 新增
     * 修改时间： 2017年11月29日 上午10:32:00
     *
     * @param detailInqrData
     * @return
     * <AUTHOR>
     */
    private String getChnlOrderId(String detailInqrData) {
        if (StringUtils.isBlank(detailInqrData) || !detailInqrData.contains(SwtContants.CHNLORDERID_TAG)) {
            return "";
        }
        int startIdx = detailInqrData.indexOf(SwtContants.CHNLORDERID_TAG) + SwtContants.CHNLORDERID_TAG.length() + 1;
        int endIdx = detailInqrData.lastIndexOf(SwtContants.CHNLORDERID_TAG) - 2;
        return detailInqrData.substring(startIdx, endIdx);
    }

    /**
     * 方法注释： 创建 RUT传输对象
     * 修改内容： 新增
     * 修改时间： 2017年11月27日 上午11:46:43
     *
     * @param busiCd
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    private KbpsDataBean createDataBean(String busiCd, String srcInsCd) throws Exception {
        KbpsDataBean data = new KbpsDataBean();
        data.setBusiCd(busiCd);
        data.setTxnDir("Q");
        data.setBusiChnl(SwtContants.WEB_BUSICHNL_CD);
        data.setSrcInsCd(srcInsCd);
        data.setSrcInsSsn(SsnGenerator.getSsn(srcInsCd));
        Date now = new Date();
        data.setLocDt(DateUtil.format(now, "yyyyMMdd"));
        data.setLocTm(DateUtil.format(now, "hhMMss"));
        return data;
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
        String str="208朱娟�? 185****1161\n" +
                "�?1阶段 收款";
        String s = StringUtils.rightPad(str, 200, " ");
        System.out.println(s+":"+s.length());


        String s1="177朱娟娟 185****1161\n" +
                "第1阶段 收款";
//        s1=StringUtils.rightPad(s1, 200, " ");


        s1=StringUtils.isBlank(s1)?"":String.format("%s%s",s1,StringUtils.repeat(" ",200-s1.getBytes("GBK").length));
        System.out.println(s1.getBytes("GBK").length);
    }

}
