package com.fuiou.dips.data.resp;

import java.io.Serializable;

/***
* @Description:  支付页信息
* @Author: Joker
* @Date: 2025/5/14 18:42
*/
public class PayPageInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 终端编号
     */
    private String fyTermId;

    /**
     * 阶段编号
     */
    private String stageNo;
    /**
    * @Description: 阶段名称
    * @Author: Joker
    * @Date: 2025/5/14 20:21
    */

    private String stageName;
    /**
     * 阶段已收款金额，单位分
     */
    private String stageActualAmt;

    /**
     * 本次需支付金额
     */
    private String amt;

    public String getProjectNo() {

        return projectNo == null ? null : projectNo.trim();
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectName() {

        return projectName == null ? null : projectName.trim();
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getMchntCd() {

        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getFyTermId() {

        return fyTermId == null ? null : fyTermId.trim();
    }

    public void setFyTermId(String fyTermId) {
        this.fyTermId = fyTermId;
    }

    public String getStageNo() {

        return stageNo == null ? null : stageNo.trim();
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public String getStageName() {

        return stageName == null ? null : stageName.trim();
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getStageActualAmt() {

        return stageActualAmt == null ? null : stageActualAmt.trim();
    }

    public void setStageActualAmt(String stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public String getAmt() {

        return amt == null ? null : amt.trim();
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }
}
