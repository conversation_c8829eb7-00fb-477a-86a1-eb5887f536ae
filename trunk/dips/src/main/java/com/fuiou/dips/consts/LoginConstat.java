package com.fuiou.dips.consts;

import com.fuiou.dips.data.resp.LoginResp;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @program: dips
 * @description:
 * @author: Joker
 * @create: 2025/05/08 17:34
 */
public class LoginConstat {
    private static final ThreadLocal<LoginResp> TOKEN_THREADLOCAL = new ThreadLocal<>();
    public static void setLoginToken(LoginResp info) {
        TOKEN_THREADLOCAL.set(info);
    }

    public static LoginResp getLoginToken() {
        return TOKEN_THREADLOCAL.get();
    }

    public static void clear() {
        TOKEN_THREADLOCAL.remove();
    }


    public static Set<String> getUserPermissions(String token) {
        // 根据 token 查询用户权限（示例返回静态数据）
        return new HashSet<>(Arrays.asList("user:read", "order:write","*"));
    }
}
