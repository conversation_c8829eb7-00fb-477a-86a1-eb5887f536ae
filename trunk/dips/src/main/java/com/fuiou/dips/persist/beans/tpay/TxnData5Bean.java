package com.fuiou.dips.persist.beans.tpay;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.enums.OrderSubAddTypeEnum;
import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * tpay的Order表txnData5数据
 *
 * <AUTHOR>
 * @create 2022-05-09 11:50
 **/
public class TxnData5Bean implements Serializable {

    private static final long serialVersionUID = -8375032567046376400L;

    //节点
    private String n;

    //创建时间
    private String ct;

    /**
    * @Description:  订单转移日表标识，1表示需要转移
    * @Author: 程军
    * @Date: 2024/4/3 11:17
    */
    private String trsf;
    /**
     * @Description:  订单转移日表标识，1表示云秘订单，按云秘推送
     * @Author: 程军
     * @Date: 2024/4/3 11:17
     */
    private String ymOrder;

    /**
     * 附加订单类型
     * @see OrderSubAddTypeEnum
     * @return
     */
    private String subAddOrderType;

    public String getN() {
        return n;
    }

    public void setN(String n) {
        this.n = n;
    }

    public String getCt() {
        return ct;
    }

    public void setCt(String ct) {
        this.ct = ct;
    }



    public String getTrsf() {
        return trsf == null ? "" : trsf.trim();
    }

    public String getYmOrder() {
        return ymOrder == null ? "" : ymOrder.trim();
    }

    public void setYmOrder(String ymOrder) {
        this.ymOrder = ymOrder;
    }

    public void setTrsf(String trsf) {
        this.trsf = trsf;
    }

    public String getSubAddOrderType() {
        return subAddOrderType;
    }

    public void setSubAddOrderType(String subAddOrderType) {
        this.subAddOrderType = subAddOrderType;
    }

    public TxnData5Bean(String n, String ct, String trsf, String ymOrder, String subAddOrderType) {
        this.n = n;
        this.ct = ct;
        this.trsf = trsf;
        this.ymOrder = ymOrder;
        this.subAddOrderType = subAddOrderType;
    }

    public TxnData5Bean(String n, String ct, String trsf, String ymOrder) {
        this.n = n;
        this.ct = ct;
        this.trsf = trsf;
        this.ymOrder = ymOrder;
    }

    public TxnData5Bean(String n, String trsf, String ymOrder) {
        this.n = n;
        this.trsf = trsf;
        this.ymOrder = ymOrder;
    }

    public TxnData5Bean() {
    }

    public TxnData5Bean(String n, String trsf) {
        this.n = n;
        this.trsf = trsf;
    }


    /**
    * @Description: 转换成数据库存储格式，字段间用|分隔
    节点,创建时间,订单转移日表标识
    * @return:  
    * @Author: 程军 
    * @Date: 2024/4/3 12:32
    */
    public String toDbStr()
    {
        LogWriter.info(String.format("txndata5转换订单表记录格式begin json=%s", JSONObject.toJSONString(this)));
        StringBuffer buffer=new StringBuffer();
        buffer.append(StringUtils.trimToEmpty(getN()));
        buffer.append("|").append(StringUtils.trimToEmpty(getCt()));
        buffer.append("|").append(StringUtils.trimToEmpty(getTrsf()));
        buffer.append("|").append(StringUtils.trimToEmpty(getYmOrder()));
        buffer.append("|").append(StringUtils.trimToEmpty(getSubAddOrderType()));
        LogWriter.info(String.format("txndata5转换订单表记录格式end str=%s", buffer.toString()));
        return buffer.toString();
    }

    public static TxnData5Bean parseObject(String dbStrTxnData5)
    {
        try {
            LogWriter.info(String.format("订单表记录txndata5转换对象begin dbStrTxnData5=%s", dbStrTxnData5));
            if (StringUtils.isBlank(dbStrTxnData5)) {
                LogWriter.info(String.format("订单表记录txndata5转换对象end dbStrTxnData5为空"));
                return null;
            }
            String[] array = StringUtils.trimToEmpty(dbStrTxnData5).split("\\|");
            if (array == null || array.length < 1) {
                LogWriter.info(String.format("订单表记录txndata5转换对象end dbStrTxnData5分隔数组为空"));
                return null;
            }
            TxnData5Bean result = new TxnData5Bean();
            if (array.length >= 1) {
                result.setN(StringUtils.trimToEmpty(array[0]));
            }
            if (array.length >= 2) {
                result.setCt(StringUtils.trimToEmpty(array[1]));
            }
            if (array.length >= 3) {
                result.setTrsf(StringUtils.trimToEmpty(array[2]));
            }
            if (array.length >= 4) {
                result.setYmOrder(StringUtils.trimToEmpty(array[3]));
            }
            if (array.length >= 5) {
                result.setSubAddOrderType(StringUtils.trimToEmpty(array[4]));
            }
            LogWriter.info(String.format("订单表记录txndata5转换对象end result=%s", JSONObject.toJSONString(result)));
            return result;
        }catch (Exception e)
        {
            LogWriter.error(String.format("订单表记录txndata5转换对象Exception"),e);
        }
        return null;
    }
}
