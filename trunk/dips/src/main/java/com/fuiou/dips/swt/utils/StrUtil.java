package com.fuiou.dips.swt.utils;

import java.util.Collection;
import java.util.Iterator;

public class StrUtil {

	/**
	 * 左补零
	 * 
	 * <p>
	 * 输入参数 obj 为 null 时按空字符串处理<br>
	 * 输入参数 obj 非 String 时会被String.valueOf(obj) 转换为字符型处理<br>
	 * allowOver 为 false 时如果字符串超过指定长度则从右侧截取指定长度字符串返回
	 * </p>
	 * 
	 * @param obj
	 * @param length
	 *            限定的长度
	 * @param allowOver
	 *            是否可以超过指定长度
	 * @return
	 */
	public static String leftFillZero(Object obj, int length, boolean allowOver) {

		StringBuilder sb = new StringBuilder(obj == null ? ""
				: String.valueOf(obj));

		if (!allowOver && sb.length() > length)
			return sb.substring(sb.length() - length);

		for (int i = sb.length(); i < length; i++)
			sb.insert(0, "0");

		return sb.toString();
	}

	/**
	 * 右补零
	 * 
	 * <p>
	 * 输入参数 obj 为 null 时按空字符串处理<br>
	 * 输入参数 obj 非 String 时会被String.valueOf(obj) 转换为字符型处理<br>
	 * allowOver 为 false 时如果字符串超过指定长度则从左侧截取指定长度字符串返回
	 * </p>
	 * 
	 * @param obj
	 * @param length
	 *            限定的长度
	 * @param allowOver
	 *            是否可以超过指定长度
	 * @return
	 */
	public static String rightFillZero(Object obj, int length, boolean allowOver) {

		StringBuilder sb = new StringBuilder(obj == null ? ""
				: String.valueOf(obj));

		if (!allowOver && sb.length() > length)
			return sb.substring(0, length);

		for (int i = sb.length(); i < length; i++)
			sb.append("0");

		return sb.toString();
	}

	public static String rightFill(Object obj, int length, boolean allowOver,
			String fill) {

		StringBuilder sb = new StringBuilder(obj == null ? ""
				: String.valueOf(obj));

		if (!allowOver && sb.length() > length)
			return sb.substring(0, length);

		for (int i = sb.length(); i < length; i++)
			sb.append(fill);

		return sb.toString();
	}

	/**
	 * 判断是否为空白
	 * 
	 * @param cs
	 * @return
	 */
	public static boolean isBlank(CharSequence cs) {
		int strLen;
		if (cs == null || (strLen = cs.length()) == 0) {
			return true;
		}
		for (int i = 0; i < strLen; i++) {
			if (Character.isWhitespace(cs.charAt(i)) == false) {
				return false;
			}
		}
		return true;
	}
	
	public static String join(Collection<?> coll, String delim, String prefix,
			String suffix) {
		
		StringBuilder str = new StringBuilder();
		Iterator<?> it = coll.iterator();
		while(it.hasNext()) {
			str.append(prefix).append(it.next()).append(suffix);
			if (it.hasNext())
				str.append(delim);
		}
		return str.toString();
	}

	public static <T>String join(char[] coll, String delim, String prefix,
			String suffix) {
		StringBuilder str = new StringBuilder();
		for (int i = 0; i < coll.length;) {
			str.append(prefix).append(coll[i]).append(suffix);
			if (++i < coll.length)
				str.append(delim);
		}
		return str.toString();
	}
	
	public static String Long2String(Object object, int paramInt){
		if (object == null) {
			return null;
		}
		String str = object.toString();
		if (str.length() >= paramInt) {
			return str.substring(str.length() - paramInt);
	    }
		int i = str.length();
	    for (int j = 0; j < paramInt - i; j++) {
	    	str = "0" + str;
	    }
	    return str;
	}
}
