package com.fuiou.dips.persist.beans.tpay;

/**
 * 终端订单表
 * @TableName t_terminals_order_info
 */
public class TerminalsOrderInfoDTO extends TerminalsOrderInfo {


    /**
    * 用于更新时乐观锁标记
    * @Author: Joker
    * @Date: 2025/5/19 15:51
    */

    private String oldStatus;
    private Integer oldOrderAmt;
    private Integer oldRefundAmt;
    private Integer oldCouponFee;
    /**
     * 备用
     */
    private String oldReserve10;

    public String getOldStatus() {

        return oldStatus == null ? null : oldStatus.trim();
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public Integer getOldOrderAmt() {

        return oldOrderAmt;
    }

    public void setOldOrderAmt(Integer oldOrderAmt) {
        this.oldOrderAmt = oldOrderAmt;
    }

    public Integer getOldRefundAmt() {

        return oldRefundAmt;
    }

    public void setOldRefundAmt(Integer oldRefundAmt) {
        this.oldRefundAmt = oldRefundAmt;
    }

    private static final long serialVersionUID = 1L;

    public Integer getOldCouponFee() {

        return oldCouponFee;
    }

    public void setOldCouponFee(Integer oldCouponFee) {
        this.oldCouponFee = oldCouponFee;
    }

    public String getOldReserve10() {

        return oldReserve10 == null ? null : oldReserve10.trim();
    }

    public void setOldReserve10(String oldReserve10) {
        this.oldReserve10 = oldReserve10;
    }
}