package com.fuiou.dips.enums;

/**
 * <AUTHOR>
 * @Description redis key 枚举
 * @Date 2025/4/3 14:52
 **/
public enum RedisKeyEnum {

    login_token(String.format("%s:login_token:", RedisKeyEnum.REDIS_SYS_ID), "登录鉴权凭证，2小时过期", 60 * 60 * 2),
    wx_token(String.format("%s:wx_token:", RedisKeyEnum.REDIS_SYS_ID), "接口调用凭据，2小时过期", 59 * 60 * 2),
    sms_last_send_time(String.format("%s:sms_last_send_time:", RedisKeyEnum.REDIS_SYS_ID), "手机号上次发送短信时间，1分钟过期", 60 ),
    sms_code(String.format("%s:login_sms_code:", RedisKeyEnum.REDIS_SYS_ID), "手机验证码，10分钟过期", 60*10 );

    private static final String REDIS_SYS_ID = "DIPS";

    private String key_prefix;
    /***
     * <AUTHOR>
     * @Description 过期时间 ,单位秒，小于1表示永不过期
     * @Date 2025/4/3 16:16
     **/
    private int expireSecond;
    private String msg;

    RedisKeyEnum(String key_prefix, String msg, int expireSecond) {
        this.key_prefix = key_prefix;
        this.msg = msg;
        this.expireSecond = expireSecond;
    }

    RedisKeyEnum(String key_prefix, String msg) {
        this.key_prefix = key_prefix;
        this.msg = msg;
    }

    public String getKey_prefix() {
        return key_prefix;
    }

    public String getMsg() {
        return msg;
    }

    public int getExpireSecond() {
        return expireSecond;
    }

}
