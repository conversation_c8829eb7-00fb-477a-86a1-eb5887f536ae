<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.dips.persist.jfzwdb.GroupShopRelateMapper">

    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.GroupShopRelate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="shop_user_id" property="shopUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="reserve1" property="reserve1" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="BIGINT"/>
        <result column="mchnt_cd" property="mchntCd" jdbcType="CHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, shop_id, shop_user_id, create_time, update_time, reserve1, sn, mchnt_cd
    </sql>


    <select id="selecStoreIdsByGroupId"  resultMap="BaseResultMap">
        SELECT
            a.id,a.group_id,a.shop_id,a.shop_user_id,b.group_name
        FROM t_scte_group_shop_relate a
                 left join t_scte_group_inf b
                           on a.group_id = b.group_id where a.group_id = #{groupId,jdbcType=BIGINT}
    </select>

</mapper>