package com.fuiou.dips.framework.validator;

import cn.hutool.core.util.StrUtil;
import com.fuiou.dips.framework.annotation.ExcelValid;
import com.fuiou.dips.framework.annotation.ExcelValidationType;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Excel校验错误消息生成器
 * 根据校验参数自动生成友好的错误消息
 * 
 * <AUTHOR>
 */
public class ExcelValidationMessageGenerator {
    
    /**
     * 生成错误消息
     * 
     * @param field 字段
     * @param validation 校验注解
     * @param currentRowNum 当前行号
     * @return 格式化的错误消息
     */
    public static String generateMessage(Field field, ExcelValid validation, Integer currentRowNum) {
        // 如果提供了自定义消息，直接使用
        if (StrUtil.isNotBlank(validation.customMessage())) {
            return formatMessage(currentRowNum, validation.index(), validation.customMessage());
        }
        
        // 获取字段显示名称
        String fieldDisplayName = getFieldDisplayName(field, validation);
        
        // 根据校验类型生成消息
        String message = generateMessageByType(validation, fieldDisplayName);
        
        return formatMessage(currentRowNum, validation.index(), message);
    }
    
    /**
     * 获取字段显示名称
     */
    private static String getFieldDisplayName(Field field, ExcelValid validation) {
        if (StrUtil.isNotBlank(validation.fieldName())) {
            return validation.fieldName();
        }
        
        // 尝试从字段名转换为友好的显示名称
        return convertFieldNameToDisplayName(field.getName());
    }
    
    /**
     * 将字段名转换为显示名称
     * 例如：mchntCd -> 商户号, customerName -> 客户姓名
     */
    private static String convertFieldNameToDisplayName(String fieldName) {
        // 这里可以根据实际需要添加更多的映射规则
        switch (fieldName) {
            case "mchntCd": return "商户号";
            case "storeName": return "所属门店";
            case "customerName": return "客户姓名";
            case "phone": return "客户手机号";
            case "projectAmt": return "项目总金额";
            case "loginId": return "老板登录账号";
            case "projectSt": return "项目状态";
            case "stageOrder": return "阶段顺序";
            case "stageName": return "阶段名称";
            case "stageAmt": return "阶段应收金额";
            case "stageActualAmt": return "阶段实收金额";
            case "stageSt": return "阶段状态";
            case "createTime": return "创建时间";
            default: return fieldName;
        }
    }
    
    /**
     * 根据校验类型生成消息
     */
    private static String generateMessageByType(ExcelValid validation, String fieldDisplayName) {
        switch (validation.type()) {
            case NOT_NULL:
                return fieldDisplayName + "不能为空";
                
            case STRING_LENGTH:
                return generateStringLengthMessage(validation, fieldDisplayName);
                
            case LONG_PARSE:
                return fieldDisplayName + "必须是有效的长整数";
                
            case INTEGER_PARSE:
                return fieldDisplayName + "必须是有效的整数";
                
            case DOUBLE_PARSE:
                return fieldDisplayName + "必须是有效的数字";
                
            case STOP_ANALYZE:
                return fieldDisplayName + "为空，解析终止";
                
            case LOCAL_DATE_PARSE:
                return fieldDisplayName + "日期格式不正确，应为：" + validation.dateFormat();
                
            case LOCAL_DATE_TIME_PARSE:
                return fieldDisplayName + "日期时间格式不正确，应为：" + validation.dateTimeFormat();
                
            case REGEX_PATTERN:
                return generateRegexMessage(validation, fieldDisplayName);
                
            case NUMBER_RANGE:
                return generateNumberRangeMessage(validation, fieldDisplayName);
                
            case ENUM_VALUE:
                return generateEnumValueMessage(validation, fieldDisplayName);
                
            default:
                return fieldDisplayName + "校验失败";
        }
    }
    
    /**
     * 生成字符串长度校验消息
     */
    private static String generateStringLengthMessage(ExcelValid validation, String fieldDisplayName) {
        StringBuilder sb = new StringBuilder();
        sb.append(fieldDisplayName);
        
        if (validation.maxLength() != Integer.MAX_VALUE && validation.minLength() > 0) {
            sb.append("长度必须在").append(validation.minLength())
              .append("到").append(validation.maxLength()).append("个字符之间");
        } else if (validation.maxLength() != Integer.MAX_VALUE) {
            sb.append("长度不能超过").append(validation.maxLength()).append("个字符");
        } else if (validation.minLength() > 0) {
            sb.append("长度不能少于").append(validation.minLength()).append("个字符");
        }
        
        if (validation.maxZhLength() != Integer.MAX_VALUE) {
            if (sb.length() > fieldDisplayName.length()) {
                sb.append("，");
            }
            sb.append("字节长度不能超过").append(validation.maxZhLength()).append("字节");
        }
        
        return sb.toString();
    }
    
    /**
     * 生成正则表达式校验消息
     */
    private static String generateRegexMessage(ExcelValid validation, String fieldDisplayName) {
        // 根据常见的正则表达式模式提供友好的消息
        String pattern = validation.pattern();
        
        if (pattern.equals("^1[3-9]\\d{9}$")) {
            return fieldDisplayName + "格式不正确，请输入有效的手机号";
        } else if (pattern.contains("@")) {
            return fieldDisplayName + "格式不正确，请输入有效的邮箱地址";
        } else if (pattern.contains("\\d")) {
            return fieldDisplayName + "格式不正确，必须包含数字";
        } else {
            return fieldDisplayName + "格式不正确";
        }
    }
    
    /**
     * 生成数值范围校验消息
     */
    private static String generateNumberRangeMessage(ExcelValid validation, String fieldDisplayName) {
        boolean hasLongRange = validation.minValue() != Long.MIN_VALUE || validation.maxValue() != Long.MAX_VALUE;
        boolean hasDoubleRange = validation.minDoubleValue() != Double.MIN_VALUE || validation.maxDoubleValue() != Double.MAX_VALUE;
        
        if (hasLongRange) {
            if (validation.minValue() != Long.MIN_VALUE && validation.maxValue() != Long.MAX_VALUE) {
                return fieldDisplayName + "必须在" + validation.minValue() + "到" + validation.maxValue() + "之间";
            } else if (validation.minValue() != Long.MIN_VALUE) {
                return fieldDisplayName + "不能小于" + validation.minValue();
            } else if (validation.maxValue() != Long.MAX_VALUE) {
                return fieldDisplayName + "不能大于" + validation.maxValue();
            }
        }
        
        if (hasDoubleRange) {
            if (validation.minDoubleValue() != Double.MIN_VALUE && validation.maxDoubleValue() != Double.MAX_VALUE) {
                return String.format("%s必须在%.2f到%.2f之间", fieldDisplayName, validation.minDoubleValue(), validation.maxDoubleValue());
            } else if (validation.minDoubleValue() != Double.MIN_VALUE) {
                return String.format("%s不能小于%.2f", fieldDisplayName, validation.minDoubleValue());
            } else if (validation.maxDoubleValue() != Double.MAX_VALUE) {
                return String.format("%s不能大于%.2f", fieldDisplayName, validation.maxDoubleValue());
            }
        }
        
        return fieldDisplayName + "数值范围不正确";
    }
    
    /**
     * 生成枚举值校验消息
     */
    private static String generateEnumValueMessage(ExcelValid validation, String fieldDisplayName) {
        if (validation.allowedValues().length == 0) {
            return fieldDisplayName + "值不在允许的范围内";
        }
        
        String allowedValuesStr = Arrays.stream(validation.allowedValues())
                .collect(Collectors.joining("、"));
        
        return fieldDisplayName + "只能是：" + allowedValuesStr;
    }
    
    /**
     * 格式化最终的错误消息
     */
    private static String formatMessage(Integer currentRowNum, int columnIndex, String message) {
        return String.format("第%d行第%d列：%s", currentRowNum, columnIndex + 1, message);
    }
}
