package com.fuiou.dips.framework.validator;

import cn.hutool.core.util.StrUtil;
import com.fuiou.dips.framework.annotation.ExcelValid;
import com.fuiou.dips.framework.annotation.ExcelValidationType;
import com.fuiou.dips.framework.annotation.ExcelValids;
import com.fuiou.dips.framework.exception.ExcelStopAnalyzeException;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.utils.LogWriter;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * Excel字段校验器 V2版本
 * 支持新的简化注解和自动消息生成
 * 
 * <AUTHOR>
 */
public class ExcelFieldValidatorV2 {
    
    /**
     * 校验字段
     * 
     * @param data 数据对象
     * @param field 字段
     * @param currentRowNum 当前行号
     * @throws FUException 校验失败异常
     * @throws ExcelStopAnalyzeException 停止解析异常
     */
    public static void validateField(Object data, Field field, Integer currentRowNum) 
            throws FUException, ExcelStopAnalyzeException {
        
        // 获取字段上的所有校验注解
        List<ExcelValid> validations = getValidations(field);
        
        if (validations.isEmpty()) {
            return;
        }
        
        // 按order排序执行校验
        validations.sort((v1, v2) -> Integer.compare(v1.order(), v2.order()));
        
        for (ExcelValid validation : validations) {
            try {
                validateSingle(data, field, validation, currentRowNum);
            } catch (IllegalAccessException e) {
                LogWriter.error("字段访问异常: {}", e.getMessage(), e);
                throw new FUException("9999", "字段访问异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取字段上的所有校验注解
     */
    private static List<ExcelValid> getValidations(Field field) {
        List<ExcelValid> validations;
        
        // 检查是否有ExcelValids注解（多个校验）
        ExcelValids validsAnnotation = field.getAnnotation(ExcelValids.class);
        if (validsAnnotation != null) {
            validations = Arrays.asList(validsAnnotation.value());
        } else {
            // 检查是否有单个ExcelValid注解
            ExcelValid validation = field.getAnnotation(ExcelValid.class);
            if (validation != null) {
                validations = Arrays.asList(validation);
            } else {
                validations = Arrays.asList();
            }
        }
        
        return validations;
    }
    
    /**
     * 执行单个校验
     */
    private static void validateSingle(Object data, Field field, ExcelValid validation, Integer currentRowNum) 
            throws IllegalAccessException, FUException, ExcelStopAnalyzeException {
        
        field.setAccessible(true);
        Object fieldValue = field.get(data);
        
        switch (validation.type()) {
            case NOT_NULL:
                validateNotNull(fieldValue, field, validation, currentRowNum);
                break;
            case STRING_LENGTH:
                validateStringLength(fieldValue, field, validation, currentRowNum);
                break;
            case LONG_PARSE:
                validateLongParse(fieldValue, field, validation, currentRowNum);
                break;
            case INTEGER_PARSE:
                validateIntegerParse(fieldValue, field, validation, currentRowNum);
                break;
            case DOUBLE_PARSE:
                validateDoubleParse(fieldValue, field, validation, currentRowNum);
                break;
            case STOP_ANALYZE:
                validateStopAnalyze(fieldValue, field, validation, currentRowNum);
                break;
            case LOCAL_DATE_PARSE:
                validateLocalDateParse(fieldValue, field, validation, currentRowNum);
                break;
            case LOCAL_DATE_TIME_PARSE:
                validateLocalDateTimeParse(fieldValue, field, validation, currentRowNum);
                break;
            case REGEX_PATTERN:
                validateRegexPattern(fieldValue, field, validation, currentRowNum);
                break;
            case NUMBER_RANGE:
                validateNumberRange(fieldValue, field, validation, currentRowNum);
                break;
            case ENUM_VALUE:
                validateEnumValue(fieldValue, field, validation, currentRowNum);
                break;
            default:
                LogWriter.warn("未知的校验类型: {}", validation.type());
        }
    }
    
    /**
     * 非空校验
     */
    private static void validateNotNull(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (Objects.isNull(fieldValue)) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            throw new FUException("9906", message);
        }
    }
    
    /**
     * 字符串长度校验
     */
    private static void validateStringLength(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = fieldValue.toString();
        if (StrUtil.isBlank(strValue)) {
            return;
        }
        
        boolean isValid = true;
        
        // 字符长度校验
        if (strValue.length() > validation.maxLength()) {
            isValid = false;
        }
        
        if (strValue.length() < validation.minLength()) {
            isValid = false;
        }
        
        // 中文字节长度校验
        if (validation.maxZhLength() != Integer.MAX_VALUE) {
            if (checkCommentLengthIsOut(strValue, validation.maxZhLength())) {
                isValid = false;
            }
        }
        
        if (!isValid) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            throw new FUException("9901", message);
        }
    }
    
    /**
     * 计算中文字节长度是否超出
     */
    private static boolean checkCommentLengthIsOut(String comments, Integer length) {
        if (StrUtil.isNotBlank(comments)) {
            try {
                int wordCountCode = comments.getBytes("UTF-8").length;
                return wordCountCode > length;
            } catch (UnsupportedEncodingException e) {
                LogWriter.error("字符编码异常: {}", e.getMessage(), e);
                throw new FUException("9904", e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * Long类型转换校验
     */
    private static void validateLongParse(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Long.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            LogWriter.error("Long类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9905", message);
        }
    }
    
    /**
     * Integer类型转换校验
     */
    private static void validateIntegerParse(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Integer.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            LogWriter.error("Integer类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9910", message);
        }
    }
    
    /**
     * Double类型转换校验
     */
    private static void validateDoubleParse(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Double.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            LogWriter.error("Double类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9911", message);
        }
    }
    
    /**
     * 停止解析校验
     */
    private static void validateStopAnalyze(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws ExcelStopAnalyzeException {
        if (Objects.isNull(fieldValue)) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            throw new ExcelStopAnalyzeException("9908", message, null);
        }
    }
    
    /**
     * 日期解析校验
     */
    private static void validateLocalDateParse(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(validation.dateFormat());
            LocalDate.parse(strValue, formatter);
        } catch (DateTimeParseException e) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            LogWriter.error("日期解析异常: {}", e.getMessage(), e);
            throw new FUException("9912", message);
        }
    }
    
    /**
     * 日期时间解析校验
     */
    private static void validateLocalDateTimeParse(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(validation.dateTimeFormat());
            LocalDateTime.parse(strValue, formatter);
        } catch (DateTimeParseException e) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            LogWriter.error("日期时间解析异常: {}", e.getMessage(), e);
            throw new FUException("9913", message);
        }
    }

    /**
     * 正则表达式校验
     */
    private static void validateRegexPattern(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue)) {
            return;
        }

        if (StrUtil.isBlank(validation.pattern())) {
            LogWriter.warn("正则表达式模式为空，跳过校验");
            return;
        }

        try {
            Pattern pattern = Pattern.compile(validation.pattern());
            if (!pattern.matcher(strValue).matches()) {
                String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
                throw new FUException("9914", message);
            }
        } catch (Exception e) {
            LogWriter.error("正则表达式校验异常: {}", e.getMessage(), e);
            throw new FUException("9915",
                String.format("第%d行第%d列：正则表达式校验异常", currentRowNum, validation.index() + 1));
        }
    }

    /**
     * 数值范围校验
     */
    private static void validateNumberRange(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }

        try {
            boolean isValid = true;

            // 尝试解析为数值
            if (strValue.contains(".")) {
                // 包含小数点，按Double处理
                double doubleValue = Double.parseDouble(strValue);
                if (validation.minDoubleValue() != Double.MIN_VALUE && doubleValue < validation.minDoubleValue()) {
                    isValid = false;
                }
                if (validation.maxDoubleValue() != Double.MAX_VALUE && doubleValue > validation.maxDoubleValue()) {
                    isValid = false;
                }
            } else {
                // 整数处理
                long longValue = Long.parseLong(strValue);
                if (validation.minValue() != Long.MIN_VALUE && longValue < validation.minValue()) {
                    isValid = false;
                }
                if (validation.maxValue() != Long.MAX_VALUE && longValue > validation.maxValue()) {
                    isValid = false;
                }
            }

            if (!isValid) {
                String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
                throw new FUException("9916", message);
            }
        } catch (NumberFormatException e) {
            LogWriter.error("数值解析异常: {}", e.getMessage(), e);
            throw new FUException("9917",
                String.format("第%d行第%d列：数值格式不正确", currentRowNum, validation.index() + 1));
        }
    }

    /**
     * 枚举值校验
     */
    private static void validateEnumValue(Object fieldValue, Field field, ExcelValid validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue)) {
            return;
        }

        if (validation.allowedValues().length == 0) {
            LogWriter.warn("允许的枚举值为空，跳过校验");
            return;
        }

        List<String> allowedList = Arrays.asList(validation.allowedValues());
        if (!allowedList.contains(strValue)) {
            String message = ExcelValidationMessageGenerator.generateMessage(field, validation, currentRowNum);
            throw new FUException("9918", message);
        }
    }
}
