package com.fuiou.dips.services;

import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.ProjectCustomer;
import com.fuiou.dips.persist.dipsdb.ProjectCustomerMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 项目客户关联服务
 *
 * <AUTHOR>
 */
@Service
public class ProjectCustomerService {

    @Resource
    private ProjectCustomerMapper projectCustomerMapper;

    public int insert(String projectNo, String mchntCd, String phone) {
        // 保存项目客户关联表
        ProjectCustomer projectCustomer = new ProjectCustomer();
        projectCustomer.setProjectNo(projectNo);
        projectCustomer.setMchntCd(mchntCd);
        projectCustomer.setPhone(phone);
        return projectCustomerMapper.insert(projectCustomer);
    }

    public int updatePhoneByPrimaryKey(Long rowId, String phone) {
        ProjectCustomer customer = new ProjectCustomer();
        customer.setRowId(rowId);
        customer.setPhone(phone);
        return projectCustomerMapper.updateByPrimaryKey(customer);
    }

    public int updatePhoneByMchntCdAndOldPhone(String newPhone, String mchntCd, String oldPhone) {
        return projectCustomerMapper.updatePhoneByMchntCdAndOldPhone(newPhone, mchntCd, oldPhone);
    }

    @LogAnnotation("项目客户信息-详情")
    public ProjectCustomer selectByProjectNoAndMchntCdAndPhone(String projectNo, String mchntCd, String phone) {
        return projectCustomerMapper.selectByProjectNoAndMchntCdAndPhone(projectNo, mchntCd, phone);
    }

    public void deleteByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        projectCustomerMapper.deleteByProjectNoAndMchntCd(projectNo, mchntCd);
    }
}
