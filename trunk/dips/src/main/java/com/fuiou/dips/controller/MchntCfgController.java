package com.fuiou.dips.controller;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.persist.beans.MchntCfg;
import com.fuiou.dips.services.MchntCfgService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商户配置信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mchnt/cfg")
public class MchntCfgController {

    @Resource
    private MchntCfgService mchntCfgService;

    /**
     * 根据商户号，获取商户配置信息
     */
    @GetMapping
    @ResponseBody
    public ResponseEntity<MchntCfg> detailByMchntCd() {
        return ResponseEntityFactory.ok(
                mchntCfgService.getDetailByMchntCd(LoginConstat.getLoginToken().getMchntInfo().getMchntCd()));
    }
}
