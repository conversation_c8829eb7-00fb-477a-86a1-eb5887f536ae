package com.fuiou.dips.framework.validator;

import cn.hutool.core.util.StrUtil;
import com.fuiou.dips.framework.annotation.ExcelValidation;
import com.fuiou.dips.framework.annotation.ExcelValidationType;
import com.fuiou.dips.framework.annotation.ExcelValidations;
import com.fuiou.dips.framework.exception.ExcelStopAnalyzeException;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.utils.LogWriter;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Excel字段校验器
 * 
 * <AUTHOR>
 */
public class ExcelFieldValidator {
    
    /**
     * 校验字段
     * 
     * @param data 数据对象
     * @param field 字段
     * @param currentRowNum 当前行号
     * @throws FUException 校验失败异常
     * @throws ExcelStopAnalyzeException 停止解析异常
     */
    public static void validateField(Object data, Field field, Integer currentRowNum) 
            throws FUException, ExcelStopAnalyzeException {
        
        // 获取字段上的所有校验注解
        List<ExcelValidation> validations = getValidations(field);
        
        if (validations.isEmpty()) {
            return;
        }
        
        // 按order排序执行校验
        validations.sort((v1, v2) -> Integer.compare(v1.order(), v2.order()));
        
        for (ExcelValidation validation : validations) {
            try {
                validateSingle(data, field, validation, currentRowNum);
            } catch (IllegalAccessException e) {
                LogWriter.error("字段访问异常: {}", e.getMessage(), e);
                throw new FUException("9999", "字段访问异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取字段上的所有校验注解
     */
    private static List<ExcelValidation> getValidations(Field field) {
        List<ExcelValidation> validations = Arrays.asList();
        
        // 检查是否有ExcelValidations注解（多个校验）
        ExcelValidations validationsAnnotation = field.getAnnotation(ExcelValidations.class);
        if (validationsAnnotation != null) {
            validations = Arrays.asList(validationsAnnotation.value());
        } else {
            // 检查是否有单个ExcelValidation注解
            ExcelValidation validation = field.getAnnotation(ExcelValidation.class);
            if (validation != null) {
                validations = Arrays.asList(validation);
            }
        }
        
        return validations;
    }
    
    /**
     * 执行单个校验
     */
    private static void validateSingle(Object data, Field field, ExcelValidation validation, Integer currentRowNum) 
            throws IllegalAccessException, FUException, ExcelStopAnalyzeException {
        
        field.setAccessible(true);
        Object fieldValue = field.get(data);
        
        switch (validation.type()) {
            case NOT_NULL:
                validateNotNull(fieldValue, validation, currentRowNum);
                break;
            case STRING_LENGTH:
                validateStringLength(fieldValue, validation, currentRowNum);
                break;
            case LONG_PARSE:
                validateLongParse(fieldValue, validation, currentRowNum);
                break;
            case INTEGER_PARSE:
                validateIntegerParse(fieldValue, validation, currentRowNum);
                break;
            case DOUBLE_PARSE:
                validateDoubleParse(fieldValue, validation, currentRowNum);
                break;
            case STOP_ANALYZE:
                validateStopAnalyze(fieldValue, validation, currentRowNum);
                break;
            case LOCAL_DATE_PARSE:
                validateLocalDateParse(fieldValue, validation, currentRowNum);
                break;
            case LOCAL_DATE_TIME_PARSE:
                validateLocalDateTimeParse(fieldValue, validation, currentRowNum);
                break;
            case REGEX_PATTERN:
                validateRegexPattern(fieldValue, validation, currentRowNum);
                break;
            case NUMBER_RANGE:
                validateNumberRange(fieldValue, validation, currentRowNum);
                break;
            case ENUM_VALUE:
                validateEnumValue(fieldValue, validation, currentRowNum);
                break;
            default:
                LogWriter.warn("未知的校验类型: {}", validation.type());
        }
    }
    
    /**
     * 非空校验
     */
    private static void validateNotNull(Object fieldValue, ExcelValidation validation, Integer currentRowNum) 
            throws FUException {
        if (Objects.isNull(fieldValue)) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "当前字段不能为空";
            throw new FUException("9906", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }
    
    /**
     * 字符串长度校验
     */
    private static void validateStringLength(Object fieldValue, ExcelValidation validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = fieldValue.toString();
        if (StrUtil.isBlank(strValue)) {
            return;
        }
        
        // 字符长度校验
        if (strValue.length() > validation.maxLength()) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "字符长度不能超过" + validation.maxLength() + "个字符";
            throw new FUException("9901", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
        
        if (strValue.length() < validation.minLength()) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "字符长度不能少于" + validation.minLength() + "个字符";
            throw new FUException("9901", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
        
        // 中文字节长度校验
        if (validation.maxZhLength() != Integer.MAX_VALUE) {
            if (checkCommentLengthIsOut(strValue, validation.maxZhLength())) {
                String message = StrUtil.isNotBlank(validation.message()) ? 
                    validation.message() : "字节长度不能超过" + validation.maxZhLength() + "字节";
                throw new FUException("9902", 
                    String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
            }
        }
    }
    
    /**
     * 计算中文字节长度是否超出
     */
    private static boolean checkCommentLengthIsOut(String comments, Integer length) {
        if (StrUtil.isNotBlank(comments)) {
            try {
                int wordCountCode = comments.getBytes("UTF-8").length;
                return wordCountCode > length;
            } catch (UnsupportedEncodingException e) {
                LogWriter.error("字符编码异常: {}", e.getMessage(), e);
                throw new FUException("9904", e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * Long类型转换校验
     */
    private static void validateLongParse(Object fieldValue, ExcelValidation validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Long.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "当前字段不能转换为长整型数字类型";
            LogWriter.error("Long类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9905", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }
    
    /**
     * Integer类型转换校验
     */
    private static void validateIntegerParse(Object fieldValue, ExcelValidation validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Integer.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "当前字段不能转换为整型数字类型";
            LogWriter.error("Integer类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9910", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }
    
    /**
     * Double类型转换校验
     */
    private static void validateDoubleParse(Object fieldValue, ExcelValidation validation, Integer currentRowNum) 
            throws FUException {
        if (fieldValue == null) {
            return;
        }
        
        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }
        
        try {
            Double.valueOf(strValue);
        } catch (NumberFormatException e) {
            String message = StrUtil.isNotBlank(validation.message()) ? 
                validation.message() : "当前字段不能转换为双精度数字类型";
            LogWriter.error("Double类型转换异常: {}", e.getMessage(), e);
            throw new FUException("9911", 
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }
    
    /**
     * 停止解析校验
     */
    private static void validateStopAnalyze(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws ExcelStopAnalyzeException {
        if (Objects.isNull(fieldValue)) {
            String message = StrUtil.isNotBlank(validation.message()) ?
                validation.message() : "当前字段为空，表格解析终止";
            throw new ExcelStopAnalyzeException("9908",
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message), null);
        }
    }

    /**
     * 日期解析校验
     */
    private static void validateLocalDateParse(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(validation.dateFormat());
            LocalDate.parse(strValue, formatter);
        } catch (DateTimeParseException e) {
            String message = StrUtil.isNotBlank(validation.message()) ?
                validation.message() : "当前字段不能转换为日期格式";
            LogWriter.error("日期解析异常: {}", e.getMessage(), e);
            throw new FUException("9912",
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }

    /**
     * 日期时间解析校验
     */
    private static void validateLocalDateTimeParse(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(validation.dateTimeFormat());
            LocalDateTime.parse(strValue, formatter);
        } catch (DateTimeParseException e) {
            String message = StrUtil.isNotBlank(validation.message()) ?
                validation.message() : "当前字段不能转换为日期时间格式";
            LogWriter.error("日期时间解析异常: {}", e.getMessage(), e);
            throw new FUException("9913",
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }

    /**
     * 正则表达式校验
     */
    private static void validateRegexPattern(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue)) {
            return;
        }

        if (StrUtil.isBlank(validation.pattern())) {
            LogWriter.warn("正则表达式模式为空，跳过校验");
            return;
        }

        try {
            Pattern pattern = Pattern.compile(validation.pattern());
            if (!pattern.matcher(strValue).matches()) {
                String message = StrUtil.isNotBlank(validation.message()) ?
                    validation.message() : "当前字段格式不正确";
                throw new FUException("9914",
                    String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
            }
        } catch (Exception e) {
            LogWriter.error("正则表达式校验异常: {}", e.getMessage(), e);
            throw new FUException("9915",
                String.format("第%d行第%d列：正则表达式校验异常", currentRowNum, validation.index()));
        }
    }

    /**
     * 数值范围校验
     */
    private static void validateNumberRange(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue) || "null".equals(strValue)) {
            return;
        }

        try {
            // 尝试解析为Long
            if (strValue.contains(".")) {
                // 包含小数点，按Double处理
                double doubleValue = Double.parseDouble(strValue);
                if (doubleValue < validation.minDoubleValue() || doubleValue > validation.maxDoubleValue()) {
                    String message = StrUtil.isNotBlank(validation.message()) ?
                        validation.message() : String.format("数值必须在%.2f到%.2f之间",
                            validation.minDoubleValue(), validation.maxDoubleValue());
                    throw new FUException("9916",
                        String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
                }
            } else {
                // 整数处理
                long longValue = Long.parseLong(strValue);
                if (longValue < validation.minValue() || longValue > validation.maxValue()) {
                    String message = StrUtil.isNotBlank(validation.message()) ?
                        validation.message() : String.format("数值必须在%d到%d之间",
                            validation.minValue(), validation.maxValue());
                    throw new FUException("9916",
                        String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
                }
            }
        } catch (NumberFormatException e) {
            LogWriter.error("数值解析异常: {}", e.getMessage(), e);
            throw new FUException("9917",
                String.format("第%d行第%d列：数值格式不正确", currentRowNum, validation.index()));
        }
    }

    /**
     * 枚举值校验
     */
    private static void validateEnumValue(Object fieldValue, ExcelValidation validation, Integer currentRowNum)
            throws FUException {
        if (fieldValue == null) {
            return;
        }

        String strValue = String.valueOf(fieldValue);
        if (StrUtil.isBlank(strValue)) {
            return;
        }

        if (validation.allowedValues().length == 0) {
            LogWriter.warn("允许的枚举值为空，跳过校验");
            return;
        }

        List<String> allowedList = Arrays.asList(validation.allowedValues());
        if (!allowedList.contains(strValue)) {
            String message = StrUtil.isNotBlank(validation.message()) ?
                validation.message() : "当前字段值不在允许的范围内，允许的值：" +
                    String.join(", ", validation.allowedValues());
            throw new FUException("9918",
                String.format("第%d行第%d列：%s", currentRowNum, validation.index(), message));
        }
    }
}
