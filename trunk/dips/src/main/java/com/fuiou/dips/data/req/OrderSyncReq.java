package com.fuiou.dips.data.req;

import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.SignSort;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;

/**
 * 订单信息同步请求对象
 *
 * @author: Joker
 * @create: 2025/05/20 20:13
 */
public class OrderSyncReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商户号
     */
    @NotBlank(message = "商户编号不能为空")
    @Size(min = 5, max = 15, message = "商户编号需在5-15字符")
    private String mchntCd;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Size(min = 1, max = 50, message = "支付类型需在1-50个字符")
    private String orderNo;

    @NotBlank(message = "订单类型不能为空")
    @Size(max = 8, message = "支付类型需在8个字符")
    private String orderType;

    /**
     * 订单金额，单位分
     */
    @NotNull(message = "订单金额不能为空")
    @Min(value = 0L, message = "订单金额不能为空")
    private Integer orderAmt;
    /**
     * 支付日期 yyyyMMdd
     */
    @NotBlank(message = "支付日期不能为空")
    @Size(min = 8, max = 8, message = "支付日期需在8个字符")
    private String tradeDt;

    /**
     * 优惠金额，单位分
     */
    @Min(value = 0L, message = "优惠金额不能为空")
    private Integer couponAmt;

    /**
     * 支付状态
     *
     * @see com.fuiou.dips.enums.OrderStatusEnum
     */
    @NotBlank(message = "支付状态不能为空")
    @Size(max = 8, message = "支付状态需在8个字符")
    private String payState;

    /**
     * 已退款金额，单位分
     */
    @Min(value = 0L, message = "已退款金额不能为空")
    private Integer refundAmt;

    /**
     * 应答码
     */
    @Size(max = 30, message = "应答码需在8个字符")
    private String respCode;

    /**
     * 应答描述
     */
    @Size(max = 8, message = "应答描述需在100个字符")
    private String respMsg;

    /**
     * 清算日期 yyyyMMdd
     */
    @Size(max = 8, message = "清算日期需在8个字符")
    private String fyFettleDt;

    /**
     * 富友内部追踪号
     */
    @Size(max = 32, message = "富友内部追踪号需在30个字符")
    private String fyTraceNo;

    /**
     * 条码流水
     */
    @Size(max = 32, message = "条码流水号需在32个字符")
    private String channelOrderId;

    /**
     * 渠道订单号
     */
    @Size(max = 32, message = "渠道订单号需在32个字符")
    private String transactionId;
    /**
     * 支付时间
     */
    @Size(min = 19, max = 19, message = "支付时间需在19个字符,格式yyyy-MM-dd HH:mm:ss")
    private String payTime;
    /**
     * 随机字符串
     */
    @NotBlank(message = "随机字符串不能为空")
    @Size(max = 40, message = "随机字符串需在40个字符")
    private String randomStr;
    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    public String getMchntCd() {

        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getOrderNo() {

        return orderNo == null ? null : orderNo.trim();
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {

        return orderType == null ? null : orderType.trim();
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderAmt() {

        return orderAmt;
    }

    public void setOrderAmt(Integer orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getTradeDt() {

        return tradeDt == null ? null : tradeDt.trim();
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public Integer getCouponAmt() {

        return couponAmt;
    }

    public void setCouponAmt(Integer couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getPayState() {

        return payState == null ? null : payState.trim();
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Integer getRefundAmt() {

        return refundAmt;
    }

    public void setRefundAmt(Integer refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getRespCode() {

        return respCode == null ? null : respCode.trim();
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {

        return respMsg == null ? null : respMsg.trim();
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getFyFettleDt() {

        return fyFettleDt == null ? null : fyFettleDt.trim();
    }

    public void setFyFettleDt(String fyFettleDt) {
        this.fyFettleDt = fyFettleDt;
    }

    public String getFyTraceNo() {

        return fyTraceNo == null ? null : fyTraceNo.trim();
    }

    public void setFyTraceNo(String fyTraceNo) {
        this.fyTraceNo = fyTraceNo;
    }

    public String getChannelOrderId() {

        return channelOrderId == null ? null : channelOrderId.trim();
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    public String getTransactionId() {

        return transactionId == null ? null : transactionId.trim();
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRandomStr() {

        return randomStr == null ? null : randomStr.trim();
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    public String getSign() {

        return sign == null ? null : sign.trim();
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String toSignStr() {
        String signStr = SignSort.generateSignContent(this, new HashSet<String>(Arrays.asList("sign", "reserved_")), true);
        LogWriter.info(this, String.format("待验签字符串为:%s", signStr));
        return signStr;
    }
}
