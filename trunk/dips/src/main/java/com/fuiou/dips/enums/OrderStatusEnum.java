package com.fuiou.dips.enums;

import com.fuiou.dips.utils.StringUtil;

import java.util.Arrays;

/**
 * 订单状态枚举类
 *
 * @Author: Joker
 * @Date: 2025/5/15 20:05
 */

public enum OrderStatusEnum {


    INIT_STATUS("00", "未支付状态"),
    PAY_SUCCESS("01", "已支付"),
    PAY_FAIL("02", "支付失败"),
    REFUND_SUCCESS("03", "已退款"),
    PAY_ING("04", "支付中"),
    REVOKED("05", "已撤销"),
    Returned("06", "已退货"),
    time_out("07", "超时"),
    PAFINISH("11", "预授权完成"),
    PFFINISH("12", "预授权完成退款");


    private String code;
    private String name;

    OrderStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {

        return code == null ? null : code.trim();
    }

    public String getName() {

        return name == null ? null : name.trim();
    }

    public static String getDescByOrderType(String code) {
        return Arrays.stream(values()).filter(e -> StringUtil.isNotBlank(code) && e.getCode().equals(code.trim()))
                .findFirst().map(OrderStatusEnum::getName).orElse(code);
    }
}
