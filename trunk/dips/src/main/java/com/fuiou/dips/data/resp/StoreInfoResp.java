package com.fuiou.dips.data.resp;

import cn.hutool.core.util.StrUtil;

import java.io.Serializable;
import java.util.Objects;

/**
 * 装修通门店信息
 *
 * <AUTHOR>
 */
public class StoreInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 终端ID
     */
    private String termId;

    public StoreInfoResp() {
    }

    public StoreInfoResp(String storeId, String storeName) {
        this.storeId = storeId;
        this.storeName = storeName;
    }

    /**
     * 获取门店ID
     */
    public String getStoreId() {
        return StrUtil.isNotBlank(storeId) ? storeId.trim() : "";
    }

    public String getTermId() {
        return termId;
    }

    public void setTermId(String termId) {
        this.termId = termId;
    }

    /**
     * 设置门店ID
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    /**
     * 获取门店名称
     */
    public String getStoreName() {
        return StrUtil.isNotBlank(storeName) ? storeName.trim() : "";
    }

    /**
     * 设置门店名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        StoreInfoResp storeInfoResp = (StoreInfoResp) o;

        return Objects.equals(storeId, storeInfoResp.storeId);
    }

    @Override
    public int hashCode() {
        return storeId != null ? storeId.hashCode() : 0;
    }
}
