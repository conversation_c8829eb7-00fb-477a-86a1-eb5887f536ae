package com.fuiou.dips.framework.filter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

public class XssRequestWrapper extends HttpServletRequestWrapper {


	public XssRequestWrapper(HttpServletRequest request) {
		super(request);
	}


	public String[] getParameterValues(String s) {
		String[] as = super.getParameterValues(s);
		return xssClean(as);
	}
	
	private String[] xssClean(String[] as) {
		if (as == null) 
			return null;
		int i = as.length;
		String as1[] = new String[i];
		for (int j = 0; j < i; j++) 
			as1[j] = XssCleaner.xssClean(as[j]);
		return as1;
	}

	@Override
	public Map<String, String[]> getParameterMap() {
		Map<String, String[]> cleanMap=new HashMap<String, String[]>();
		Map<String, String[]> paramMap = super.getParameterMap();
		if (paramMap == null||paramMap.size()==0) 
			return paramMap;
		for (Entry<String, String[]> entry : paramMap.entrySet()) 
			cleanMap.put(entry.getKey(), xssClean(entry.getValue()));
		return cleanMap;
	}

	public String getParameter(String s) {
		String s1 = super.getParameter(s);
		if (s1 == null) 
			return null;
		 else 
			return XssCleaner.xssClean(s1);
		
	}

	/*public String getHeader(String s) {
		String s1 = super.getHeader(s);
		if (s1 == null) 
			return null;
		 else 
			return xssClean(s1);
	}*/
	
	public boolean hasIllegalParam(){
		return true;
	}
	
	
	
	
	
}