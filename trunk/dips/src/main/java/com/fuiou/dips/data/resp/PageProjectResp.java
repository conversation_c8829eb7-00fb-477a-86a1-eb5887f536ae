package com.fuiou.dips.data.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
/**
 * 分页项目信息响应对象
 */
public class PageProjectResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目编号，唯一标识一个项目的编号
     */
    private String projectNo;

    /**
     * 项目名称，用于展示项目的具体名称或用途
     */
    private String projectName;
    /**
     * 项目状态，表项目进度 1进行中 2 已关闭 9 已完成
     */
    private String projectSt;

    /**
     * 阶段名称，表示当前项目的阶段状态名称（如“签约阶段”）
     */
    private String stageName;

    /**
     * 客户姓名，与该项目关联的客户名称
     */
    private String customerName;

    /**
     * 门店名称，表示该项目所属门店的名称
     */
    private String storeName;

    /**
     * 创建时间，表示该项目的创建时间
     */
    private Timestamp createTime;

    /**
     * 当前阶段金额，表示该项目当前阶段应支付的总金额
     */
    private BigDecimal stageAmt;

    /**
     * 已收款金额，表示该项目当前阶段已经实际收到的金额
     */
    private BigDecimal stageActualAmt;

    /**
     * 退款金额，表示该项目当前阶段已退还给客户的金额
     */
    private BigDecimal refundAmt;


    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getProjectSt() {
        return projectSt;
    }

    public void setProjectSt(String projectSt) {
        this.projectSt = projectSt;
    }

    public BigDecimal getStageAmt() {
        return stageAmt;
    }

    public void setStageAmt(BigDecimal stageAmt) {
        this.stageAmt = stageAmt;
    }

    public BigDecimal getStageActualAmt() {
        return stageActualAmt;
    }

    public void setStageActualAmt(BigDecimal stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }
}
