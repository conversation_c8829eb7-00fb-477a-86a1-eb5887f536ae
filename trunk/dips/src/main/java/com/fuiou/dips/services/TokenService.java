package com.fuiou.dips.services;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.enums.RedisKeyEnum;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.RedisHandler;
import com.fuiou.dips.utils.TrimDataUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class TokenService {

    @Resource(name="redisHandler")
    private RedisHandler<String,String> redisHandler;

    public String generateToken(){
        String token = UUID.randomUUID().toString().replace("-", "");
        LogWriter.info(this, String.format("token=%s", token));
        return token;
    }

    public static void main(String[] args) {
        System.out.println(new TokenService().generateToken());
    }

    public LoginResp getRedisInfo(String token){
        try {
            String tokenKey = String.format("%s%s", RedisKeyEnum.login_token.getKey_prefix(), token);
            LogWriter.info(this, String.format("get tokenKey=%s", tokenKey));
            String redisStr = redisHandler.getCacheObject(tokenKey);
            LogWriter.info(this, String.format("get redisStr=%s", redisStr));
            Map<String, Class<?>> classMap = new HashMap<>();
            classMap.put("relateStoreList", StoreInfo.class);
            LoginResp redisInfo = (LoginResp)JsonUtil.getObjectFromJsonString(redisStr, LoginResp.class, classMap);

            return (LoginResp) TrimDataUtil.invokeTrimString(redisInfo);
        } catch (Exception e) {
            LogWriter.error(this, "按token获取Redis信息异常", e);
            return null;
        }
    }

    public void setRedisInfo(LoginResp info) throws Exception {
        String redisStr = JsonUtil.bean2Json((LoginResp) TrimDataUtil.invokeTrimString(info));
        LogWriter.info(this, String.format("set redisStr=%s", redisStr));
        String tokenKey = String.format("%s%s", RedisKeyEnum.login_token.getKey_prefix(), info.getToken());
        LogWriter.info(this, String.format("set tokenKey=%s", tokenKey));
    	redisHandler.setCacheObjectTimeOut(tokenKey, redisStr, RedisKeyEnum.login_token.getExpireSecond());
    }

    public void removeRedisInfo(String token){
        String tokenKey = String.format("%s%s", RedisKeyEnum.login_token.getKey_prefix(), token);
        redisHandler.deleteCacheObject(tokenKey);
    }

}
