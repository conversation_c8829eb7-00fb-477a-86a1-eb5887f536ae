package com.fuiou.dips.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.req.LoginReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.services.WeChatLoginService;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * 微信授权
 */
@Controller
@RequestMapping("/wechat/")
public class WeChatLoginController {

    @Autowired
    private WeChatLoginService weChatLoginService;


    @ResponseBody
    @PostMapping(value = "login")
    public ResponseEntity<LoginResp> login(@Valid @RequestBody LoginReq loginReq) throws Exception{
        if (loginReq == null) {
            LogWriter.info(this, "微信授权登录，loginReq为空");
            return ResponseEntityFactory.fail(ResponseCodeEnum.FIELD_EMPTY_ERROR);
        }
        LogWriter.info(this, String.format("微信授权登录，loginReq=%s", JsonUtil.bean2Json(loginReq)));
        return ResponseEntityFactory.ok(weChatLoginService.login(loginReq));
    }

}
