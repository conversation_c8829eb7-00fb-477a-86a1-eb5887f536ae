package com.fuiou.dips.valid;

import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class AtLeastOneNotBlankValidator implements ConstraintValidator<AtLeastOneNotBlank, Object> {
    private String[] fields;

    @Override
    public void initialize(AtLeastOneNotBlank constraintAnnotation) {
        this.fields = constraintAnnotation.fields();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) return true; // 若对象为空，由其他注解（如@NotNull）处理

        boolean isBlank = true;
        for (String field : fields) {
            try {
                Object fieldValue = BeanUtils.getPropertyDescriptor(value.getClass(), field).getReadMethod().invoke(value);
                if (StringUtils.isNotBlank(fieldValue != null ? fieldValue.toString() : null)) {
                    isBlank = false;
                    break;
                }
            } catch (Exception e) {
                LogWriter.error("参数校验发生异常",e);
                throw new FUException(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getCode()+ field);
            }
        }
        return !isBlank;
    }
}