package com.fuiou.dips.services;

import cn.hutool.core.collection.ListUtil;
import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.dips.convert.TxnLogConvertMapper;
import com.fuiou.dips.data.req.TxnLogPageReq;
import com.fuiou.dips.data.req.TxnLogReq;
import com.fuiou.dips.data.resp.PageRespBase;
import com.fuiou.dips.data.resp.TxnLogResp;
import com.fuiou.dips.enums.OrderStatusEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.beans.TxnLogQueryDTO;
import com.fuiou.dips.persist.dipsdb.TxnLogMapper;
import com.fuiou.dips.utils.PairMap;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 交易订单服务
 *
 * <AUTHOR>
 */
@Service
public class TxnLogService {
    private static final Logger log = LoggerFactory.getLogger(TxnLogService.class);

    @Resource
    private TxnLogMapper txnLogMapper;
    @Resource
    private TxnLogConvertMapper txnLogConvertMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private MchntService mchntService;

    @LogAnnotation(value = "交易订单",
                   methodName = "交易订单列表查询")
    public PageRespBase<TxnLogResp> listByProjectAndStage(TxnLogPageReq txnLogPageReq) {
        // 检查项目查看权限
        PairMap<Boolean, Boolean> pairMap = projectService.checkLoginUserPermission(txnLogPageReq.getProjectNo());
        FUApiAssert.isTrue(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION, pairMap.getLeft());

        List<String> payStatus = ListUtil.of(OrderStatusEnum.PAY_SUCCESS.getCode(),
                OrderStatusEnum.REFUND_SUCCESS.getCode(), OrderStatusEnum.REVOKED.getCode(),
                OrderStatusEnum.Returned.getCode(), OrderStatusEnum.PAFINISH.getCode(),
                OrderStatusEnum.PFFINISH.getCode());

        PageHelper.startPage(txnLogPageReq.getPage(), txnLogPageReq.getLimit());
        List<TxnLog> txnLogs = txnLogMapper.selectByProjectAndStage(txnLogPageReq.getProjectNo(),
                txnLogPageReq.getMchntCd(), txnLogPageReq.getStageNo(), payStatus);
        long total = new PageInfo<>(txnLogs).getTotal();
        return new PageRespBase<>(total, txnLogConvertMapper.txnLogToRespList(txnLogs));
    }

    @LogAnnotation(value = "交易订单",
                   methodName = "交易订单详情查询查询")
    public TxnLogResp detail(TxnLogReq txnLogReq) {
        // 检查项目查看权限
        PairMap<Boolean, Boolean> pairMap = projectService.checkLoginUserPermission(txnLogReq.getProjectNo());
        FUApiAssert.isTrue(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION, pairMap.getLeft());

        TxnLog txnLog = txnLogMapper.selectByProjectAndStageAndOrderNo(txnLogReq);
        TxnLogResp txnLogResp = txnLogConvertMapper.txnLogToResp(txnLog);
        // 项目信息
        PairMap<String, String> projectNameAndStageName = getProjectNameAndStageName(txnLogReq);
        txnLogResp.setProjectName(projectNameAndStageName.getLeft());
        txnLogResp.setStageName(projectNameAndStageName.getRight());
        // 商户信息
        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(txnLogReq.getMchntCd());
        txnLogResp.setMchntName(insMchntCacheData.getInsNameCn());
        txnLogResp.setMchntShortName(insMchntCacheData.getInsNmJcCn());
        return txnLogResp;
    }

    @LogAnnotation("订单-合并获取项目名和阶段名称")
    public PairMap<String, String> getProjectNameAndStageName(TxnLogReq txnLogReq) {
        Project project = projectService.queryProjectByProjectNoAndMchntCd(txnLogReq.getProjectNo(),
                txnLogReq.getMchntCd());
        ProjectStage projectStage = projectStageService.queryProjectStage(txnLogReq.getProjectNo(),
                txnLogReq.getMchntCd(), txnLogReq.getStageNo());
        return PairMap.of(project.getProjectName(), projectStage.getStageName());
    }


    @LogAnnotation("订单-保存")
    public int insert(TxnLog record) {
        return txnLogMapper.insert(record);
    }


    @LogAnnotation("订单-查询详情")
    public TxnLog queryOrderInfo(String mchntCd, String mchntOrderNo) {
        return txnLogMapper.queryOrderInfo(mchntCd, mchntOrderNo);
    }

    @LogAnnotation("订单-更新订单信息")
    public int update(TxnLog txnLogOrder) {
        return txnLogMapper.update(txnLogOrder);
    }

    @LogAnnotation("订单-外部查询订单列表")
    public List<TxnLog> listExternal(TxnLogQueryDTO txnLogQueryDTO) {
        return txnLogMapper.listExternal(txnLogQueryDTO);
    }

    @LogAnnotation("订单查询-根据交易类型，交易状态，阶段，查询订单信息")
    public List<TxnLogResp> listByProjectAndStageAndState(String projectNo, String stageNo, String orderType,
            String tradeType, String orderSt)
    {
        List<TxnLog> txnLogs = txnLogMapper.listByProjectAndStageAndState(projectNo, stageNo, orderType, tradeType,
                orderSt);
        return txnLogConvertMapper.txnLogToRespList(txnLogs);
    }
}
