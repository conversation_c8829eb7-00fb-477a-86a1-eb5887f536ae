package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

public class BaseReq {

    /**
     * 商户号
     */
    @NotBlank(message = "商户编号不能为空")
    @Size(min = 5,
            max = 20,
            message = "商户编号需在5-20字符")
    private String mchntCd;

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }
}
