package com.fuiou.dips.data.req;

import com.fuiou.dips.utils.LogWriter;
import com.fuiou.sk.cipher.FuiouRsaCipher;
import org.nuxeo.common.xmap.annotation.XNode;
import org.nuxeo.common.xmap.annotation.XObject;

/**
  *实体类
  时间2017-05-24 10:36:46
*/
@XObject(value = "xml")
public class PreCreateDataReq {
	
	@XNode("addn_inf")
	private String addn_inf = "";

	@XNode("curr_type")
	private String curr_type = "";

	@XNode(value = "goods_des",cdata = true)
	private String goods_des = "";
	
	@XNode("goods_detail")
	private String goods_detail = "";
	
	@XNode("goods_tag")
	private String goods_tag = "";
	
	@XNode("ins_cd")
    private String ins_cd;
	
	@XNode("limit_pay")
	private String limit_pay = "";
	
	@XNode("mchnt_cd")
	private String mchnt_cd;
	
	@XNode("mchnt_order_no")
	private String mchnt_order_no;
	
	@XNode("notify_url")
	private String notify_url;
	
	@XNode("order_amt")
	private String order_amt;
	
	@XNode("openid")
	private String openid = "";
	
	@XNode("product_id")
	private String product_id = "";
	
	@XNode("random_str")
	private String random_str = "";
	
	@XNode("sign")
	private String sign;
	
	@XNode("sub_openid")
	private String sub_openid = "";
	
	@XNode("sub_appid")
	private String sub_appid = "";
	
	@XNode("term_id")
	private String term_id = "";
	
	@XNode("term_ip")
	private String term_ip = "";
	
	@XNode("txn_begin_ts")
	private String txn_begin_ts;
	
	@XNode("trade_type")
	private String trade_type;
	
	@XNode("version")
    private String version = "1.0";
	
	@XNode("reserved_fy_term_id")
	private String reserved_fy_term_id;
	
	@XNode("reserved_expire_minute")
	private String reserved_expire_minute;
	
	@XNode("reserved_txn_bonus")
	private String reserved_txn_bonus = "";//积分抵扣金额
	
	@XNode("reserved_discount_amt")
	private String reserved_discount_amt;//优惠券抵扣优惠金额
	
	@XNode("reserved_undisc_amt")
	private String reserved_undisc_amt;//【前置-口碑专用字段】不参与优惠金额
	
	private String groupId;

	/***
	 * <AUTHOR>
	 * @Description   银行合作项目底层改造新增字段
	 * @Date 2020/5/19 10:19
	 **/
	@XNode("reserved_ori_busi_id")
	private String reserved_ori_busi_id;

	/**
	 * <AUTHOR>
	 * @Date 2022/9/15 15:21
	 * @Description 商户传入业务信息，应用于安全，营销等参数直传场景，格式为 json 格式:{"data":"123"}
	**/
	@XNode("reserved_business_params")
	private String reserved_business_params	;

	/** <AUTHOR>
	 * 存储分期相关字段
	 */
	@XNode("reserved_ali_extend_params")
	private String reserved_ali_extend_params;
	/**
	* @Description: 网联机构编号
	* @Author: 程军
	* @Date: 2025/1/22 16:24
	*/
	@XNode("reserved_nuccpay_issrid")
	private String reserved_nuccpay_issrid;

	public String getReserved_ali_extend_params() {
		return reserved_ali_extend_params;
	}

	public void setReserved_ali_extend_params(String reserved_ali_extend_params) {
		this.reserved_ali_extend_params = reserved_ali_extend_params;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getReserved_discount_amt() {
		return reserved_discount_amt;
	}

	public void setReserved_discount_amt(String reserved_discount_amt) {
		this.reserved_discount_amt = reserved_discount_amt;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getIns_cd() {
		return ins_cd;
	}

	public void setIns_cd(String ins_cd) {
		this.ins_cd = ins_cd;
	}

	public String getMchnt_cd() {
		return mchnt_cd;
	}

	public void setMchnt_cd(String mchnt_cd) {
		this.mchnt_cd = mchnt_cd;
	}

	public String getTerm_id() {
		return term_id;
	}

	public void setTerm_id(String term_id) {
		this.term_id = term_id;
	}

	public String getRandom_str() {
		return random_str;
	}

	public void setRandom_str(String random_str) {
		this.random_str = random_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getGoods_des() {
		return goods_des;
	}

	public void setGoods_des(String goods_des) {
		this.goods_des = goods_des;
	}

	public String getGoods_detail() {
		return goods_detail;
	}

	public void setGoods_detail(String goods_detail) {
		this.goods_detail = goods_detail;
	}

	public String getAddn_inf() {
		return addn_inf;
	}

	public void setAddn_inf(String addn_inf) {
		this.addn_inf = addn_inf;
	}

	public String getMchnt_order_no() {
		return mchnt_order_no;
	}

	public void setMchnt_order_no(String mchnt_order_no) {
		this.mchnt_order_no = mchnt_order_no;
	}

	public String getCurr_type() {
		return curr_type;
	}

	public void setCurr_type(String curr_type) {
		this.curr_type = curr_type;
	}

	public String getOrder_amt() {
		return order_amt;
	}

	public void setOrder_amt(String order_amt) {
		this.order_amt = order_amt;
	}

	public String getTerm_ip() {
		return term_ip;
	}

	public void setTerm_ip(String term_ip) {
		this.term_ip = term_ip;
	}

	public String getTxn_begin_ts() {
		return txn_begin_ts;
	}

	public void setTxn_begin_ts(String txn_begin_ts) {
		this.txn_begin_ts = txn_begin_ts;
	}

	public String getGoods_tag() {
		return goods_tag;
	}

	public void setGoods_tag(String goods_tag) {
		this.goods_tag = goods_tag;
	}

	public String getNotify_url() {
		return notify_url;
	}

	public void setNotify_url(String notify_url) {
		this.notify_url = notify_url;
	}

	

	public String getReserved_expire_minute() {
		return reserved_expire_minute;
	}

	public void setReserved_expire_minute(String reserved_expire_minute) {
		this.reserved_expire_minute = reserved_expire_minute;
	}

	public String getReserved_fy_term_id() {
		return reserved_fy_term_id;
	}

	public void setReserved_fy_term_id(String reserved_fy_term_id) {
		this.reserved_fy_term_id = reserved_fy_term_id;
	}

	public String getLimit_pay() {
		return limit_pay;
	}

	public void setLimit_pay(String limit_pay) {
		this.limit_pay = limit_pay;
	}

	public String getTrade_type() {
		return trade_type;
	}

	public void setTrade_type(String trade_type) {
		this.trade_type = trade_type;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getSub_openid() {
		return sub_openid;
	}

	public void setSub_openid(String sub_openid) {
		this.sub_openid = sub_openid;
	}

	public String getSub_appid() {
		return sub_appid;
	}

	public void setSub_appid(String sub_appid) {
		this.sub_appid = sub_appid;
	}
	
	public String getProduct_id() {
		return product_id;
	}

	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	
	public String getReserved_txn_bonus() {
		return reserved_txn_bonus;
	}

	public void setReserved_txn_bonus(String reserved_txn_bonus) {
		this.reserved_txn_bonus = reserved_txn_bonus;
	}

	public String getReserved_undisc_amt() {
		return reserved_undisc_amt;
	}

	public void setReserved_undisc_amt(String reserved_undisc_amt) {
		this.reserved_undisc_amt = reserved_undisc_amt;
	}

	public String getReserved_ori_busi_id() {
		return reserved_ori_busi_id;
	}

	public void setReserved_ori_busi_id(String reserved_ori_busi_id) {
		this.reserved_ori_busi_id = reserved_ori_busi_id;
	}

	public String getReserved_business_params() {
		return reserved_business_params == null ? null : reserved_business_params.trim();
	}

	public void setReserved_business_params(String reserved_business_params) {
		this.reserved_business_params = reserved_business_params;
	}

	public String getReserved_nuccpay_issrid() {
		return reserved_nuccpay_issrid == null ? null : reserved_nuccpay_issrid.trim();
	}

	public void setReserved_nuccpay_issrid(String reserved_nuccpay_issrid) {
		this.reserved_nuccpay_issrid = reserved_nuccpay_issrid;
	}

	public void encodeSign(String key) {
		StringBuilder sb = new StringBuilder();
		sb.append("addn_inf=").append(this.addn_inf).append("&")
		.append("curr_type=").append(this.curr_type).append("&")
		.append("goods_des=").append(this.goods_des).append("&")
		.append("goods_detail=").append(this.goods_detail).append("&")
		.append("goods_tag=").append(this.goods_tag).append("&")
		.append("ins_cd=").append(this.ins_cd).append("&")
		.append("limit_pay=").append(this.limit_pay).append("&")
		.append("mchnt_cd=").append(this.mchnt_cd).append("&")
		.append("mchnt_order_no=").append(this.mchnt_order_no).append("&")
		.append("notify_url=").append(this.notify_url).append("&")
		.append("openid=").append(this.openid).append("&")
		.append("order_amt=").append(this.order_amt).append("&")
		.append("product_id=").append(this.product_id).append("&")
		.append("random_str=").append(this.random_str).append("&")
		.append("sub_appid=").append(this.sub_appid).append("&")
		.append("sub_openid=").append(this.sub_openid).append("&")
		.append("term_id=").append(this.term_id).append("&")
		.append("term_ip=").append(this.term_ip).append("&")
		.append("trade_type=").append(this.trade_type).append("&")
		.append("txn_begin_ts=").append(this.txn_begin_ts).append("&")
		.append("version=").append(this.version);
		LogWriter.info("加密前拼接字符串为:"+sb.toString());
		LogWriter.info("key"+key);
		this.setSign(FuiouRsaCipher.sign2Base64ByMd5Rsa(key,sb.toString(),"GBK" ));
	}
}