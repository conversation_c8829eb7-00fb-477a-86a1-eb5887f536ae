package com.fuiou.dips.valid;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.apache.commons.lang.StringUtils;

public class DateFormatValidator implements
		ConstraintValidator<DateFormat, String> {

	/**
	 * 没有分隔符的日期格式
	 */
	public static final String FULL_TIME_WITH_OUT_SEPARATOR = "yyyyMMddHHmmss";

	private String format;
	private boolean nullable;

	@Override
	public void initialize(DateFormat constraintAnnotation) {
		format = constraintAnnotation.format();
		nullable = constraintAnnotation.nullable();
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if (nullable && StringUtils.isBlank(value)) {
			return true;
		}
		if (StringUtils.isBlank(value))
			return false;
		return validateDate(value, format);
	}

	/**
	 * 判断日期格式是否正确，只支持"yyyyMMddHHmmss"和"yyyyMMdd"
	 *
	 * @param dateString
	 * @param formateStr
	 * @return
	 */
	public static boolean validateDate(String dateString,String formatString) {
		try {
			// 得到年月日
			int year = Integer.parseInt(dateString.substring(0, 4));
			int month = Integer.parseInt(dateString.substring(4, 6));
			int day = Integer.parseInt(dateString.substring(6, 8));
			//判断年月日
			if (month < 1 || month > 12) {
				return false;
			}
			int[] monthLengths = new int[] { 0, 31, -1, 31, 30, 31, 30, 31, 31, 30,
					31, 30, 31 };
			if (isLeapYear(year)) {
				monthLengths[2] = 29;
			} else {
				monthLengths[2] = 28;
			}
			int monthLength = monthLengths[month];
			if (day < 1 || day > monthLength) {
				return false;
			}
			if(FULL_TIME_WITH_OUT_SEPARATOR.equals(formatString)){
				//时分秒
				int hour = Integer.parseInt(dateString.substring(8, 10));
				int minuter = Integer.parseInt(dateString.substring(10, 12));
				int second = Integer.parseInt(dateString.substring(12, 14));
				//判断时分秒
				if(hour<0||hour>24){
					return false;
				}
				if(minuter<0||minuter>59){
					return false;
				}
				if(second<0||second>59){
					return false;
				}
			}
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	/** 是否是闰年 */
	private static boolean isLeapYear(int year) {
		return ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0);
	}

	public static void main(String[] args) {
		System.out.println(validateDate("20180409181055", "yyyyMMddHHmmss"));

		System.out.println(validateDate("20200631181055", "yyyyMMddHHmmss"));

		System.out.println(validateDate("20200630251055", "yyyyMMddHHmmss"));

		System.out.println(validateDate("20240630001055", "yyyyMMddHHmmss"));

		System.out.println(validateDate("20240630", "yyyyMMdd"));
	}

}
