package com.fuiou.dips.framework.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fuiou.dips.framework.exception.ExcelStopAnalyzeException;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.validator.ExcelFieldValidator;
import com.fuiou.dips.utils.LogWriter;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 表格解析
 *
 * <AUTHOR>
 */
public class ProjectExcelReadListener<T> extends AnalysisEventListener<T> {

    private final List<T> dataList = new ArrayList<>(16);

    public List<T> getDataList() {
        return dataList;
    }

    // 先将没行数据读取到对象中，最后在获取对象值后，挨个遍历，前边空， 就映射到上个项目中就行了。

    @Override
    public void invoke(T data, AnalysisContext context) throws FUException {
        Integer currentRowNum = context.getCurrentRowNum();
        LogWriter.info("ProjectExcelReadListener.invoke,{},{}" + currentRowNum + JSONUtil.toJsonStr(data));
        Field[] declaredFields = data.getClass().getDeclaredFields();

        // 使用统一的校验器进行校验
        for (Field field : declaredFields) {
            try {
                ExcelFieldValidator.validateField(data, field, currentRowNum);
            } catch (ExcelStopAnalyzeException e) {
                // 重新抛出停止解析异常
                throw e;
            } catch (FUException e) {
                // 重新抛出业务异常
                throw e;
            } catch (Exception e) {
                LogWriter.error("字段校验异常: {}", e.getMessage(), e);
                throw new FUException("9999", "字段校验异常: " + e.getMessage());
            }
        }

        dataList.add(data);
    }



    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }

}
