package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.ValidPhone;
import com.fuiou.dips.valid.group.CreateGroup;
import com.fuiou.dips.valid.group.QueryGroup;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 装修通门店信息分页查询参数
 */
public class StoreSearchPageReq extends PageReqBase {

    /**
     * 商户号
     */
    @NotBlank(message = "商户编号不能为空",
              groups = {QueryGroup.class})
    private String mchntCd;

    private String storeName;

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}