package com.fuiou.dips.framework.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 注解迁移辅助工具
 * 用于将旧的分散注解转换为新的统一注解
 * 
 * <AUTHOR>
 */
public class AnnotationMigrationHelper {
    
    /**
     * 将旧注解代码转换为新注解代码
     * 
     * @param oldCode 包含旧注解的代码
     * @return 转换后的新注解代码
     */
    public static String migrateAnnotations(String oldCode) {
        String result = oldCode;
        
        // 替换导入语句
        result = replaceImports(result);
        
        // 替换注解
        result = replaceNotNullField(result);
        result = replaceStringLengthField(result);
        result = replaceLongParseField(result);
        result = replaceStopAnalyzeParseAnnotation(result);
        
        return result;
    }
    
    /**
     * 替换导入语句
     */
    private static String replaceImports(String code) {
        String result = code;
        
        // 删除旧的导入
        result = result.replaceAll("import\\s+com\\.fuiou\\.dips\\.framework\\.annotation\\.NotNullField;\\s*\\n", "");
        result = result.replaceAll("import\\s+com\\.fuiou\\.dips\\.framework\\.annotation\\.StringLengthField;\\s*\\n", "");
        result = result.replaceAll("import\\s+com\\.fuiou\\.dips\\.framework\\.annotation\\.LongParseField;\\s*\\n", "");
        result = result.replaceAll("import\\s+com\\.fuiou\\.dips\\.framework\\.annotation\\.StopAnalyzeParseAnnotation;\\s*\\n", "");
        
        // 添加新的导入（如果还没有的话）
        if (!result.contains("import com.fuiou.dips.framework.annotation.ExcelValidation;")) {
            result = result.replaceFirst(
                "(package\\s+[^;]+;\\s*\\n)",
                "$1\\nimport com.fuiou.dips.framework.annotation.ExcelValidation;\\n" +
                "import com.fuiou.dips.framework.annotation.ExcelValidationType;\\n"
            );
        }
        
        return result;
    }
    
    /**
     * 替换 @NotNullField 注解
     */
    private static String replaceNotNullField(String code) {
        Pattern pattern = Pattern.compile(
            "@NotNullField\\s*\\(\\s*index\\s*=\\s*(\\d+)\\s*,\\s*msg\\s*=\\s*\"([^\"]+)\"\\s*\\)",
            Pattern.MULTILINE
        );
        
        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String index = matcher.group(1);
            String message = matcher.group(2);
            
            String replacement = String.format(
                "@ExcelValidation(\\n" +
                "        type = ExcelValidationType.NOT_NULL,\\n" +
                "        index = %s,\\n" +
                "        message = \"%s\"\\n" +
                "    )",
                index, message
            );
            
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 替换 @StringLengthField 注解
     */
    private static String replaceStringLengthField(String code) {
        Pattern pattern = Pattern.compile(
            "@StringLengthField\\s*\\(\\s*index\\s*=\\s*(\\d+)\\s*,\\s*msg\\s*=\\s*\"([^\"]+)\"\\s*,\\s*length\\s*=\\s*(\\d+)\\s*,\\s*zhLength\\s*=\\s*(\\d+)\\s*\\)",
            Pattern.MULTILINE
        );
        
        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String index = matcher.group(1);
            String message = matcher.group(2);
            String length = matcher.group(3);
            String zhLength = matcher.group(4);
            
            String replacement = String.format(
                "@ExcelValidation(\\n" +
                "        type = ExcelValidationType.STRING_LENGTH,\\n" +
                "        index = %s,\\n" +
                "        message = \"%s\",\\n" +
                "        maxLength = %s,\\n" +
                "        maxZhLength = %s\\n" +
                "    )",
                index, message, length, zhLength
            );
            
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 替换 @LongParseField 注解
     */
    private static String replaceLongParseField(String code) {
        Pattern pattern = Pattern.compile(
            "@LongParseField\\s*\\(\\s*index\\s*=\\s*(\\d+)\\s*,\\s*msg\\s*=\\s*\"([^\"]+)\"\\s*\\)",
            Pattern.MULTILINE
        );
        
        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String index = matcher.group(1);
            String message = matcher.group(2);
            
            String replacement = String.format(
                "@ExcelValidation(\\n" +
                "        type = ExcelValidationType.LONG_PARSE,\\n" +
                "        index = %s,\\n" +
                "        message = \"%s\"\\n" +
                "    )",
                index, message
            );
            
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 替换 @StopAnalyzeParseAnnotation 注解
     */
    private static String replaceStopAnalyzeParseAnnotation(String code) {
        Pattern pattern = Pattern.compile(
            "@StopAnalyzeParseAnnotation\\s*\\(\\s*index\\s*=\\s*(\\d+)\\s*,\\s*msg\\s*=\\s*\"([^\"]+)\"\\s*\\)",
            Pattern.MULTILINE
        );
        
        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String index = matcher.group(1);
            String message = matcher.group(2);
            
            String replacement = String.format(
                "@ExcelValidation(\\n" +
                "        type = ExcelValidationType.STOP_ANALYZE,\\n" +
                "        index = %s,\\n" +
                "        message = \"%s\"\\n" +
                "    )",
                index, message
            );
            
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 生成迁移报告
     */
    public static Map<String, Integer> generateMigrationReport(String oldCode, String newCode) {
        Map<String, Integer> report = new HashMap<>();
        
        report.put("NotNullField", countOccurrences(oldCode, "@NotNullField"));
        report.put("StringLengthField", countOccurrences(oldCode, "@StringLengthField"));
        report.put("LongParseField", countOccurrences(oldCode, "@LongParseField"));
        report.put("StopAnalyzeParseAnnotation", countOccurrences(oldCode, "@StopAnalyzeParseAnnotation"));
        report.put("ExcelValidation", countOccurrences(newCode, "@ExcelValidation"));
        
        return report;
    }
    
    private static int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
    
    /**
     * 示例用法
     */
    public static void main(String[] args) {
        String oldCode = """
            package com.example;
            
            import com.fuiou.dips.framework.annotation.NotNullField;
            import com.fuiou.dips.framework.annotation.StringLengthField;
            import com.fuiou.dips.framework.annotation.LongParseField;
            
            public class Example {
                @NotNullField(index = 0, msg = "不能为空")
                @StringLengthField(index = 0, msg = "长度超限", length = 20, zhLength = 20)
                private String name;
                
                @LongParseField(index = 1, msg = "转换失败")
                private Long amount;
            }
            """;
        
        String newCode = migrateAnnotations(oldCode);
        System.out.println("迁移后的代码:");
        System.out.println(newCode);
        
        Map<String, Integer> report = generateMigrationReport(oldCode, newCode);
        System.out.println("\\n迁移报告:");
        report.forEach((key, value) -> System.out.println(key + ": " + value));
    }
}
