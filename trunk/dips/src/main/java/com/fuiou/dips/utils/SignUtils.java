package com.fuiou.dips.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

public class SignUtils {

    public static String getSignContent(Map<String, String> params) {
        if (params == null) {
            return null;
        }

        // 使用TreeMap自动按key排序
        Map<String, String> sortedParams = new TreeMap<String, String>(params);

        StringBuilder content = new StringBuilder();
        Iterator<Map.Entry<String, String>> it = sortedParams.entrySet().iterator();

        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            String key = entry.getKey();
            String value = entry.getValue();

            // 跳过sign字段和空值
            if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(value)
                    && !"sign".equals(key) && !"sign_type".equals(key)) {
                if (content.length() > 0) {
                    content.append("&");
                }
                content.append(key).append("=").append(value);
            }
        }
        LogWriter.info(String.format("sign content: %s", content.toString()));
        return content.toString();
    }
}
