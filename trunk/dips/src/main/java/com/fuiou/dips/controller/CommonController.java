package com.fuiou.dips.controller;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.req.SendSmsCodeReq;
import com.fuiou.dips.data.req.UserAccountLoginReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.data.resp.UserAccountLoginResp;
import com.fuiou.dips.services.CaptchaVerifyService;
import com.fuiou.dips.services.SmsService;
import com.fuiou.dips.services.TokenService;
import com.fuiou.dips.services.WeChatLoginService;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * 公共
 */
@Controller
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private SmsService smsService;

    @Autowired
    private CaptchaVerifyService captchaVerifyService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WeChatLoginService weChatLoginService;

    @ResponseBody
    @PostMapping(value = "/login/smscode")
    public ResponseEntity smscode(@Valid @RequestBody SendSmsCodeReq sendSmsCodeReq) throws Exception{
        //校验安全验证码
        captchaVerifyService.verify(sendSmsCodeReq.getVcode(), sendSmsCodeReq.getPhone(), sendSmsCodeReq.getPhone());
        //发送登录短信
        smsService.sendLoginSmsCodeWithRedis(sendSmsCodeReq.getPhone());
        return ResponseEntityFactory.ok();
    }

    @ResponseBody
    @PostMapping(value = "/logout")
    public ResponseEntity logout() throws Exception{
        String token = LoginConstat.getLoginToken().getToken();
        LogWriter.info(this, String.format("登出, token=%s", token));
        LoginResp loginResp = tokenService.getRedisInfo(token);
        loginResp.setAccountLogin(false);
        loginResp.setMchntInfo(null);
        tokenService.setRedisInfo(loginResp);
        weChatLoginService.updateUserInfoForLogout(loginResp);
        return ResponseEntityFactory.ok();
    }
}
