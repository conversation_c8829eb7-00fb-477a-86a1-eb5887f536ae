package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

public class MchntChooseForUserReq {

    @NotBlank(message = "商户号不能为空")
    @Size(min = 5,
            max = 15,
            message = "商户号需在5-15字符")
    private String mchntCd;



    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

}
