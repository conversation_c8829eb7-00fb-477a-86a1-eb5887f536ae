package com.fuiou.dips.utils;

import java.io.Serializable;
import java.util.Objects;

/**
 * 自定义键值对，用于封装两个对象
 *
 * @param <L>
 * @param <R>
 * <AUTHOR>
 */
public final class PairMap<L, R> implements Serializable {

    private static final long serialVersionUID = 1L;
    private final L left;
    private final R right;

    private PairMap(L left, R right) {
        this.left = left;
        this.right = right;
    }

    public L getLeft() {
        return left;
    }

    public R getRight() {
        return right;
    }

    public static <L, R> PairMap<L, R> of(L left, R right) {
        return new PairMap<>(left, right);
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        PairMap<?, ?> pairMap = (PairMap<?, ?>) o;
        return Objects.equals(left, pairMap.left) && Objects.equals(right, pairMap.right);
    }

    @Override
    public int hashCode() {
        return Objects.hash(left, right);
    }

    @Override
    public String toString() {
        return "PairMap{" + "left=" + left + ", right=" + right + '}';
    }
}
