package com.fuiou.dips.utils;

import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2025/5/17 14:09
 * @Description
 **/
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);
    private static final ZoneId DEFAULT_ZONE = ZoneId.of("Asia/Shanghai");
    public static final String FORMAT_DEFAULT =  "yyyyMMddHHmmss";


    public static Date parseDate(String str, String... parsePatterns) throws ParseException {
        return parseDate(str, Locale.CHINA, parsePatterns);
    }

    public static Date parseDateYYYYMMddHHmmss(String str) throws ParseException {
        return parseDate(str, Locale.CHINA, FORMAT_DEFAULT);
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意三个参数的时间格式要一致
     * @param nowTime
     * @param startTime
     * @param endTime
     * @return 在时间段内返回true，不在返回false
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return date.after(begin) && date.before(end);
    }

    /**
     * 比较两个时间（时分秒）
     *
     * 该方法接受两个 Calendar 对象，并返回它们之间的比较结果。
     * 返回值为负数表示第一个时间早于第二个时间，0 表示两个时间相等，正数表示第一个时间晚于第二个时间。
     *
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 比较结果，负数表示 time1 早于 time2，0 表示相等，正数表示 time1 晚于 time2
     */
    public static int compareTimes(Calendar time1, Calendar time2) {
        return time1.compareTo(time2);
    }

    /**
     * 比较两个时间（时分秒）
     *
     * 该方法接受两个 Calendar 对象，并返回它们之间的比较结果。
     * 返回值为负数表示第一个时间早于第二个时间，0 表示两个时间相等，正数表示第一个时间晚于第二个时间。
     *
     * @param date1 第一个时间
     * @param date2 第二个时间
     * @return 比较结果，负数表示 date1 早于 time2，0 表示相等，正数表示 date1 晚于 date2
     */
    public static int compareTimes(Date date1, Date date2) {

        Calendar time1 = Calendar.getInstance();
        time1.setTime(date1);
        time1.set(Calendar.YEAR,2024);
        time1.set(Calendar.MONTH, Calendar.JANUARY);
        time1.set(Calendar.DAY_OF_MONTH,1);
        time1.set(Calendar.MILLISECOND, 0); // 清除毫秒部分

        Calendar time2 = Calendar.getInstance();
        time2.setTime(date2);
        time2.set(Calendar.YEAR,2024);
        time2.set(Calendar.MONTH, Calendar.JANUARY);
        time2.set(Calendar.DAY_OF_MONTH,1);
        time2.set(Calendar.MILLISECOND, 0); // 清除毫秒部分
        return compareTimes(time1,time2);
    }

    public static void main(String[] args) throws ParseException {
        String d1="09:22:00";
        String d2="10:22:00";
        Date d1Date= DateUtils.parseDate(d1,"HH:mm:ss");
        Date d2Date= DateUtils.parseDate(d2,"HH:mm:ss");

        System.out.println(compareTimes(new Date(),d2Date));

        Date start = DateUtils.parseDate("2025-03-01 00:23:00", "yyyy-MM-dd HH:mm:ss");
        Date end = DateUtils.parseDate("2025-09-01 23:00:00", "yyyy-MM-dd HH:mm:ss");
        System.out.println(getDaysBetween(start, end));
    }



    public static String replaceDatePlaceholders(String template,Date date,String targetDateFormat) {
        // 定义正则表达式模式来匹配日期占位符
        Pattern pattern = Pattern.compile(("\\{T([+-]?\\d+)?\\}"));
        Matcher matcher = pattern.matcher(template);

        StringBuffer result = new StringBuffer();
        Date placeDate=null;
        while (matcher.find()) {
            String placeholder = matcher.group();
            if (placeholder.length() > 3) {
                // 提取数字部分
                try {
                    int days = Integer.parseInt(placeholder.substring(2, placeholder.length() - 1));
                    placeDate = addDays(date, days) ;
                } catch (NumberFormatException e) {
                    // 处理数字解析错误
                   log.warn("Invalid date placeholder: " + placeholder);
                    continue;
                }
            }
            // 替换占位符为实际日期
            matcher.appendReplacement(result, DateFormatUtils.format(placeDate,targetDateFormat));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 根据yyyy-MM-dd格式的时间字符串，返回对应的Timestamp数组，其中第一个元素为起始时间，第二个元素为结束时间。
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static Timestamp[] convertToTimestamps(String startDateStr,String endDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(DEFAULT_ZONE);
        try {
            LocalDate startDate = LocalDate.parse(startDateStr, formatter);
            LocalDate endDate = LocalDate.parse(endDateStr, formatter);
            // 构造当天的最小时间和最大时间
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalDateTime.MAX.toLocalTime());
            Timestamp startTimestamp = Timestamp.valueOf(startDateTime);
            Timestamp endTimestamp = Timestamp.valueOf(endDateTime);
            return new Timestamp[]{startTimestamp, endTimestamp};
        } catch (DateTimeParseException e) {
            throw new FUException(ResponseCodeEnum.TIME_FORMAT_ERROR);
        }
    }

    /**
     * 根据输入的月份（yyyy-MM）获取当月的起止时间
     *
     * @param monthStr 输入月份，格式为 yyyy-MM
     * @return Timestamp[] 第一个元素为月初开始时间，第二个为月末结束时间
     */
    public static Timestamp[] getMonthlyTimestamp(String monthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        try {
            YearMonth yearMonth = YearMonth.parse(monthStr, formatter);

            LocalDate startOfMonth = yearMonth.atDay(1);
            LocalDate endOfMonth = yearMonth.atEndOfMonth();

            LocalDateTime startDateTime = startOfMonth.atStartOfDay();
            LocalDateTime endDateTime = endOfMonth.atTime(LocalTime.MAX).atZone(DEFAULT_ZONE).toLocalDateTime();

            return new Timestamp[]{
                    Timestamp.valueOf(startDateTime), // 起始时间：2025-04-01 00:00:00.0
                    Timestamp.valueOf(endDateTime)    // 结束时间：2025-04-30 23:59:59.999999999
            };
        } catch (DateTimeParseException e) {
            throw new FUException(ResponseCodeEnum.TIME_FORMAT_ERROR);
        }
    }

    /**
     * 根据输入的月份（yyyy-MM）获取**上个月**的起止时间
     *
     * @param monthStr 输入月份，格式为 yyyy-MM
     * @return Timestamp[] 第一个元素为上月开始时间，第二个为上月结束时间
     */
    public static Timestamp[] getLastMonthTimestamp(String monthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        try {
            YearMonth yearMonth = YearMonth.parse(monthStr, formatter);
            LocalDate startOfLastMonth = yearMonth.minusMonths(1).atDay(1);
            LocalDate endOfLastMonth = yearMonth.minusMonths(1).atEndOfMonth();

            LocalDateTime startDateTime = startOfLastMonth.atStartOfDay();
            LocalDateTime endDateTime = endOfLastMonth.atTime(LocalTime.MAX)
                    .atZone(DEFAULT_ZONE)
                    .toLocalDateTime();

            return new Timestamp[]{
                    Timestamp.valueOf(startDateTime), // 上月第一天 00:00:00
                    Timestamp.valueOf(endDateTime)     // 上月最后一天 23:59:59.999999999
            };
        } catch (DateTimeParseException e) {
            throw new FUException(ResponseCodeEnum.TIME_FORMAT_ERROR);
        }
    }

    public static boolean isCurrentMonth(String queryMonth) {
        if (queryMonth == null || queryMonth.trim().isEmpty()) {
            return false;
        }
        try {
            YearMonth inputYearMonth = YearMonth.parse(queryMonth);
            return inputYearMonth.equals(YearMonth.now());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 取得两个时间段的时间间隔
     * @return t2 与t1的间隔天数
     */
    public static int getBetweenDays(String startTime, String endTime, String formatDate) {
        if (StringUtil.isEmpty(formatDate)) {
            formatDate = "yyyyMMdd";
        }
        DateFormat format = new SimpleDateFormat(formatDate);
        int betweenDays = 0;
        Date startDate = null;
        Date endDate = null;
        try {
            format.setLenient(false);
            startDate = format.parse(startTime);
            endDate = format.parse(endTime);
        } catch (Exception e) {
            return -1;
        }
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        startCal.setTime(startDate);
        endCal.setTime(endDate);
        // 保证第二个时间一定大于第一个时间
        if (startCal.after(endCal)) {
            return -1;
        }
        int betweenYears = endCal.get(Calendar.YEAR) - startCal.get(Calendar.YEAR);
        betweenDays = endCal.get(Calendar.DAY_OF_YEAR)
                - startCal.get(Calendar.DAY_OF_YEAR);
        for (int i = 0; i < betweenYears; i++) {
            startCal.set(Calendar.YEAR, (startCal.get(Calendar.YEAR) + 1));
            betweenDays += startCal.getMaximum(Calendar.DAY_OF_YEAR);
        }
        return betweenDays;
    }

    // 支持Date
    public static long getDaysBetween(Date start, Date end) {
        return (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    }

    /**
     * 判断日期范围是否满足：
     * ① 日期为当前月第一天到今天
     * ② 日期为过去的某个月的第一天到最后一天
     *
     * @param startDateStr 开始日期字符串(格式：yyyy-MM-dd)
     * @param endDateStr   结束日期字符串(格式：yyyy-MM-dd)
     * @return 如果满足条件返回true，否则返回false
     */
    public static boolean isFullMonthDateRange(String startDateStr, String endDateStr) {
        if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr)) {
            return false;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
            LocalDate startDate = LocalDate.parse(startDateStr, formatter);
            LocalDate endDate = LocalDate.parse(endDateStr, formatter);

            LocalDate today = LocalDate.now();
            LocalDate currentMonthFirstDay = today.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate currentMonthLastDay = today.with(TemporalAdjusters.lastDayOfMonth());

            // 情况①: 日期为当前月第一天到今天
            if (startDate.equals(currentMonthFirstDay) &&  currentMonthLastDay.equals(endDate)) {
                return true;
            }

            // 情况②: 日期为过去的某个月的第一天到最后一天
            if (startDate.getDayOfMonth() == 1) {
                LocalDate startMonthFirstDay = startDate.with(TemporalAdjusters.firstDayOfMonth());
                LocalDate startMonthLastDay = startDate.with(TemporalAdjusters.lastDayOfMonth());

                // 判断结束日期是否是该月的最后一天
                if (endDate.equals(startMonthLastDay)) {
                    // 判断是否是同一个自然月
                    if (startMonthFirstDay.equals(endDate.with(TemporalAdjusters.firstDayOfMonth()))) {
                        LocalDate inputMonth = startMonthFirstDay;
                        LocalDate nowMonth = today.with(TemporalAdjusters.firstDayOfMonth());
                        return inputMonth.isBefore(nowMonth);
                    }
                }
            }

        } catch (DateTimeParseException e) {
            // 只捕捉日期解析异常
            return false;
        } catch (Exception e) {
            // 其他意外异常也返回false
            return false;
        }

        return false;
    }

}
