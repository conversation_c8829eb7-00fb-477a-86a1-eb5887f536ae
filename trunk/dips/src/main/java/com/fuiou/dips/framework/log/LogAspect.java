package com.fuiou.dips.framework.log;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SimpleDateFormatSerializer;
import com.fuiou.dips.framework.exception.FUException;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 * @Date 2025/5/9 16:20
 * @Description 请求日志切面，记录请求前后的数据
 **/
@Aspect
@Component
public class LogAspect {

    private static final Logger log = LoggerFactory.getLogger("LogAspect");

    @Pointcut("@annotation(com.fuiou.dips.framework.log.LogAnnotation)")
    private void logPointCut() {
    }


    @Around(value = "logPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        StopWatch stopWatch=new StopWatch();
        String methodName = getMethodName(joinPoint);
        stopWatch.start(methodName);

        MDC.put("reqUri",methodName);
        log.info("[logIndex=dips;reqUri={};begin;params={}]", methodName, printObject(joinPoint));
        Object result = joinPoint.proceed(joinPoint.getArgs());
        if (printResult(joinPoint)) {
            stopWatch.stop();
            log.info("[{}  不需要打印返回对象 end ,耗时:{}ms;]", methodName, stopWatch.getTotalTimeMillis());
            return result;
        }
        stopWatch.stop();

        log.info("[logIndex=dips;{} end,耗时:{}ms,result={}]", methodName,  stopWatch.getTotalTimeMillis(), printObject(result));
        return result;
    }

    @AfterThrowing(pointcut="logPointCut()",
            throwing="e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e){

        String methodName = getMethodName(joinPoint);
        if(e instanceof FUException)
        {
            log.error("{} 发生异常,FuiouException={}", methodName,e.toString());
            return ;
        }
        log.error("{} 发生异常", methodName,e);

    }

    private String getMethodName(JoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            LogAnnotation annotation = method.getAnnotation(LogAnnotation.class);
            if (annotation == null) {
                return null;
            }
            String methodName = String.format("|%s.%s()|:%s-%s", method.getDeclaringClass().getName(), method.getName(), annotation.value(), annotation.methodName());
            return methodName;
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 2025/5/10 17:21 
     * @Description 判断是否打印返回值，void和注解属性
     * @param joinPoint
     * @return boolean
     **/
    private boolean printResult(JoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Class<?> returnType = method.getReturnType();
            LogAnnotation logAnnotation = getLogAnnotation(joinPoint);
            log.debug("判断是否打印返回值,returnType={},logAnnotation.printResult={}", returnType.getName(), logAnnotation == null ? null : logAnnotation.printResult());
            return Void.TYPE.equals(returnType) || (logAnnotation != null && !logAnnotation.printResult());
        } catch (Exception e) {
            log.error(" 判断是否打印返回值发生异常", e);
        }
        return false;
    }

    private String printObject(JoinPoint joinPoint) {
        try {
            Object[] params = joinPoint.getArgs();
            LogAnnotation logAnnotation = getLogAnnotation(joinPoint);
            if (params == null || params.length < 1 || logAnnotation == null || logAnnotation.excludePrintParamIndex() == null || logAnnotation.excludePrintParamIndex().length < 1) {
                return printObject(params);
            }
            //排除不需要打印的参数
            int[] excludePrintParamIndex = logAnnotation.excludePrintParamIndex();
            List<Object> paramList = new ArrayList<>(params.length);
            for (int i = 0;i < params.length;i++) {
                if (ArrayUtils.contains(excludePrintParamIndex, i)) {
                    continue;
                }
                paramList.add(params[i]);
            }
            Object[] printParams = new Object[paramList.size()];
            return printObject(paramList.toArray(printParams));
        } catch (Exception e) {
            log.error("日志打印发生异常", e);
            return "";
        }
    }

    private LogAnnotation getLogAnnotation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LogAnnotation annotation = method.getAnnotation(LogAnnotation.class);
        log.debug("logAnnotation={}", JSONObject.toJSONString(annotation));
        return annotation;
    }

    private String printObject(Object... params) {
        try {
            if (params == null || params.length < 1) {
                log.debug("需打印的对象为空");
                return "";
            }
            if(params.length == 1 && params[0] instanceof String)
            {
                return (String)params[0];
            }
            SerializeConfig config = new SerializeConfig();
            config.put(Date.class, new SimpleDateFormatSerializer("yyyy-MM-dd HH:mm:ss"));
            return JSONObject.toJSONString(params.length == 1 ? params[0] : params,config);
        } catch (Exception e) {
            log.error("日志打印发生异常", e);
            return "";
        }
    }


}
