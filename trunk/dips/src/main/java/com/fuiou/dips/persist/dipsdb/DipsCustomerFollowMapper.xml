<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fuiou.dips.persist.dipsdb.DipsCustomerFollowMapper" >
  <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.DipsCustomerFollow" >
    <id column="row_id" property="rowId" jdbcType="BIGINT" />
    <result column="log_no" property="logNo" jdbcType="VARCHAR" />
    <result column="mchnt_cd" property="mchntCd" jdbcType="CHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="creater" property="creater" jdbcType="VARCHAR" />
    <result column="follow_man" property="followMan" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
    <result column="reserved2" property="reserved2" jdbcType="VARCHAR" />
    <result column="reserved3" property="reserved3" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    row_id, log_no, mchnt_cd, phone, remark, creater, follow_man, create_time, update_time, 
    reserved1, reserved2, reserved3
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_dips_customer_follow
    where row_id = #{rowId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey"  >
    delete from t_dips_customer_follow
    where row_id = #{rowId,jdbcType=BIGINT} and mchnt_cd = #{mchntCd}
  </delete>
  <insert id="insert" parameterType="com.fuiou.dips.persist.beans.DipsCustomerFollow" >
    insert into t_dips_customer_follow (row_id, log_no, mchnt_cd, 
      phone, remark, creater, 
      follow_man, create_time, update_time, 
      reserved1, reserved2, reserved3
      )
    values (#{rowId,jdbcType=BIGINT}, #{logNo,jdbcType=VARCHAR}, #{mchntCd,jdbcType=CHAR}, 
      #{phone,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creater,jdbcType=VARCHAR}, 
      #{followMan,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reserved1,jdbcType=VARCHAR}, #{reserved2,jdbcType=VARCHAR}, #{reserved3,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fuiou.dips.persist.beans.DipsCustomerFollow" >
    insert into t_dips_customer_follow
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rowId != null" >
        row_id,
      </if>
      <if test="logNo != null" >
        log_no,
      </if>
      <if test="mchntCd != null" >
        mchnt_cd,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="creater != null" >
        creater,
      </if>
      <if test="followMan != null" >
        follow_man,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="reserved1 != null" >
        reserved1,
      </if>
      <if test="reserved2 != null" >
        reserved2,
      </if>
      <if test="reserved3 != null" >
        reserved3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rowId != null" >
        #{rowId,jdbcType=BIGINT},
      </if>
      <if test="logNo != null" >
        #{logNo,jdbcType=VARCHAR},
      </if>
      <if test="mchntCd != null" >
        #{mchntCd,jdbcType=CHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creater != null" >
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="followMan != null" >
        #{followMan,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserved1 != null" >
        #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null" >
        #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null" >
        #{reserved3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fuiou.dips.persist.beans.DipsCustomerFollow" >
    update t_dips_customer_follow
    <set >
      <if test="logNo != null" >
        log_no = #{logNo,jdbcType=VARCHAR},
      </if>
      <if test="mchntCd != null" >
        mchnt_cd = #{mchntCd,jdbcType=CHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creater != null" >
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="followMan != null" >
        follow_man = #{followMan,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserved1 != null" >
        reserved1 = #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null" >
        reserved2 = #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null" >
        reserved3 = #{reserved3,jdbcType=VARCHAR},
      </if>
    </set>
    where row_id = #{rowId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fuiou.dips.persist.beans.DipsCustomerFollow" >
    update t_dips_customer_follow
    set log_no = #{logNo,jdbcType=VARCHAR},
      mchnt_cd = #{mchntCd,jdbcType=CHAR},
      phone = #{phone,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creater = #{creater,jdbcType=VARCHAR},
      follow_man = #{followMan,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reserved1 = #{reserved1,jdbcType=VARCHAR},
      reserved2 = #{reserved2,jdbcType=VARCHAR},
      reserved3 = #{reserved3,jdbcType=VARCHAR}
    where row_id = #{rowId,jdbcType=BIGINT}
  </update>

  <select id="selectByMchntCdAndPhone" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM t_dips_customer_follow
    WHERE mchnt_cd = #{mchntCd}
    AND phone = #{phone}
    ORDER BY create_time DESC
  </select>

  <!-- 更新手机号 -->
  <update id="updatePhoneByMchntCdAndOldPhone" parameterType="map">
    UPDATE t_dips_customer_follow
    SET phone = #{newPhone,jdbcType=VARCHAR}
    WHERE mchnt_cd = #{mchntCd,jdbcType=CHAR}
      AND phone = #{oldPhone,jdbcType=VARCHAR}
  </update>
</mapper>