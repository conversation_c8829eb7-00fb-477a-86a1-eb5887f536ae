package com.fuiou.dips.data.resp;

import java.io.Serializable;

/**
 * 项目信息计数响应对象
 */
public class CountProjectResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所有符合条件的项目总数
     */
    private Long total;

    /**
     * 进行中的项目数量（project_st = '1'）
     */
    private Long inProgressCount;

    /**
     * 已关闭的项目数量（project_st = '2'）
     */
    private Long closedCount;

    /**
     * 已完成的项目数量（project_st = '9'）
     */
    private Long completedCount;


    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getInProgressCount() {
        return inProgressCount;
    }

    public void setInProgressCount(Long inProgressCount) {
        this.inProgressCount = inProgressCount;
    }

    public Long getClosedCount() {
        return closedCount;
    }

    public void setClosedCount(Long closedCount) {
        this.closedCount = closedCount;
    }

    public Long getCompletedCount() {
        return completedCount;
    }

    public void setCompletedCount(Long completedCount) {
        this.completedCount = completedCount;
    }
}
