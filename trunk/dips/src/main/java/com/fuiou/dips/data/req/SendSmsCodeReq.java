package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.ValidPhone;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

public class SendSmsCodeReq {
    //手机号
    @NotBlank(message = "手机号不能为空")
    @ValidPhone
    private String phone;

    //图形验证码或其他安全验证码
    private String vcode;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVcode() {
        return vcode;
    }

    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}
