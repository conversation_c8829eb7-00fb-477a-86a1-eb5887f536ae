package com.fuiou.dips.data.req;

import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

public class MchntHomePageReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询月份（格式：yyyy-MM）
     */
    @Pattern(regexp = "^$|\\d{4}-(0[1-9]|1[0-2])$", message = "查询月份格式必须为 yyyy-MM，例如 2025-04")
    private String queryMonth;

    /**
     * 开始时间（格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^$|\\d{4}-\\d{2}-\\d{2}$", message = "开始时间格式必须为 yyyy-MM-dd")
    private String startDate;

    /**
     * 结束时间（格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^$|\\d{4}-\\d{2}-\\d{2}$", message = "结束时间格式必须为 yyyy-MM-dd")
    private String endDate;


    /**
     * 客户列表
     */
    private List<String> storeIds;

    @AssertTrue(message = "查询月份和筛选时间段必填其一")
    public Boolean getQueryDateValid(){
        boolean hasValidDateRange = StringUtils.isNoneBlank(this.startDate, this.endDate);
        boolean hasValidQueryMonth = StringUtils.isNotBlank(this.queryMonth);
        return hasValidDateRange || hasValidQueryMonth;
    }

    public String getQueryMonth() {
        return queryMonth;
    }

    public void setQueryMonth(String queryMonth) {
        this.queryMonth = queryMonth;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }


    public List<String> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<String> storeIds) {
        this.storeIds = storeIds;
    }
}
