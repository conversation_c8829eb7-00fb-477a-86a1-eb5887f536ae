<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.dips.persist.dipsdb.QrcodeInfMapper">
    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.QrcodeInf">
        <id property="rowId" column="row_id" jdbcType="BIGINT"/>
        <result property="qrcodeToken" column="qrcode_token" jdbcType="VARCHAR"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="stageNo" column="stage_no" jdbcType="VARCHAR"/>
        <result property="mchntCd" column="mchnt_cd" jdbcType="VARCHAR"/>
        <result property="qrcodeType" column="qrcode_type" jdbcType="VARCHAR"/>
        <result property="orderState" column="order_state" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderAmt" column="order_amt" jdbcType="DECIMAL"/>
        <result property="notifyUrl" column="notify_url" jdbcType="VARCHAR"/>
        <result property="lockFlag" column="lock_flag" jdbcType="CHAR"/>
        <result property="goodsDes" column="goods_des" jdbcType="VARCHAR"/>
        <result property="addnInf" column="addn_inf" jdbcType="VARCHAR"/>
        <result property="expireTime" column="expire_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="reserved1" column="reserved1" jdbcType="VARCHAR"/>
        <result property="reserved2" column="reserved2" jdbcType="VARCHAR"/>
        <result property="reserved3" column="reserved3" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
    row_id,qrcode_token,project_no,stage_no,mchnt_cd,qrcode_type,order_state,order_no,order_amt,notify_url,create_time,update_time,expire_time,addn_inf,
    goods_des,lock_flag,reserved1,reserved2,reserved3
    </sql>


    <insert id="insert" parameterType="com.fuiou.dips.persist.beans.QrcodeInf">
        INSERT INTO t_dips_qrcode_inf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rowId != null">row_id,</if>
            <if test="qrcodeToken != null">qrcode_token,</if>
            <if test="projectNo != null">project_no,</if>
            <if test="mchntCd != null">mchnt_cd,</if>
            <if test="stageNo != null">stage_no,</if>
            <if test="qrcodeType != null">qrcode_type,</if>
            <if test="orderState != null">order_state,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="orderAmt != null">order_amt,</if>
            <if test="notifyUrl != null">notify_url,</if>
            <if test="lockFlag != null">lock_flag,</if>
            <if test="goodsDes != null">goods_des,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="reserved1 != null">reserved1,</if>
            <if test="reserved2 != null">reserved2,</if>
            <if test="reserved3 != null">reserved3,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rowId != null">#{rowId},</if>
            <if test="qrcodeToken != null">#{qrcodeToken},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="mchntCd != null">#{mchntCd},</if>
            <if test="stageNo != null">#{stageNo},</if>
            <if test="qrcodeType != null">#{qrcodeType},</if>
            <if test="orderState != null">#{orderState},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderAmt != null">#{orderAmt},</if>
            <if test="notifyUrl != null">#{notifyUrl},</if>
            <if test="lockFlag != null">#{lockFlag},</if>
            <if test="goodsDes != null">#{goodsDes},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="reserved1 != null">#{reserved1},</if>
            <if test="reserved2 != null">#{reserved2},</if>
            <if test="reserved3 != null">#{reserved3},</if>
        </trim>
    </insert>

    <!-- 根据ID查询记录 -->
    <select id="select"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_dips_qrcode_inf
        WHERE 1=1
        <if test="'' != mchntCd and null !=mchntCd ">
            AND mchnt_cd = #{mchntCd}
        </if>
        <if test="'' != qrcodeToken and null !=qrcodeToken ">
            AND qrcode_token = #{qrcodeToken}
        </if>
        <if test="'' != orderNo and null !=orderNo ">
            AND order_no = #{orderNo}
        </if>
        <if test="'' != orderState and null !=orderState ">
            AND order_state = #{orderState}
        </if>
          <if test="'' != projectNo and null !=projectNo ">
            AND project_no = #{projectNo}
        </if>
          <if test="'' != stageNo and null !=stageNo ">
            AND stage_no = #{stageNo}
        </if>
        <if test="'' != lockFlag and null !=lockFlag ">
            AND lock_flag = #{lockFlag}
        </if>
          <if test=" null !=expireTime ">
              <![CDATA[ AND expire_time >= #{expireTime,jdbcType=TIMESTAMP} ]]>
        </if>
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.fuiou.dips.persist.beans.QrcodeInf">
        UPDATE t_dips_qrcode_inf
        <set>
            <if test="qrcodeToken != null">order_no = #{qrcodeToken},</if>
            <if test="projectNo != null">project_no = #{projectNo},</if>
            <if test="mchntCd != null">mchnt_cd = #{mchntCd},</if>
            <if test="stageNo != null">stage_no = #{stageNo},</if>
            <if test="qrcodeType != null">qrcode_type = #{qrcodeType},</if>
            <if test="orderState != null">order_state = #{orderState},</if>
            <if test="orderAmt != null">order_amt = #{orderAmt},</if>
            <if test="notifyUrl != null">notify_url = #{notifyUrl},</if>
            <if test="lockFlag != null">lock_flag = #{lockFlag},</if>
            <if test="goodsDes != null">goods_des = #{goodsDes},</if>
            <if test="expireTime != null">pay_time = #{expireTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="reserved1 != null">reserved1 = #{reserved1},</if>
            <if test="reserved2 != null">reserved2 = #{reserved2},</if>
            <if test="reserved3 != null">reserved3 = #{reserved3},</if>
        </set>
        WHERE mchnt_cd = #{mchntCd} and qrcode_token = #{qrcodeToken}
    </update>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_dips_qrcode_inf
    </select>
    <select id="queryOrder" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_dips_qrcode_inf    WHERE mchnt_cd = #{mchntCd} and order_no = #{orderNo}
        <if test="qrcodeType != null and qrcodeType != ''">
            AND qrcode_type = #{qrcodeType}
        </if>
         limit 1
    </select>


    <update id="updateFinish" >
        UPDATE t_dips_qrcode_inf
        SET  order_state = '1' , update_time = current_timestamp
        WHERE mchnt_cd = #{mchntCd} AND order_no = #{orderNo}
    </update>


</mapper>
