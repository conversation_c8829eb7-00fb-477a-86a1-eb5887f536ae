package com.fuiou.dips.services;

import com.alibaba.fastjson.JSON;
import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.req.CreatePayQrcodeReq;
import com.fuiou.dips.data.resp.CreatePayQrcodeResp;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.QrcodeInf;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.dipsdb.MchntCfgMapper;
import com.fuiou.dips.persist.dipsdb.QrcodeInfMapper;
import com.fuiou.dips.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 二维码支付
 *
 * <AUTHOR>
 * @create 2025-05-19 16:10
 **/
@Service
public class QrcodePayService {

    private static final Logger log = LoggerFactory.getLogger(QrcodePayService.class);

    @Resource
    private TxnLogService txnLogService;
    @Resource
    private QrcodeInfMapper qrcodeInfMapper;
    @Resource
    private MchntCfgMapper mchntCfgMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private TpayOrderService tpayOrderService;
    @Resource
    private OrderService orderService;
    @Resource
    private TermInfoService termInfoService;

    /**
     * 创建支付二维码
     * 1：生成订单存t_dips_qrcode_inf
     * 2:返回开票二维码url=域名+接口名+payToken
     *
     * @param req
     * @return
     */
    public CreatePayQrcodeResp createPayQrcode(CreatePayQrcodeReq req) throws Exception {
        CreatePayQrcodeResp resp = null;

        resp = new CreatePayQrcodeResp();
        LogWriter.info("QrcodePayService.createPayQrcode 获取支付二维码入参："+JSON.toJSONString(req));
        Project project=projectService.queryProject(req.getProjectNo(),req.getMchntCd());
        ProjectStage projectStage=projectStageService.queryProjectStageValidStatus(req.getProjectNo(),req.getMchntCd(),req.getStageNo());
        BigDecimal reqStageActualAmt = new BigDecimal(req.getStageActualAmt()==null?  "0" : req.getStageActualAmt().toString());
        TermCacheData termCacheData = orderService.validProjectStage(reqStageActualAmt, projectStage, project);
        QrcodeInf qrcodeInf = generatorTxnLog(req,termCacheData);
        String qrCodeUrl = covertPayUrl(qrcodeInf,termCacheData);
        resp.setPayQrcodeUrl(qrCodeUrl);
        resp.setPayQrcodePicUrl(createQrPic(qrcodeInf, qrCodeUrl));
        resp.setOrderAmt(qrcodeInf.getOrderAmt());
        resp.setOrderNo(qrcodeInf.getOrderNo());
        resp.setProjectNo(qrcodeInf.getProjectNo());
        resp.setStageNo(qrcodeInf.getStageNo());
        resp.setMchntCd(qrcodeInf.getMchntCd());
        resp.setQrcodeType(qrcodeInf.getQrcodeType());
        resp.setExpTime(DateFormatUtils.format(qrcodeInf.getExpireTime(), "yyyy-MM-dd HH:mm:ss"));
        LogWriter.info("OrderService.createPayQrcode 创建支付二维码 end：" + JSON.toJSONString(resp));
        return resp;
    }



    /**
     * 生成订单记录、收款二维码记录
     *
     * @param req
     * @param termCacheData
     * @return
     */
    private QrcodeInf generatorTxnLog(CreatePayQrcodeReq req, TermCacheData termCacheData) {
        TxnLog log = new TxnLog();
        Date now = new Date();
        LoginResp loginToken = LoginConstat.getLoginToken();
        //木有终端号，截取商户号后8位
        String orderNo = covertOrderNo(req.getMchntCd().substring(req.getMchntCd().length() - 8), OrderNumPreEnum.ZXT, loginToken.getUserIp());
        log.setOrderNo(orderNo);
        log.setTradeDt(DateFormatUtils.format(now, "yyyyMMdd"));
        log.setProjectNo(req.getProjectNo());
        log.setMchntCd(req.getMchntCd());
        log.setStageNo(req.getStageNo());
        log.setStoreId(StringUtil.trimToEmpty(termCacheData.getStoreId()));
        log.setTradeType(TradeTypeEnum.POSITIVE_TXN.getCode());
        log.setOrderAmt(new BigDecimal(req.getAmt()));
        log.setPayState(OrderStatusEnum.INIT_STATUS.getCode());
        log.setFyFettleDt(DateFormatUtils.formatCurrDate("yyyyMMdd"));
        log.setCreateTime(now);
        log.setLoginId(loginToken.getLoginId());
        log.setCreateUserMobile(loginToken.getMobile());
        log.setCreateUserName(loginToken.getFullName());
        log.setStoreId(StringUtil.trimToNull(termCacheData.getStoreId()));
        int insert = txnLogService.insert(log);
        if (insert < 0) {
            throw new FUException(ResponseCodeEnum.ORDER_CREATE_ERROR);
        }
        LogWriter.info("OrderService.insertPreMchtOrder 更新商户订单表 end");
        QrcodeInf qrcodeInf = new QrcodeInf();
        qrcodeInf.setQrcodeToken(UUID.randomUUID().toString().replace("-", ""));
        qrcodeInf.setOrderNo(orderNo);
        qrcodeInf.setOrderAmt(String.valueOf(req.getAmt()));
        qrcodeInf.setExpireTime(DateUtils.addMinutes(now, 30));
        qrcodeInf.setMchntCd(req.getMchntCd());
        qrcodeInf.setOrderState(QrcodeStateEnum.INIT.getState());
        qrcodeInf.setProjectNo(req.getProjectNo());
        qrcodeInf.setStageNo(req.getStageNo());
        qrcodeInf.setQrcodeType(req.getQrcodeType());
        int i = qrcodeInfMapper.insert(qrcodeInf);
        if (i < 0) {
            throw new FUException(ResponseCodeEnum.PAY_QR_CREATE_ERROR);
        }
        LogWriter.info("OrderService.createPayQrcode 存支付二维码创建信息 end：" + JSON.toJSONString(qrcodeInf));
        return qrcodeInf;
    }


    /**
     * 生成订单号
     *
     * @param fyTermId
     * @param orderNumPreEnum
     * @param reqIp
     * @return
     */
    private static String covertOrderNo(String fyTermId, OrderNumPreEnum orderNumPreEnum, String reqIp) {

        String orderNoPrefix = String.format("%s%s", orderNumPreEnum.getOrderNoPrefix(), fyTermId);
        //订单号
        String orderNo = String.format("%s%s", orderNoPrefix, StringUtil.random(6));

        return LRCUtil.calculateLRCCheck(orderNo, orderNoPrefix.length(), orderNoPrefix.length() + 2, reqIp);
    }

    /**
     * 跳转台卡支付
     *
     * @param mchntCd
     * @param qrcodeToken
     * @return
     */
    public String toDeccaPay( String mchntCd, String qrcodeToken) throws Exception {
        QrcodeInf inf = new QrcodeInf();
        inf.setMchntCd(mchntCd);
        inf.setQrcodeToken(qrcodeToken);
        inf.setExpireTime(new Date());
        inf.setOrderState(QrcodeStateEnum.INIT.getState());
        List<QrcodeInf> qrcodeInfList = qrcodeInfMapper.select(inf);
        if (CollectionUtils.isEmpty(qrcodeInfList) || qrcodeInfList.get(0) == null) {
            LogWriter.info("OrderService.toDeccaPay 获取二维码信息失败..跳转失败信息页面");
            return Constant.QR_CODE_ERROR_PAGE_URL;
        }
        LogWriter.info("二维码token校验正常组装台卡支付url...");
        QrcodeInf qrcodeInf = qrcodeInfList.get(0);
        String projectNo = qrcodeInf.getProjectNo();
        String stageNo = qrcodeInf.getStageNo();
        Project project = projectService.queryProject(projectNo, mchntCd);

        TermCacheData termCacheData = termInfoService.getTermId(mchntCd, project.getStoreId());
        String toDeccaPayUrl = covertDeccaPayUrl( qrcodeInf, termCacheData);
        return toDeccaPayUrl;
    }


    @LogAnnotation("下单记录表-查询记录信息")
    public QrcodeInf queryOrder(String mchntCd, String orderNo, QrcodeTypeEnum qrcodeType) {
        return qrcodeInfMapper.queryOrder(mchntCd, orderNo, qrcodeType.getType());
    }

    private static String createQrPic(QrcodeInf qrcodeInf, String qrCodeUrl) {
        if (!Arrays.asList(QrcodeTypeEnum.PAY_QRCODE.getType(), QrcodeTypeEnum.SHARE_CARD.getType()).contains(StringUtil.trimToEmpty(qrcodeInf.getQrcodeType()))) {
            log.info("当前下单类型不需要生成二维码图片");
            return null;
        }
        String year = DateFormatUtils.format(new Date(), "yyyy");
        String month = DateFormatUtils.format(new Date(), "MM");
        String fileName = qrcodeInf.getQrcodeToken() + ".png";
        String fileUrl = Constant.ZXT_STATIC_FILE_ROOT + year + "/" + month;
        //生成二维码图片
        QrCodeUtil.createCodeToFile(qrCodeUrl, new File(fileUrl), fileName);
        String qrPicUrl = String.format("%s/%s/%s/%s", Constant.ZXT_STATIC_PIC_URL, year, month, fileName);
        return qrPicUrl;
    }

    private  String covertDeccaPayUrl(QrcodeInf qrcodeInf, TermCacheData termCacheData) throws Exception {
        //备注字段 用户姓名+手机号
        String addnInf = tpayOrderService.covertTerminalsOrderInfoReserve7(qrcodeInf.getMchntCd(), qrcodeInf.getProjectNo(), qrcodeInf.getStageNo());
        LogWriter.info("二维码token校验正常组装台卡支付addnInf前：" + addnInf);
        if (StringUtil.isNotEmpty(addnInf)) {
            addnInf = DesUtils.encrypt(addnInf, Constant.DES_KEY);
        }
        LogWriter.info("二维码token校验正常组装台卡支付addnInf：" + addnInf);
        //将rmk加密
        String sign = DesUtils.encrypt(qrcodeInf.getOrderNo() + qrcodeInf.getOrderAmt(), Constant.DES_KEY);
        ;
        String toDeccaPayUrl = String.format("%s%s?v=1.0&a=%s&addnInf=%s&extOrderNo=%s&sysId=%s&m=%s&sign=%s&storeId=%s"
                , Constant.DECCA_PAY_URL
                , StringUtil.trimToEmpty(termCacheData.getTmSerialNo())
                , StringUtil.trimToEmpty(qrcodeInf.getOrderAmt())
                , StringUtil.trimToEmpty(addnInf)
                , StringUtil.trimToEmpty(qrcodeInf.getOrderNo())
                , StringUtil.trimToEmpty(Constant.ZXT_TPAY_ORDER_SYS_ID)
                , StringUtil.trimToEmpty(qrcodeInf.getMchntCd())
                , StringUtil.trimToEmpty(sign)
                , StringUtil.trimToEmpty(termCacheData.getStoreId())
        );

        LogWriter.info("二维码token校验正常组装台卡支付url：" + toDeccaPayUrl);
        return toDeccaPayUrl;
    }




    private  String covertPayUrl(QrcodeInf qrcodeInf, TermCacheData termCacheData) throws Exception {

        String qrCodeUrl = null;
        if (Arrays.asList(QrcodeTypeEnum.PAY_QRCODE.getType(), QrcodeTypeEnum.SHARE_CARD.getType()).contains(StringUtil.trimToEmpty(qrcodeInf.getQrcodeType()))) {
            qrCodeUrl = Constant.PAY_QRCODE_URL + qrcodeInf.getMchntCd() + "/" + qrcodeInf.getQrcodeToken();
            LogWriter.info("OrderService.createPayQrcode 生成token支付二维码 ：" + qrCodeUrl);
            return qrCodeUrl;
        }
        qrCodeUrl = covertDeccaPayUrl(  qrcodeInf,termCacheData);
        LogWriter.info("OrderService.createPayQrcode 生成直跳台卡支付二维码 ：" + qrCodeUrl);
        return qrCodeUrl;

    }
}
