package com.fuiou.dips.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2020/5/19 9:55
 **/
public enum OriBusiIdEnum {
    TPAY("TPAY","tpay前置扫码"),
    DECA("DECA","DECCA台卡"),
    YUNM("YUNM","云秘下单") ,
    SAAS("SAAS","SAAS订单") ,
    SACZ("SACZ","C端充值") ,
    JFTS("JFTS","缴费通（原教培）") ,
    OPEN_PLATFORM("API","开放平台") ,
    ZXT("ZXT","装修通") ;


    /***
     * <AUTHOR> 
     * @Description   云秘订单后缀分隔符
     * @Date 2020/5/20 10:27
     * @param null 
     * @return 
    **/
    public static final String PLACEORDERNO_SEPARATOR="$$";
    
    private String code;
    private String name;

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }

     OriBusiIdEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
