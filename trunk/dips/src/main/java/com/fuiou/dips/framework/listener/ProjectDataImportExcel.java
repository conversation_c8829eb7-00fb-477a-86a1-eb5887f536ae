package com.fuiou.dips.framework.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fuiou.dips.framework.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 待处理导入
 *
 * <AUTHOR>
 */
public class ProjectDataImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    // 商户信息
    @StringLengthField(index = 0,
                       msg = "商户号长度不能超过20个字符",
                       length = 20,
                       zhLength = 20)
    @ExcelProperty(value = "商户号",
                   index = 0)
    private String mchntCd;          // 商户号

    @StringLengthField(index = 1,
                       msg = "所属门店长度不能超过200字符",
                       length = 200,
                       zhLength = 200)
    @ExcelProperty(value = "所属门店",
                   index = 1)
    private String storeName;        // 所属门店（非必填）

    @LocalDateParseField(index = 2,
                         msg = "项目创建日期，日期格式转换错误！")
    @ExcelProperty(value = "项目创建日期",
                   index = 2)
    private LocalDate createTime;         // 项目创建日期

    // 客户信息
    @StringLengthField(index = 3,
                       msg = "客户姓名长度不能超过60个字符",
                       length = 60,
                       zhLength = 60)
    @ExcelProperty(value = "客户姓名",
                   index = 3)
    private String customerName;     // 客户姓名

    @StringLengthField(index = 4,
                       msg = "客户手机号长度不能超过16个字符",
                       length = 16,
                       zhLength = 16)
    @ExcelProperty(value = "客户手机号",
                   index = 4)
    private String phone;            // 客户手机号

    // 项目信息
    @LongParseField(index = 5,
                    msg = "项目总金额转换Long类型失败")
    @ExcelProperty(value = "项目总金额",
                   index = 5)
    private Long projectAmt;         // 项目总金额(分)

    @StringLengthField(index = 6,
                       msg = "老板登录账号长度不能超过30个字符",
                       length = 30,
                       zhLength = 30)
    @ExcelProperty(value = "老板登录账号",
                   index = 6)
    private String loginId;          // 老板登录账号

    @StringLengthField(index = 7,
                       msg = "项目状态长度不能超过10个字符",
                       length = 10,
                       zhLength = 10)
    @ExcelProperty(value = "项目状态",
                   index = 7)
    private String projectSt;        // 项目状态

    // 阶段信息
    // private List<ProjectStageDataImportExcel> stages;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ProjectDataImportExcel that = (ProjectDataImportExcel) o;
        return Objects.equals(mchntCd, that.mchntCd) && Objects.equals(storeName, that.storeName) && Objects.equals(
                createTime, that.createTime) && Objects.equals(customerName, that.customerName) && Objects.equals(phone,
                that.phone) && Objects.equals(projectAmt, that.projectAmt) && Objects.equals(loginId, that.loginId) &&
               Objects.equals(projectSt, that.projectSt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mchntCd, storeName, createTime, customerName, phone, projectAmt, loginId, projectSt);
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public LocalDate getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDate createTime) {
        this.createTime = createTime;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getProjectAmt() {
        return projectAmt;
    }

    public void setProjectAmt(Long projectAmt) {
        this.projectAmt = projectAmt;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getProjectSt() {
        return projectSt;
    }

    public void setProjectSt(String projectSt) {
        this.projectSt = projectSt;
    }

    // public List<ProjectStageDataImportExcel> getStages() {
    //     return stages;
    // }
    //
    // public void setStages(List<ProjectStageDataImportExcel> stages) {
    //     this.stages = stages;
    // }
}
