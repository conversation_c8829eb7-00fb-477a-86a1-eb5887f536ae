package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

/**
 * 交易订单查询请求
 *
 * <AUTHOR>
 */
public class TxnLogReq {

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空")
    private String projectNo;

    /**
     * 商户号
     */
    @NotBlank(message = "商户编号不能为空")
    @Size(min = 5,
          max = 15,
          message = "商户编号需在5-15字符")
    private String mchntCd;

    /**
     * 阶段编号
     */
    @NotBlank(message = "商户编号不能为空")
    private String stageNo;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStageNo() {
        return stageNo;
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
