package com.fuiou.dips.utils;


import java.io.Serializable;
import java.util.Objects;

/**
 * 自定义 Triple 类，用于封装三个对象（左、中、右）
 *
 * @param <L> 左值类型
 * @param <M> 中间值类型
 * @param <R> 右值类型
 * <AUTHOR>
 */
public final class TripleMap<L, M, R> implements Serializable {

    private static final long serialVersionUID = 1L;
    private final L left;
    private final M middle;
    private final R right;

    private TripleMap(L left, M middle, R right) {
        this.left = left;
        this.middle = middle;
        this.right = right;
    }

    public L getLeft() {
        return left;
    }

    public M getMiddle() {
        return middle;
    }

    public R getRight() {
        return right;
    }

    public static <L, M, R> TripleMap<L, M, R> of(L left, M middle, R right) {
        return new TripleMap<>(left, middle, right);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TripleMap<?, ?, ?> triple = (TripleMap<?, ?, ?>) obj;
        return Objects.equals(left, triple.left) && Objects.equals(middle, triple.middle) && Objects.equals(right,
                triple.right);
    }

    @Override
    public int hashCode() {
        return Objects.hash(left, middle, right);
    }

    @Override
    public String toString() {
        return "Triple{" + "left=" + left + ", middle=" + middle + ", right=" + right + '}';
    }
}