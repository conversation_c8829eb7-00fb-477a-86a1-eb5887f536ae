package com.fuiou.dips.services;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.consts.WeChatConsts;
import com.fuiou.dips.data.entity.MessageNotificationBean;
import com.fuiou.dips.data.entity.PaymentNotificationDetailBean;
import com.fuiou.dips.data.req.PageMsgListReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.MchntInfo;
import com.fuiou.dips.data.resp.PageRespBase;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.*;
import com.fuiou.dips.persist.dipsdb.DipsMsgInfMapper;
import com.fuiou.dips.persist.dipsdb.DipsMsgUserMapper;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.RedisHandler;
import com.fuiou.dips.utils.Util;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
public class MsgService {
    @Resource
    private DipsMsgInfMapper dipsMsgInfMapper;
    @Resource
    private DipsMsgUserMapper dipsMsgUserMapper;
    @Resource
    private ProjectEmployeeService projectEmployeeService;
    @Resource(name = "redisHandler")
    private RedisHandler<String, String> redisHandler;
    @Resource
    private TxnLogService txnLogService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private ProjectCustomerService projectCustomerService;
    @Resource
    private CustomerService customerService;

    /**
     * 获取消息列表
     *
     * @param msgListReq
     * @return
     */
    public PageRespBase<MessageNotificationBean> getMsgList(PageMsgListReq msgListReq) {
        PageHelper.startPage(msgListReq.getPage(), msgListReq.getLimit());
        LoginResp loginToken = LoginConstat.getLoginToken();

        String loginId = getLoginId();

        String userType = UserTypeEnum.STAFF.getCode();

        List<MessageNotificationBean> msgListResps = dipsMsgInfMapper.selectMsgList(loginId, userType,
                loginToken.getMchntInfo().getMchntCd(), msgListReq.getReadFlag());
        return new PageRespBase<>(new PageInfo<>(msgListResps));
    }

    /**
     * 标记消息已读
     *
     * @param msgNo
     * @return
     */
    public int markMessageAsRead(String msgNo) {
        String loginId = getLoginId();
        String userType = UserTypeEnum.STAFF.getCode();
        return dipsMsgUserMapper.updateReadFlag(loginId, userType, msgNo);
    }

    public String getLoginId() {
        LoginResp loginToken = LoginConstat.getLoginToken();
        String mchntLoginId = Optional.ofNullable(loginToken).map(token -> token.getMchntInfo()).map(
                MchntInfo::getLoginId).orElse("");
        return StrUtil.isBlank(mchntLoginId) ? loginToken.getLoginId() : mchntLoginId;
    }

    /**
     * 推送小程序消息
     *
     * @param toUser     用户ID
     * @param templateId 模板ID
     * @param page       页面路径
     * @param data       消息数据
     * @return 是否推送成功
     */
    public boolean pushWxMsg(String toUser, String templateId, String page, Map<String, Object> data) {
        Map<String, Object> params = new HashMap<>();
        params.put("template_id", templateId);
        params.put("page", page);
        params.put("touser", toUser);
        params.put("data", data);
        try {
            String requestLog = String.format("pushWxMsg请求：%s", JsonUtil.bean2Json(params));
            LogWriter.info(this, requestLog);
            String respContent = HttpUtil.post(WeChatConsts.WX_MESSAGE_SUBSCRIBE_SEND_URL.concat(getWxToken()),
                    JsonUtil.toJsonStr(params), 1000 * 5);
            String responseLog = String.format("pushWxMsg响应：%s", respContent);
            LogWriter.info(this, responseLog);
            JSONObject jsonObject = JSONUtil.parseObj(respContent);
            int errCode = jsonObject.getInt("errcode", -1);
            return errCode == 0;
        } catch (Exception e) {
            LogWriter.error(this, "推送小程序消息异常", e);
            return false;
        }
    }

    /**
     * 获取微信token
     *
     * @return
     */
    public String getWxToken() {
        String wxTokenKey = String.format("%s%s", RedisKeyEnum.wx_token.getKey_prefix(), WeChatConsts.FUIOU_APP_ID);
        String wxToken = redisHandler.getCacheObject(wxTokenKey);
        if (StrUtil.isNotBlank(wxToken)) {
            return wxToken;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("grant_type", "client_credential");
        map.put("appid", WeChatConsts.FUIOU_APP_ID);
        map.put("secret", WeChatConsts.FUIOU_APP_SCRET);

        try {
            String response = HttpUtil.get(WeChatConsts.WX_TOKEN_URL, map, 1000 * 5);
            LogWriter.info("微信 token 获取成功（部分响应）: " + response);
            if (StrUtil.isBlank(response)) {
                throw new FUException(ResponseCodeEnum.WECHAT_LOGIN_FAIL);
            }
            JSONObject jsonObject = JSONUtil.parseObj(response);
            int errCode = jsonObject.getInt("errcode", -1);
            if (errCode != 0) {
                LogWriter.error("获取微信 token 失败，errcode=" + errCode);
                throw new FUException(ResponseCodeEnum.WECHAT_LOGIN_FAIL);
            }
            String accessToken = jsonObject.getStr("access_token");
            if (StrUtil.isBlank(accessToken)) {
                throw new FUException(ResponseCodeEnum.WECHAT_LOGIN_FAIL);
            }
            redisHandler.setCacheObjectTimeOut(wxTokenKey, accessToken, RedisKeyEnum.wx_token.getExpireSecond());
            return accessToken;

        } catch (Exception e) {
            LogWriter.error("获取微信 token 异常", e);
            throw new FUException(ResponseCodeEnum.WECHAT_LOGIN_FAIL);
        }
    }

    /**
     * 新增订单通知
     *
     * @param orderNo
     * @param mchntCd
     * @return
     */
    public int addOrderMsg(String mchntCd, String orderNo) {
        try {
            TxnLog txnLog = txnLogService.queryOrderInfo(mchntCd, orderNo);
            FUApiAssert.notNull(ResponseCodeEnum.ORDER_NON_EXIST, txnLog);
            String projectNo = txnLog.getProjectNo();
            String stageNo = txnLog.getStageNo();
            Project project = projectService.queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
            ProjectStage projectStage = projectStageService.queryProjectStage(projectNo, mchntCd, stageNo);
            FUApiAssert.notNull(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, projectStage);
            String msgType = TradeTypeEnum.POSITIVE_TXN.getCode().equals(txnLog.getTradeType()) ? "01" : "03";
            String msgContent;
            switch (msgType) {
                case "01":
                    msgContent = getPayMsg(txnLog, project, projectStage);
                    break;
                case "03":
                    msgContent = getRefundMsg(txnLog, project, projectStage);
                    break;
                default:
                    throw new FUException("Unsupported message type: " + msgType);
            }
            return addMsg(msgType, mchntCd, txnLog.getCreateUserName(), projectNo, msgContent);
        } catch (FUException e) {
            LogWriter.warn("addOrderMsg: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 支付：进入到支付/退款详情页面
     * - 支付成功通知
     * -customerName-stageName-paymentMethod-paymentStatus-paymentAmount-paymentTime-initiator
     *
     * @return
     */
    private String getPayMsg(TxnLog txnLog, Project project, ProjectStage projectStage) {
        PaymentNotificationDetailBean.PaymentSuccessDetail paymentSuccessDetail =
                new PaymentNotificationDetailBean.PaymentSuccessDetail();
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(
                project.getProjectNo(), txnLog.getMchntCd(), null);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, projectCustomer);
        Customer customer = customerService.selectByMchntCdAndPhone(projectCustomer.getMchntCd(),
                projectCustomer.getPhone());
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer);
        paymentSuccessDetail.setCustomerName(customer.getCustomerName());
        paymentSuccessDetail.setPaymentAmount(
                txnLog.getOrderAmt().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        paymentSuccessDetail.setPaymentMethod(OrderTypeEnum.getDescByOrderType(txnLog.getOrderType()));
        paymentSuccessDetail.setPaymentStage(projectStage.getStageName());
        paymentSuccessDetail.setPaymentStatus(OrderStatusEnum.getDescByOrderType(txnLog.getPayState()));
        paymentSuccessDetail.setPaymentTime(txnLog.getPayTime());
        paymentSuccessDetail.setProjectNo(txnLog.getProjectNo());
        paymentSuccessDetail.setOrderNo(txnLog.getOrderNo());
        paymentSuccessDetail.setInitiator(txnLog.getCreateUserName());
        paymentSuccessDetail.setStageNo(projectStage.getStageNo());
        return JSONUtil.toJsonStr(paymentSuccessDetail);
    }

    /**
     * 获取退款消息
     *
     * @param txnLog
     * @param project
     * @param projectStage
     * @return
     */
    private String getRefundMsg(TxnLog txnLog, Project project, ProjectStage projectStage) {
        PaymentNotificationDetailBean.RefundDetail refundDetail = new PaymentNotificationDetailBean.RefundDetail();
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(
                project.getProjectNo(), txnLog.getMchntCd(), null);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, projectCustomer);
        Customer customer = customerService.selectByMchntCdAndPhone(projectCustomer.getMchntCd(),
                projectCustomer.getPhone());
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer);
        refundDetail.setCustomerName(customer.getCustomerName());
        refundDetail.setRefundAmount(txnLog.getOrderAmt().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        refundDetail.setRefundChannel(OrderTypeEnum.getDescByOrderType(txnLog.getOrderType()));
        refundDetail.setTotalProjectAmount(
                project.getProjectAmt().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        refundDetail.setProjectNo(txnLog.getProjectNo());
        refundDetail.setOrderNo(txnLog.getOrderNo());
        refundDetail.setRefundStage(projectStage.getStageName());
        refundDetail.setStageNo(projectStage.getStageNo());
        return JSONUtil.toJsonStr(refundDetail);
    }

    /**
     * 项目收款金额通知
     *
     * @param oldProjectStage
     * @param newProjectStage
     * @param remark
     * @return
     */
    public int addProjectAmtChangeMsg(ProjectStage oldProjectStage, ProjectStage newProjectStage, String remark) {
        PaymentNotificationDetailBean.ProjectAmountChangeDetail changeDetail =
                new PaymentNotificationDetailBean.ProjectAmountChangeDetail();
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(
                oldProjectStage.getProjectNo(), oldProjectStage.getMchntCd(), null);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, projectCustomer);
        Customer customer = customerService.selectByMchntCdAndPhone(projectCustomer.getMchntCd(),
                projectCustomer.getPhone());
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer);
        changeDetail.setCustomerName(customer.getCustomerName());
        changeDetail.setProjectNo(oldProjectStage.getProjectNo());
        changeDetail.setChangeStage(oldProjectStage.getStageName());
        changeDetail.setOriginalAmount(
                oldProjectStage.getStageAmt().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        changeDetail.setNewAmount(newProjectStage.getStageAmt().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        changeDetail.setModifier(LoginConstat.getLoginToken().getFullName());
        changeDetail.setStageNo(oldProjectStage.getStageNo());
        changeDetail.setProjectNo(oldProjectStage.getProjectNo());
        changeDetail.setRemarks(StrUtil.blankToDefault(remark, "项目金额变动通知"));
        String msgContent = JSONUtil.toJsonStr(changeDetail);
        LoginResp loginToken = LoginConstat.getLoginToken();
        return addMsg("02", oldProjectStage.getMchntCd(), loginToken.getFullName(), oldProjectStage.getProjectNo(),
                msgContent);
    }

    /**
     * 消息持久化
     *
     * @param msgType
     * @param mchntCd
     * @param operator
     * @param msgContent
     * @param projectNo
     */
    private int addMsg(String msgType, String mchntCd, String operator, String projectNo, String msgContent) {
        String msgNo = Util.generateRecordNo();
        DipsMsgInf dipsMsgInf = new DipsMsgInf();
        dipsMsgInf.setMsgType(msgType);
        dipsMsgInf.setMchntCd(mchntCd);
        dipsMsgInf.setOperator(operator);
        dipsMsgInf.setMsgContent(msgContent);
        dipsMsgInf.setCreateTime(new java.util.Date());
        dipsMsgInf.setMsgTitle(MsgTypeEnum.getDescByCode(msgType));
        dipsMsgInf.setMsgNo(msgNo);
        dipsMsgInf.setCreateTime(new Date());
        dipsMsgInf.setUpdateTime(new Date());
        int i = addMsgInf(dipsMsgInf);
        if (i < 1) {
            throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
        }

        List<DipsMsgUser> dipsMsgUsers = new ArrayList<>();
        List<ProjectEmployee> projectEmployees = projectEmployeeService.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        for (ProjectEmployee projectEmployee : projectEmployees) {
            DipsMsgUser dipsMsgUser = new DipsMsgUser();
            dipsMsgUser.setMsgNo(msgNo);
            dipsMsgUser.setLoginId(projectEmployee.getEmployeeLoginId());
            dipsMsgUser.setUserType(UserTypeEnum.STAFF.getCode());
            dipsMsgUser.setReadFlag("0");
            dipsMsgUser.setCreateTime(new Date());
            dipsMsgUser.setUpdateTime(new Date());
            dipsMsgUsers.add(dipsMsgUser);
        }
        int j = addMsgUsers(dipsMsgUsers);
        if (j < 1) {
            throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
        }
        return j;
    }

    /**
     * 添加消息
     *
     * @param dipsMsgInf
     * @return
     */
    private int addMsgInf(DipsMsgInf dipsMsgInf) {
        return dipsMsgInfMapper.insertSelective(dipsMsgInf);
    }

    /**
     * 批量添加消息用户
     *
     * @param dipsMsgUsers
     * @return
     */
    private int addMsgUsers(List<DipsMsgUser> dipsMsgUsers) {
        return dipsMsgUserMapper.batchInsert(dipsMsgUsers);
    }

}
