package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;

/**
 * 员工表分页查询参数
 *
 * <AUTHOR>
 * @TableName t_mchnt_acnt_login_inf
 */
public class EmpPageReq extends PageReqBase {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String mchntCd;

    public String getMchntCd() {

        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

}