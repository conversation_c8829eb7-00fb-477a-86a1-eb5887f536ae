package com.fuiou.dips.data.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.utils.StringUtil;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易订单响应对象
 *
 * <AUTHOR>
 */
public class TxnLogResp {

    /**
     * 支付时间
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 支付类型（1-正交易，2-反交易）
     */
    private String tradeType;

    /**
     * 支付类型（1-正交易，2-反交易）
     */
    private String tradeTypeName;

    public String getTradeTypeName() {
        if (StringUtil.isNotBlank(tradeType)) {
            return TradeTypeEnum.getDescByCode(tradeType);
        }
        return tradeTypeName;
    }

    /**
     * 支付金额（单位：分）
     */
    private BigDecimal payAmount;

    /**
     * 订单金额（单位：分）
     */
    private BigDecimal orderAmt;

    /**
     * 退款金额（单位：分）
     */
    private BigDecimal refundAmt;

    /**
     * JSAPI:微信主扫，FWC:支付宝主扫
     */
    private String orderType;

    /**
     * JSAPI:微信主扫，FWC:支付宝主扫
     */
    private String orderTypeName;

    public String getOrderTypeName() {
        if (StringUtil.isNotBlank(orderType)) {
            return OrderTypeEnum.getDescByOrderType(orderType);
        }
        return orderTypeName;
    }

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 终端号
     */
    private String fyTermId;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 条码流水号
     */
    private String channelOrderId;

    /**
     * 渠道交易号
     */
    private String transactionId;

    /**
     * 清算日期
     */
    private String fyFettleDt;

    /**
     * 手续费
     */
    private BigDecimal feeAmt;

    /**
     * 00:未支付状态,01:已支付02:支付失败,03:已退款,04:支付中,05:已撤销,06:已退货,07:超时,11:预授权完成,12:预授权完成退款
     */
    private String payState;

    private String payStateName;

    public String getPayStateName() {
        if (StringUtil.isNotBlank(payState)) {
            return OrderStatusEnum.getDescByOrderType(payState);
        }
        return payStateName;
    }

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 商户名称
     */
    private String mchntName;

    /**
     * 商户简称
     */
    private String mchntShortName;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntName() {
        return mchntName;
    }

    public void setMchntName(String mchntName) {
        this.mchntName = mchntName;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getMchntShortName() {
        return mchntShortName;
    }

    public void setMchntShortName(String mchntShortName) {
        this.mchntShortName = mchntShortName;
    }

    public String getFyTermId() {
        return fyTermId;
    }

    public void setFyTermId(String fyTermId) {
        this.fyTermId = fyTermId;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    public String getFyFettleDt() {
        return fyFettleDt;
    }

    public void setFyFettleDt(String fyFettleDt) {
        this.fyFettleDt = fyFettleDt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public BigDecimal getPayAmount() {
        if (orderAmt != null && refundAmt != null) {
            return orderAmt.subtract(refundAmt);
        } else if (orderAmt != null) {
            return orderAmt;
        }
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public void setTradeTypeName(String tradeTypeName) {
        this.tradeTypeName = tradeTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public void setPayStateName(String payStateName) {
        this.payStateName = payStateName;
    }
}
