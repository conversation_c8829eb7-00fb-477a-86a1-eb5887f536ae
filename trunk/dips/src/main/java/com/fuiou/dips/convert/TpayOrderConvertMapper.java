package com.fuiou.dips.convert;

import com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo;
import com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
* tpay订单信息转换
* @Author: Joker
* @Date: 2025/5/20 18:39
*/

@Mapper(componentModel = "spring")
public interface TpayOrderConvertMapper {

    @Mapping(source = "payState", target = "oldStatus")
    @Mapping(source = "orderAmt", target = "oldOrderAmt")
    @Mapping(source = "refundAmt", target = "oldRefundAmt")
    @Mapping(source = "couponFee", target = "oldCouponFee")
    @Mapping(source = "reserve10", target = "oldReserve10")
    TerminalsOrderInfoDTO covertTerminalsOrderInfo(TerminalsOrderInfo orderInfo);
}

