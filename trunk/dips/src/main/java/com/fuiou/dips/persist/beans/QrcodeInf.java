package com.fuiou.dips.persist.beans;

import java.util.Date;

/**
 * 二维码信息
 *
 * <AUTHOR>
 * @create 2025-05-19 16:33
 **/
public class QrcodeInf {
    private Long rowId;// '主键',
    private String qrcodeToken;//  '收款码token',
    private String projectNo;//  '项目编号',
    private String stageNo;// '阶段编号',
    private String mchntCd;// '商户号',
    private String qrcodeType;//'类型 ： 1 收款码 2 分享小卡片 3 刷卡支付',
    private String orderState;// '0 初始 1 完成 2 失效',
    private String orderNo;//  '交易订单号',
    private String orderAmt;// '订单金额，单位分',
    private String notifyUrl;// '回调地址',
    private Date createTime;// '创建时间',
    private Date updateTime;// '修改时间',
    private Date expireTime;//  '失效时间',
    private String addnInf;// '附加数据',
    private String goodsDes;// '商品描述',
    private String lockFlag;//  '锁定标记 0 未锁定 1锁定',
    private String reserved1;// '备注1',
    private String reserved2;//'备注2',
    private String reserved3;// '备注3',

    public Long getRowId() {
        return rowId;
    }

    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    public String getQrcodeToken() {
        return qrcodeToken;
    }

    public void setQrcodeToken(String qrcodeToken) {
        this.qrcodeToken = qrcodeToken;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getStageNo() {
        return stageNo;
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getQrcodeType() {
        return qrcodeType;
    }

    public void setQrcodeType(String qrcodeType) {
        this.qrcodeType = qrcodeType;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getAddnInf() {
        return addnInf;
    }

    public void setAddnInf(String addnInf) {
        this.addnInf = addnInf;
    }

    public String getGoodsDes() {
        return goodsDes;
    }

    public void setGoodsDes(String goodsDes) {
        this.goodsDes = goodsDes;
    }

    public String getLockFlag() {
        return lockFlag;
    }

    public void setLockFlag(String lockFlag) {
        this.lockFlag = lockFlag;
    }

    public String getReserved1() {
        return reserved1;
    }

    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    public String getReserved2() {
        return reserved2;
    }

    public void setReserved2(String reserved2) {
        this.reserved2 = reserved2;
    }

    public String getReserved3() {
        return reserved3;
    }

    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }
}
