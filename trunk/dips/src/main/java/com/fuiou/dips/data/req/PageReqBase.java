package com.fuiou.dips.data.req;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: dips
 * @description:
 * @author: Joker
 * @create: 2025/05/07 11:19
 */
public class PageReqBase implements Serializable {

    @NotNull
    @Min(value = 1, message = "limit不能小于1")
    @Max(value = 100, message = "limit不能大于100")
    private Integer limit;

    @NotNull
    @Min(value = 1, message = "page不能小于1")
    @Max(value = 10000, message = "page不能大于10000")
    private Integer page;

    public Integer getLimit() {

        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getPage() {

        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }
}
