package com.fuiou.dips.data.req;

import com.fuiou.dips.swt.utils.StrUtil;
import com.fuiou.dips.valid.group.UpdateGroup;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 装修通项目阶段请求参数
 *
 * <AUTHOR>
 */
public class ProjectStageReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 阶段顺序
     */
    @NotNull(message = "阶段顺序不能为空")
    private Integer stageOrder;

    /**
     * 阶段名称
     */
    @NotBlank(message = "阶段名称不能为空")
    private String stageName;

    /**
     * 阶段编号
     */
    private String stageNo;

    /**
     * 阶段金额
     */
    @NotNull(message = "阶段金额不能为空",
             groups = {UpdateGroup.class})
    private BigDecimal stageAmt;

    /**
     * 项目备注
     */
    private String reserved1;

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getReserved1() {
        return StrUtil.isBlank(reserved1) ? "" : reserved1;
    }

    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    public String getStageNo() {
        return stageNo;
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public BigDecimal getStageAmt() {
        return stageAmt;
    }

    public void setStageAmt(BigDecimal stageAmt) {
        this.stageAmt = stageAmt;
    }

    public Integer getStageOrder() {
        return stageOrder;
    }

    public void setStageOrder(Integer stageOrder) {
        this.stageOrder = stageOrder;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }
}
