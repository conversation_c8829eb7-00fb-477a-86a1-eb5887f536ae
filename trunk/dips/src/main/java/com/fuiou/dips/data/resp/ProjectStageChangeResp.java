package com.fuiou.dips.data.resp;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 装修通项目阶段付款变更记录表
 *
 * <AUTHOR>
 * @TableName t_dips_project_stage_change
 */
public class ProjectStageChangeResp implements Serializable {

    /**
     * 记录流水号
     */
    private String logNo;

    /**
     * 变更内容
     */
    private String changeContent;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * 记录流水号
     */
    public String getLogNo() {
        return logNo;
    }

    /**
     * 记录流水号
     */
    public void setLogNo(String logNo) {
        this.logNo = logNo;
    }

    /**
     * 变更内容
     */
    public String getChangeContent() {
        return changeContent;
    }

    /**
     * 变更内容
     */
    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
