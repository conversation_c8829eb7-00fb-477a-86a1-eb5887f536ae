package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.DateFormat;
import com.fuiou.dips.valid.DateFormatValidator;
import com.fuiou.dips.valid.Money;
import com.fuiou.dips.valid.MoneyNotBlank;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;


public class OrderCallBackReq {

    @NotBlank(message = "order_no不能为空")
    private String order_no; //订单号

    @NotBlank(message = "mchnt_cd不能为空")
    private String mchnt_cd; //商户号

    @NotBlank(message = "trade_dt不能为空")
    private String trade_dt; //支付日期

    @NotBlank(message = "order_type不能为空")
    @Size(max = 20, message = "order_type最大长度为20")
    private String order_type;

    @MoneyNotBlank
    private String order_amt; //订单金额

    @Money(message = "coupon_amt 格式错误",  hasZero = true)
    private String coupon_amt; //优惠金额

    @Money(message = "acual_amt 格式错误",  hasZero = true)
    private String acual_amt; //实收金额

    @NotBlank(message = "trade_type不能为空")
    @Size(min = 1, max = 1, message = "trade_type长度必须为1")
    private String trade_type; //正反交易类型  1 正交易 2 反交易

    @DateFormat(nullable = false, format = DateFormatValidator.FULL_TIME_WITH_OUT_SEPARATOR)
    private String timestamp; //发送时间  格式yyyyMMddHHmmss

    @NotBlank(message = "pay_state不能为空")
    private String pay_state; //支付状态

    private String resp_code; //交易应答码

    private String resp_msg; //交易描述

    @DateFormat(nullable = false, format = DateFormatValidator.FULL_TIME_WITH_OUT_SEPARATOR)
    private String txn_time; //交易时间

    @DateFormat(format = DateFormatValidator.FULL_TIME_WITH_OUT_SEPARATOR)
    private String pay_time; //支付完成时间  正交易传递

    private String src_order_no; //源订单号

    @Money(message = "total_refund_amt 格式错误",  hasZero = true)
    private String total_refund_amt; //退款总金额

    private String channel_order_id; //条码流水

    private String transaction_id; //渠道流水号

    private String fy_trace_no; //富友跟踪号
    @NotBlank(message = "fy_term_id不能为空")
    private String fy_term_id; //富友终端号

    /**
     * 1. 获取所有 post 内容，不包括字节类型参数，如文件、字节流，剔除 sign 字段，剔除值为空的参数；
     * 2. 按照第一个字符的键值 ASCII 码递增排序（字母升序排序），如果遇到相同字符则按照第二个字符的键值 ASCII 码递增排序，以此类推；
     * 3. 将排序后的参数与其对应值，组合成 参数=参数值 的格式，并且把这些参数用 & 字符连接起来，此时生成的字符串为待签名字符串。
     */
    @NotBlank(message = "sign不能为空")
    private String sign;

    /**
    * 原单清算日期
    * @Author: Joker
    * @Date: 2025/6/18 15:42
    */

    private String src_trade_dt;

    /**
    * 原单参考号
    * @Author: Joker
    * @Date: 2025/6/18 15:42
    */

    private String src_fy_trace_no;
    
    
    /**
    *扫码交易为用户id，刷卡交易传卡号
    * @Author: Joker
    * @Date: 2025/6/19 17:41 
    */ 
    
    private String openId;


    public String getAcual_amt() {
        return acual_amt;
    }

    public void setAcual_amt(String acual_amt) {
        this.acual_amt = acual_amt;
    }

    public String getFy_trace_no() {
        return fy_trace_no;
    }

    public void setFy_trace_no(String fy_trace_no) {
        this.fy_trace_no = fy_trace_no;
    }

    public String getTxn_time() {
        return txn_time;
    }

    public void setTxn_time(String txn_time) {
        this.txn_time = txn_time;
    }

    public String getCoupon_amt() {
        return coupon_amt;
    }

    public void setCoupon_amt(String coupon_amt) {
        this.coupon_amt = coupon_amt;
    }

    public String getChannel_order_id() {
        return channel_order_id;
    }

    public void setChannel_order_id(String channel_order_id) {
        this.channel_order_id = channel_order_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getSrc_order_no() {
        return src_order_no;
    }

    public void setSrc_order_no(String src_order_no) {
        this.src_order_no = src_order_no;
    }

    public String getTotal_refund_amt() {
        return total_refund_amt;
    }

    public void setTotal_refund_amt(String total_refund_amt) {
        this.total_refund_amt = total_refund_amt;
    }

    public String getResp_code() {
        return resp_code;
    }

    public void setResp_code(String resp_code) {
        this.resp_code = resp_code;
    }

    public String getResp_msg() {
        return resp_msg;
    }

    public void setResp_msg(String resp_msg) {
        this.resp_msg = resp_msg;
    }

    public String getOrder_type() {
        return order_type;
    }

    public void setOrder_type(String order_type) {
        this.order_type = order_type;
    }

    public String getPay_time() {
        return pay_time;
    }

    public void setPay_time(String pay_time) {
        this.pay_time = pay_time;
    }

    public String getPay_state() {
        return pay_state;
    }

    public void setPay_state(String pay_state) {
        this.pay_state = pay_state;
    }

    public String getOrder_no() {
        return order_no;
    }

    public void setOrder_no(String order_no) {
        this.order_no = order_no;
    }

    public String getMchnt_cd() {
        return mchnt_cd;
    }

    public void setMchnt_cd(String mchnt_cd) {
        this.mchnt_cd = mchnt_cd;
    }

    public String getTrade_dt() {
        return trade_dt;
    }

    public void setTrade_dt(String trade_dt) {
        this.trade_dt = trade_dt;
    }

    public String getOrder_amt() {
        return order_amt;
    }

    public void setOrder_amt(String order_amt) {
        this.order_amt = order_amt;
    }

    public String getTrade_type() {
        return trade_type;
    }

    public void setTrade_type(String trade_type) {
        this.trade_type = trade_type;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getFy_term_id() {
        return fy_term_id;
    }

    public void setFy_term_id(String fy_term_id) {
        this.fy_term_id = fy_term_id;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSrc_trade_dt() {

        return src_trade_dt == null ? null : src_trade_dt.trim();
    }

    public void setSrc_trade_dt(String src_trade_dt) {
        this.src_trade_dt = src_trade_dt;
    }

    public String getSrc_fy_trace_no() {

        return src_fy_trace_no == null ? null : src_fy_trace_no.trim();
    }

    public void setSrc_fy_trace_no(String src_fy_trace_no) {
        this.src_fy_trace_no = src_fy_trace_no;
    }

    public String getOpenId() {

        return openId == null ? null : openId.trim();
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
