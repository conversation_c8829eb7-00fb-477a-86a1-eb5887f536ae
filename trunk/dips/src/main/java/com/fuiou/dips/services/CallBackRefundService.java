package com.fuiou.dips.services;

import com.fuiou.dips.data.req.OrderCallBackReq;
import com.fuiou.dips.enums.OrderStatusEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.TradeTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.dipsdb.ProjectStageMapper;
import com.fuiou.dips.utils.DateUtils;
import com.fuiou.dips.utils.JsonToMapConverter;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 退款回调处理
 */
@Service
public class CallBackRefundService {

    @Autowired
    private SignService signService;

    @Autowired
    private TxnLogService txnLogService;

    @Autowired
    private ProjectStageMapper projectStageMapper;

    /**
     * 退款回调处理
     * @param req
     */
    public void deal(OrderCallBackReq req){
        //校验参数
        checkParams(req);
        //验签
        checkSign(req);
        //查询原交易
        TxnLog srcTxnLog = queryOrderInfo(req);
        //校验退款订单信息唯一
        checkRefundOrderInfoUnique(req);
        //插入退款订单
        TxnLog refundTxnlog = insertRefundTxnLog(req, srcTxnLog);
        //更新原订单
        int srcUpdateResult = updateSrcTxnLog(req, srcTxnLog);
        if(srcUpdateResult != 1){
            LogWriter.info(this, "更新原订单失败, 不需要执行后续操作");
            return;
        }
        //更新项目阶段信息
        updateProjectStage(refundTxnlog, req);
    }

    private void checkSign(OrderCallBackReq req) {
        try  {
            signService.check(SignUtils.getSignContent(getTextParams(req)), req.getSign());;
        } catch (Exception e) {
            LogWriter.error(this,  "验签失败", e);
            throw new FUException(ResponseCodeEnum.CHECK_SIGN_ERROR);
        }
    }

    private Map<String, String> getTextParams(OrderCallBackReq req) {
        return JsonToMapConverter.convertToMap(req);
    }

    private void checkParams(OrderCallBackReq req){
        if(!TradeTypeEnum.COUNTER_TXN.getCode().equals(req.getTrade_type())){
            throw new FUException(ResponseCodeEnum.PARAM_ERROR.getCode(), "trade_type 不正确");
        }
        if(StringUtils.isBlank(req.getSrc_order_no())){
            throw new FUException(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "src_order_no 不能为空");
        }
        if(!OrderStatusEnum.REFUND_SUCCESS.getCode().equals(req.getPay_state())){
            throw new FUException(ResponseCodeEnum.PARAM_ERROR.getCode(), "pay_state 非退款成功");
        }

        if(StringUtils.isBlank(req.getSrc_fy_trace_no())){
            throw new FUException(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "src_fy_trace_no 不能为空");
        }

        if(StringUtils.isBlank(req.getSrc_trade_dt())){
            throw new FUException(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "src_trade_dt 不能为空");
        }
    }

    private TxnLog queryOrderInfo(OrderCallBackReq req){
        TxnLog txnLog = txnLogService.queryOrderInfo(req.getMchnt_cd(), req.getSrc_order_no());
        if(txnLog == null){
            throw new FUException(ResponseCodeEnum.ORDER_NON_EXIST);
        }
        if(!OrderStatusEnum.PAY_SUCCESS.getCode().equals(txnLog.getPayState()) && !OrderStatusEnum.REFUND_SUCCESS.getCode().equals(txnLog.getPayState()) ){
            throw new FUException(ResponseCodeEnum.ORDER_STATUS_ERROR);
        }
        if(!StringUtils.trimToEmpty(req.getSrc_trade_dt()).equals(StringUtils.trimToEmpty(txnLog.getFyFettleDt())) ||!StringUtils.trimToEmpty(req.getSrc_fy_trace_no()).equals(StringUtils.trimToEmpty(txnLog.getFyTraceNo())) ){
            throw new FUException(ResponseCodeEnum.ORDER_NON_EXIST);
        }
        return  txnLog;
    }

    private void checkRefundOrderInfoUnique(OrderCallBackReq req){
        TxnLog txnLog = txnLogService.queryOrderInfo(req.getMchnt_cd(), req.getOrder_no());
        if(txnLog != null){
            throw new FUException(ResponseCodeEnum.ORDER_HAS_EXIST);
        }
    }

    private TxnLog insertRefundTxnLog(OrderCallBackReq req, TxnLog srcTxnLog){
        try {
            TxnLog refundTxnLog = new TxnLog();
            refundTxnLog.setMchntCd(req.getMchnt_cd());
            refundTxnLog.setOrderNo(req.getOrder_no());
            refundTxnLog.setOrderType(req.getOrder_type());
            refundTxnLog.setTradeDt(req.getTrade_dt());
            refundTxnLog.setFyFettleDt(req.getTrade_dt());
            refundTxnLog.setOrderAmt(new BigDecimal(req.getOrder_amt()));
            refundTxnLog.setCreateTime(DateUtils.parseDateYYYYMMddHHmmss(req.getTxn_time()));
            refundTxnLog.setUpdateTime(refundTxnLog.getCreateTime());
            refundTxnLog.setPayTime(refundTxnLog.getCreateTime());
            refundTxnLog.setTradeType(TradeTypeEnum.COUNTER_TXN.getCode());
            refundTxnLog.setFyFettleDt(req.getTrade_dt());
            refundTxnLog.setFyTraceNo(req.getFy_trace_no());
            refundTxnLog.setPayState(req.getPay_state());
            refundTxnLog.setTransactionId(req.getTransaction_id());
            refundTxnLog.setChannelOrderId(req.getChannel_order_id());
            refundTxnLog.setSrcOrderNo(req.getSrc_order_no());
            refundTxnLog.setRespCode(req.getResp_code());
            refundTxnLog.setChannelOrderId(req.getChannel_order_id());
            refundTxnLog.setTransactionId(req.getTransaction_id());
            refundTxnLog.setRespMsg(StringUtils.substring(req.getResp_msg(), 0, 50));
            refundTxnLog.setProjectNo(srcTxnLog.getProjectNo());
            refundTxnLog.setStageNo(srcTxnLog.getStageNo());
            refundTxnLog.setStoreId(srcTxnLog.getStoreId());
            refundTxnLog.setFyTermId(srcTxnLog.getFyTermId());
            int result = txnLogService.insert(refundTxnLog);
            if (result != 1){
                throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
            }
            return refundTxnLog;
        } catch (Exception e) {
            LogWriter.error(this,  "退款订单插入失败", e);
            throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
        }
    }

    private int updateSrcTxnLog(OrderCallBackReq req, TxnLog srcTxnLog){
        if(!OrderStatusEnum.REFUND_SUCCESS.getCode().equals(req.getPay_state())){
            LogWriter.info(this, "状态不是退款成功，不需要更新原单信息");
            return 0;
        }
        try {
            srcTxnLog.setOldPayStatus(srcTxnLog.getPayState());
            srcTxnLog.setPayState(OrderStatusEnum.REFUND_SUCCESS.getCode());
            srcTxnLog.setUpdateTime(DateUtils.parseDateYYYYMMddHHmmss(req.getTxn_time()));
            srcTxnLog.setRefundAmt(new BigDecimal(req.getTotal_refund_amt()));
            int result = txnLogService.update(srcTxnLog);
            if (result != 1){
                throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
            }
            return result;
        } catch (Exception e) {
            LogWriter.error(this,  "原订单更新失败", e);
            throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
        }
    }

    /**
     * 更新项目阶段信息
     * @param txnLogDB
     * @param req
     */
    private void updateProjectStage(TxnLog txnLogDB, OrderCallBackReq req){
        if(!OrderStatusEnum.REFUND_SUCCESS.getCode().equals(req.getPay_state())){
            LogWriter.info(this, "订单状态不是退款成功，不需要更新项目阶段信息");
            return;
        }
        for(int i = 0; i < 3; i++){
            //重试更新，防止统一阶段交易回调并发情况
            int result = updateProjectStageData(txnLogDB);
            try {
                if(result > 0 ){
                    return;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                LogWriter.error(this,  "更新项目阶段信息失败", e);
                //TODO 预警
            }
        }
    }

    private int getTxnStateOrderNo(List<ProjectStage> projectStages, TxnLog txnLogDB){
        for(int i = 0; i < projectStages.size(); i++){
            //本次收款交易对应阶段
            if(projectStages.get(i).getStageNo().equals(txnLogDB.getStageNo())){
                return i;
            }
        }
        throw new FUException(ResponseCodeEnum.STAGE_NON_EXIST);
    }

    private int updateProjectStageData(TxnLog txnLogDB){
        //查询阶段列表
        List<ProjectStage> projectStages = projectStageMapper.selectByMchntCdAndProjectNo(txnLogDB.getMchntCd(),  txnLogDB.getProjectNo());
        //获取阶段序号
        int stateOrderNo = getTxnStateOrderNo(projectStages, txnLogDB);
        ProjectStage projectStageThisTxn = projectStages.get(stateOrderNo);
        if("1".equals(projectStageThisTxn.getLockFlag())){
            LogWriter.info(this, "当前阶段已被其他线程更新，稍后重试");
            return 0;
        }
        int lockResult = projectStageMapper.updateLock(projectStageThisTxn.getMchntCd(), projectStageThisTxn.getProjectNo(), projectStageThisTxn.getStageNo());
        if (lockResult == 0){
            LogWriter.info(this, "当前阶段已被其他线程更新，稍后重试");
            return 0;
        }
        projectStageThisTxn.setOldRefundAmt(projectStageThisTxn.getRefundAmt());
        //累计退款金额
        BigDecimal refundAmt = projectStageThisTxn.getRefundAmt().add(txnLogDB.getOrderAmt());
        LogWriter.info(this, String.format("累计退款金额: %s", refundAmt.toPlainString()));
        projectStageThisTxn.setRefundAmt(refundAmt);

        projectStageMapper.updateStageForRefund(projectStageThisTxn);
        return 1;
    }

}
