package com.fuiou.dips.valid;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * @program: dips
 * @description:
 * @author: Joker
 * @create: 2025/05/06 21:57
 */ // 2. 实现校验逻辑
public class PhoneValidator implements ConstraintValidator<ValidPhone, String> {
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    @Override
    public void initialize(ValidPhone validPhone) {
//        super.initialize()
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtils.isBlank(value))
        {return true;}
        return value != null && PHONE_PATTERN.matcher(value).matches();
    }
}
