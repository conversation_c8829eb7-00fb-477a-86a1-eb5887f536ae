package com.fuiou.dips.data;

import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.SignTypeEnum;
import com.fuiou.dips.services.SignService;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.StringUtil;
import com.fuiou.fsp.soa.data.MchntKeyData;
import com.fuiou.sk.service.FuiouSkServiceWrapper;

/**
 * <AUTHOR>
 * @Description 响应工厂类
 * @Date 2025/4/3 12:36
 **/
public class ResponseEntityFactory {

    public static <T> ResponseEntity<T> generatorResult(String code, String msg, T data) {
        return new ResponseEntity<T>(code, msg,data);
    }

    public static <T> ResponseEntity<T> generatorResult(String code, String msg) {
        return new ResponseEntity<T>(code, msg);
    }

    public static <T> ResponseEntity<T> generatorResult(ResponseCodeEnum responseCodeEnum) {
        return new ResponseEntity<T>(responseCodeEnum.getCode(), responseCodeEnum.getMsg());
    }

    public static <T> ResponseEntity<T> generatorResult(ResponseCodeEnum responseCodeEnum, T data) {
        ResponseEntity<T> responseEntity = new ResponseEntity<T>(responseCodeEnum.getCode(), responseCodeEnum.getMsg());
        responseEntity.setData(data);
        return responseEntity;
    }


    public static <T> ResponseEntity<T> ok() {
        return generatorResult(ResponseCodeEnum.SUCCESS,null);
    }

    public static <T> ResponseEntity<T> ok(T data) {
        return generatorResult(ResponseCodeEnum.SUCCESS, data);
    }

    public static <T> ResponseEntity<T> fail(String code, String msg) {
        return generatorResult(code, msg, null);
    }

    public static <T>  ResponseEntity<T> fail(ResponseCodeEnum responseCodeEnum) {
        return generatorResult(responseCodeEnum);
    }

    public static <T> ResponseEntity<T> fail() {
        return generatorResult(ResponseCodeEnum.EXCEPTION);
    }

    public static <T> ResponseEntity<T> ok(T data, SignTypeEnum signTypeEnum) {
        ResponseEntity<T> responseEntity = generatorResult(ResponseCodeEnum.SUCCESS, data);
        sign(signTypeEnum, responseEntity);
        return responseEntity;
    }

    public static  <T> ResponseEntity<T> fail(ResponseCodeEnum responseCodeEnum, SignTypeEnum signTypeEnum) {
        ResponseEntity<T> responseEntity = generatorResult(responseCodeEnum);
        sign(signTypeEnum, responseEntity);
        return responseEntity;
    }


    private static <T> void sign(SignTypeEnum signTypeEnum, ResponseEntity<T> responseEntity) {
        responseEntity.setRandomStr(StringUtil.random(10));
        responseEntity.setSignType(signTypeEnum.getCode());
        MchntKeyData mchntKeyData = FuiouSkServiceWrapper.getCachedMchntKey(SignService.TPAY_INS_CD);
        switch (signTypeEnum) {
            case RSA: {
                LogWriter.info(String.format("商户rsa密钥信息为:rsaKey=%s", mchntKeyData.getRsaFuiouPriKey()));
                responseEntity.signRsa(mchntKeyData.getRsaFuiouPriKey());
                break;
            }
            case MD5:
            default: {
                LogWriter.info(String.format("商户md5密钥信息为:md5key=%s", mchntKeyData.getMd5Key()));
                responseEntity.signMd5(mchntKeyData.getMd5Key());
                break;
            }
        }
    }


    public static<T> ResponseEntity<T>  ok(SignTypeEnum signTypeEnum) {
        ResponseEntity<T> responseEntity = generatorResult(ResponseCodeEnum.SUCCESS);
        sign(signTypeEnum, responseEntity);
        return responseEntity;
    }

    public static <T> ResponseEntity<T> fail(SignTypeEnum signTypeEnum) {
        ResponseEntity<T> responseEntity = generatorResult(ResponseCodeEnum.EXCEPTION);
        sign(signTypeEnum, responseEntity);
        return responseEntity;
    }
}
