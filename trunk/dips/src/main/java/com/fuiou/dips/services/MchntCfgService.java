package com.fuiou.dips.services;

import com.fuiou.dips.enums.OpenStatusEnum;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.MchntCfg;
import com.fuiou.dips.persist.dipsdb.MchntCfgMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 商户配置信息
 *
 * <AUTHOR>
 */
@Service
public class MchntCfgService {

    @Resource
    private MchntCfgMapper mchntCfgMapper;

    @LogAnnotation(value = "商户配置",
                   methodName = "获取商户配置信息")
    public MchntCfg getDetailByMchntCd(String mchntCd) {
        return mchntCfgMapper.getDetailByMchntCd(mchntCd, OpenStatusEnum.OPENED.getState());
    }
}