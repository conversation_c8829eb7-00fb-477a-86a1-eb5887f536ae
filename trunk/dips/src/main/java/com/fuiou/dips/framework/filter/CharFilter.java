package com.fuiou.dips.framework.filter;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.NetworkUtils;
import org.apache.log4j.Logger;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.UUID;

/**
 * 非法字符过滤器
 * 1.所有非法字符配置在web.xml中，如需添加新字符，请自行配置
 * 2.请注意请求与相应时的编码格式设置，否则遇到中文时，会出现乱码(GBK与其子集应该没问题)
 *
 * <AUTHOR>
 */
public class CharFilter implements Filter {

    private Logger log = Logger.getLogger(CharFilter.class);
    private String encoding;
    private String[] legalNames;
    private String[] illegalChars;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        encoding = filterConfig.getInitParameter("encoding");
        legalNames = filterConfig.getInitParameter("legalNames").split(",");
        illegalChars = filterConfig.getInitParameter("illegalChars").split(",");

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        try {
            MDC.put("uuid", UUID.randomUUID().toString().replaceAll("-", ""));


            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse res = (HttpServletResponse) response;

            //必须手动指定编码格式
            req.setCharacterEncoding(encoding);
            String tempURL = req.getRequestURI();
            MDC.put("reqUri", tempURL);
            LogWriter.info(String.format("reqUri=%s;ip=%s;remoteAdd=%s", tempURL, NetworkUtils.getIpAddressWhenEmptyReturnDefalutIp(req), req.getRemoteAddr()));

//        log.info(tempURL);  
            Enumeration params = req.getParameterNames();

            //是否执行过滤  true：执行过滤  false：不执行过滤
            boolean executable = true;

            //非法状态  true：非法  false；不非法
            boolean illegalStatus = false;
            String illegalChar = "";
            //对参数名与参数进行判断
            w:
            while (params.hasMoreElements()) {

                String paramName = (String) params.nextElement();

                executable = true;

                //密码不过滤
                if (paramName.toLowerCase().contains("password")) {
                    executable = false;
                } else {
                    //检查提交参数的名字，是否合法，即不过滤其提交的值
                    f:
                    for (int i = 0; i < legalNames.length; i++) {
                        if (legalNames[i].equals(paramName)) {
                            executable = false;
                            break f;
                        }
                    }
                }
            /*if(executable){  
                String[] paramValues = req.getParameterValues(paramName);  
                  
                f1:for(int i=0;i<paramValues.length;i++){  
                      
                    String paramValue = paramValues[i];  
                      
                    f2:for(int j=0;j<illegalChars.length;j++){  
                          
                        illegalChar = illegalChars[j];  
                          
                        if(paramValue.indexOf(illegalChar) != -1){  
                            illegalStatus = true;//非法状态  
                            break f2;  
                        }  
                    }  
                      
                    if(illegalStatus){  
                        break f1;  
                    }  
                      
                } 
            }  */

                if (illegalStatus) {
                    break w;
                }
            }
            //对URL进行判断
            for (int j = 0; j < illegalChars.length; j++) {

                illegalChar = illegalChars[j];

                if (tempURL.indexOf(illegalChar) != -1) {
                    illegalStatus = true;//非法状态
                    break;
                }
            }
            if (illegalStatus) {
                //必须手动指定编码格式
                res.setContentType("text/html;charset=" + encoding);
                res.setCharacterEncoding(encoding);
                res.getWriter().print("<script>window.alert('当前链接中存在非法字符');window.history.go(-1);</script>");
            } else {
                chain.doFilter(request, response);
            }
        } finally {
            MDC.clear();
            LoginConstat.clear();
        }
    }

    @Override
    public void destroy() {
        encoding = null;
        legalNames = null;
        illegalChars = null;

    }

}
