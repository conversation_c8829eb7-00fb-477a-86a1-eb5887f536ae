package com.fuiou.dips.utils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;



public class TrimDataUtil {

	private static final String STRING_GET = "get";
	private static final String STRING_SET = "set";
	private static final String STRING_GETCLASS = "getClass";

	private static boolean isTrue(String string, String mark) {
		if (string.length() <= 3||!mark.equals(string.substring(0, 3)))
			return false;
		return true;
	}

	public static Object invoke(Object object) throws Exception {
		@SuppressWarnings("rawtypes")
		Class clazz = object.getClass();
		Method m1[] = clazz.getMethods();
		List<Method> listMethod = Arrays.asList(m1);
		List<Method> getMethod = new ArrayList<Method>();
		List<Method> setMethod = new ArrayList<Method>();

		for (Method method : listMethod) {
			String methodName = method.getName();
			if (isTrue(methodName, STRING_GET) && !STRING_GETCLASS.equals(methodName))
				getMethod.add(method);
			if (isTrue(methodName, STRING_SET))
				setMethod.add(method);
		}

		for (Method method : getMethod) {
			Object msg = method.invoke(object);
			String name = method.getName();
			String subname = name.substring(3);
			String getString = msg == null? "" : msg.toString().trim();// 数据是否为空转化
			for (Method method2 : setMethod) {
				if ((STRING_SET + subname).equals(method2.getName())) {
					method2.invoke(object, getString);
//					LogWriter.info(String.format("%s-------->%s", subname, getString));
				}
			}
		}
		return object;

	}
	
	public static Object invokeTrimString(Object object) throws Exception {
		@SuppressWarnings("rawtypes")
		Class clazz = object.getClass();
		Method m1[] = clazz.getMethods();
		List<Method> listMethod = Arrays.asList(m1);
		List<Method> getMethod = new ArrayList<Method>();
		List<Method> setMethod = new ArrayList<Method>();

		for (Method method : listMethod) {
			String methodName = method.getName();
			if (isTrue(methodName, STRING_GET) && !STRING_GETCLASS.equals(methodName))
				getMethod.add(method);
			if (isTrue(methodName, STRING_SET))
				setMethod.add(method);
		}

		for (Method method : getMethod) {
			Object msg = method.invoke(object);
			if(msg instanceof String){
				String name = method.getName();
				String subname = name.substring(3);
				String getString = msg == null? "" : msg.toString().trim();// 数据是否为空转化
				for (Method method2 : setMethod) {
					if ((STRING_SET + subname).equals(method2.getName())) {
						method2.invoke(object, getString);
//						LogWriter.info(String.format("%s-------->%s", subname, getString));
					}
				}
			}
		}
		return object;

	}
	
}
