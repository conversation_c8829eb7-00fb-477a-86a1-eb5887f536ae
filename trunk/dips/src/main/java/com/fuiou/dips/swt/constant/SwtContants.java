package com.fuiou.dips.swt.constant;

import com.fuiou.dips.utils.ConfigReader;

/**
 * <AUTHOR>
 * @Description SWT相关常量
 * @Date 2020/11/19 10:15
 **/
public class SwtContants {


    public static final String secretInsKey = ConfigReader.getConfig("secretInsKey");


    public static final String WEB_BUSICHNL_CD = "33";
    public static final String CHNLORDERID_TAG = "chnlOrderId";
    public static final String SRC_INS_CD_SWT = "08P0000058";



    /**
     * 交易业务类型常量
     *
     * <AUTHOR>
     */
    public static final class BUSI_CONSTS {
        public static final String CASH_TRADE_BUSI_CD = "TX86";     // 订单刷新
    }

    // kbps配置
    public static String kbpsIp = ConfigReader.getConfig("kbpsIp");
    public static int kbpsPort = Integer.valueOf(ConfigReader.getConfig("kbpsPort"));
    public static int kbpsTxnTimeout = Integer.valueOf(ConfigReader.getConfig("kbpsTxnTimeout"));





}
