package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.DipsMsgUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DipsMsgUserMapper {
    int deleteByPrimaryKey(Long rowId);

    int insertSelective(DipsMsgUser record);
    /**
     * 根据 loginId、userType 和 msgNo 更新消息状态为已读
     */
    int updateReadFlag(@Param("loginId") String loginId,
                       @Param("userType") String userType,
                       @Param("msgNo") String msgNo);

    /**
     * 批量插入项目成员
     */
    int batchInsert(@Param("list") List<DipsMsgUser> dipsMsgUsers);

    DipsMsgUser selectByPrimaryKey(Long rowId);

    int updateByPrimaryKeySelective(DipsMsgUser record);

}