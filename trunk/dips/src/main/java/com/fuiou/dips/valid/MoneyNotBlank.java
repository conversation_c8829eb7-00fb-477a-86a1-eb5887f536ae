package com.fuiou.dips.valid;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.FIELD, ElementType.METHOD})  
@Retention(RetentionPolicy.RUNTIME)  
@Constraint(validatedBy=MoneyNotBlankValidator.class)  
public @interface MoneyNotBlank {

	int max() default Integer.MAX_VALUE;

	String message() default "无效金额";  
    
    Class<?>[] groups() default {};  
     
    Class<? extends Payload>[] payload() default {};  
}
