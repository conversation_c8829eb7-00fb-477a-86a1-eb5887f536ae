package com.fuiou.dips.framework.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fuiou.dips.framework.annotation.LongParseField;
import com.fuiou.dips.framework.annotation.NotNullField;
import com.fuiou.dips.framework.annotation.StopAnalyzeParseAnnotation;
import com.fuiou.dips.framework.annotation.StringLengthField;

import java.io.Serializable;

/**
 * 待处理导入
 *
 * <AUTHOR>
 */
public class ProjectStageDataImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNullField(index = 8,
                  msg = "阶段顺序不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @LongParseField(index = 8,
                    msg = "阶段顺序转换Long类型失败")
    @StopAnalyzeParseAnnotation(index = 8,
                                msg = "阶段顺序列为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @ExcelProperty(value = "阶段顺序",
                   index = 8)
    private Integer stageOrder;      // 阶段顺序

    @NotNullField(index = 9,
                  msg = "阶段名称不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @StringLengthField(index = 9,
                       msg = "阶段名称长度不能超过100个字符",
                       length = 100,
                       zhLength = 100)
    @StopAnalyzeParseAnnotation(index = 9,
                                msg = "阶段名称列为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @ExcelProperty(value = "阶段名称",
                   index = 9)
    private String stageName;        // 阶段名称

    @LongParseField(index = 10,
                    msg = "阶段应收金额转换Long类型失败")
    @NotNullField(index = 10,
                  msg = "阶段应收金额不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @StopAnalyzeParseAnnotation(index = 10,
                                msg = "阶段应收金额列为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @ExcelProperty(value = "阶段应收金额",
                   index = 10)
    private Long stageAmt;           // 阶段应收金额(分)

    @LongParseField(index = 11,
                    msg = "阶段实收金额转换Long类型失败")
    @NotNullField(index = 11,
                  msg = "阶段实收金额不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @ExcelProperty(value = "阶段实收金额",
                   index = 11)
    private Long stageActualAmt;     // 阶段实收金额(分)

    @NotNullField(index = 12,
                  msg = "阶段状态不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @StringLengthField(index = 12,
                       msg = "阶段状态长度不能超过10个字符",
                       length = 10,
                       zhLength = 10)
    @ExcelProperty(value = "阶段状态",
                   index = 12)
    private String stageSt;          // 阶段状态

    public Integer getStageOrder() {
        return stageOrder;
    }

    public void setStageOrder(Integer stageOrder) {
        this.stageOrder = stageOrder;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public Long getStageAmt() {
        return stageAmt;
    }

    public void setStageAmt(Long stageAmt) {
        this.stageAmt = stageAmt;
    }

    public Long getStageActualAmt() {
        return stageActualAmt;
    }

    public void setStageActualAmt(Long stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public String getStageSt() {
        return stageSt;
    }

    public void setStageSt(String stageSt) {
        this.stageSt = stageSt;
    }
}
