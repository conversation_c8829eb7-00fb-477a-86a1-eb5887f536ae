package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.DipsCustomerFollow;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DipsCustomerFollowMapper {
    int deleteByPrimaryKey(@Param("rowId") Long rowId,@Param("mchntCd") String mchntCd);

    int insert(DipsCustomerFollow record);

    int insertSelective(DipsCustomerFollow record);

    DipsCustomerFollow selectByPrimaryKey(Long rowId);

    int updateByPrimaryKeySelective(DipsCustomerFollow record);

    int updateByPrimaryKey(DipsCustomerFollow record);

    /**
     * 根据商户号和手机号查询客户跟进记录
     */
    List<DipsCustomerFollow> selectByMchntCdAndPhone(
            @Param("mchntCd") String mchntCd,
            @Param("phone") String phone);

    /**
     * 根据商户编号和旧手机号更新新的手机号
     *
     * @param newPhone  新手机号
     * @param mchntCd   商户编号
     * @param oldPhone  旧手机号
     * @return 受影响行数（即是否成功更新）
     */
    int updatePhoneByMchntCdAndOldPhone(@Param("newPhone") String newPhone,
                                        @Param("mchntCd") String mchntCd,
                                        @Param("oldPhone") String oldPhone);
}
