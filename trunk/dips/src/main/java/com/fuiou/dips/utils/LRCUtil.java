package com.fuiou.dips.utils;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.enums.OrderNumPreEnum;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

public class LRCUtil {

    /***
     * @Description: 获取字符串校验值
     * @param srcStr : 待计算校验码的源字符串
     * @return:
     * @Author: 程军
     * @Date: 2024/2/20 14:48
     */
    public static int calculateLRCCheckNum(String srcStr,String ... otherStr) throws UnsupportedEncodingException {
        LogWriter.info(String.format("获取字符串校验值begin,srcStr=%s", srcStr));
        String LRCSrcStr = String.format("%s%s%s", srcStr, Constant.ORDER_NO_KEY, StringUtils.join(otherStr,""));
        LogWriter.info(String.format("实际获取字符串校验值计算,LRCSrcStr=%s", LRCSrcStr));
        int result = calculateLRCCheckNum(LRCSrcStr.getBytes("UTF-8"));
        LogWriter.info(String.format("获取字符串校验值end,result=%s", result));
        return result;
    }

    /***
     * @Description: 获取字符串校验值,限制最大返回长度2位,超过2位截取最后2位，不足左边补0
     * @param srcStr : 待计算校验码的源字符串
     * @return:
     * @Author: 程军
     * @Date: 2024/2/20 14:48
     */
    public static String calculateLRCCheckStr(String srcStr,String ... otherStr) throws UnsupportedEncodingException {
        int LRCCode = calculateLRCCheckNum(srcStr,otherStr);
        String LRCCodeStr = String.valueOf(LRCCode);
        String result = null;
        if (StringUtil.isEmpty(LRCCodeStr)) {
            LogWriter.info(String.format("获取字符串校验值结果为空,返回2位0,LRCCodeStr=%s", LRCCodeStr));
            result = StringUtil.leftPadWithZero("", 2);
            LogWriter.info(String.format("字符串校验值结果,限制最大返回长度2位,result=%s", result));
            return result;
        }
        if (LRCCodeStr.length() > 2) {
            LogWriter.info(String.format("获取字符串校验值结果超过2位,返回后2位的值,LRCCodeStr=%s", LRCCodeStr));
            result = LRCCodeStr.substring(LRCCodeStr.length()-2);
            LogWriter.info(String.format("字符串校验值结果,限制最大返回长度2位,result=%s", result));
            return result;

        }
        if (LRCCodeStr.length() < 2) {
            LogWriter.info(String.format("获取字符串校验值结果不足2位,左边补齐2位,LRCCodeStr=%s", LRCCodeStr));
            result = StringUtil.leftPadWithZero(LRCCodeStr, 2);
            LogWriter.info(String.format("字符串校验值结果,限制最大返回长度2位,result=%s", result));
            return result;
        }
        LogWriter.info(String.format("字符串校验值结果,限制最大返回长度2位,result=%s", LRCCodeStr));
        return LRCCodeStr;
    }

    /***
     * @Description: 获取字符串校验值
     * @param bytes :
     * @return:
     * @Author: 程军
     * @Date: 2024/2/20 14:48
     */
    private static int calculateLRCCheckNum(byte[] bytes) {

        int checksum = 0; // 初始化校验值

        for (byte b : bytes) {
            checksum ^= b & 0xFF; // 对每个字节与0xFF按位异或运算得到新的校验值
        }

        return checksum;
    }


    /***
     * @Description: 增加校验码
     * @param srcStr : 待增加校验码的源字符串
     * @param LRCCodeIndexStart :校验码起始位置
     * @param LRCCodeIndexEnd :校验码结束位置
     * @return:
     * @Author: 程军
     * @Date: 2024/2/20 14:48
     */
    public static String calculateLRCCheck(String srcStr,int LRCCodeIndexStart,int LRCCodeIndexEnd,String otherStr) {
        LogWriter.info(String.format("增加校验码begin,srcStr=%s,LRCCodeIndexStart=%s,LRCCodeIndexEnd=%s,otherStr=%s", srcStr,LRCCodeIndexStart,LRCCodeIndexEnd, JSONObject.toJSONString(otherStr)));


        try {
            if (StringUtil.isEmpty(srcStr) || srcStr.length() < LRCCodeIndexEnd) {
                LogWriter.info(String.format("增加校验码end,源字符串不支持增加校验码"));
                return srcStr;
            }
            //计算校验码
            String LRCCode = calculateLRCCheckStr(srcStr,otherStr);
            String snonew = String.format("%s%s%s", srcStr.substring(0, LRCCodeIndexStart), LRCCode, srcStr.substring(LRCCodeIndexEnd));
            LogWriter.info(String.format("增加校验码end,result=%s", snonew));

            return snonew;
        } catch (Exception e) {
            LogWriter.error(String.format("增加校验码Exception"), e);
        }

        LogWriter.info(String.format("增加校验码end,result=%s", srcStr));
        return srcStr;
    }

    /**
    * @Description:  校验单号校验码是否合法
    * @param LRCCodeOrderNo :
    * @param LRCCodeIndexStart :校验码起始位置
    * @param LRCCodeIndexEnd :校验码结束位置
    * @return:
    * @Author: 程军
    * @Date: 2024/2/20 15:39
    */
    public static boolean validLRCCode(String LRCCodeOrderNo,int LRCCodeIndexStart,int LRCCodeIndexEnd,String ... otherStr) {
        LogWriter.info(String.format("校验单号校验码是否合法begin,srcStr=%s", LRCCodeOrderNo));


        try {
            if (StringUtil.isEmpty(LRCCodeOrderNo) || LRCCodeOrderNo.length() < LRCCodeIndexEnd) {
                LogWriter.info(String.format("校验单号校验码是否合法end,源字符串不支持增加校验码"));
                return false;
            }
            //截取单号中包含的校验码
            String orderNoLRCCode =  LRCCodeOrderNo.substring(LRCCodeIndexStart, LRCCodeIndexEnd);
            //还原单号
            String orgiOrderNo = String.format("%s%s%s", LRCCodeOrderNo.substring(0, LRCCodeIndexStart), getCurrYearTop2Char(), LRCCodeOrderNo.substring(LRCCodeIndexEnd));

            //计算校验码
            String orgiOrderNoLRCCode = calculateLRCCheckStr(orgiOrderNo,otherStr);
            boolean reslut=orderNoLRCCode.equalsIgnoreCase(orgiOrderNoLRCCode);
            LogWriter.info(String.format("校验单号校验码是否合法end,result=%s,还原单号后计算校验码:%s,单号中截取的校验码:%s", reslut,orgiOrderNoLRCCode,orderNoLRCCode));

            return reslut;
        } catch (Exception e) {
            LogWriter.error(String.format("校验单号校验码是否合法Exception"), e);
        }

        LogWriter.info(String.format("校验单号校验码是否合法end,result=%s", LRCCodeOrderNo));
        return false;
    }

    /**
    * @Description: 获取当前年份前2位，例如2023年，获取20
    * @return:
    * @Author: 程军
    * @Date: 2024/2/27 10:19
    */
    private static String getCurrYearTop2Char()
    {
        return DateFormatUtils.format("yyyy").substring(0,2);
    }

    public static void main(String[] args) {
        String snoPre = String.format("%s%s", OrderNumPreEnum.ORDER_COMMON.getOrderNoPrefix(), "61238456");

        String userIp="**************";
        String sno = String.format("%s%s", snoPre, StringUtil.random(10));
        LogWriter.info("原流水号：" + sno);
        LogWriter.info("原流水号长度：" + sno.length());
        String snonew = calculateLRCCheck(sno,snoPre.length(),snoPre.length()+2,userIp);
        LogWriter.info("新流水号：" + snonew);
        LogWriter.info("新流水号长度：" + snonew.length());
        LogWriter.info("***************");
        LogWriter.info("校验结果：" +validLRCCode(snonew,snoPre.length(),snoPre.length()+2,userIp) );
        LogWriter.info("***************");
        LogWriter.info("拼接11校验结果：" +validLRCCode(snonew+"11",snoPre.length(),snoPre.length()+2,userIp) );
        LogWriter.info("***************");
        LogWriter.info("拼接12校验结果：" +validLRCCode(snonew+"12",snoPre.length(),snoPre.length()+2,userIp) );


        String sno1="*********20240320144329963001";
        String snoPre1="*********";
        String snonew1 = calculateLRCCheck(sno1,snoPre1.length(),snoPre1.length()+2,userIp);
        LogWriter.info("新流水号：" + snonew1);


        List<String> validResult=new ArrayList<>(orderNoList.size());
        for(OrderNoValidInfo entry: orderNoList)
        {
            validResult.add(String.format("订单号:%s,校验位前缀部分:%s,用户ip:%s,校验结果：%s",entry.getOrderNo(),
                    entry.getOrderNoPrefix(), entry.getUserIp(),validLRCCode(entry.getOrderNo(),entry.getOrderNoPrefix().length(),entry.getOrderNoPrefix().length()+2, entry.getUserIp()) ));

        }
          LogWriter.info("订单校验结果******************************");

        for(String str:validResult)
        {
          LogWriter.info(str);

        }



    }

    private static final List<OrderNoValidInfo> orderNoList =new ArrayList<>();
    static {
        orderNoList.add(new OrderNoValidInfo("*********54240430110815436001","*********","**************"));
//         orderNoList.add(new OrderNoValidInfo("*********52240320153040081001","*********","**************"));
//         orderNoList.add(new OrderNoValidInfo("*********58240320153821923001","*********","**************"));
//         orderNoList.add(new OrderNoValidInfo("*********32240320154604607001","*********","**************"));
//         orderNoList.add(new OrderNoValidInfo("*********55240320154909405001","*********","**************"));

    }

    private static class OrderNoValidInfo implements Serializable {

        private static final long serialVersionUID = 319547718632648941L;

        private String orderNo;
        private String orderNoPrefix;
        private String userIp;

        public OrderNoValidInfo(String orderNo, String orderNoPrefix, String userIp) {
            this.orderNo = orderNo;
            this.orderNoPrefix = orderNoPrefix;
            this.userIp = userIp;
        }

        public OrderNoValidInfo() {
        }

        public String getOrderNo() {
            return orderNo == null ? null : orderNo.trim();
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getOrderNoPrefix() {
            return orderNoPrefix == null ? null : orderNoPrefix.trim();
        }

        public void setOrderNoPrefix(String orderNoPrefix) {
            this.orderNoPrefix = orderNoPrefix;
        }

        public String getUserIp() {
            return userIp == null ? null : userIp.trim();
        }

        public void setUserIp(String userIp) {
            this.userIp = userIp;
        }
    }


}
