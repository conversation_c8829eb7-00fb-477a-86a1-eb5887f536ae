package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.data.entity.MessageNotificationBean;
import com.fuiou.dips.persist.beans.DipsMsgInf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DipsMsgInfMapper {
    int deleteByPrimaryKey(Long rowId);

    int insertSelective(DipsMsgInf record);

    DipsMsgInf selectByPrimaryKey(Long rowId);

    int updateByPrimaryKeySelective(DipsMsgInf record);

    List<MessageNotificationBean> selectMsgList(@Param("loginId") String loginId, @Param("userType") String userType,
             @Param("mchntCd") String mchntCd,@Param("readFlag") String readFlag);

}