package com.fuiou.dips.utils;

import com.fuiou.dips.data.resp.PageRespBase;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义分页工具类
 * 适用于非本地数据库的查询结果分页
 *
 * <AUTHOR>
 */
public class CustomPageUtil {

    /**
     * 对列表数据进行手动分页
     *
     * @param list  需要分页的数据列表
     * @param page  当前页码，从1开始
     * @param limit 每页记录数
     * @param <T>   数据类型
     * @return 分页结果
     */
    public static <T> PageRespBase<T> manualPaging(List<T> list, int page, int limit) {
        if (list == null || list.isEmpty()) {
            return new PageRespBase<>(0, new ArrayList<>());
        }
        long total = list.size();
        int fromIndex = (page - 1) * limit;
        int toIndex = Math.min(fromIndex + limit, list.size());
        // 防止索引越界
        if (fromIndex >= list.size()) {
            return new PageRespBase<>(total, new ArrayList<>());
        }
        List<T> pageData = list.subList(fromIndex, toIndex);
        return new PageRespBase<>(total, pageData);
    }
}
