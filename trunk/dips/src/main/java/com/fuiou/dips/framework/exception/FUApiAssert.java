package com.fuiou.dips.framework.exception;

import cn.hutool.core.util.ObjectUtil;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.utils.CollectionUtils;
import com.fuiou.dips.utils.StringUtil;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;


/**
 * API 断言
 *
 * <AUTHOR>
 */
public class FUApiAssert {

    private FUApiAssert() {
    }

    public static void equals(ResponseCodeEnum responseEnum, Object obj1, Object obj2) {
        if (!Objects.equals(obj1, obj2)) {
            failure(responseEnum);
        }
    }

    public static void equals(String errorCode, String msg, Object obj1, Object obj2) {
        if (!Objects.equals(obj1, obj2)) {
            failure(errorCode, msg);
        }
    }

    public static void isTrue(ResponseCodeEnum responseEnum, boolean condition) {
        if (!condition) {
            failure(responseEnum);
        }
    }

    public static void isTrue(String errorCode, String msg, boolean condition) {
        if (!condition) {
            failure(errorCode, msg);
        }
    }

    public static void isFalse(ResponseCodeEnum responseEnum, boolean condition) {
        if (condition) {
            failure(responseEnum);
        }
    }

    public static void isFalse(String errorCode, String msg, boolean condition) {
        if (condition) {
            failure(errorCode, msg);
        }
    }

    public static void isBlank(ResponseCodeEnum responseEnum, String condition) {
        if (StringUtil.isNotBlank(condition)) {
            failure(responseEnum);
        }
    }

    public static void isBlank(String errorCode, String msg, String condition) {
        if (StringUtil.isNotBlank(condition)) {
            failure(errorCode, msg);
        }
    }

    public static void isNotBlank(ResponseCodeEnum responseEnum, String condition) {
        if (StringUtil.isBlank(condition)) {
            failure(responseEnum);
        }
    }

    public static void isNotBlank(String errorCode, String msg, String condition) {
        if (StringUtil.isBlank(condition)) {
            failure(errorCode, msg);
        }
    }

    public static void isNull(ResponseCodeEnum responseEnum, Object... conditions) {
        if (!ObjectUtil.hasNull(conditions)) {
            failure(responseEnum);
        }
    }

    public static void isNull(String errorCode, String msg, Object... conditions) {
        if (!ObjectUtil.hasNull(conditions)) {
            failure(errorCode, msg);
        }
    }

    public static void notNull(ResponseCodeEnum responseEnum, Object... conditions) {
        if (ObjectUtil.hasNull(conditions)) {
            failure(responseEnum);
        }
    }

    public static void notNull(String errorCode, String msg, Object... conditions) {
        if (ObjectUtil.hasNull(conditions)) {
            failure(errorCode, msg);
        }
    }

    public static void failure(ResponseCodeEnum responseEnum) {
        throw new FUException(responseEnum);
    }

    public static void failure(String errorCode, String msg) {
        throw new FUException(errorCode, msg);
    }

    public static void noNullElements(ResponseCodeEnum responseEnum, Object[] array) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    failure(responseEnum);
                }
            }
        }
    }

    public static void notEmpty(ResponseCodeEnum responseEnum, Collection<?> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            failure(responseEnum);
        }
    }

    public static void notEmpty(ResponseCodeEnum responseEnum, Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            failure(responseEnum);
        }
    }

    public static void isEmpty(ResponseCodeEnum responseEnum, Collection<?> collection) {
        if (CollectionUtils.isNotEmpty(collection)) {
            failure(responseEnum);
        }
    }

    public static void isEmpty(ResponseCodeEnum responseEnum, Map<?, ?> map) {
        if (map != null && !map.isEmpty()) {
            failure(responseEnum);
        }
    }

}
