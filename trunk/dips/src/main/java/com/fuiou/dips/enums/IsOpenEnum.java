package com.fuiou.dips.enums;

/***
 * <AUTHOR>
 * @Date 2021/4/20 15:11
 * @Description yes or no 枚举
**/
public enum IsOpenEnum {

    YES("1","是"),
    NO("0","否")
    ;


    private String code;

    private String name;

    IsOpenEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static final String valueByCode(String code) {
        for (IsOpenEnum enu : IsOpenEnum.values()) {
            if (enu.getCode().equals(code)) {
                return enu.getName();
            }
        }
        return "";
    }


    @Override
    public String toString() {
        return this.getName();
    }
}
