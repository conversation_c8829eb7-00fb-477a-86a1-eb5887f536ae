package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.QrcodeInf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QrcodeInfMapper {

    // 插入记录
    int insert(QrcodeInf qrcodeInf);

    // 根据ID查询记录
    List<QrcodeInf> select(QrcodeInf qrcodeInf);

    // 更新记录
    int update(QrcodeInf qrcodeInf);

    // 查询所有记录
    List<QrcodeInf> selectAll(QrcodeInf qrcodeInf);

    QrcodeInf queryOrder(@Param("mchntCd") String mchntCd,@Param("orderNo") String orderNo,@Param("qrcodeType") String qrcodeType);

    /**
     *
     * 更新为已完成
     * @param mchntCd
     * @param orderNo
     * @return
     */
    int updateFinish(@Param("mchntCd") String mchntCd,@Param("orderNo") String orderNo);
}
