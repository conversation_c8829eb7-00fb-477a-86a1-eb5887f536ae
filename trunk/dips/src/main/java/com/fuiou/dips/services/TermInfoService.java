package com.fuiou.dips.services;

import cn.hutool.core.lang.id.IdConstants;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.fuiou.cacheCenter.term.TermAccessor;
import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dboffset.soa.data.TermDbData;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.MchntTermInf;
import com.fuiou.dips.persist.dipsdb.MchntTermInfMapper;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TermInfoService {

    private static final Logger log = LoggerFactory.getLogger(TermInfoService.class);
    @Autowired
    private CfgdbService cfgdbService;

    @Autowired
    private TermAccessor termAccessor;
    @Autowired
    private MchntTermInfMapper mchntTermInfMapper;

    /**
     * 获取终端详情
     * @param fyTermId
     * @return
     */
    public TermCacheData queryTermInfo(String fyTermId) {
        TermCacheData termData = null;
        try {
            termData = termAccessor.getTermByTmFuiouId(fyTermId.trim());
            if (termData == null) {
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            if (!"01" .equals(StringUtil.trimToEmpty(termData.getTmOpenState()))) {
                log.warn(String.format("通过富友终端号调用统一缓存 获取终端信息，终端状态非开通：%s", fyTermId));
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            LogWriter.info(this,String.format("通过富友终端号调用统一缓存 获取终端信息，result=%s", JsonUtil.bean2Json(termData)));
            return termData;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端号调用统一缓存 获取终端信息，发生异常"), e);
        }
        try {
            TermDbData termDbData = cfgdbService.getTermById(fyTermId);
            if (termDbData == null) {
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            if (!"01" .equals(StringUtil.trimToEmpty(termDbData.getTmOpenState()))) {
                log.warn(String.format("获取终端信息，终端状态非开通：%s", fyTermId));
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            termData = convertTermEntry(termDbData);
            return termData;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端号调用dboffset 获取终端信息，发生异常"), e);
            return null;
        }
    }

    /**
     * 根据终端序列号取商户号及终端号
     * @param serialNo
     * @return
     */
    public TermCacheData queryTermInfoBySN(String serialNo) {
        TermCacheData termData = null;
        try {
            termData = termAccessor.getTermByTmSerialNo(serialNo.trim());
            if (termData == null || !"01".equals(termData.getTmOpenState())) {
                throw new FUException(String.format("通过终端序列号调用统一缓存 获取终端信息，终端不存在：%s", serialNo));
            }
            LogWriter.info(this,String.format("通过终端序列号调用统一缓存 获取终端信息，result=%s", JsonUtil.bean2Json(termData)));
            return termData;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过终端序列号调用统一缓存 获取终端信息，发生异常"), e);
        }
        try {
            TermDbData termDbData = cfgdbService.getTermBySerialNo(serialNo);
            if (termDbData == null) {
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            if (!"01" .equals(StringUtil.trimToEmpty(termDbData.getTmOpenState()))) {
                log.warn(String.format("获取终端信息，终端状态非开通：%s", serialNo));
                throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
            }
            termData = convertTermEntry(termDbData);
            return termData;
        } catch(Exception e) {
            LogWriter.error(this, String.format("通过终端序列号调用dboffset 获取终端信息，发生异常"), e);
            return null;
        }
    }

    private TermCacheData convertTermEntry(TermDbData termDbData) throws Exception {
        if (!cfgdbService.validTermStatus(termDbData)) {
            LogWriter.info(this, "终端信息为空或终端开通状态不是已开通状态");
            return null;
        }
        TermCacheData result = new TermCacheData();
        org.springframework.beans.BeanUtils.copyProperties(termDbData,  result);
        result.setTmFuiouId(result.getTmFuiouId().trim());
        return result;
    }


    /**
     * 通过商户号+门店号获取终端详情
     * @param mchntCd,storeId
     * @return
     */
    public TermCacheData queryTermInfoByMchntCdAndStoreId(String mchntCd,String storeId) {
        TermCacheData termData = null;
        try {
            TermDbData termDbData = cfgdbService.getTermByMchntCdAndStoreId(mchntCd,storeId);
            if (termDbData == null) {
                LogWriter.info(this,String.format("通过商户号+门店号调用dboffset 获取终端信息，终端不存在，商户号：%s ;门店号：%s", mchntCd,storeId));
                return null;
            }
            termData = convertTermEntry(termDbData);
            LogWriter.info(this,String.format("通过商户号+门店号调用dboffset 获取终端信息，result=%s", JsonUtil.bean2Json(termData)));
            return termData;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端号调用dboffset 获取终端信息，发生异常"), e);
            return null;
        }
    }

    /**
     * 获取可下单终端号
     * @param mchntCd
     * @param storeId
     * @return
     */
    public TermCacheData getTermId(String mchntCd, String storeId){
        LogWriter.info("获取下单终端号，商户号："+mchntCd+"门店号："+storeId);
        MchntTermInf mchntTermInf = mchntTermInfMapper.selectByMchntAndStoreId(mchntCd,StringUtil.isEmpty(storeId) ?  Constant.STORE_ID : storeId);
        if(mchntTermInf != null){
            try {
                LogWriter.info("商户存在支付成功记录的终端号："+mchntTermInf.getTermId());
                TermCacheData termCacheData=this.queryTermInfo(mchntTermInf.getTermId());
                if(termCacheData == null){
                    LogWriter.info("商户存在支付成功记录的终端号已解绑："+mchntTermInf.getTermId());
                    throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
                }

                if(!Constant.SUPPORT_TERM_MODELS.contains(StringUtil.trimToEmpty(termCacheData.getTmModel()))){
                    LogWriter.info("终端类型不支持："+mchntTermInf.getTermId());
                    throw new FUException(ResponseCodeEnum.TERM_MODEL_NOT_SUPPORT);
                }
                if(!mchntCd.equals(termCacheData.getTmUserCd())){
                    LogWriter.info("终端所属商户与当前项目所属商户不一致：现商户号："+termCacheData.getTmUserCd());
                    throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
                }
                if(StringUtil.isNotEmpty(storeId) && !storeId.equals(termCacheData.getStoreId())){
                    LogWriter.info("终端所属门店与当前项目所属门店不一致：现门店号："+termCacheData.getTmUserCd());
                    throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
                }
                LogWriter.info("获取下单终端号成功："+mchntTermInf.getTermId());
                return termCacheData;
            } catch (FUException e) {
                LogWriter.info("从t_dips_mchnt_term_inf获取下单终端号失败："+e.getMessage());
            }
        }
        LogWriter.info("商户不存在支付成功记录的终端号：从终端表获取最新一条记录");
        TermCacheData termCacheData= this.queryTermInfoByMchntCdAndStoreId(mchntCd,storeId);
        if(termCacheData == null){
            LogWriter.info("没有可用终端...");
            throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
        }
        LogWriter.info("获取下单终端号成功："+termCacheData.getTmFuiouId());
        return termCacheData;

    }

    /**
     * 新增或修改商户最后一笔交易成功使用的终端信息
     * @param mchntTermInf
     * @return
     */
    public void insertOrUpdateMchntTermInf(MchntTermInf mchntTermInf) {
        try {
            LogWriter.info("新增或修改商户最后一笔交易成功使用的终端信息:"+ JSON.toJSONString(mchntTermInf));
            TermCacheData termCacheData =  queryTermInfo(mchntTermInf.getTermId());
            if(termCacheData == null){
                LogWriter.info("终端号不存在："+mchntTermInf.getTermId());
                return;
            }
            if(!StringUtil.trimToEmpty(mchntTermInf.getMchntCd()).equals(StringUtil.trimToEmpty(termCacheData.getTmUserCd()))){
                LogWriter.info("终端所属商户号不一致："+mchntTermInf.getMchntCd());
                return;
            }
            if(!StringUtil.trimToEmpty(mchntTermInf.getStoreId()).equals(StringUtil.trimToEmpty(termCacheData.getStoreId()))){
                LogWriter.info("终端所属门店号不一致："+mchntTermInf.getMchntCd());
                return;
            }
            //获取商户最后一笔交易成功终端记录 存在记录修改终端号和订单号  不存在则存一条记录
            MchntTermInf existingRecord = mchntTermInfMapper.selectByMchntAndStoreId(mchntTermInf.getMchntCd(),StringUtil.isEmpty(mchntTermInf.getStoreId()) ? Constant.STORE_ID : mchntTermInf.getStoreId());
            if(existingRecord != null){
                existingRecord.setStoreId(StringUtil.isEmpty(mchntTermInf.getStoreId()) ? Constant.STORE_ID : existingRecord.getStoreId());
                existingRecord.setTermId(mchntTermInf.getTermId());
                existingRecord.setOrderNo(mchntTermInf.getOrderNo());
                existingRecord.setTermModel(termCacheData.getTmModel());
                existingRecord.setTermSn(termCacheData.getTmSerialNo());
                existingRecord.setTermType(termCacheData.getTmType());
                existingRecord.setUpdateTime(new Date());
                mchntTermInfMapper.updateByRowId(existingRecord);
                LogWriter.info("更新商户最后一笔交易成功使用的终端信息成功");
                return;
            }
            MchntTermInf newRecord = getNewRecord(mchntTermInf, termCacheData);
            mchntTermInfMapper.insert(newRecord);
            LogWriter.info("新增商户最后一笔交易成功使用的终端信息成功");
        } catch (Exception e) {
            LogWriter.error("新增/修改商户最后一笔交易成功使用的终端信息发生异常：",e);;
        }
    }

    private static MchntTermInf getNewRecord(MchntTermInf mchntTermInf, TermCacheData termCacheData) {
        MchntTermInf newRecord = new MchntTermInf();
        newRecord.setMchntCd(mchntTermInf.getMchntCd());
        newRecord.setTermId(mchntTermInf.getTermId());
        newRecord.setOrderNo(mchntTermInf.getOrderNo());
        newRecord.setStoreId(StringUtil.isEmpty(mchntTermInf.getStoreId()) ? Constant.STORE_ID :mchntTermInf.getStoreId());
        newRecord.setTermModel(termCacheData.getTmModel());
        newRecord.setTermSn(termCacheData.getTmSerialNo());
        newRecord.setTermType(termCacheData.getTmType());
        newRecord.setTermModel(termCacheData.getTmModel());
        return newRecord;
    }

    public static void main(String[] args) {
        System.out.println(IdConstants.DEFAULT_WORKER_ID);
        System.out.println(IdConstants.DEFAULT_WORKER_ID);
        System.out.println(IdUtil.getWorkerId( IdUtil.getDataCenterId(31L), 31L));
        System.out.println(IdUtil.getWorkerId( IdUtil.getDataCenterId(31L), 31L));
    }
}
