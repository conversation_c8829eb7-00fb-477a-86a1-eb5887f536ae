package com.fuiou.dips.framework.exception;


import com.fuiou.dips.enums.ResponseCodeEnum;

/**
 * <AUTHOR>
 * 
 */
public class FUException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	private String errcode;
	private String errInfo;
	private Object object;

	/**
	 * 
	 */
	public FUException() {
	}

	/**
	 * 
	 * @param errcode
	 * @param errInfo
	 * @param throwable
	 */
	public FUException(String errcode, String errInfo, Throwable throwable) {
		super(throwable);
		this.errcode = errcode;
		this.errInfo = errInfo;
	}

	/**
	 *
	 * @param errInfo
	 * @param throwable
	 */
	public FUException(ResponseCodeEnum errInfo, Throwable throwable) {
		super(throwable);
		this.errcode = errInfo.getCode();
		this.errInfo = errInfo.getMsg();
	}


	/**
	 *
	 * @param errInfo
	 */
	public FUException(ResponseCodeEnum errInfo) {

		this.errcode = errInfo.getCode();
		this.errInfo = errInfo.getMsg();
	}
	/**
	 * 
	 * @param errcode
	 * @param errInfo
	 */
	public FUException(String errcode, String errInfo) {
		this.errcode = errcode;
		this.errInfo = errInfo;
	}
	
	public FUException(String errcode,String errInfo,Object object){
		this.errcode = errcode;
		this.errInfo = errInfo;
		this.object = object;
	}

	/**
	 */
	public FUException(String errInfo) {
		this.errInfo = errInfo;
	}

	/**
	 * @param arg0
	 */
	public FUException(Throwable arg0) {
		super(arg0);
	}

	/**
	 * @param arg0
	 * @param arg1
	 */
	public FUException(String arg0, Throwable arg1) {
		super(arg0, arg1);
	}

	/**
	 * @return the errcode
	 */
	public String getErrcode() {
		return errcode;
	}

	/**
	 * @return the errInfo
	 */
	public String getErrInfo() {
		return errInfo;
	}

	/**
	 * object
	 *
	 * @return  the object
	 * @since   CodingExample Ver(编码范例查看) 1.0
	 */
	
	public Object getObject() {
		return object;
	}
	

}
