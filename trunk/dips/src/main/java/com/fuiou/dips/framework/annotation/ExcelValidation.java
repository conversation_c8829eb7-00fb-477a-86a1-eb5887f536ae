package com.fuiou.dips.framework.annotation;

import java.lang.annotation.*;

/**
 * 统一的Excel字段校验注解
 * 通过校验类型枚举来区分不同的校验规则
 * 
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Inherited
@Repeatable(ExcelValidations.class)
public @interface ExcelValidation {
    
    /**
     * 校验类型
     */
    ExcelValidationType type();
    
    /**
     * 列索引
     */
    int index();
    
    /**
     * 错误消息
     */
    String message() default "";
    
    // ========== 字符串长度校验相关参数 ==========
    
    /**
     * 字符串最大长度（字符数）
     */
    int maxLength() default Integer.MAX_VALUE;
    
    /**
     * 字符串最小长度（字符数）
     */
    int minLength() default 0;
    
    /**
     * 中文字符最大长度（字节数）
     */
    int maxZhLength() default Integer.MAX_VALUE;
    
    // ========== 数值范围校验相关参数 ==========
    
    /**
     * 数值最小值
     */
    long minValue() default Long.MIN_VALUE;
    
    /**
     * 数值最大值
     */
    long maxValue() default Long.MAX_VALUE;
    
    /**
     * 双精度数值最小值
     */
    double minDoubleValue() default Double.MIN_VALUE;
    
    /**
     * 双精度数值最大值
     */
    double maxDoubleValue() default Double.MAX_VALUE;
    
    // ========== 正则表达式校验相关参数 ==========
    
    /**
     * 正则表达式模式
     */
    String pattern() default "";
    
    // ========== 枚举值校验相关参数 ==========
    
    /**
     * 允许的枚举值
     */
    String[] allowedValues() default {};
    
    // ========== 日期格式校验相关参数 ==========
    
    /**
     * 日期格式模式
     */
    String dateFormat() default "yyyy-MM-dd";
    
    /**
     * 日期时间格式模式
     */
    String dateTimeFormat() default "yyyy-MM-dd HH:mm:ss";
    
    // ========== 通用参数 ==========
    
    /**
     * 是否允许为空
     */
    boolean nullable() default true;
    
    /**
     * 是否在校验失败时停止解析
     */
    boolean stopOnFailure() default false;
    
    /**
     * 校验顺序（数值越小越先执行）
     */
    int order() default 0;
}
