package com.fuiou.dips.utils;


import cn.hutool.core.util.IdUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Util {

	public static String getNewRandomCode(int codeLen) {
		// 首先定义随机数据源
		// 根据需要得到的数据码的长度返回随机字符串
		java.util.Random randomCode = new java.util.Random();
		String strCode = "";
		while (codeLen > 0) {
			int charCode = randomCode.nextInt(10);
			strCode += charCode;
			codeLen--;
		}
		return strCode;
	}

	/**
	 * 生成流水号
	 * @return
	 */
	public static String generateRecordNo() {
		long snowflakeId = IdUtil.getSnowflakeNextId();
		String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));
		return timestamp + snowflakeId;
	}
}
