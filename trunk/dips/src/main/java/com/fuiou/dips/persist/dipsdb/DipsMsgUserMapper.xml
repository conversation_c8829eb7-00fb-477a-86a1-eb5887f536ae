<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fuiou.dips.persist.dipsdb.DipsMsgUserMapper" >
  <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.DipsMsgUser" >
    <id column="row_id" property="rowId" jdbcType="BIGINT" />
    <result column="msg_no" property="msgNo" jdbcType="VARCHAR" />
    <result column="login_id" property="loginId" jdbcType="VARCHAR" />
    <result column="user_type" property="userType" jdbcType="CHAR" />
    <result column="read_flag" property="readFlag" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
    <result column="reserved2" property="reserved2" jdbcType="VARCHAR" />
    <result column="reserved3" property="reserved3" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    row_id, msg_no, login_id, user_type, read_flag, create_time, update_time, reserved1, 
    reserved2, reserved3
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_dips_msg_user
    where row_id = #{rowId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_dips_msg_user
    where row_id = #{rowId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.fuiou.dips.persist.beans.DipsMsgUser" useGeneratedKeys="true" keyProperty="rowId">
    insert into t_dips_msg_user
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rowId != null" >
        row_id,
      </if>
      <if test="msgNo != null" >
        msg_no,
      </if>
      <if test="loginId != null" >
        login_id,
      </if>
      <if test="userType != null" >
        user_type,
      </if>
      <if test="readFlag != null" >
        read_flag,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="reserved1 != null" >
        reserved1,
      </if>
      <if test="reserved2 != null" >
        reserved2,
      </if>
      <if test="reserved3 != null" >
        reserved3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rowId != null" >
        #{rowId,jdbcType=BIGINT},
      </if>
      <if test="msgNo != null" >
        #{msgNo,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null" >
        #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=CHAR},
      </if>
      <if test="readFlag != null" >
        #{readFlag,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserved1 != null" >
        #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null" >
        #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null" >
        #{reserved3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fuiou.dips.persist.beans.DipsMsgUser" >
    update t_dips_msg_user
    <set >
      <if test="msgNo != null" >
        msg_no = #{msgNo,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null" >
        login_id = #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        user_type = #{userType,jdbcType=CHAR},
      </if>
      <if test="readFlag != null" >
        read_flag = #{readFlag,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserved1 != null" >
        reserved1 = #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null" >
        reserved2 = #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null" >
        reserved3 = #{reserved3,jdbcType=VARCHAR},
      </if>
    </set>
    where row_id = #{rowId,jdbcType=BIGINT}
  </update>

  <update id="updateReadFlag">
    UPDATE t_dips_msg_user
    SET read_flag = '1',
    update_time = NOW()
    WHERE login_id = #{loginId}
    AND user_type = #{userType}
    AND msg_no = #{msgNo}
    AND read_flag = '0'
  </update>

  <insert id="batchInsert" useGeneratedKeys="true">
    INSERT INTO t_dips_msg_user
    (
    <trim suffixOverrides=",">
      <if test="list[0].msgNo != null">msg_no,</if>
      <if test="list[0].loginId != null">login_id,</if>
      <if test="list[0].userType != null">user_type,</if>
      <if test="list[0].readFlag != null">read_flag,</if>
      <if test="list[0].createTime != null">create_time,</if>
      <if test="list[0].updateTime != null">update_time,</if>
      <if test="list[0].reserved1 != null">reserved1,</if>
      <if test="list[0].reserved2 != null">reserved2,</if>
      <if test="list[0].reserved3 != null">reserved3,</if>
    </trim>
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      <trim suffixOverrides=",">
        <if test="item.msgNo != null">#{item.msgNo},</if>
        <if test="item.loginId != null">#{item.loginId},</if>
        <if test="item.userType != null">#{item.userType},</if>
        <if test="item.readFlag != null">#{item.readFlag},</if>
        <if test="item.createTime != null">#{item.createTime},</if>
        <if test="item.updateTime != null">#{item.updateTime},</if>
        <if test="item.reserved1 != null">#{item.reserved1},</if>
        <if test="item.reserved2 != null">#{item.reserved2},</if>
        <if test="item.reserved3 != null">#{item.reserved3},</if>
      </trim>
      )
    </foreach>
  </insert>

</mapper>