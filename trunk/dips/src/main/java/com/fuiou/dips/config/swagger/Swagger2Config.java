package com.fuiou.dips.config.swagger;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

@Configuration
//@Conditional(SwaggerEnableCondition.class)
//@EnableSwagger2
public class Swagger2Config extends WebMvcConfigurationSupport {


//    @Bean
//    @Conditional(SwaggerEnableCondition.class)
//    public Docket buildDocket() {
//        return  new Docket(DocumentationType.SWAGGER_2).enable(true)
//                .apiInfo(apiInfo())//调用下面apiInfo()方法
//                .select()
//                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
//                .paths(PathSelectors.any())
//                .build();
//
//    }
//
//    public ApiInfo apiInfo() {
//        return  new ApiInfoBuilder()
//                .title("苏州锦鲤dcoupon")
//                .description("")
////                http://localhost:8080/swagger-ui.html
////                .termsOfServiceUrl("http://localhost:8080")
//                .contact(new Contact("程军","","<EMAIL>"))
//                .version("1.0")
//                .build();
//
//    }

}