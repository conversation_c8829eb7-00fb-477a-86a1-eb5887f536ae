package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/***
 * @Description: 查询订单支付状态入参
 * @Author: bl
 * @Date: 2025/5/14 18:42
 */
public class QueryPayStatusReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    @Size(min = 1,
            max = 20,
            message = "商户号需在5-20字符")
    private String mchntCd;

    /**
     * 本次需支付金额
     */
    @NotNull(message = "订单号")
    private String orderNo;


    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
