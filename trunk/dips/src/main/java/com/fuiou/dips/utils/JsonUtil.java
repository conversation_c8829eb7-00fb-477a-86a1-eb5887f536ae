package com.fuiou.dips.utils;

import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.resp.LoginResp;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON相关操作工具类
 * @description JSON相关操作工具类
 * <AUTHOR>
 * @version 1.0.1
 * @date 2014-10-20
 */
public class JsonUtil {
	
	/**
	 * JAVA对象转换为JSON对象（去除值是空或NULL的属性）
	 * @param obj 转换的JAVA对象
	 * @return String JSON串
	 */
	public static String bean2Json(Object obj) {
		JsonConfig jsonConfig = new JsonConfig();  
		  
	    PropertyFilter filter = new PropertyFilter() {
	        public boolean apply(Object source, String name, Object value) {
	            if (null == value) {
	            	value = "";
	            }
	            return false;
	        }
	    };
	    jsonConfig.setJsonPropertyFilter(filter);
	    
	    JSONObject jo = JSONObject.fromObject(obj, jsonConfig);
	    return jo.toString();
	}
	
	/**
	 * JAVA对象转换为JSON对象（去除值是空或NULL的属性）
	 * @param obj 转换的JAVA对象
	 * @param jsonpCallback JSONP回调方法名
	 * @return String JSON串
	 */
	public static String bean2Json(Object obj, String jsonpCallback) {
		JsonConfig jsonConfig = new JsonConfig();  
		  
	    PropertyFilter filter = new PropertyFilter() {
	        public boolean apply(Object source, String name, Object value) {
	            if (null == value) {
	            	value = "";
//	                return true; 	//过滤掉该属性
	            }
	            return false;
	        }
	    };
	    jsonConfig.setJsonPropertyFilter(filter);
	    
	    JSONObject jo = JSONObject.fromObject(obj, jsonConfig);
	    if(StringUtils.isEmpty(jsonpCallback))
			return jo.toString();
		return jsonpCallback + "(" + jo.toString() + ")";
		
	}

    /**
     * 封装从电子券端的返回数据
     * @param json
     * @param jsonpCallback
     * @return
     */
    public static String json2Json(String json, String jsonpCallback) {

        return jsonpCallback + "(" + json + ")";
    }

	
	/**
	 * 从一个JSON 对象字符格式中得到一个java对象
	 * @param jsonString
	 * @param pojoCalss
	 * @return
	 */
	public static Object getObjectFromJsonString(String jsonString,
			Class<?> pojoCalss) {
		Object pojo;
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		pojo = JSONObject.toBean(jsonObject, pojoCalss);
		return pojo;
	}

	/**
	 * json转Object，可解析一个数组
	 * @param jsonString json
	 * @param pojoCalss 整体JSON字符串
	 * @param classMap 数组对象 classMap.put("data", Data.class);
	 * @return
	 */
	public static Object getObjectFromJsonString(String jsonString,
												 Class<?> pojoCalss, Map classMap) {
		Object obj = null;
		if(classMap==null){
			JSONObject jsonObject = JSONObject.fromObject(jsonString);
			obj = JSONObject.toBean(jsonObject, pojoCalss);
			return obj;
		}
		obj = JSONObject.toBean(JSONObject.fromObject(jsonString),
				pojoCalss, classMap);
		return obj;
	}

	/**
	 * 通过反射提取类中所有 List<T> 字段的泛型类型
	 */
	private static Map<String, Class<?>> buildGenericTypeMap(Class<?> clazz) {
		Map<String, Class<?>> classMap = new HashMap<>();
		for (Field field : clazz.getDeclaredFields()) {
			if (List.class.isAssignableFrom(field.getType())) {
				try {
					ParameterizedType genericType = (ParameterizedType) field.getGenericType();
					Class<?> actualType = (Class<?>) genericType.getActualTypeArguments()[0];
					classMap.put(field.getName(), actualType);
				} catch (Exception ignored) {
					// 非泛型字段或类型擦除时忽略
				}
			}
		}
		return classMap;
	}
	
	
	public static String toJsonStr(Object result)  {
		if (result instanceof Collection)
			return JSONArray.fromObject(result).toString();
		return JSONObject.fromObject(result).toString();
	}


	/**
	 * 截取字符串长度
	 */

	public static String subString(String str,int lenth) {

		if(StringUtils.isBlank(str)) {
			return "";
		}
		if(str.trim().length() > lenth) {
			return str.substring(0, lenth);
		}
		return str;
	}


	public static void main(String[] args) {
		String jsonStr = "{\"accountLogin\":true,\"appId\":\"wxba338220dd74657b\",\"employeeRoleType\":\"2\",\"expireTime\":*************,\"fullName\":\"区域100\",\"loginId\":\"shimj20\",\"mchntInfo\":{\"insCd\":\"08J0561752\",\"insNameCn\":\"CRM积分测试商户\",\"insNmJcCn\":\"CRM积分测试JC测试服务一二三四\",\"insTp\":\"1A\",\"loginId\":\"shimj20\",\"mchntCd\":\"0002230F0561752\",\"mchntTp\":\"Z724\",\"storeId\":\"\",\"userTp\":\"4\"},\"mobile\":\"\",\"openid\":\"o3uG56_BmMwuoGGxWlnHlna-oxeI\",\"relateStoreList\":[{\"storeId\":\"BTPOS1000071979\"},{\"storeId\":\"BTPOS1000114197\"}],\"relateStoreUserId\":\"\",\"token\":\"8fcefa3a669b4aa4837035afdb0e81bd\",\"unionid\":\"\",\"userIp\":\"**************\",\"userType\":\"02\",\"wechatAuthed\":true}";
//		String jsonStr = "{\"accountLogin\":true,\"appId\":\"wxba338220dd74657b\"}";
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("relateStoreList", StoreInfo.class);
		LoginResp loginResp = (LoginResp)getObjectFromJsonString(jsonStr, LoginResp.class, classMap);
//		LoginResp loginResp = (LoginResp)getObjectFromJsonString(jsonStr, LoginResp.class);

		System.out.println(loginResp.getRelateStoreList().get(0).getStoreId());
	}

}