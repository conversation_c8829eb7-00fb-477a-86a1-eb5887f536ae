package com.fuiou.dips.framework.annotation;

import java.lang.annotation.*;

/**
 * 支持在同一个字段上使用多个ExcelValid注解
 * 使用方式：@ExcelValids({@ExcelValid(...), @ExcelValid(...), ...})
 * 
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Inherited
public @interface ExcelValids {
    
    /**
     * 多个校验注解
     */
    ExcelValid[] value();
}
