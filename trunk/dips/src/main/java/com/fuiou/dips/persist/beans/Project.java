package com.fuiou.dips.persist.beans;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 装修通项目表
 *
 * <AUTHOR>
 * @TableName t_dips_project
 */
public class Project implements Serializable {
    /**
     * 主键
     */
    private Long rowId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 项目总金额，单位分
     */
    private BigDecimal projectAmt;

    /**
     * 项目进度 1进行中 2 已关闭 9 已完成
     */
    private String projectSt;

    /**
     * 当前阶段编号
     */
    private String currentStageNo;

    /**
     * 锁定标记 0 未锁定 1锁定
     */
    private String lockFlag;

    /**
     * 项目备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间（最后操作时间）
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注1
     */
    private String reserved1;

    /**
     * 备注2
     */
    private String reserved2;

    /**
     * 备注3
     */
    private String reserved3;

    /**
     * 项目创建人login_id
     */
    private String creatorLoginId;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getRowId() {
        return rowId;
    }

    /**
     * 主键
     */
    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    /**
     * 项目编号
     */
    public String getProjectNo() {
        return projectNo;
    }

    /**
     * 项目编号
     */
    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    /**
     * 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * 项目名称
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * 商户号
     */
    public String getMchntCd() {
        return mchntCd;
    }

    /**
     * 商户号
     */
    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    /**
     * 门店id
     */
    public String getStoreId() {
        return storeId;
    }

    /**
     * 门店id
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    /**
     * 项目总金额，单位分
     */
    public BigDecimal getProjectAmt() {
        return projectAmt;
    }

    /**
     * 项目总金额，单位分
     */
    public void setProjectAmt(BigDecimal projectAmt) {
        this.projectAmt = projectAmt;
    }

    /**
     * 项目进度 1进行中 2 已关闭 9 已完成
     */
    public String getProjectSt() {
        return projectSt;
    }

    /**
     * 项目进度 1进行中 2 已关闭 9 已完成
     */
    public void setProjectSt(String projectSt) {
        this.projectSt = projectSt;
    }

    /**
     * 当前阶段编号
     */
    public String getCurrentStageNo() {
        return currentStageNo;
    }

    /**
     * 当前阶段编号
     */
    public void setCurrentStageNo(String currentStageNo) {
        this.currentStageNo = currentStageNo;
    }

    /**
     * 锁定标记 0 未锁定 1锁定
     */
    public String getLockFlag() {
        return lockFlag;
    }

    /**
     * 锁定标记 0 未锁定 1锁定
     */
    public void setLockFlag(String lockFlag) {
        this.lockFlag = lockFlag;
    }

    /**
     * 项目备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 项目备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 备注1
     */
    public String getReserved1() {
        return reserved1;
    }

    /**
     * 备注1
     */
    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    /**
     * 备注2
     */
    public String getReserved2() {
        return reserved2;
    }

    /**
     * 备注2
     */
    public void setReserved2(String reserved2) {
        this.reserved2 = reserved2;
    }

    /**
     * 备注3
     */
    public String getReserved3() {
        return reserved3;
    }

    /**
     * 备注3
     */
    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }

    public String getCreatorLoginId() {
        return creatorLoginId;
    }

    public void setCreatorLoginId(String creatorLoginId) {
        this.creatorLoginId = creatorLoginId;
    }

    public Project() {
    }

    public Project(String projectNo, String mchntCd) {
        this.projectNo = projectNo;
        this.mchntCd = mchntCd;
    }
}
