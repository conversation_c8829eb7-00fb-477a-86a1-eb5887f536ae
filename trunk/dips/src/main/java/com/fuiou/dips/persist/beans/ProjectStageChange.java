package com.fuiou.dips.persist.beans;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 装修通项目阶段付款变更记录表
 *
 * <AUTHOR>
 * @TableName t_dips_project_stage_change
 */
public class ProjectStageChange implements Serializable {
    /**
     * 主键
     */
    private Long rowId;

    /**
     * 记录流水号
     */
    private String logNo;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 变更内容
     */
    private String changeContent;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间（最后操作时间）
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注1
     */
    private String reserved1;

    /**
     * 备注2
     */
    private String reserved2;

    /**
     * 备注3
     */
    private String reserved3;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getRowId() {
        return rowId;
    }

    /**
     * 主键
     */
    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    /**
     * 记录流水号
     */
    public String getLogNo() {
        return logNo;
    }

    /**
     * 记录流水号
     */
    public void setLogNo(String logNo) {
        this.logNo = logNo;
    }

    /**
     * 项目编号
     */
    public String getProjectNo() {
        return projectNo;
    }

    /**
     * 项目编号
     */
    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    /**
     * 商户号
     */
    public String getMchntCd() {
        return mchntCd;
    }

    /**
     * 商户号
     */
    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    /**
     * 变更内容
     */
    public String getChangeContent() {
        return changeContent;
    }

    /**
     * 变更内容
     */
    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 备注1
     */
    public String getReserved1() {
        return reserved1;
    }

    /**
     * 备注1
     */
    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    /**
     * 备注2
     */
    public String getReserved2() {
        return reserved2;
    }

    /**
     * 备注2
     */
    public void setReserved2(String reserved2) {
        this.reserved2 = reserved2;
    }

    /**
     * 备注3
     */
    public String getReserved3() {
        return reserved3;
    }

    /**
     * 备注3
     */
    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }

}
