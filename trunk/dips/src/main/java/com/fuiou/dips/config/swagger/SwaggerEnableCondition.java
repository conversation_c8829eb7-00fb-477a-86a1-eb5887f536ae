package com.fuiou.dips.config.swagger;

import com.fuiou.dips.utils.ConfigReader;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class SwaggerEnableCondition implements Condition {

	@Override
	public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
		String active = ConfigReader.getConfig("profiles.active");
		return "mchnt".equals(active) ;
//		return false;
	}

}