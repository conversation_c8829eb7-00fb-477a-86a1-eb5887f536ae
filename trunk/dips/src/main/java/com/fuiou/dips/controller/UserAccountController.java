package com.fuiou.dips.controller;

import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.entity.ProjectSimple;
import com.fuiou.dips.data.req.BaseReq;
import com.fuiou.dips.data.req.MchntChooseForUserReq;
import com.fuiou.dips.data.req.MchntChooseReq;
import com.fuiou.dips.data.req.UserAccountLoginReq;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.data.resp.UserAccountLoginResp;
import com.fuiou.dips.services.UserAccountService;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户端登录
 */
@Controller
@RequestMapping("/user/account")
public class UserAccountController {

    @Autowired
    private UserAccountService userAccountService;

    @ResponseBody
    @PostMapping(value = "login")
    public ResponseEntity<UserAccountLoginResp> login(@Valid @RequestBody UserAccountLoginReq userAccountLoginReq) throws Exception{
        LogWriter.info(this, String.format("用户端登录，userAccountLoginReq=%s", JsonUtil.bean2Json(userAccountLoginReq)));
        UserAccountLoginResp resp = userAccountService.login(userAccountLoginReq);
        LogWriter.info(this, String.format("用户端登录，resp=%s", JsonUtil.bean2Json(resp)));
        return ResponseEntityFactory.ok(resp);
    }

    @ResponseBody
    @PostMapping(value = "/choose")
    public ResponseEntity choose(@Valid @RequestBody MchntChooseForUserReq mchntChooseForUserReq) throws Exception{
        LogWriter.info(this, String.format("用户端端选择商户，mchntChooseReq=%s", JsonUtil.bean2Json(mchntChooseForUserReq)));
        userAccountService.chooseMchntAndSet(mchntChooseForUserReq);
        LogWriter.info(this, "用户端选择商户，操作完成");
        return ResponseEntityFactory.ok();
    }

    @ResponseBody
    @PostMapping(value = "projects")
    public ResponseEntity<List<ProjectSimple>> projects( @Valid @RequestBody BaseReq req) throws Exception{
        LogWriter.info(this, String.format("用户端查询项目列表，projects BaseReq=%s", JsonUtil.bean2Json(req)));
        List<ProjectSimple> projects = userAccountService.getProjectList(req.getMchntCd());
        LogWriter.info(this, String.format("用户端查询项目列表，projects=%s", JsonUtil.toJsonStr(projects)));
        return ResponseEntityFactory.ok(projects);
    }

}
