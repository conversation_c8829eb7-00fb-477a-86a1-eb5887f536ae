package com.fuiou.dips.enums;

/***
 * <AUTHOR>
 * @Date 2022/6/24 15:03
 * @Description 签名方式
 **/
public enum SignTypeEnum {
    RSA("RSA", "rsa SHA256withRSA签名"),
    MD5("MD5", "md5 utf-8签名");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    SignTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return "SignTypeEnum{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
