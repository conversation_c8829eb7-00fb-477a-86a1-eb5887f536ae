package com.fuiou.dips.data.resp;

import com.fuiou.dips.data.entity.StoreInfo;

import java.util.List;

public class MchntChooseResp {

    private String mchntCd;

    private String userType = "02"; //用户类型

    private List<StoreInfo> relateStoreList; //账号关联门店

    /**
     * 员工角色类型
     */
    private String employeeRoleType = "";

    private String fullName; //姓名
    private String  mobile ; //手机号

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public List<StoreInfo> getRelateStoreList() {
        return relateStoreList;
    }

    public void setRelateStoreList(List<StoreInfo> relateStoreList) {
        this.relateStoreList = relateStoreList;
    }

    public String getEmployeeRoleType() {
        return employeeRoleType;
    }

    public void setEmployeeRoleType(String employeeRoleType) {
        this.employeeRoleType = employeeRoleType;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
