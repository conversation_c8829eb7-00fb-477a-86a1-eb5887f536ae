package com.fuiou.dips.controller;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.req.LoginReq;
import com.fuiou.dips.data.req.MchntAccountLoginReq;
import com.fuiou.dips.data.req.MchntChooseReq;
import com.fuiou.dips.data.req.MchntMobileLoginReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.MchntAccountLoginResp;
import com.fuiou.dips.data.resp.MchntChooseResp;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.framework.interceptor.RequiresPermissions;
import com.fuiou.dips.services.MchntAccountMobileService;
import com.fuiou.dips.services.MchntAccountService;
import com.fuiou.dips.services.WeChatLoginService;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * 商户端登录
 */
@Controller
@RequestMapping("/mchnt/account")
public class MchntAccountController {

    @Autowired
    private MchntAccountService mchntAccountService;

    @Autowired
    private MchntAccountMobileService mchntAccountMobileService;

    @ResponseBody
    @PostMapping(value = "login")
    public ResponseEntity<MchntAccountLoginResp> login(@Valid @RequestBody MchntAccountLoginReq mchntAccountLoginReq) throws Exception{
        LogWriter.info(this, String.format("商户端登录，mchntAccountLoginReq = %s", JsonUtil.bean2Json(mchntAccountLoginReq)));
        MchntAccountLoginResp resp = mchntAccountService.accountLogin(mchntAccountLoginReq);
        LogWriter.info(this, String.format("商户端登录，resp=%s", JsonUtil.bean2Json(resp)));
        return ResponseEntityFactory.ok(resp);
    }

    @ResponseBody
    @PostMapping(value = "/mobilelogin")
    public ResponseEntity<MchntAccountLoginResp> mobileLogin(@Valid @RequestBody MchntMobileLoginReq mchntMobileLoginReq) throws Exception{
        LogWriter.info(this, String.format("商户端手机号登录，mchntMobileLoginReq = %s", JsonUtil.bean2Json(mchntMobileLoginReq)));
        MchntAccountLoginResp resp = mchntAccountMobileService.mobileLogin(mchntMobileLoginReq);
        LogWriter.info(this, String.format("商户端手机号登录，resp=%s", JsonUtil.bean2Json(resp)));
        return ResponseEntityFactory.ok(resp);
    }

    @ResponseBody
    @PostMapping(value = "/choose")
    public ResponseEntity<MchntChooseResp> choose(@Valid @RequestBody MchntChooseReq mchntChooseReq) throws Exception{
        LogWriter.info(this, String.format("商户端选择商户，mchntChooseReq = %s", JsonUtil.bean2Json(mchntChooseReq)));
        MchntChooseResp resp = mchntAccountService.chooseMchntAndSet(mchntChooseReq);
        LogWriter.info(this, String.format("商户端选择商户，mchntChooseResp = %s", JsonUtil.bean2Json(resp)));
        return ResponseEntityFactory.ok(resp);
    }

}
