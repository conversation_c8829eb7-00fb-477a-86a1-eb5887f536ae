package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.ValidPhone;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 装修通客户表分页查询参数
 *
 * @TableName t_dips_customer
 */
public class CustomerQueryReq implements Serializable {

    /**
     * 商户号
     */
    @NotBlank(message = "商户编号不能为空")
    @Size(min = 5,
          max = 20,
          message = "商户编号需在5-20字符")
    private String mchntCd;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 手机号（商户号加手机号确定客户唯一）
     */
    @ValidPhone(message = "手机号格式错误❌")
    private String phone;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 登录用户ID
     */
    private String loginId;

    /**
     * 门店ID
     */
    private String storeId;

    private List<String> storeIds;

    /**
     * 客户电话列表
     */
    private List<String> phones;

    /**
     * 1，我的客户；  其他： 全部客户
     */
    private String searchType;

    private static final long serialVersionUID = 1L;

    public String getMchntCd() {
        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getCustomerName() {
        return customerName == null ? null : customerName.trim();
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPhone() {
        return phone == null ? null : phone.trim();
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCustomerSource() {
        return customerSource == null ? null : customerSource.trim();
    }

    public void setCustomerSource(String customerSource) {
        this.customerSource = customerSource;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public List<String> getPhones() {
        return phones;
    }

    public void setPhones(List<String> phones) {
        this.phones = phones;
    }

    public List<String> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<String> storeIds) {
        this.storeIds = storeIds;
    }
}