package com.fuiou.dips.persist.beans;

import java.sql.Timestamp;

public class GroupShopRelate {
    private Long id;
    private Long groupId;
    private Long shopId;
    private String shopUserId;
    private Timestamp createTime;
    private Timestamp updateTime;
    private String reserve1;
    private Long sn;
    private String mchntCd;

    private String groupName;

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(String shopUserId) {
        this.shopUserId = shopUserId;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getReserve1() {
        return reserve1;
    }

    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1;
    }

    public Long getSn() {
        return sn;
    }

    public void setSn(Long sn) {
        this.sn = sn;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    @Override
    public String toString() {
        return "GroupShopRelate{" +
                "id=" + id +
                ", groupId=" + groupId +
                ", shopId=" + shopId +
                ", shopUserId='" + shopUserId + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", reserve1='" + reserve1 + '\'' +
                ", sn=" + sn +
                ", mchntCd='" + mchntCd + '\'' +
                '}';
    }
}