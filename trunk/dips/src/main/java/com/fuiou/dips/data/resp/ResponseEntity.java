package com.fuiou.dips.data.resp;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.data.entity.SignBaseEntry;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.SignTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.services.SignService;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.MD5Util;
import com.fuiou.dips.utils.SignSort;
import com.fuiou.fsp.soa.data.MchntKeyData;
import com.fuiou.sk.cipher.FuiouRsaCipher;
import com.fuiou.sk.service.FuiouSkServiceWrapper;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 响应对象
 *
 * <AUTHOR>
 * @Date 2025/4/3 12:35
 **/
public class ResponseEntity<T> implements SignBaseEntry {
    /**
     * 响应码，成功为：0000
     *
     * @Author: Joker
     * @Date: 2025/5/21 22:33
     */
    private String code;
    /**
     * 结果描述
     *
     * @Author: Joker
     * @Date: 2025/5/21 22:33
     */
    private String msg;
    /**
     * 请求结果数据
     *
     * @Author: Joker
     * @Date: 2025/5/21 22:33
     */
    private T data;
    /**
     * 随机字符串 外部调用有效
     *
     * @Author: Joker
     * @Date: 2025/5/21 12:11
     */
    private String randomStr;

    /**
     * 签名 外部调用有效
     *
     * @Author: Joker
     * @Date: 2025/5/21 12:11
     */

    private String sign;
    /**
     * 签名算法类型 外部调用有效
     *
     * @Author: Joker
     * @Date: 2025/5/21 12:11
     */
    private String signType;

    public ResponseEntity() {
    }

    public ResponseEntity(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseEntity(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getRandomStr() {

        return randomStr == null ? null : randomStr.trim();
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    public String getSign() {

        return sign == null ? null : sign.trim();
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSignType() {

        return signType == null ? null : signType.trim();
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    @Override
    public String toSignStr() {
        return SignSort.generateSignContent(this, new ArrayList<String>(Arrays.asList("sign", "resultMsg")), true);

    }

    public void signMd5(String md5Key) {
        LogWriter.info(this, String.format("md5加签，begin,md5Key=%s", md5Key));

        String srcStr = String.format("%s&md5Key=%s", toSignStr(), md5Key);
        LogWriter.info(this, String.format("md5加签，实际待加签字符串为srcStr=%s", srcStr));
        String result = MD5Util.encode(srcStr, MD5Util.UTF_8);
        LogWriter.info(this, String.format("md5加签，end,result=%s", result));
        setSign(result);
    }

    public void signRsa(String priKey) {
        LogWriter.info(this, String.format("rsa加签，begin,priKey=%s", priKey));

        String srcStr = toSignStr();
        LogWriter.info(this, String.format("rsa加签，实际待加签字符串为srcStr=%s", srcStr));
        String result = FuiouRsaCipher.sign2Base64BySha2(priKey, srcStr);
        LogWriter.info(this, String.format("rsa加签，end,result=%s", result));
        setSign(result);
    }

    public void validSignMd5(String md5Key) throws Exception {
        LogWriter.info(this, String.format("md5验签，begin,md5Key=%s", md5Key));
        String srcStr = String.format("%s&md5Key=%s", toSignStr(), md5Key);
        LogWriter.info(this, String.format("md5验签，实际待加签字符串为srcStr=%s", srcStr));
        String result = MD5Util.encode(srcStr, MD5Util.UTF_8);
        if (!getSign().equals(result)) {
            LogWriter.info(this, String.format("md5验签失败"));
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
        }
    }

    public void validSignRsa(String pubKey) throws Exception {
        LogWriter.info(this, String.format("rsa验签，begin,pubKey=%s", pubKey));

        String srcStr = toSignStr();
        LogWriter.info(this, String.format("rsa验签，实际待加签字符串为srcStr=%s", srcStr));
        if (!FuiouRsaCipher.verifySignBySha2(pubKey, srcStr, getSign())) {
            LogWriter.info(this, String.format("rsa验签失败"));
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);

        }
    }


    public void validSign(SignTypeEnum signTypeEnum) throws Exception {

        LogWriter.info(this, String.format("验签，begin,signTypeEnum=%s", signTypeEnum.toString()));
        MchntKeyData mchntKeyData = FuiouSkServiceWrapper.getCachedMchntKey(SignService.TPAY_INS_CD);
        switch (signTypeEnum) {
            case RSA: {
                LogWriter.info(String.format("商户rsa密钥信息为:mchntKeyData=%s", JSONObject.toJSONString(mchntKeyData)));
                validSignRsa(mchntKeyData.getRsaFuiouPubKey());
                break;
            }
            case MD5:
            default: {
                LogWriter.info(String.format("商户md5密钥信息为:md5key=%s", JSONObject.toJSONString(mchntKeyData.getMd5Key())));
                validSignMd5(mchntKeyData.getMd5Key());
                break;
            }
        }
    }
}
