package com.fuiou.dips.enums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/2/19 10:48
 * @Description 服务器节点类型
 **/
public enum ServerNodeTypeEnum {
    xs("xs","下沙节点",new HashSet<String>(Arrays.asList("xs"))),
    jq("jq","金桥节点",new HashSet<String>()),
    jqins("jqins","金桥机构联调节点",new HashSet<String>()),
    cloudjqins("cloud|jqins","金桥机构联调节点",new HashSet<String>()),
    cloud("ali","云上节点",new HashSet<String>());

    private String code;
    private String name;
    private Set<String> kewWords;

    ServerNodeTypeEnum(String code, String name, Set<String> kewWords) {
        this.code = code;
        this.name = name;
        this.kewWords = kewWords;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Set<String> getKewWords() {
        return kewWords;
    }
}
