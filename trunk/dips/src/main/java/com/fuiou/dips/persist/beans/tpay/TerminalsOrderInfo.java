package com.fuiou.dips.persist.beans.tpay;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端订单表
 * @TableName t_terminals_order_info
 */
public class TerminalsOrderInfo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户代码
     */
    private String merchantCode;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户订单号
     */
    private String mchntOrderNo;

    /**
     * 订单金额
     */
    private String orderAmt;

    /**
     * 退款金额
     */
    private String refundAmt;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 支付状态00：未支付，01：已支付，02：支付失败，03:已退款
     */
    private String payState;

    /**
     * 创建时间
     */
    private Date crtTime;

    /**
     * 
     */
    private Date updTime;

    /**
     * 
     */
    private Date payTime;

    /**
     * 条码流水
     */
    private String channelOrderId;

    /**
     * 渠道订单号
     */
    private String transactionId;

    /**
     * 富友终端号
     */
    private String fyTermId;

    /**
     * 富友终端号版本号
     */
    private String termVersion;

    /**
     * 优惠金额
     */
    private String couponFee;

    /**
     * 银行sim卡号
     */
    private String simNo;

    /**
     * 乐观锁标记
     */
    private String lockFlag;

    /**
     * 备用1
     */
    private String reserve1;

    /**
     * 备用2
     */
    private String reserve2;

    /**
     * 备用3
     */
    private String reserve3;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 富友内部追踪号
     */
    private String fyTraceNo;

    /**
     * 富友清算日
     */
    private String fySettleDt;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 口碑优惠信息
     */
    private String fundBillList;

    /**
     * 口碑不参与优惠金额
     */
    private String undiscAmt;

    /**
     * 商户实收金额
     */
    private String receiptAmount;

    /**
     * 商户优惠金额
     */
    private String merchantFund;

    /**
     * 通道优惠金额
     */
    private String channelFund;

    /**
     * 请求退款金额
     */
    private String reqRefundAmt;

    /**
     * 交易日期格式YYYY-MM-DD
     */
    private String tradeDt;

    /**
     * 正反交易类型00：正交易；01：反交易
     */
    private String tradeType;

    /**
     * 渠道退款流水号
     */
    private String refundId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 终端打印商户号
     */
    private String termMchntCd;

    /**
     * 支付失败原因
     */
    private String errorMsg;

    /**
     * 店铺ID
     */
    private String shopName;

    /**
     * 支付状态查询次数（不能大于5次）
     */
    private Integer queryTimes;

    /**
     * 业务系统id
     */
    private String sysId;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 回调通知地址
     */
    private String noticeUrl;

    /**
     * 源订单号
     */
    private String srcMchntOrderNo;

    /**
     * POS流水号
     */
    private String posSsn;

    /**
     * 是否校验卡号 1 校验
     */
    private String checkCardNo;

    /**
     * 打印标示:1未打印，2已打印
     */
    private String markPrint;

    /**
     * 附加订单状态
     */
    private String addOrderType;

    /**
     * 备用
     */
    private String reserve4;

    /**
     * 备用
     */
    private String reserve5;

    /**
     * 备用
     */
    private String reserve6;

    /**
     * 备用
     */
    private String reserve7;

    /**
     * 地理位置信息
     */
    private String reserve8;

    /**
     * 备用
     */
    private String reserve9;

    /**
     * 备用
     */
    private String reserve10;

    /**
     * 终端大类，台卡用15
     */
    private String tmType;

    /**
     * 终端序列号
     */
    private String tmSn;

    /**
     * 终端型号
     */
    private String tmModel;

    /**
     * WPOS返回代金卷
     */
    private String reservedPromotionDetail;

    /**
     * 预留字段1
     */
    private String txnData1;

    /**
     * 预留字段2
     */
    private String txnData2;

    /**
     * 预留字段3
     */
    private String txnData3;

    /**
     * 预留字段4
     */
    private String txnData4;

    /**
     * 预留字段5
     */
    private String txnData5;




    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 商户代码
     */
    public String getMerchantCode() {
        return merchantCode;
    }

    /**
     * 商户代码
     */
    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    /**
     * 商户名称
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * 商户名称
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * 商户订单号
     */
    public String getMchntOrderNo() {
        return mchntOrderNo;
    }

    /**
     * 商户订单号
     */
    public void setMchntOrderNo(String mchntOrderNo) {
        this.mchntOrderNo = mchntOrderNo;
    }

    /**
     * 订单金额
     */
    public String getOrderAmt() {
        return orderAmt;
    }

    /**
     * 订单金额
     */
    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    /**
     * 退款金额
     */
    public String getRefundAmt() {
        return refundAmt;
    }

    /**
     * 退款金额
     */
    public void setRefundAmt(String refundAmt) {
        this.refundAmt = refundAmt;
    }

    /**
     * 订单类型
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * 订单类型
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     * 支付状态00：未支付，01：已支付，02：支付失败，03:已退款
     */
    public String getPayState() {
        return payState;
    }

    /**
     * 支付状态00：未支付，01：已支付，02：支付失败，03:已退款
     */
    public void setPayState(String payState) {
        this.payState = payState;
    }

    /**
     * 创建时间
     */
    public Date getCrtTime() {
        return crtTime;
    }

    /**
     * 创建时间
     */
    public void setCrtTime(Date crtTime) {
        this.crtTime = crtTime;
    }

    /**
     * 
     */
    public Date getUpdTime() {
        return updTime;
    }

    /**
     * 
     */
    public void setUpdTime(Date updTime) {
        this.updTime = updTime;
    }

    /**
     * 
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     * 
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * 条码流水
     */
    public String getChannelOrderId() {
        return channelOrderId;
    }

    /**
     * 条码流水
     */
    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    /**
     * 渠道订单号
     */
    public String getTransactionId() {
        return transactionId;
    }

    /**
     * 渠道订单号
     */
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * 富友终端号
     */
    public String getFyTermId() {
        return fyTermId;
    }

    /**
     * 富友终端号
     */
    public void setFyTermId(String fyTermId) {
        this.fyTermId = fyTermId;
    }

    /**
     * 富友终端号版本号
     */
    public String getTermVersion() {
        return termVersion;
    }

    /**
     * 富友终端号版本号
     */
    public void setTermVersion(String termVersion) {
        this.termVersion = termVersion;
    }

    /**
     * 优惠金额
     */
    public String getCouponFee() {
        return couponFee;
    }

    /**
     * 优惠金额
     */
    public void setCouponFee(String couponFee) {
        this.couponFee = couponFee;
    }

    /**
     * 银行sim卡号
     */
    public String getSimNo() {
        return simNo;
    }

    /**
     * 银行sim卡号
     */
    public void setSimNo(String simNo) {
        this.simNo = simNo;
    }

    /**
     * 乐观锁标记
     */
    public String getLockFlag() {
        return lockFlag;
    }

    /**
     * 乐观锁标记
     */
    public void setLockFlag(String lockFlag) {
        this.lockFlag = lockFlag;
    }

    /**
     * 备用1
     */
    public String getReserve1() {
        return reserve1;
    }

    /**
     * 备用1
     */
    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1;
    }

    /**
     * 备用2
     */
    public String getReserve2() {
        return reserve2;
    }

    /**
     * 备用2
     */
    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2;
    }

    /**
     * 备用3
     */
    public String getReserve3() {
        return reserve3;
    }

    /**
     * 备用3
     */
    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3;
    }

    /**
     * 授权码
     */
    public String getAuthCode() {
        return authCode;
    }

    /**
     * 授权码
     */
    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    /**
     * 富友内部追踪号
     */
    public String getFyTraceNo() {
        return fyTraceNo;
    }

    /**
     * 富友内部追踪号
     */
    public void setFyTraceNo(String fyTraceNo) {
        this.fyTraceNo = fyTraceNo;
    }

    /**
     * 富友清算日
     */
    public String getFySettleDt() {
        return fySettleDt;
    }

    /**
     * 富友清算日
     */
    public void setFySettleDt(String fySettleDt) {
        this.fySettleDt = fySettleDt;
    }

    /**
     * 退款订单号
     */
    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    /**
     * 退款订单号
     */
    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    /**
     * 口碑优惠信息
     */
    public String getFundBillList() {
        return fundBillList;
    }

    /**
     * 口碑优惠信息
     */
    public void setFundBillList(String fundBillList) {
        this.fundBillList = fundBillList;
    }

    /**
     * 口碑不参与优惠金额
     */
    public String getUndiscAmt() {
        return undiscAmt;
    }

    /**
     * 口碑不参与优惠金额
     */
    public void setUndiscAmt(String undiscAmt) {
        this.undiscAmt = undiscAmt;
    }

    /**
     * 商户实收金额
     */
    public String getReceiptAmount() {
        return receiptAmount;
    }

    /**
     * 商户实收金额
     */
    public void setReceiptAmount(String receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    /**
     * 商户优惠金额
     */
    public String getMerchantFund() {
        return merchantFund;
    }

    /**
     * 商户优惠金额
     */
    public void setMerchantFund(String merchantFund) {
        this.merchantFund = merchantFund;
    }

    /**
     * 通道优惠金额
     */
    public String getChannelFund() {
        return channelFund;
    }

    /**
     * 通道优惠金额
     */
    public void setChannelFund(String channelFund) {
        this.channelFund = channelFund;
    }

    /**
     * 请求退款金额
     */
    public String getReqRefundAmt() {
        return reqRefundAmt;
    }

    /**
     * 请求退款金额
     */
    public void setReqRefundAmt(String reqRefundAmt) {
        this.reqRefundAmt = reqRefundAmt;
    }

    /**
     * 交易日期格式YYYY-MM-DD
     */
    public String getTradeDt() {
        return tradeDt;
    }

    /**
     * 交易日期格式YYYY-MM-DD
     */
    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    /**
     * 正反交易类型00：正交易；01：反交易
     */
    public String getTradeType() {
        return tradeType;
    }

    /**
     * 正反交易类型00：正交易；01：反交易
     */
    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    /**
     * 渠道退款流水号
     */
    public String getRefundId() {
        return refundId;
    }

    /**
     * 渠道退款流水号
     */
    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    /**
     * 店铺ID
     */
    public String getShopId() {
        return shopId;
    }

    /**
     * 店铺ID
     */
    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    /**
     * 终端打印商户号
     */
    public String getTermMchntCd() {
        return termMchntCd;
    }

    /**
     * 终端打印商户号
     */
    public void setTermMchntCd(String termMchntCd) {
        this.termMchntCd = termMchntCd;
    }

    /**
     * 支付失败原因
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 支付失败原因
     */
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * 店铺ID
     */
    public String getShopName() {
        return shopName;
    }

    /**
     * 店铺ID
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * 支付状态查询次数（不能大于5次）
     */
    public Integer getQueryTimes() {
        return queryTimes;
    }

    /**
     * 支付状态查询次数（不能大于5次）
     */
    public void setQueryTimes(Integer queryTimes) {
        this.queryTimes = queryTimes;
    }

    /**
     * 业务系统id
     */
    public String getSysId() {
        return sysId;
    }

    /**
     * 业务系统id
     */
    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    /**
     * 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 错误码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 银行卡号
     */
    public String getCardNo() {
        return cardNo;
    }

    /**
     * 银行卡号
     */
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    /**
     * 回调通知地址
     */
    public String getNoticeUrl() {
        return noticeUrl;
    }

    /**
     * 回调通知地址
     */
    public void setNoticeUrl(String noticeUrl) {
        this.noticeUrl = noticeUrl;
    }

    /**
     * 源订单号
     */
    public String getSrcMchntOrderNo() {
        return srcMchntOrderNo;
    }

    /**
     * 源订单号
     */
    public void setSrcMchntOrderNo(String srcMchntOrderNo) {
        this.srcMchntOrderNo = srcMchntOrderNo;
    }

    /**
     * POS流水号
     */
    public String getPosSsn() {
        return posSsn;
    }

    /**
     * POS流水号
     */
    public void setPosSsn(String posSsn) {
        this.posSsn = posSsn;
    }

    /**
     * 是否校验卡号 1 校验
     */
    public String getCheckCardNo() {
        return checkCardNo;
    }

    /**
     * 是否校验卡号 1 校验
     */
    public void setCheckCardNo(String checkCardNo) {
        this.checkCardNo = checkCardNo;
    }

    /**
     * 打印标示:1未打印，2已打印
     */
    public String getMarkPrint() {
        return markPrint;
    }

    /**
     * 打印标示:1未打印，2已打印
     */
    public void setMarkPrint(String markPrint) {
        this.markPrint = markPrint;
    }

    /**
     * 附加订单状态
     */
    public String getAddOrderType() {
        return addOrderType;
    }

    /**
     * 附加订单状态
     */
    public void setAddOrderType(String addOrderType) {
        this.addOrderType = addOrderType;
    }

    /**
     * 备用
     */
    public String getReserve4() {
        return reserve4;
    }

    /**
     * 备用
     */
    public void setReserve4(String reserve4) {
        this.reserve4 = reserve4;
    }

    /**
     * 备用
     */
    public String getReserve5() {
        return reserve5;
    }

    /**
     * 备用
     */
    public void setReserve5(String reserve5) {
        this.reserve5 = reserve5;
    }

    /**
     * 备用
     */
    public String getReserve6() {
        return reserve6;
    }

    /**
     * 备用
     */
    public void setReserve6(String reserve6) {
        this.reserve6 = reserve6;
    }

    /**
     * 备用
     */
    public String getReserve7() {
        return reserve7;
    }

    /**
     * 备用
     */
    public void setReserve7(String reserve7) {
        this.reserve7 = reserve7;
    }

    /**
     * 地理位置信息
     */
    public String getReserve8() {
        return reserve8;
    }

    /**
     * 地理位置信息
     */
    public void setReserve8(String reserve8) {
        this.reserve8 = reserve8;
    }

    /**
     * 备用
     */
    public String getReserve9() {
        return reserve9;
    }

    /**
     * 备用
     */
    public void setReserve9(String reserve9) {
        this.reserve9 = reserve9;
    }

    /**
     * 备用
     */
    public String getReserve10() {
        return reserve10;
    }

    /**
     * 备用
     */
    public void setReserve10(String reserve10) {
        this.reserve10 = reserve10;
    }

    /**
     * 终端大类，台卡用15
     */
    public String getTmType() {
        return tmType;
    }

    /**
     * 终端大类，台卡用15
     */
    public void setTmType(String tmType) {
        this.tmType = tmType;
    }

    /**
     * 终端序列号
     */
    public String getTmSn() {
        return tmSn;
    }

    /**
     * 终端序列号
     */
    public void setTmSn(String tmSn) {
        this.tmSn = tmSn;
    }

    /**
     * 终端型号
     */
    public String getTmModel() {
        return tmModel;
    }

    /**
     * 终端型号
     */
    public void setTmModel(String tmModel) {
        this.tmModel = tmModel;
    }

    /**
     * WPOS返回代金卷
     */
    public String getReservedPromotionDetail() {
        return reservedPromotionDetail;
    }

    /**
     * WPOS返回代金卷
     */
    public void setReservedPromotionDetail(String reservedPromotionDetail) {
        this.reservedPromotionDetail = reservedPromotionDetail;
    }

    /**
     * 预留字段1
     */
    public String getTxnData1() {
        return txnData1;
    }

    /**
     * 预留字段1
     */
    public void setTxnData1(String txnData1) {
        this.txnData1 = txnData1;
    }

    /**
     * 预留字段2
     */
    public String getTxnData2() {
        return txnData2;
    }

    /**
     * 预留字段2
     */
    public void setTxnData2(String txnData2) {
        this.txnData2 = txnData2;
    }

    /**
     * 预留字段3
     */
    public String getTxnData3() {
        return txnData3;
    }

    /**
     * 预留字段3
     */
    public void setTxnData3(String txnData3) {
        this.txnData3 = txnData3;
    }

    /**
     * 预留字段4
     */
    public String getTxnData4() {
        return txnData4;
    }

    /**
     * 预留字段4
     */
    public void setTxnData4(String txnData4) {
        this.txnData4 = txnData4;
    }

    /**
     * 预留字段5
     */
    public String getTxnData5() {
        return txnData5;
    }

    /**
     * 预留字段5
     */
    public void setTxnData5(String txnData5) {
        this.txnData5 = txnData5;
    }

}