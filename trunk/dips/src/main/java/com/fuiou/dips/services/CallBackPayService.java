package com.fuiou.dips.services;

import com.fuiou.dips.data.req.OrderCallBackReq;
import com.fuiou.dips.enums.OrderStatusEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.TradeTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.dipsdb.ProjectMapper;
import com.fuiou.dips.persist.dipsdb.ProjectStageMapper;
import com.fuiou.dips.persist.dipsdb.QrcodeInfMapper;
import com.fuiou.dips.utils.DateUtils;
import com.fuiou.dips.utils.JsonToMapConverter;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 支付回调处理Service
 */
@Service
public class CallBackPayService {

    @Autowired
    private SignService signService;

    @Autowired
    private TxnLogService txnLogService;

    @Autowired
    private ProjectStageMapper  projectStageMapper;

    @Autowired
    private ProjectMapper  projectMapper;

    @Resource
    private TxnBaseService txnBaseService;

    @Resource
    private QrcodeInfMapper qrcodeInfMapper;


    /**
     * 支付回调处理
     * @param req
     */
    public void deal(OrderCallBackReq req){
        //校验参数
        checkParams(req);
        //验签
        checkSign(req);
        //查询原交易
        TxnLog txnLogDB = queryOrderInfo(req);
        //更新订单信息
        updateOrderInfo(txnLogDB, req);
        //更新项目阶段及项目信息
        updateProjectStage(txnLogDB, req);
        // 保存最近交易终端信息
        txnBaseService.saveMchntTermInfo(txnLogDB);
    }

    private void checkSign(OrderCallBackReq req) {
        try  {
            signService.check(SignUtils.getSignContent(getTextParams(req)), req.getSign());;
        } catch (Exception e) {
            LogWriter.error(this,  "验签失败", e);
            throw new FUException(ResponseCodeEnum.CHECK_SIGN_ERROR);
        }
    }

    private void checkParams(OrderCallBackReq req){
        if(!TradeTypeEnum.POSITIVE_TXN.getCode().equals(req.getTrade_type())){
            throw new FUException(ResponseCodeEnum.PARAM_ERROR.getCode(), "trade_type 不正确");
        }
        if(StringUtils.isBlank(req.getPay_time())){
            throw new FUException(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "pay_time 不能为空");
        }
    }

    private Map<String, String> getTextParams(OrderCallBackReq req) {
        return JsonToMapConverter.convertToMap(req);
    }

    private TxnLog queryOrderInfo(OrderCallBackReq req){
        TxnLog txnLog = txnLogService.queryOrderInfo(req.getMchnt_cd(), req.getOrder_no());
        if(txnLog == null){
            throw new FUException(ResponseCodeEnum.ORDER_NON_EXIST);
        }
        if(!OrderStatusEnum.INIT_STATUS.getCode().equals(txnLog.getPayState())){
            throw new FUException(ResponseCodeEnum.ORDER_STATUS_ERROR);
        }
        return  txnLog;
    }

    private int updateOrderInfo(TxnLog txnLogDB, OrderCallBackReq req){
        try {
            if(OrderStatusEnum.PAY_SUCCESS.getCode().equals(req.getPay_state())){
                txnLogDB.setPayState(OrderStatusEnum.PAY_SUCCESS.getCode());
                txnLogDB.setPayTime(DateUtils.parseDateYYYYMMddHHmmss(req.getPay_time()));
            }
            //清算日期
            txnLogDB.setFyFettleDt(req.getTrade_dt());
            //跟踪号
            txnLogDB.setFyTraceNo(req.getFy_trace_no());
            txnLogDB.setRespMsg(StringUtils.substring(req.getResp_msg(), 0, 50));
            txnLogDB.setRespCode(req.getResp_code());
            txnLogDB.setUpdateTime(new Date());
            txnLogDB.setOrderType(req.getOrder_type());
            txnLogDB.setPayState(req.getPay_state());
            txnLogDB.setOldPayStatus(OrderStatusEnum.INIT_STATUS.getCode());
            txnLogDB.setFyTermId(req.getFy_term_id());
            txnLogDB.setChannelOrderId(req.getChannel_order_id());
            txnLogDB.setTransactionId(req.getTransaction_id());
            txnLogDB.setOpenId(req.getOpenId());
            int result = txnLogService.update(txnLogDB);
            if(result <= 0) {
                throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
            }
            qrcodeInfMapper.updateFinish(txnLogDB.getMchntCd(), txnLogDB.getOrderNo());
            return result;
        } catch (Exception e) {
            LogWriter.error(this,  "更新订单信息失败", e);
            throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
        }
    }

    /**
     * 更新项目阶段信息
     * @param txnLogDB
     * @param req
     */
    public void updateProjectStage(TxnLog txnLogDB, OrderCallBackReq req){
        if(!OrderStatusEnum.PAY_SUCCESS.getCode().equals(req.getPay_state())){
            LogWriter.info(this, "订单状态不是支付成功，不需要更新项目阶段信息");
            return;
        }
        for(int i = 0; i < 3; i++){
            //重试更新，防止统一阶段交易回调并发情况
            int result = updateProjectStageData(txnLogDB);
            try {
                if(result > 0 ){
                    return;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                LogWriter.error(this,  "更新项目阶段信息失败", e);
                //TODO 预警
            }
        }
    }

    private int getTxnStateOrderNo(List<ProjectStage> projectStages, TxnLog txnLogDB){
        for(int i = 0; i < projectStages.size(); i++){
            //本次收款交易对应阶段
            if(projectStages.get(i).getStageNo().equals(txnLogDB.getStageNo())){
                return i;
            }
        }
        throw new FUException(ResponseCodeEnum.STAGE_NON_EXIST);
    }

    private int updateProjectStageData(TxnLog txnLogDB){
        //查询阶段列表
        List<ProjectStage> projectStages = projectStageMapper.selectByMchntCdAndProjectNo(txnLogDB.getMchntCd(),  txnLogDB.getProjectNo());
        //获取阶段序号
        int stageOrderNo = getTxnStateOrderNo(projectStages, txnLogDB);
        ProjectStage projectStageThisTxn = projectStages.get(stageOrderNo);
        if("1".equals(projectStageThisTxn.getLockFlag())){
            LogWriter.info(this, "当前阶段已被其他线程更新，稍后重试");
            return 0;
        }
        int lockResult = projectStageMapper.updateLock(projectStageThisTxn.getMchntCd(), projectStageThisTxn.getProjectNo(), projectStageThisTxn.getStageNo());
        if (lockResult == 0){
            LogWriter.info(this, "当前阶段已被其他线程更新，稍后重试");
            return 0;
        }
        projectStageThisTxn.setOldStageActualAmt(projectStageThisTxn.getStageActualAmt());
        //实收金额
        projectStageThisTxn.setStageActualAmt(projectStageThisTxn.getStageActualAmt().add(txnLogDB.getOrderAmt()));
        BigDecimal refundAmt = projectStageThisTxn.getRefundAmt() == null ? BigDecimal.ZERO : projectStageThisTxn.getRefundAmt();
        LogWriter.info(this, String.format("已退款金额: %s", refundAmt.toPlainString()));
        BigDecimal stageActualAmtConversion = projectStageThisTxn.getStageActualAmt().subtract(refundAmt);
        LogWriter.info(this, String.format("实际已收款金额（计算退款） = 实际收款金额 - 退款金额 = %s", stageActualAmtConversion.toPlainString()));
        //当前阶段收款金额+现金收款金额-退款金额>=当前阶段应收款金额时，自动更改状态为“已完成收款”
        if (stageActualAmtConversion.compareTo(projectStageThisTxn.getStageAmt()) >= 0){
            projectStageThisTxn.setStageSt(ProjectEnum.StageStEnum.COMPLETED.getState());
        }
        int updateResult = projectStageMapper.updateStageForTxn(projectStageThisTxn);
        if(updateResult== 1 && ProjectEnum.StageStEnum.COMPLETED.getState().equals(projectStageThisTxn.getStageSt())) {
            //开启下一阶段项目，修改状态为进行中
            startNextProjectStage(projectStages, stageOrderNo + 1);
            // 更新项目状态及当前阶段号
            updateProject(projectStages, stageOrderNo + 1);
        }
        return 1;
    }

    private void startNextProjectStage(List<ProjectStage> projectStages, int nextStageOrderNo){
        try {
            if(nextStageOrderNo >= projectStages.size()){
                LogWriter.info(this, "无下一阶段项目");
                return;
            }
            ProjectStage nextProjectStage = projectStages.get(nextStageOrderNo);
            projectStageMapper.startStageForTxn(nextProjectStage.getMchntCd(), nextProjectStage.getProjectNo(), nextProjectStage.getStageNo());
        } catch (Exception e) {
            LogWriter.error(this,  "开启下一项目阶段失败", e);
        }
    }

    private void updateProject(List<ProjectStage> projectStages, int nextStageOrderNo){
        try {
            Project project = new Project();
            project.setProjectNo(projectStages.get(0).getProjectNo());
            project.setMchntCd(projectStages.get(0).getMchntCd());
            if(nextStageOrderNo >= projectStages.size()){
                LogWriter.info(this, "无下一阶段项目，项目完成");
                project.setProjectSt(ProjectEnum.ProjectStEnum.COMPLETED.getState());
            } else {
                LogWriter.info(this, "有下一阶段项目，项目进行中，更新CurrentStageNo");
                project.setCurrentStageNo(projectStages.get(nextStageOrderNo).getStageNo());
            }
            projectMapper.updateProjectForTxn(project);
        } catch (Exception e) {
            LogWriter.error(this,  "更新项目失败", e);
        }
    }



}
