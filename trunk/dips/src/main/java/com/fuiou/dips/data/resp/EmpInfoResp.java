package com.fuiou.dips.data.resp;

import cn.hutool.core.util.StrUtil;
import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.utils.StringUtil;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 员工信息
 *
 * <AUTHOR>
 */
public class EmpInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 登录id
     */
    @NotBlank(message = "登录id不能为空")
    private String loginId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 员工类型
     */
    private String userTp;

    /**
     * 员工类型中文
     */
    private String userTpName;

    private String mobile;

    public EmpInfoResp() {
    }

    public EmpInfoResp(String loginId, String name) {
        this.loginId = loginId;
        this.name = name;
    }

    public String getUserTpName() {
        if (StringUtil.isNotBlank(userTp)) {
            return EmployeeRoleTypeEnum.getMsgByCode(userTp.trim());
        }
        return StrUtil.isNotBlank(userTpName) ? userTpName.trim() : "";
    }

    public void setUserTpName(String userTpName) {
        this.userTpName = userTpName;
    }

    public String getUserTp() {
        return StrUtil.isNotBlank(userTp) ? userTp.trim() : "";
    }

    public void setUserTp(String userTp) {
        this.userTp = userTp;
    }

    public String getLoginId() {
        return StrUtil.isNotBlank(loginId) ? loginId.trim() : "";
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getName() {
        return StrUtil.isNotBlank(name) ? name.trim() : "";
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        EmpInfoResp dipsStore = (EmpInfoResp) o;

        return loginId != null ? loginId.equals(dipsStore.loginId) : dipsStore.loginId == null;
    }

    @Override
    public int hashCode() {
        return loginId != null ? loginId.hashCode() : 0;
    }

    public String getMobile() {
        return StrUtil.isNotBlank(mobile) ? mobile.trim() : "";
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
