package com.fuiou.dips.framework.convertor;

import com.fuiou.dips.utils.DateUtils;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.core.convert.converter.Converter;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DateConverter implements Converter<String, Date> {
  @Override    
  public Date convert(String source) {
      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
     dateFormat.setLenient(false);    
      try {    
         return DateUtils.parseDate(source,"yyyyy-MM-dd HH:mm:ss","yyyyy-MM-dd");
     } catch (Exception e) {
          LogWriter.error("日期转换异常");
     }           
     return null;    
 }
 }