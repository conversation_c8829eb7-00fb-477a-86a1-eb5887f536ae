package com.fuiou.dips.services;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.convert.ProjectConvertMapper;
import com.fuiou.dips.data.entity.CompensationStep;
import com.fuiou.dips.data.req.ProjectStageReq;
import com.fuiou.dips.enums.LockFlagEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.context.ApplicationContextKeeper;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.dipsdb.ProjectStageMapper;
import com.fuiou.dips.swt.utils.StrUtil;
import com.fuiou.dips.utils.CollectionUtils;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.PairMap;
import com.fuiou.dips.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.awt.print.PrinterJob;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 项目阶段服务
 *
 * <AUTHOR>
 */
@Service
public class ProjectStageService {

    @Resource
    private ProjectStageMapper projectStageMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageChangeService projectStageChangeService;
    @Resource
    private ProjectConvertMapper projectConvertMapper;
    @Resource
    private MsgService msgService;

    @LogAnnotation("项目阶段-插入阶段数据")
    public int insert(ProjectStage record) {
        if (StringUtil.isBlank(record.getStageSt())) {
            record.setStageSt(ProjectEnum.StageStEnum.NON_START.getState());
        }
        return projectStageMapper.insert(record);
    }

    @LogAnnotation("项目阶段-根据项目编号+商户号查询阶段列表")
    public List<ProjectStage> selectByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        return projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
    }

    @LogAnnotation("项目阶段-根据项目编号+商户号查询第一个阶段数据")
    public ProjectStage selectFirstStageByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        return projectStageMapper.selectFirstStageByProjectNoAndMchntCd(projectNo, mchntCd);
    }

    @LogAnnotation("项目阶段-编辑项目阶段")
    public void edit(String mchntCd, String projectNo, BigDecimal projectAmt, List<ProjectStageReq> stageListParams,
            String remark)
    {
        // 初始化参数
        List<ProjectStageReq> stageList = new ArrayList<>();
        for (int i = 0; i < stageListParams.size(); i++) {
            ProjectStageReq stage = stageListParams.get(i);
            stage.setMchntCd(mchntCd);
            stage.setProjectNo(projectNo);
            stage.setStageOrder(i + 1);
            stageList.add(stage);
        }

        // 校验参数
        validateStageData(projectNo, stageList, projectAmt);
        // 已知：项目
        Project project = projectService.queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
        // 已知：项目阶段
        List<ProjectStage> existingStages = getAndValidateExistingStages(projectNo, mchntCd);
        // 已知：阶段
        Map<String, ProjectStage> existingStageMaps = existingStages.stream().collect(
                Collectors.toMap(ProjectStage::getStageNo, stage -> stage));

        // 请求：全量的阶段
        List<ProjectStage> projectStages = projectConvertMapper.projectStageReqToProjectStageList(stageList);
        // 请求：带编号的阶段
        Map<String, ProjectStage> reqStageMaps = projectStages.stream().filter(
                s -> StringUtil.isNotBlank(s.getStageNo())).collect(
                Collectors.toMap(ProjectStage::getStageNo, stage -> stage));
        // 请求：left:可以编辑的阶段；right:可以删除的阶段
        PairMap<List<String>, List<String>> canOperatorStageNos = getCanOperatorStageNos(project, existingStages,
                reqStageMaps);
        // 请求：可删除的阶段
        List<String> right = canOperatorStageNos.getRight();
        // 请求：可编辑的阶段
        List<String> left = canOperatorStageNos.getLeft();
        boolean lockSuccess = false;
        try {
            // 加锁
            lockSuccess = isLockSuccess(mchntCd, projectNo, new ArrayList<>(existingStageMaps.keySet()));
            // 删除
            right.removeAll(reqStageMaps.keySet());
            if (CollUtil.isNotEmpty(right)) {
                int count = projectStageMapper.deleteByStageNo(projectNo, mchntCd, new HashSet<>(right));
                FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_DELETE_FAILURE, count == right.size());
            }
            // 新增+编辑
            projectStages.forEach(p -> {
                // 新增
                if (StrUtil.isBlank(p.getStageNo())) {
                    addNewStage(p, projectNo, mchntCd);
                    return;
                }
                // 更新
                if (!left.contains(p.getStageNo())) {
                    return;
                }
                ProjectStage projectStage = existingStageMaps.get(p.getStageNo());
                if (projectStage != null && isStageChanged(p, projectStage)) {
                    int count = projectStageMapper.updateByStageNo(p);
                    FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_UPDATE_FAILURE, count == 1);
                }
            });
            //  更新项目总金额
            projectService.updateProjectAmt(project, ProjectEnum.StageStEnum.ONGOING.getState(), projectAmt);
            // 记录变更日志
            insertStageChangeRecord(project.getProjectSt(), existingStages, projectNo, mchntCd,
                    projectConvertMapper.projectStageReqToProjectStageList(stageList),
                    StrUtil.isBlank(remark) ? "" : remark);
        } finally {
            unLocked(mchntCd, projectNo, lockSuccess, new ArrayList<>(existingStageMaps.keySet()));
        }
    }

    private void unLocked(String mchntCd, String projectNo, boolean lockSuccess, List<String> existingStageNos) {
        if (lockSuccess) {
            try {
                int unlockedCount = batchUpdateUnLock(mchntCd, projectNo, existingStageNos);
                LogWriter.info(
                        String.format("成功解锁 %s 个阶段，项目编号：%s，商户号：%s", unlockedCount, projectNo, mchntCd));
            } catch (Exception e) {
                LogWriter.info(String.format("解锁阶段失败，项目编号：%s，商户号：%s，错误信息：%s, %s", projectNo, mchntCd,
                        e.getMessage(), e));
            }
        }
    }

    private boolean isLockSuccess(String mchntCd, String projectNo, List<String> existingStageNos)
    {
        // 尝试给所有现有阶段加锁
        int lockedCount = batchUpdateLock(mchntCd, projectNo, existingStageNos);
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED, lockedCount != existingStageNos.size());
        LogWriter.info(String.format("成功加锁 %s 个阶段，项目编号：%s，商户号：%s", lockedCount, projectNo, mchntCd));
        return true;
    }

    @LogAnnotation("项目阶段-返回可以编辑的，可以删除的阶段编号")
    public PairMap<List<String>, List<String>> getCanOperatorStageNos(Project project,
            List<ProjectStage> existingStages, Map<String, ProjectStage> reqStageMaps)
    {
        if (StrUtil.isBlank(project.getCurrentStageNo())) {
            return PairMap.of(new ArrayList<>(), new ArrayList<>());
        }
        List<String> editResult = new ArrayList<>(), delResult = new ArrayList<>();
        AtomicReference<Integer> stageOrder = new AtomicReference<>();
        // 返回哪些阶段可以编辑，哪些阶段可以删除
        existingStages.stream().filter(s -> s.getStageNo().equals(project.getCurrentStageNo())).findAny().ifPresent(
                s -> {
                    stageOrder.set(s.getStageOrder());
                    BigDecimal stageAmt = s.getStageAmt();
                    BigDecimal stageActualAmt = s.getStageActualAmt();
                    String stageNo = s.getStageNo();
                    String stageSt = s.getStageSt();

                    // 大于当前阶段的,未开始的。小于当前阶段的不能动
                    List<String> nonStartStageNo = existingStages.stream().filter(
                            c -> c.getStageOrder() > stageOrder.get()).filter(
                            c -> ProjectEnum.StageStEnum.NON_START.getState().equals(c.getStageSt())).map(
                            ProjectStage::getStageNo).collect(Collectors.toList());
                    editResult.addAll(nonStartStageNo);
                    delResult.addAll(nonStartStageNo);

                    // 当前阶段不是进行中，就不用判断下面的了。
                    if (!ProjectEnum.StageStEnum.ONGOING.getState().equals(stageSt)) {
                        return;
                    }
                    // 当前阶段的，判断是否可以编辑，满足更新计划金额》=已收款金额即可。
                    ProjectStage projectStage = reqStageMaps.get(s.getStageNo());
                    if (projectStage != null) {
                        if (ObjUtil.notEqual(projectStage.getStageAmt(), stageAmt)) {
                            if (stageActualAmt != null && projectStage.getStageAmt().compareTo(stageActualAmt) >= 0) {
                                editResult.add(stageNo);
                            }
                        } else {
                            editResult.add(stageNo);
                        }
                    }
                    // 当前阶段，判断是否可删除，不可删除，删除要考虑一堆问题（是否第一阶段，是否金额已收齐，项目是不是要改成已完成等等）
                    // if (stageActualAmt == null || stageActualAmt.compareTo(BigDecimal.ZERO) == 0) {
                    //     delResult.add(stageNo);
                    // }
                });

        // 可以编辑的，需要跟reqStageMaps进行比较，编号不能小于当前阶段编号
        editResult.forEach(e -> {
            ProjectStage projectStage = reqStageMaps.get(e);
            if (projectStage == null) {
                return;
            }
            Integer stageOrder1 = projectStage.getStageOrder();
            String currentStageNo = project.getCurrentStageNo();
            if (projectStage.getStageNo().equals(currentStageNo)) {
                FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED.getCode(),
                        "当前阶段进行中，不可以调整顺序", !Objects.equals(stageOrder1, stageOrder.get()));
            } else {
                FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED.getCode(),
                        "阶段顺序调整时，不可以调整历史阶段", stageOrder1 > stageOrder.get());
            }
        });
        return PairMap.of(editResult, delResult);
    }

    /**
     * 验证阶段数据
     */
    private void validateStageData(String projectNo, List<ProjectStageReq> stageList, BigDecimal projectAmt) {
        // 检查项目编辑权限
        PairMap<Boolean, Boolean> pairMap = projectService.checkLoginUserPermission(projectNo);
        FUApiAssert.isTrue(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION, pairMap.getRight());
        // 验证阶段金额总和是否等于项目总金额
        validateStageAmounts(stageList, projectAmt);
        // 验证新阶段的必填字段
        validateRequiredFields(stageList);
        // 验证阶段顺序是否从1开始递增且不重复
        List<ProjectStage> projectStages = projectConvertMapper.projectStageReqToProjectStageList(stageList);
        validateStageOrder(projectStages);
    }

    /**
     * 获取并验证现有阶段信息
     */
    private List<ProjectStage> getAndValidateExistingStages(String projectNo, String mchntCd) {
        List<ProjectStage> existingStages = projectStageMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        validateStageLock(existingStages);
        return existingStages;
    }

    /**
     * 判断阶段是否发生变化（金额、排序、名称任一变化）
     */
    private boolean isStageChanged(ProjectStage newStage, ProjectStage existingStage) {
        // 比较金额
        boolean amountChanged = !Objects.equals(newStage.getStageAmt(), existingStage.getStageAmt());
        // 比较排序
        boolean orderChanged = !Objects.equals(newStage.getStageOrder(), existingStage.getStageOrder());
        // 比较名称
        boolean nameChanged = !Objects.equals(newStage.getStageName(), existingStage.getStageName());
        return amountChanged || orderChanged || nameChanged;
    }

    /**
     * 新增阶段
     */
    @LogAnnotation(value = "项目阶段",
                   methodName = "项目阶段编辑时新增阶段")
    public void addNewStage(ProjectStage projectStage, String projectNo, String mchntCd) {
        projectStage.setProjectNo(projectNo);
        projectStage.setMchntCd(mchntCd);
        projectStage.setStageNo(generateStageNo());
        insert(projectStage);
    }

    private void validateStageLock(List<ProjectStage> existingStages) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NOT_EXIST, existingStages);
        existingStages.stream().filter(s -> s.getLockFlag().equals(LockFlagEnum.LOCK.getState())).findAny().ifPresent(
                s -> FUApiAssert.failure(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED));
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "插入阶段变更记录")
    public void insertStageChangeRecord(String projectSt, List<ProjectStage> existingStages, String projectNo,
            String mchntCd, List<ProjectStage> stageList, String remark)
    {
        // 存在，按照编号处理
        Map<String, ProjectStage> existingMap = Optional.ofNullable(existingStages).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(ProjectStage::getStageNo, s -> s));

        // 提交，有编号的阶段列表
        Map<String, ProjectStage> updateStageMaps = Optional.ofNullable(stageList).orElse(Collections.emptyList())
                .stream().filter(s -> StringUtil.isNotBlank(s.getStageNo())).collect(
                        Collectors.toMap(ProjectStage::getStageNo, s -> s));
        // 提交，没编号的，新增数据
        List<ProjectStage> addStageLists = Optional.ofNullable(stageList).orElse(Collections.emptyList()).stream()
                .filter(s -> StringUtil.isBlank(s.getStageNo())).collect(Collectors.toList());
        // 收集变更信息
        List<String> changes = new ArrayList<>();
        addStageLists.forEach(stage -> changes.add(
                String.format("新增阶段%s: 名称=%s, 金额=%s", stage.getStageOrder(), stage.getStageName(),
                        stage.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))));
        List<Map<String, Object>> changedMapList = new ArrayList<>();
        // 比较修改的阶段
        existingMap.forEach((order, oldStage) -> {
            // 删除
            if (!updateStageMaps.containsKey(order)) {
                changes.add(String.format("删除阶段：%s ，编号=%s ，名称=%s ，金额=%s", oldStage.getStageOrder(),
                        oldStage.getStageNo(), oldStage.getStageName(),
                        oldStage.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
                return;
            }
            // 更新
            ProjectStage newStage = updateStageMaps.get(order);
            if (!Objects.equals(oldStage.getStageName(), newStage.getStageName())) {
                changes.add(String.format("阶段：%s ，名称: %s -> %s", oldStage.getStageOrder(), oldStage.getStageName(),
                        newStage.getStageName()));
            }
            if (oldStage.getStageAmt() != null && newStage.getStageAmt() != null && oldStage.getStageAmt().compareTo(
                    newStage.getStageAmt()) != 0)
            {
                changes.add(String.format("阶段：%s ，金额: %s -> %s", oldStage.getStageOrder(),
                        oldStage.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP),
                        newStage.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
                HashMap<String, Object> notifyMap = MapUtil.newHashMap();
                notifyMap.put("oldState", oldStage);
                notifyMap.put("newState", newStage);
                changedMapList.add(notifyMap);
            }
            if (!Objects.equals(oldStage.getStageOrder(), newStage.getStageOrder())) {
                changes.add(String.format("阶段：%s ，顺序: %s -> %s", oldStage.getStageName(), oldStage.getStageOrder(),
                        newStage.getStageOrder()));
            }
        });
        Project project = projectService.queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
        if (!project.getProjectSt().equals(projectSt)) {
            ProjectStage projectStage = addStageLists.get(0);
            String format = "项目状态发生变更：已完成 -> 进行中";
            if (projectStage != null) {
                format += String.format("；并设置阶段：%s ，%s 为进行中阶段。", projectStage.getStageOrder(),
                        projectStage.getStageName());
            }
            changes.add(format);
        }
        // 插入变更记录
        String content = changes.isEmpty() ? "无变更" : String.join("\n", changes);
        String submitUser = LoginConstat.getLoginToken().getFullName() + "（" + msgService.getLoginId() + "）";
        projectStageChangeService.insert(projectNo, mchntCd,
                submitUser + "操作了项目阶段编辑。\n\n" + content + "\n\n备注内容：" + remark);
        if (CollUtil.isNotEmpty(changedMapList)) {
            changedMapList.forEach(change -> msgService.addProjectAmtChangeMsg((ProjectStage) change.get("oldState"),
                    (ProjectStage) change.get("newState"), remark));
        }
    }

    /**
     * 验证阶段金额总和是否等于项目总金额
     */
    private void validateStageAmounts(List<ProjectStageReq> stageList, BigDecimal projectAmt) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, stageList);
        BigDecimal totalStageAmt = stageList.stream().map(ProjectStageReq::getStageAmt).filter(Objects::nonNull).reduce(
                BigDecimal.ZERO, BigDecimal::add);
        FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_AMOUNTS_MUST_EQUAL_TOTAL_PROJECT_AMOUNTS,
                totalStageAmt.compareTo(projectAmt) == 0);
    }

    /**
     * 添加的阶段金额为必填，否则提示"请完成页面设置"
     */
    private void validateRequiredFields(List<ProjectStageReq> stageList) {
        for (ProjectStageReq stage : stageList) {
            FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY,
                    stage.getStageOrder() == null || !StringUtils.hasText(stage.getStageName()));
            FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY,
                    stage.getStageAmt() == null || stage.getStageAmt().compareTo(BigDecimal.ZERO) <= 0);
        }
    }

    /**
     * 验证阶段顺序是否从1开始递增且不重复
     */
    private void validateStageOrder(List<ProjectStage> stageList) {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, stageList);
        // 按照阶段顺序排序
        List<Integer> orders = stageList.stream().sorted(
                Comparator.comparing(ProjectStage::getStageOrder, Comparator.nullsLast(Comparator.naturalOrder()))).map(
                ProjectStage::getStageOrder).collect(Collectors.toList());
        FUApiAssert.isTrue(ResponseCodeEnum.PROJECT_STAGE_ORDER_MUST_START_FROM_ONE_AND_INCREASE,
                isValidSequence(orders));
    }

    private boolean isValidSequence(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        return IntStream.range(0, list.size()).allMatch(i -> list.get(i) == i + 1);
    }

    private String generateStageNo() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    @LogAnnotation("项目阶段-批量保存项目阶段")
    public void batchSaveStages(String projectNo, String mchntCd, List<ProjectStage> stages) {
        // 验证阶段顺序是否从1开始递增且不重复
        validateStageOrder(stages);

        // 按阶段顺序排序
        List<ProjectStage> sortedStages = stages.stream().sorted(
                        Comparator.comparing(ProjectStage::getStageOrder,
                                Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        // 保存所有阶段
        IntStream.range(0, sortedStages.size()).forEach(i -> {
            ProjectStage stage = sortedStages.get(i);
            stage.setProjectNo(projectNo);
            stage.setMchntCd(mchntCd);
            // 生成阶段编号
            stage.setStageNo(generateStageNo());
            // 第一个阶段进行中，其他未开始
            stage.setStageSt(
                    i == 0 ? ProjectEnum.StageStEnum.ONGOING.getState() : ProjectEnum.StageStEnum.NON_START.getState());
            insert(stage);
        });
    }

    @LogAnnotation("项目阶段-增减金额")
    public int editAmt(ProjectStageReq projectStageReq) {
        // 检查项目编辑权限
        PairMap<Boolean, Boolean> pairMap = projectService.checkLoginUserPermission(projectStageReq.getProjectNo());
        FUApiAssert.isTrue(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION, pairMap.getRight());
        ProjectStage stage = queryProjectStage(projectStageReq.getProjectNo(), projectStageReq.getMchntCd(),
                projectStageReq.getStageNo());
        // 验证
        vaildParamsAndAmtAndLock(stage, projectStageReq);
        // 计算金额差值
        BigDecimal diffAmt = projectStageReq.getStageAmt().subtract(stage.getStageAmt());
        // 更新项目总金额
        Project project = projectService.queryProjectByProjectNoAndMchntCd(projectStageReq.getProjectNo(),
                projectStageReq.getMchntCd());
        BigDecimal newProjectAmt = project.getProjectAmt().add(diffAmt);
        // 更新阶段金额
        ProjectStage updateStage = new ProjectStage();
        updateStage.setRowId(stage.getRowId());
        updateStage.setStageAmt(projectStageReq.getStageAmt());
        updateStage.setReserved1(projectStageReq.getReserved1());
        List<String> stageNos = Collections.singletonList(projectStageReq.getStageNo());
        // 尝试给当前阶段加锁
        boolean lockSuccess = false;
        lockSuccess = isLockSuccess(projectStageReq.getMchntCd(), projectStageReq.getProjectNo(), stageNos);
        int result;
        try {
            projectService.updateProjectAmt(projectStageReq.getProjectNo(), stage.getMchntCd(), newProjectAmt);
            result = projectStageMapper.updateByPrimaryKey(updateStage);
        } catch (Exception e) {
            // 执行失败，补偿操作
            updateStage.setStageAmt(stage.getStageAmt());
            updateStage.setReserved1(stage.getReserved1());
            compensateEditAmtOperation(projectStageReq.getProjectNo(), projectStageReq.getMchntCd(),
                    project.getProjectAmt(), updateStage);
            throw e;
        } finally {
            unLocked(projectStageReq.getMchntCd(), projectStageReq.getProjectNo(), lockSuccess, stageNos);
        }
        // 插入变更记录
        String typeTxt = diffAmt.compareTo(BigDecimal.ZERO) > 0 ? "增加" : "减少";
        String content = String.format("项目总金额: %s -> %s，阶段%s金额: %s -> %s，本期%s金额：%s ，备注: %s ",
                project.getProjectAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP),
                newProjectAmt.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP), stage.getStageOrder(),
                stage.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP),
                projectStageReq.getStageAmt().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP), typeTxt,
                diffAmt.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP), projectStageReq.getReserved1());
        String submitUser = LoginConstat.getLoginToken().getFullName() + "（" + msgService.getLoginId() + "）";
        projectStageChangeService.insert(stage.getProjectNo(), stage.getMchntCd(),
                submitUser + "操作了项目增减金额。\n\n" + content);
        msgService.addProjectAmtChangeMsg(stage, updateStage, projectStageReq.getReserved1());
        return result;
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "增减金额-失败补偿操作")
    public void compensateEditAmtOperation(String projectNo, String mchntCd, BigDecimal oldProjectAmt,
            ProjectStage updateStage)
    {
        LogWriter.info(String.format("开始执行增减金额失败补偿操作，项目编号：%s，商户号：%s", projectNo, mchntCd));
        List<CompensationStep> steps = Arrays.asList(new CompensationStep("项目数据更新回滚",
                        () -> projectService.updateProjectAmt(projectNo, mchntCd, oldProjectAmt),
                        String.format("projectNo=%s,mchntCd=%s,oldProjectAmt=%s", projectNo, mchntCd, oldProjectAmt)),
                new CompensationStep("项目阶段数据更新回滚", () -> projectStageMapper.updateByPrimaryKey(updateStage),
                        String.format("rowId=%s,stageAmt=%s,reserved1=%s", updateStage.getRowId(),
                                updateStage.getStageAmt(), updateStage.getReserved1())));
        projectService.runCompensate(steps);
    }

    @LogAnnotation("项目阶段-查询阶段详情")
    public ProjectStage queryProjectStage(String projectNo, String mchntCd, String stageNo) {
        return projectStageMapper.selectStageByProjectNoAndMchntCdAndNo(projectNo, mchntCd, stageNo);
    }

    /**
     * 验证阶段编辑参数非空
     *
     * @param projectStageReq 阶段请求参数
     */
    private void vaildParamsAndAmtAndLock(ProjectStage stage, ProjectStageReq projectStageReq) {
        FUApiAssert.notNull(ResponseCodeEnum.PARAM_ERROR, projectStageReq);
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "项目编号不能为空",
                projectStageReq.getProjectNo());
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "商户号不能为空",
                projectStageReq.getMchntCd());
        FUApiAssert.isNotBlank(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "阶段编号不能为空",
                projectStageReq.getStageNo());
        FUApiAssert.isFalse(ResponseCodeEnum.FIELD_EMPTY_ERROR.getCode(), "阶段金额不能为空，且为正整数",
                projectStageReq.getStageAmt() == null || projectStageReq.getStageAmt().compareTo(BigDecimal.ZERO) <= 0);
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED,
                ProjectEnum.StageStEnum.COMPLETED.getState().equals(stage.getStageSt()));
        // 新阶段金额不能小于已收款金额
        boolean con = stage.getStageActualAmt() != null && stage.getStageActualAmt().compareTo(
                projectStageReq.getStageAmt()) > 0;
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_PAYED_AND_AMT_NON_MODIFIED, con);
        FUApiAssert.equals(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED, stage.getLockFlag(),
                LockFlagEnum.UNLOCK.getState());
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_STAGE_AMOUNT_CHANGE_FAIL,
                stage.getStageAmt().equals(projectStageReq.getStageAmt()));
    }

    @LogAnnotation("项目阶段-根据项目编号+商户号删除项目阶段")
    public void deleteByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        // 根据项目编号删除项目阶段数据
        projectStageMapper.deleteByProjectNoAndMchntCd(projectNo, mchntCd);
    }

    @LogAnnotation("项目阶段-验证项目阶段可修改操作")
    public static void validProjectStage(ProjectStage projectStage) {
        if (projectStage == null) {
            throw new FUException(ResponseCodeEnum.PROJECT_NON_EXIST);
        }
        if (!ProjectEnum.ProjectStEnum.ONGOING.getState().equals(projectStage.getStageSt())) {
            LogWriter.info(
                    String.format("项目阶段状态为：%s，项目锁定状态为：%s，不允许执行此操作", projectStage.getStageSt(),
                            projectStage.getLockFlag()));
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
        if (!LockFlagEnum.UNLOCK.getState().equals(projectStage.getLockFlag())) {
            LogWriter.info(
                    String.format("项目阶段状态为：%s，项目锁定状态为：%s，不允许执行此操作", projectStage.getStageSt(),
                            projectStage.getLockFlag()));
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "查询项目阶段列表")
    public List<ProjectStage> queryProjectStages(String mchntCd, List<String> projectNos, List<String> stageSts) {
        return projectStageMapper.selectByProjectNos(mchntCd, projectNos, stageSts);
    }


    /***
     *汇总项目实收金额
     * @param mchntCd :
     * @param projectNos :
     * @param stageSts :
     * @return: java.math.BigDecimal
     * @Author: Joker
     * @Date: 2025/5/21 21:50
     */
    public BigDecimal sumProjectPaidInAmt(String mchntCd, List<String> projectNos, List<String> stageSts) {

        List<ProjectStage> projectStages = ApplicationContextKeeper.getBean(this.getClass()).queryProjectStages(mchntCd,
                projectNos, stageSts);
        if (CollectionUtils.isEmpty(projectStages)) {
            return BigDecimal.ZERO;
        }
        BigDecimal bigDecimal = BigDecimal.ZERO;
        projectStages.parallelStream().filter(Objects::nonNull).filter(
                projectStage -> projectStage.getStageActualAmt() != null).forEach(projectStage -> {
            bigDecimal.add(sumStagePaidInAmt(projectStage));
        });

        return bigDecimal;
    }

    /**
     * 汇总阶段实收金额
     *
     * @param projectStage :
     * @return: java.math.BigDecimal
     * @Author: Joker
     * @Date: 2025/5/21 21:49
     */

    public static BigDecimal sumStagePaidInAmt(ProjectStage projectStage) {

        if (projectStage == null) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal("0").add(
                projectStage.getStageActualAmt() == null ? BigDecimal.ZERO : projectStage.getStageActualAmt()).subtract(
                projectStage.getRefundAmt() == null ? BigDecimal.ZERO : projectStage.getRefundAmt());
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段锁定状态")
    public int updateLock(String mchntCd, String projectNo, String stageNo) {
        return projectStageMapper.updateLock(mchntCd, projectNo, stageNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段锁定状态")
    public void updateUnLock(String mchntCd, String projectNo, String stageNo) {
        projectStageMapper.updateUnLock(mchntCd, projectNo, stageNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "批量加锁项目阶段")
    public int batchUpdateLock(String mchntCd, String projectNo, List<String> stageNos) {
        if (CollUtil.isEmpty(stageNos)) {
            return 0;
        }
        return projectStageMapper.batchUpdateLock(mchntCd, projectNo, stageNos);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "批量解锁项目阶段")
    public int batchUpdateUnLock(String mchntCd, String projectNo, List<String> stageNos) {
        if (CollUtil.isEmpty(stageNos)) {
            return 0;
        }
        return projectStageMapper.batchUpdateUnLock(mchntCd, projectNo, stageNos);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "查询项目阶段list")
    public List<ProjectStage> selectByMchntCdAndProjectNo(String mchntCd, String projectNo) {
        return projectStageMapper.selectByMchntCdAndProjectNo(mchntCd, projectNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "更新项目阶段")
    public int updateStageForTxnWithoutUnLock(ProjectStage projectStageThisTxn) {
        return projectStageMapper.updateStageForTxnWithoutUnLock(projectStageThisTxn);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "开启下一阶段状态")
    public int startStageForTxn(String mchntCd, String projectNo, String stageNo) {
        return projectStageMapper.startStageForTxn(mchntCd, projectNo, stageNo);
    }

    @LogAnnotation(value = "项目阶段",
                   methodName = "查询项目阶段详情并校验状态")
    public ProjectStage queryProjectStageValidStatus(String projectNo, String mchntCd, String stageNo) {
        ProjectStage projectStage = projectStageMapper.selectStageByProjectNoAndMchntCdAndNo(projectNo, mchntCd,
                stageNo);
        ProjectStageService.validProjectStage(projectStage);
        return projectStage;
    }
}
