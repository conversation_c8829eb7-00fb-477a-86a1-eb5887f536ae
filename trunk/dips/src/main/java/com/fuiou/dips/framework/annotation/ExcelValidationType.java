package com.fuiou.dips.framework.annotation;

/**
 * Excel校验类型枚举
 * 
 * <AUTHOR>
 */
public enum ExcelValidationType {
    
    /**
     * 非空校验
     */
    NOT_NULL,
    
    /**
     * 字符串长度校验
     */
    STRING_LENGTH,
    
    /**
     * Long类型转换校验
     */
    LONG_PARSE,
    
    /**
     * 终止解析校验
     */
    STOP_ANALYZE,
    
    /**
     * 日期时间转换校验
     */
    LOCAL_DATE_TIME_PARSE,
    
    /**
     * 日期转换校验
     */
    LOCAL_DATE_PARSE,
    
    /**
     * 整数转换校验
     */
    INTEGER_PARSE,
    
    /**
     * 双精度浮点数转换校验
     */
    DOUBLE_PARSE,
    
    /**
     * 正则表达式校验
     */
    REGEX_PATTERN,
    
    /**
     * 数值范围校验
     */
    NUMBER_RANGE,
    
    /**
     * 枚举值校验
     */
    ENUM_VALUE
}
