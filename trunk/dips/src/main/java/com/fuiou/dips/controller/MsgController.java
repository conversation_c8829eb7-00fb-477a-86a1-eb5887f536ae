package com.fuiou.dips.controller;

import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.entity.MessageNotificationBean;
import com.fuiou.dips.data.req.PageMsgListReq;
import com.fuiou.dips.data.resp.PageRespBase;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.services.MsgService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 信息
 */
@RestController
@RequestMapping("/msg")
public class MsgController {
    @Resource
    private MsgService msgService;

    /**
     * 获取消息列表
     * @param msgListReq
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseEntity<PageRespBase<MessageNotificationBean>> getMsgList(@Valid @RequestBody PageMsgListReq msgListReq) {
        return ResponseEntityFactory.ok(msgService.getMsgList(msgListReq));
    }


    /**
     * 标记某条消息为已读
     * @param msgNo 消息编号（URL 路径参数）
     */
    @GetMapping("/{msgNo}/read")
    public ResponseEntity markMessageAsRead(@PathVariable String msgNo) {
        msgService.markMessageAsRead(msgNo);
        return ResponseEntityFactory.ok();
    }

}
