package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.data.resp.CountProjectResp;
import com.fuiou.dips.data.resp.PageProjectResp;
import com.fuiou.dips.persist.beans.Project;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * 项目表
 *
 * <AUTHOR>
 * @description 针对表【t_dips_project(装修通项目表)】的数据库操作Mapper
 */
public interface ProjectMapper {

    int insert(Project record);

    Project selectByProjectNoAndMchntCd(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd);

    List<PageProjectResp> selectProjectsWithStageAndCustomer(@Param("mchntCd") String mchntCd,@Param("customerName") String customerName,@Param("phone") String phone,
                                                             @Param("projectSt") String projectSt,@Param("storeIds") List<String> storeIds,@Param("projectNos") List<String> projectNos);

    CountProjectResp countProjectWithStatus(@Param("mchntCd") String mchntCd,
                                            @Param("storeIds") List<String> storeIds,
                                            @Param("customerName") String customerName,
                                            @Param("phone") String phone,@Param("projectNos") List<String> projectNos);

    List<Project> selectProjectNosByEmployee(@Param("mchntCd") String mchntCd,@Param("employeeLoginId") String employeeLoginId);
    List<String> selectProjectStoreIds(@Param("mchntCd") String mchntCd);

    List<Project> selectProjectList(@Param("mchntCd") String mchntCd, @Param("storeIds") List<String> storeIds,
            @Param("startDate") Timestamp startDate, @Param("endDate") Timestamp endDate,@Param("projectSt") String projectSt);

    int updateByPrimaryKey(Project record);

    int updateProjectStatus(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd,
            @Param("projectSt") String projectSt);

    List<Project> selectByMchntCdAndPhone(@Param("mchntCd") String mchntCd, @Param("phone") String phone);

    void updateByProjectNoAndMchntCd(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd,
            @Param("projectAmt") BigDecimal projectAmt);

    void updateLockFlag(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd,
            @Param("lockFlag") String lockFlag, @Param("state") String state);

    // 根据项目编号和商户编号删除
    void deleteByProjectNoAndMchntCd(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd);

    int updateProjectForTxn(Project record);

    String selectReserved1ByStoreId(@Param("storeId") String storeId, @Param("mchntCd") String mchntCd);

}
