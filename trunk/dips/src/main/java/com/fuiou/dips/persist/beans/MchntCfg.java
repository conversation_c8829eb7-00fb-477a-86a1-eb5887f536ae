package com.fuiou.dips.persist.beans;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * 装修通商户配置表
 *
 * <AUTHOR>
 * @TableName t_dips_mchnt_cfg
 */
public class MchntCfg implements Serializable {
    /**
     * 主键
     */
    private Long rowId;

    /**
     * 开通状态 1 开启 0 未开启
     */
    private String openStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 商户号
     */
    private String mchntCd;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间（最后操作时间）
     */
    @JsonFormat(timezone = "GMT+8",
                pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 默认绑定终端号
     */
    private String defaultTermId;

    /**
     * 备注1
     */
    private String reserved1;

    /**
     * 备注2
     */
    private String reserved2;

    /**
     * 备注3
     */
    private String reserved3;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getRowId() {
        return rowId;
    }

    /**
     * 主键
     */
    public void setRowId(Long rowId) {
        this.rowId = rowId;
    }

    /**
     * 开通状态 1 开启 0 未开启
     */
    public String getOpenStatus() {
        return openStatus;
    }

    /**
     * 开通状态 1 开启 0 未开启
     */
    public void setOpenStatus(String openStatus) {
        this.openStatus = openStatus;
    }

    /**
     * 操作人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作人
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 商户号
     */
    public String getMchntCd() {
        return mchntCd;
    }

    /**
     * 商户号
     */
    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间（最后操作时间）
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 默认绑定终端号
     */
    public String getDefaultTermId() {
        return defaultTermId;
    }

    /**
     * 默认绑定终端号
     */
    public void setDefaultTermId(String defaultTermId) {
        this.defaultTermId = defaultTermId;
    }

    /**
     * 备注1
     */
    public String getReserved1() {
        return reserved1;
    }

    /**
     * 备注1
     */
    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    /**
     * 备注2
     */
    public String getReserved2() {
        return reserved2;
    }

    /**
     * 备注2
     */
    public void setReserved2(String reserved2) {
        this.reserved2 = reserved2;
    }

    /**
     * 备注3
     */
    public String getReserved3() {
        return reserved3;
    }

    /**
     * 备注3
     */
    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }
}
