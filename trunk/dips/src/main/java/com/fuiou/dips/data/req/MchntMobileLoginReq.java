package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.Size;

public class MchntMobileLoginReq {

    @NotBlank(message = "请填写手机号")
    @Size(min = 11, max = 11, message = "手机号格式不正确")
    private String phone;

    @NotBlank(message = "请填写短信验证码")
    @Size(min = 6, max = 6, message = "短信验证码格式不正确")
    private String smsCode;

    @NotBlank(message = "安全验证错误")
    private String vcode; //安全验证码， 阿里云验证码2.0对应其captchaVerifyParam


    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getVcode() {
        return vcode;
    }

    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}
