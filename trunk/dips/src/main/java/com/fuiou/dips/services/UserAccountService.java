package com.fuiou.dips.services;

import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.convert.MchntConvertMapper;
import com.fuiou.dips.convert.ProjectConvertMapper;
import com.fuiou.dips.data.entity.ProjectSimple;
import com.fuiou.dips.data.req.*;
import com.fuiou.dips.data.resp.*;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.UserTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.*;
import com.fuiou.dips.persist.dipsdb.CustomerMapper;
import com.fuiou.dips.persist.dipsdb.DipsUserInfMapper;
import com.fuiou.dips.persist.dipsdb.ProjectMapper;
import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class UserAccountService {

    @Resource
    private SmsService smsService;

    @Resource
    private CaptchaVerifyService captchaVerifyService;

    @Resource
    private TokenService tokenService;

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private MchntService mchntService;

    @Resource
    private ProjectConvertMapper projectConvertMapper;

    @Resource
    private MchntConvertMapper mchntConvertMapper;

    @Resource
    private DipsUserInfMapper dipsUserInfMapper;


    public UserAccountLoginResp login(UserAccountLoginReq req) {
        //安全验证
        captchaVerifyService.verify(req.getVcode(), req.getPhone(), req.getPhone());
        //校验短信验证码
        checkSmsCode(req);
        //初始化返回信息
        UserAccountLoginResp resp = initResp(req);
        //获取用户信息
        List<Customer> customerList = getCustomerByPhone(req.getPhone());
        //商户（公司）信息
        setMchntList(resp, customerList);
        //项目信息
        setProjectRespList(resp);
        //保存用户信息
        saveUserInfo(req, resp, customerList);
        //保存用户缓存信息
        setLoginInfoRedis(req, resp);
        return resp;
    }

    /**
     * 选择商户号 并 保存用户信息表默认商户号
     * @param req
     */
    public void chooseMchntAndSet(MchntChooseForUserReq req){
        LoginResp loginInfo = LoginConstat.getLoginToken();
        String userType = loginInfo.getUserType();
        DipsUserInf userInf = dipsUserInfMapper.selectByLoginIdAndType(loginInfo.getLoginId(), userType);
        //校验用户信息
        checkUser(userInf);
        //获取用户信息
        Customer customer = getCustomerByPhoneAndMchnt(loginInfo.getLoginId(), req.getMchntCd());
        //獲取商戶緩存信息
        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(req.getMchntCd());
        //保存登录缓存信息
        setLoginMchntInfoRedis(insMchntCacheData, loginInfo, req);
        //保存用户信息表默认商户号
        saveUserForChooseMchnt(req, userInf, customer);
    }

    private void checkUser(DipsUserInf userInf){
        if (userInf == null){
            LogWriter.error(this, "用户不存在");
            throw new FUException(ResponseCodeEnum.USER_NOT_EXIST);
        }
    }

    private void setLoginMchntInfoRedis(InsMchntCacheData insMchntCacheData, LoginResp loginInfo, MchntChooseForUserReq req){
        try {
            MchntInfo info = new MchntInfo();
            BeanUtils.copyProperties(info, insMchntCacheData);
            info.setLoginId(loginInfo.getLoginId());
            loginInfo.setMchntInfo(info);
            loginInfo.setAccountLogin(true);
            tokenService.setRedisInfo(loginInfo);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户登录缓存异常", e);
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
        }
    }


    private void checkSmsCode(UserAccountLoginReq req){
        ResponseEntity res = smsService.validateSmsCode(req.getPhone(), req.getSmsCode());
        if(!ResponseCodeEnum.SUCCESS.getCode().equals(res.getCode())){
            throw new FUException(res.getCode(), res.getMsg());
        }
    }

    private UserAccountLoginResp initResp(UserAccountLoginReq req){
        UserAccountLoginResp resp = new UserAccountLoginResp();
        resp.setUserType(UserTypeEnum.CUSTOMER.getCode());
        resp.setMobile(req.getPhone());
        return resp;
    }

    private void setMchntList(UserAccountLoginResp resp, List<Customer> customerList){
        for (Customer customer : customerList) {
            if(StringUtils.isBlank(customer.getMchntCd())){
                continue;
            }
            InsMchntCacheData mchnt = mchntService.queryMchntInfo(customer.getMchntCd());
            if (mchnt == null){
                continue;
            }
            MchntInfo mchntInfo = mchntConvertMapper.mchntCacheToMchntInfo(mchnt);
            mchntInfo.setLoginId(resp.getMobile());
            resp.getMchntList().add(mchntInfo);
        }
    }

    private List<Customer> getCustomerByPhone(String phone){
        List<Customer> customerList = customerMapper.selectListByPhone(phone);
        if(customerList == null || customerList.size() == 0){
            throw new FUException(ResponseCodeEnum.CUSTOMER_NOT_EXIST);
        }
        return customerList;
    }

    private Customer getCustomerByPhoneAndMchnt(String phone, String mchntCd){
        Customer customer = customerMapper.selectByPhoneAndMchntCd(mchntCd, null, phone);
        if(customer == null){
            throw new FUException(ResponseCodeEnum.CUSTOMER_NOT_EXIST);
        }
        return customer;
    }

    private void setProjectRespList(UserAccountLoginResp resp){
        if(resp.getMchntList().size() != 1){
            return;
        }
        resp.getProjectList().addAll(getProjectList(resp.getMchntList().get(0).getMchntCd()));
    }

    public List<ProjectSimple> getProjectList(String mchntCd) {
        LoginResp loginInfo = LoginConstat.getLoginToken();
        List<ProjectSimple> list = new ArrayList<ProjectSimple>();
        List<Project> projectList = projectMapper.selectByMchntCdAndPhone(mchntCd, loginInfo.getLoginId());
        if(projectList == null || projectList.isEmpty()){
            return list;
        }
        for (Project project : projectList){
            list.add(projectConvertMapper.projectToProjectSimple(project));
        }
        return list;
    }

    private void saveUserInfo(UserAccountLoginReq req, UserAccountLoginResp resp, List<Customer> customerList){
        try {
            DipsUserInf userInf = dipsUserInfMapper.selectByLoginIdAndType(req.getPhone(), resp.getUserType());
            if(userInf != null) {
                userInf.setMobile(req.getPhone());
                setUserInfoCommonValues(userInf, resp, customerList);
                dipsUserInfMapper.update(userInf);
                return;
            }
            userInf = new DipsUserInf();
            userInf.setLoginId(req.getPhone());
            userInf.setMobile(req.getPhone());
            userInf.setUserType(resp.getUserType());
            setUserInfoCommonValues(userInf, resp, customerList);
            dipsUserInfMapper.insert(userInf);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户信息异常", e);
            throw new FUException(ResponseCodeEnum.LOGIN_FAIL);
        }
    }

    private void setUserInfoCommonValues(DipsUserInf userInf, UserAccountLoginResp resp, List<Customer> customerList){
        userInf.setOpenId(LoginConstat.getLoginToken().getOpenid());
        userInf.setLastLoginTime(new Date());
        userInf.setUpdateTime(new Date());
        if(resp.getMchntList() != null && resp.getMchntList().size() == 1){
            //如果用户没有多商户，则添加为默认商户
            userInf.setMchntCd(resp.getMchntList().get(0).getMchntCd());
        }
        if(customerList.size() == 1){
            //客户姓名
            resp.setFullName(customerList.get(0).getCustomerName());
            userInf.setFullName(customerList.get(0).getCustomerName());
        }
    }

    private void setLoginInfoRedis(UserAccountLoginReq req, UserAccountLoginResp resp){
        if(resp.getMchntList() == null){
            return;
        }
        LoginResp loginInfo = LoginConstat.getLoginToken();
        try {
            if(resp.getMchntList().size() == 1){
                loginInfo.setMchntInfo(resp.getMchntList().get(0));
                loginInfo.setAccountLogin(true);
            }
            loginInfo.setUserType(resp.getUserType());
            loginInfo.setRelateStoreList(new ArrayList<>());
            loginInfo.setEmployeeRoleType("");
            loginInfo.setLoginId(req.getPhone());
            loginInfo.setMobile(req.getPhone());
            loginInfo.setFullName(resp.getFullName());
            tokenService.setRedisInfo(loginInfo);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户登录缓存异常", e);
            throw new FUException(ResponseCodeEnum.ACCOUNT_HAS_NOT_LOGIN_ERROR);
        }
    }

    private void saveUserForChooseMchnt(MchntChooseForUserReq req, DipsUserInf userInf, Customer customer){
        //保存用户默认商户号
        userInf.setMchntCd(req.getMchntCd());
        userInf.setFullName(customer.getCustomerName());
        dipsUserInfMapper.update(userInf);
    }
}
