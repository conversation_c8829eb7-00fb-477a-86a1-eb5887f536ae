package com.fuiou.dips.config;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Objects;

/**
 * RequestBody参数处理，自动填充商户编号
 *
 * <AUTHOR>
 */
@ControllerAdvice
public class RequestBodyMchntCdAdvice extends RequestBodyAdviceAdapter {

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
            Class<? extends HttpMessageConverter<?>> converterType)
    {
        // 只处理controller包下的请求
        return methodParameter.getContainingClass().getPackage().getName().contains("controller");
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
            Class<? extends HttpMessageConverter<?>> converterType)
    {
        try {
            // 检查对象是否有mchntCd字段
            Field[] fields = body.getClass().getDeclaredFields();
            boolean hasMchntCdField = Arrays.stream(fields).anyMatch(field -> "mchntCd".equals(field.getName()));

            if (hasMchntCdField) {
                // 通过反射设置mchntCd的值
                Field mchntCdField = body.getClass().getDeclaredField("mchntCd");
                mchntCdField.setAccessible(true);

                // 从登录token中获取商户号，自动注入对象中
                if (LoginConstat.getLoginToken() != null && LoginConstat.getLoginToken().getMchntInfo() != null) {
                    String mchntCd = LoginConstat.getLoginToken().getMchntInfo().getMchntCd();

                    Object mchntCdReq = mchntCdField.get(body);
                    // 检查请求中的商户号是否有效且与登录商户号不一致
                    if (Objects.nonNull(mchntCdReq) &&
                            (mchntCdReq instanceof String) &&
                            !((String) mchntCdReq).trim().isEmpty() &&
                            Objects.nonNull(mchntCd) &&
                            !mchntCd.equals(mchntCdReq.toString().trim()))
                    {
                        LogWriter.error(String.format("商户号不一致，请求商户号：%s，登录商户号：%s", mchntCdReq, mchntCd));
                        throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
                    }
                    mchntCdField.set(body, mchntCd);
                }
            }
        } catch (Exception e) {
            // 发生异常时记录日志但不影响请求处理
            LogWriter.error("自动填充商户编号失败", e);
        }
        return body;
    }
}