package com.fuiou.dips.persist.beans.tpay;

/**
 * 商家可配置代金券 WPOS返回
 */


public class CashCouponRes {
	
	private String activity_id;
	private String amount;
	private String merchant_contribute;
	private String name;
	private String other_contribute;
	private String promotion_id;
	private String scope;
	private String type;
	private String wxpay_contribute;
	
	
	private String a;	//代金卷金额
	private String n;	//代金卷名称
	private String p;	//代金卷ID
	private String t;	//代金卷类型
	
	
	public String getT() {
		return t;
	}
	public void setT(String t) {
		this.t = t;
	}
	public String getActivity_id() {
		return activity_id;
	}
	public void setActivity_id(String activity_id) {
		this.activity_id = activity_id;
	}
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getMerchant_contribute() {
		return merchant_contribute;
	}
	public void setMerchant_contribute(String merchant_contribute) {
		this.merchant_contribute = merchant_contribute;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOther_contribute() {
		return other_contribute;
	}
	public void setOther_contribute(String other_contribute) {
		this.other_contribute = other_contribute;
	}
	public String getPromotion_id() {
		return promotion_id;
	}
	public void setPromotion_id(String promotion_id) {
		this.promotion_id = promotion_id;
	}
	public String getScope() {
		return scope;
	}
	public void setScope(String scope) {
		this.scope = scope;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getWxpay_contribute() {
		return wxpay_contribute;
	}
	public void setWxpay_contribute(String wxpay_contribute) {
		this.wxpay_contribute = wxpay_contribute;
	}
	
	public String getA() {
		return a;
	}
	public void setA(String a) {
		this.a = a;
	}
	public String getN() {
		return n;
	}
	public void setN(String n) {
		this.n = n;
	}
	public String getP() {
		return p;
	}
	public void setP(String p) {
		this.p = p;
	}
	
	
	
	
}
