package com.fuiou.dips.data.resp;

import com.fuiou.dips.valid.ValidPhone;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 客户查询请求参数
 */
public class CustomerCheckReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String mchntCd;

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 手机号（必填）
     */
    @NotBlank(message = "手机号不能为空")
    @ValidPhone(message = "手机号格式错误❌")
    private String phone;

    public String getMchntCd() {
        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getPhone() {
        return phone == null ? null : phone.trim();
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }
}
