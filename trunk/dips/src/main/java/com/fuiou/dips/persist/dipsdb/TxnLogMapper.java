package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.data.req.TxnLogReq;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.beans.TxnLogQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * 交易订单表
 *
 * <AUTHOR>
 * @description 针对表【t_dips_txn_log(装修通交易订单表)】的数据库操作Mapper
 */
public interface TxnLogMapper {
    List<TxnLog> selectByMchntCdAndDate(@Param("mchntCd") String mchntCd, @Param("startDate") Timestamp startDate,
            @Param("endDate") Timestamp endDate, @Param("payStates") List<String> payStates,
            @Param("storeIds") List<String> storeIds);

    List<TxnLog> selectByProjectAndStage(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd,
            @Param("stageNo") String stageNo, @Param("payStates") List<String> payStates);

    TxnLog selectByProjectAndStageAndOrderNo(TxnLogReq txnLogReq);

    int insert(TxnLog record);


    TxnLog queryOrderInfo(@Param("mchntCd") String mchntCd, @Param("orderNo") String orderNo);

    int update(TxnLog txnLogOrder);

    List<TxnLog> listExternal(TxnLogQueryDTO txnLogQueryDTO);

    List<TxnLog> listByProjectAndStageAndState(@Param("projectNo") String projectNo, @Param("stageNo") String stageNo,
            @Param("orderType") String orderType, @Param("tradeType") String tradeType,
            @Param("orderSt") String orderSt);

}
