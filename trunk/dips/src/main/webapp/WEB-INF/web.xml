<?xml version="1.0" encoding="UTF-8"?>
<web-app
		xmlns="http://java.sun.com/xml/ns/javaee"
		id="WebApp_ID" version="3.0">
	
	<display-name>dcoupon web application</display-name>

	
	<!-- 禁用WebDAV，或禁用http中的一些不需要的方法 -->
	<security-constraint>  
   		<web-resource-collection>  
      		<url-pattern>/*</url-pattern>  
      		<http-method>PUT</http-method>  
			<http-method>DELETE</http-method>  
			<http-method>HEAD</http-method>  
			<!--<http-method>OPTIONS</http-method>  -->
			<http-method>TRACE</http-method>  
   		</web-resource-collection>  
   		<auth-constraint>  
   		</auth-constraint>  
 	</security-constraint>  
 	<login-config>  
   		<auth-method>BASIC</auth-method>  
 	</login-config>
 	
	<context-param>
		<param-name>spring.profiles.default</param-name>
		<param-value>active</param-value>
	</context-param>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath*:/config/applicationContext-beans.xml
		</param-value>
	</context-param>
	<context-param>
		<param-name>log4jRefreshInterval</param-name>
		<param-value>60000</param-value>
	</context-param>
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<servlet>
		<servlet-name>Spring MVC</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>
			classpath*:/config/springmvc-servlet.xml
			</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>Spring MVC</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.fuiou.dips.framework.init.SysInitListener</listener-class>
	</listener>

	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>
	
	<filter>  
        <filter-name>charFilter</filter-name>  
        <filter-class>com.fuiou.dips.framework.filter.CharFilter</filter-class>
        <init-param>  
            <param-name>encoding</param-name>  
            <param-value>UTF-8</param-value>  
        </init-param>  
        <init-param>  
            <param-name>legalNames</param-name>  
            <param-value>content1,ver,historyURL,listURL</param-value>  
        </init-param>  
        <init-param>  
            <param-name>illegalChars</param-name>  
            <param-value>|,@,',$,",\',\",>,(,),+,CR,LF,\",",\,http</param-value>  
        </init-param>  
    </filter> 
	 <filter-mapping>
		<filter-name>charFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
 	<filter>
		<filter-name>sqlFilter</filter-name>
		<filter-class>com.fuiou.dips.framework.filter.SqlFilter</filter-class>
		<init-param>  
            <param-name>encoding</param-name>  
            <param-value>UTF-8</param-value>  
        </init-param>
	</filter>
	<filter-mapping>
	<filter-name>sqlFilter</filter-name>
	<url-pattern>/*</url-pattern>
	</filter-mapping> 
	
	 <filter>
		<filter-name>xssFilter</filter-name>
		<filter-class>com.fuiou.dips.framework.filter.XssFilter</filter-class>
		<init-param>
			<param-name>rejIgalReq</param-name>
			<param-value>false</param-value>
		</init-param>
		<init-param>
			<param-name>illegalPage</param-name>
			<param-value>/error.jsp</param-value>
		</init-param>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>xssFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>


</web-app>