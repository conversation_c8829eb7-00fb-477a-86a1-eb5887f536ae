package com.fuiou.fww;

import com.fuiou.key.SysKeyManager;
import com.fuiou.key.SysKeyManagerService;

public class ClearingClient {

    public static void main(String[] args) {
        SysKeyManager km = new SysKeyManagerService("http://192.168.6.22:29002/kms/key/sysKeyLoaderService?wsdl").getSysKeyManagerPort();//商户测试环境
        //SysKeyManager km = new SysKeyManagerService("http://192.168.8.29:29002/kms/key/sysKeyManagerService?wsdl").getSysKeyManagerPort();//内部测试环境
        km.addSysKey("dcoupon", "ds_url_cfgdb", "***********************************");//数据库url
        km.addSysKey("dcoupon", "ds_url_cfgdb", "dev_dba");//数据库用户名
        km.addSysKey("dcoupon", "ds_url_cfgdb", "P3MsT6f5eQ76ewQ");//数据库密码

        km.addSysKey("dcoupon", "ds_url_tpaydb", "****************************************************************************************************************");//数据库url
        km.addSysKey("dcoupon", "ds_usr_tpaydb", "fuiou");//数据库用户名
        km.addSysKey("dcoupon", "ds_pwd_tpaydb", "fuiou");//数据库密码
        //第一个参数配置platId，对应xml中platId参数，
        //第二个参数配置数据库名称,对应xml中的databaseName参数。
    }
}
