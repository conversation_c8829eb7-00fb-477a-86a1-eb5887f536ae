package com.fuioupay.common.utils;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用 AI 模型 HTTP 调用工具类（支持流式 & 非流式）
 *
 * <AUTHOR>
 */
public class WebClientUtils {

    // 设置10分钟的超时时间（600秒）
    private static final int TIMEOUT_SECONDS = 600;

    private static WebClient getWebClient(String baseUrl) {
        HttpClient httpClient = HttpClient.create()
                // 连接超时
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, TIMEOUT_SECONDS * 1000)
                // 响应超时
                .responseTimeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                // 读写超时
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(TIMEOUT_SECONDS, TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(TIMEOUT_SECONDS, TimeUnit.SECONDS)));

        return WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    /**
     * 通用 POST 请求（非流式）
     *
     * @param endpoint    API 路径（如 "/v1/chat/completions"）
     * @param requestBody 请求体（Map 或 POJO）
     * @return Mono<T> 异步结果
     */
    public static Mono<Object> postForMono(String url, String endpoint, Object requestBody) {
        return postForMonoWithHeaders(url, endpoint, requestBody, new HashMap<>(0));
    }

    /**
     * 通用 POST 请求（流式 SSE）
     *
     * @param endpoint    API 路径（如 "/v1/chat/completions"）
     * @param requestBody 请求体（Map 或 POJO）
     * @return Flux<T> 流式结果
     */
    public static Flux<Object> postForFlux(String url, String endpoint, Object requestBody) {
        return postForFluxWithHeaders(url, endpoint, requestBody, new HashMap<>(0));
    }

    /**
     * 带自定义 Header 的 POST 请求（非流式）
     */
    public static Mono<Object> postForMonoWithHeaders(String url,
            String endpoint,
            Object requestBody,
            Map<String, String> customHeaders)
    {
        return getWebClient(url).post()
                .uri(endpoint)
                .headers(headers -> customHeaders.forEach(headers::add))
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class) // 获取原始字符串响应
                .map(response -> (Object) response) // 转换为Object类型
                .timeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                .onErrorResume(e -> {
                    System.out.println("API调用出错: " + e.getMessage());
                    return Mono.error(new RuntimeException("API调用失败: " + e.getMessage(), e));
                });
    }

    /**
     * 带自定义 Header 的 POST 请求（流式）
     */
    public static Flux<Object> postForFluxWithHeaders(String url,
            String endpoint,
            Object requestBody,
            Map<String, String> customHeaders)
    {
        return getWebClient(url).post()
                .uri(uriBuilder -> uriBuilder.path(endpoint).queryParam("stream", "true").build())
                .headers(headers -> customHeaders.forEach(headers::add))
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class) // 获取原始字符串响应
                .map(response -> (Object) response) // 转换为Object类型
                .timeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                .takeUntil(response -> {
                    // 当接收到[DONE]标记时，结束流
                    return response.toString().equalsIgnoreCase("[DONE]");
                })
                .onErrorResume(e -> {
                    System.out.println("流式API调用出错: " + e.getMessage());
                    return Flux.error(new RuntimeException("流式API调用失败: " + e.getMessage(), e));
                });
    }
}