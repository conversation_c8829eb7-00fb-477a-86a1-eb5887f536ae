package com.fuiou.vcc.api.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class StaticPropertiesUtil {
    public static String staticClientID;
    public static String staticBuyerId;
    public static String staticProxyPoolId;
    public static String staticMultiplyProxyPoolId;
    public static String staticVccOpenCloseUrl;

    public static String staticVccHttpUserId;
    public static String staticVccHttpPassword;
    public static String staticVccHttpP12;

    public static String staticVccRateUrl;
    public static String staticVccCvvUrl;
    public static String staticVccGetPaymentControllerUrl;

    public static String staticRateProtect;

    public static String staticVccGetAuthorizationDataUrl;
    public static String staticVccGetAuthorizationData2Url;

    public static String staticVccRegisteredUserDetails;

    public static String staticVccAuthBankId;
    public static String staticVccAuthAccount;
    public static String staticVccAuthCompanyId;
    public static String staticVccAuthRegionId;

    public static String staticVccAuthProcessorId;
    public static String staticVccGetTrxnDataUrl;
    public static String staticVccGetTrxnV2DataUrl;
    public static String staticSkipVerifySign;

    public static String staticNodeIp;

    public static ThreadLocal<String> staticMchntTheadLocal = new ThreadLocal<>();

    @Value("${clientID}")
    public String clientID;
    @Value("${buyerId}")
    public String buyerId;
    @Value("${proxyPoolId}")
    public String proxyPoolId;
    @Value("${multiplyProxyPoolId}")
    public String multiplyProxyPoolId;
    @Value("${vccOpenCloseUrl}")
    public String vccOpenCloseUrl;
    @Value("${vccHttpUserId}")
    public String vccHttpUserId;
    @Value("${vccHttpPassword}")
    public String vccHttpPassword;
    @Value("${vccHttpP12}")
    public String vccHttpP12;

    @Value("${vccRateUrl}")
    public String vccRateUrl;

    @Value("${vccCvvUrl}")
    public String vccCvvUrl;

    @Value("${vccGetPaymentControllerUrl}")
    public String vccGetPaymentControllerUrl;

    @Value("${rate.protect}")
    public String rateProtect;

    @Value("${vccGetAuthorizationDataUrl}")
    public String vccGetAuthorizationDataUrl;

    @Value("${vccGetAuthorizationData2Url}")
    public String vccGetAuthorizationData2Url;
    @Value("${vccRegisteredUserDetails}")
    public String vccRegisteredUserDetails;

    @Value("${vccAuthBankId}")
    public String vccAuthBankId;
    @Value("${vccAuthAaccount}")
    public String vccAuthAccount;
    @Value("${vccAuthCompanyId}")
    public String vccAuthCompanyId;
    @Value("${vccAuthRegionId}")
    public String vccAuthRegionId;

    @Value("${vccAuthProcessorId}")
    public String vccAuthProcessorId;

    @Value("${vccGetTrxnDataUrl}")
    public String vccGetTrxnDataUrl;
    @Value("${vccGetTrxnV2DataUrl}")
    public String vccGetTrxnV2DataUrl;
    @Value("${skipVerifySign}")
    public String skipVerifySign;

    @Value("${nodeIp}")
    private String nodeIp;

    @PostConstruct
    public void setStaticPort() {
        staticClientID = clientID;
        staticBuyerId = buyerId;
        staticProxyPoolId = proxyPoolId;
        staticMultiplyProxyPoolId = multiplyProxyPoolId;
        staticVccOpenCloseUrl = vccOpenCloseUrl;
        staticVccHttpP12 = vccHttpP12;
        staticVccHttpUserId = vccHttpUserId;
        staticVccHttpPassword = vccHttpPassword;
        staticVccRateUrl = vccRateUrl;
        staticVccCvvUrl = vccCvvUrl;
        staticVccGetPaymentControllerUrl = vccGetPaymentControllerUrl;
        staticRateProtect = rateProtect;
        staticVccGetAuthorizationDataUrl = vccGetAuthorizationDataUrl;
        staticVccRegisteredUserDetails = vccRegisteredUserDetails;
        staticVccAuthBankId = vccAuthBankId;
        staticVccAuthCompanyId = vccAuthCompanyId;
        staticVccAuthAccount = vccAuthAccount;
        staticVccAuthRegionId = vccAuthRegionId;
        staticVccAuthProcessorId = vccAuthProcessorId;
        staticVccGetTrxnDataUrl = vccGetTrxnDataUrl;
        staticVccGetTrxnV2DataUrl = vccGetTrxnV2DataUrl;
        staticSkipVerifySign = skipVerifySign;
        staticVccGetAuthorizationData2Url = vccGetAuthorizationData2Url;
        staticNodeIp = nodeIp;
    }

}
