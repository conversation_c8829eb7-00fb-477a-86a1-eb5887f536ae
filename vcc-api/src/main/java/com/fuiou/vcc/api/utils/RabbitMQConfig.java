package com.fuiou.vcc.api.utils;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitMQConfig {
    // 记录交换器
    public static final String ORDER_EXCHANGE_NAME = "vcc.order.exchange";

    // 记录队列
    public static final String ORDER_QUEUE_NAME = "vcc.order.queue";

    // 记录 Routing Key
    public static final String ORDER_ROUTING_KEY = "vcc.order.routingKey";


    // 记录队列
    public static final String NOTIFY_QUEUE_NAME = "vcc.notify.queue";

    // 记录 Routing Key
    public static final String NOTIFY_ROUTING_KEY = "vcc.notify.routingKey";

    // 延时队列 TTL（单位：毫秒）
    public static final int DELAY_TIME = 10 * 1000;

    // 创建记录队列
    @Bean
    public Queue orderQueue() {
        Map<String, Object> args = new HashMap<>();
        // 设置队列的 TTL 属性
        args.put("x-message-ttl", DELAY_TIME);
        // 设置队列中的消息过期后进入的交换器
        args.put("x-dead-letter-exchange", ORDER_EXCHANGE_NAME);
        // 设置队列中的消息过期后进入的 Routing Key
        args.put("x-dead-letter-routing-key", ORDER_ROUTING_KEY);
        return new Queue(ORDER_QUEUE_NAME, true, false, false, args);
    }

    // 创建记录交换器
    @Bean
    public DirectExchange orderExchange() {
        return new DirectExchange(ORDER_EXCHANGE_NAME);
    }

    // 将记录队列绑定到记录交换器上
    @Bean
    public Binding orderBinding() {
        return BindingBuilder.bind(orderQueue()).to(orderExchange()).with(ORDER_ROUTING_KEY);
    }

    @Bean
    public Queue notifyQueue() {
        Map<String, Object> args = new HashMap<>();
        // 设置队列的 TTL 属性
        args.put("x-message-ttl", DELAY_TIME);
        // 设置队列中的消息过期后进入的交换器
        args.put("x-dead-letter-exchange", ORDER_EXCHANGE_NAME);
        // 设置队列中的消息过期后进入的 Routing Key
        args.put("x-dead-letter-routing-key", NOTIFY_ROUTING_KEY);
        return new Queue(NOTIFY_QUEUE_NAME, true, false, false, args);
    }


    // 将记录队列绑定到记录交换器上
    @Bean
    public Binding notifyBinding() {
        return BindingBuilder.bind(notifyQueue()).to(orderExchange()).with(NOTIFY_ROUTING_KEY);
    }
}
