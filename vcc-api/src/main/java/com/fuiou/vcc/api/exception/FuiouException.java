package com.fuiou.vcc.api.exception;

public class FuiouException extends RuntimeException {
    private static final long serialVersionUID = -8015275973552064550L;


    protected String iErrCode = "";

    protected String iErrMessage = "";

    private String aMessage;

    public FuiouException(String aMessage) {
        super(aMessage);
    }

    public FuiouException(String aCode, String aMessage) {
        super(aMessage);
        iErrCode = aCode.trim();
    }

    public String getCode() {
        return iErrCode;
    }


    public String getMessage() {
        return super.getMessage();
    }

    public FuiouException(String aCode, String aMessage, String aDetailMessage) {
        super(aMessage.trim());
        iErrCode = aCode.trim();
        iErrMessage = aDetailMessage.trim();
    }

    /**
     * @return the errInfo
     */
    public String getErrInfo() {
        return aMessage;
    }

}
