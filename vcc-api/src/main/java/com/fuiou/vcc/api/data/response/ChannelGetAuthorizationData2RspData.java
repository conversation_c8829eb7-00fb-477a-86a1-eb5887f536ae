package com.fuiou.vcc.api.data.response;

import cn.hutool.core.collection.ListUtil;
import lombok.Data;

import java.util.List;

@Data
public class ChannelGetAuthorizationData2RspData extends ChannelBaseRspData {

    private List<RspEntity> rspEntities = ListUtil.empty();
    private Metadata metadata;

    @Data
    public static class Metadata {
        private int totalMatchedRecords;
        private int pageNum;

    }

    @Data
    public static class RspEntity {
        private String issrBinOwnrBusIdDrvd;
        private String acqrBinUsrBusIdCtryCdDrvd;
        private String acqrBinCtryCd;
        private String crdhBillAmt;
        private String issrPcrBusIdCtryCdDrvd;
        private String onUsIndDrvd;
        private String acqrBinOwnrBusIdRegnCdDrvd;
        private String issuer;
        private String authDesc;
        private String issrPcrBusIdDrvd;
        private String msgRespSntTs;
        private String cvv2StipRespCd;
        private String acqrBinUsrBusIdDrvd;
        private String isoTranCurrCd;
        private String authStatusCode;
        private String mrchCtryCd;
        private String origlTranId;
        private String acctNum;
        private String crdAcptrId;
        private String acqrPcrDrvd;
        private String crdhBillCurrCd;
        private String issrSetlmtCurrCd;
        private String processor;
        private String acqrBinCtryCdDrvd;
        private String crdhBillAmtUsd;
        private String acqrBinOwnrBusIdCtryCdDrvd;
        private String mrchZipCd;
        private String acqrBinOwnrBusIdDrvd;
        private String acctNumAltEncryptNonpan;
        private String region;
        private String tranId;
        private String mrchCatgCd;
        private String stipAdvcCd;
        private String acqrNtwrkIdCriscros;
        private String isoTranAmt;
        private String authTime;
        private String stipSwtchRsnCd;
        private String issrBinOwnrBusIdCtryCdDrvd;
        private String msgReqstSntTs;
        private String acqrBinRegnCdDrvd;
        private String stipRsnDesc;
        private String msgRespRcvdTs;
        private String msgReqstRcvdTs;
        private String company;
        private String acqrPcrBusIdCtryCdDrvd;
        private String mrchNm;
        private String acqrBinUsrBusIdRegnCdDrvd;
        private String stipEligFlg;
        private String acqrPcrCtryCd;
        private String crdTypCd;
        private String acqrBinDrvd;
        private String acqrSetlmtCurrCd;
        private String authStatus;
        private String acqrNtwrkIdDrvd;
        private String authDt;
        private String issrPcrBusIdRegnCdDrvd;
        private String prchDtDrvd;
        private String mrchCity;
        private String acqrSetlmtAmtConvrsnRt;

        private String authRsnDesc;
        private String authCatgCd;
        private String authCatgDesc;

        private String tokenIndicator;
        private String tokenNumber;
        private String msgType;
        private String msgTypeDesc;
        private String partialAuthAmt;
        private String preAuth;
        private String trgrSvcType;
        private String trgrSvcDesc;
        private String tranProcCd;
        private String authIdResp;
        private String mrchStatePrvCd;
        private String vpcPropActnCd;
        private String vpcActTknCd;
        private String uniqSeqId;
    }
}
