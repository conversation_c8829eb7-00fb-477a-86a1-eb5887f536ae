package com.fuiou.vcc.api.data.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChannelGetTrxnDatatRspData extends ChannelBaseRspData {

    private List<RspEntity2> rspEntities;
    private ResponseMetadata responseMetadata;

    @Data
    public static class ResponseMetadata {
        private long endIndex;
        private long nextIndex;
        private Integer requestId;
        private long startIndex;
        private Integer remainingTrxnRecCount;
        private Integer numberOfRecordsFetched;
    }

    @Data
    public static class RspEntity2 {
        private String gmrId;
        private String matId;
        private String mpsId;
        private String ddaNbr;
        private String feeAmt;
        private String loadCd;
        private String source;
        private String tipAmt;
        private String vatAmt;
        private String acctNum;
        private String acqrBin;
        private String authNbr;
        private String buyerId;
        private String feeDesc;
        private String optFld1;
        private String optFld2;
        private String optFld3;
        private String optFld4;
        private String psngrNm;
        private String purchId;
        private String tax2Amt;
        private String transDt;
        private String transTm;
        private String usageCd;
        private String regionId;
        private Supplier supplier;
        private String carrierCd;
        private String companyId;
        private String custCdCri;
        private String disputeDt;
        private String extractId;
        private String matchedId;
        private String messageId;
        private String postingDt;
        private String refundNum;
        private String rvaStatus;
        private String sourceAmt;
        private String ticketNum;
        private String tokenType;
        private String userData1;
        private String userData2;
        private String userData3;
        private String userData4;
        private String userData5;
        private String billingAmt;
        private String custVatNum;
        private String disputeAmt;
        private String origlMccCd;
        private String routingNbr;
        private String rvaEndDate;
        private String supplierNm;
        private String tranIdntfn;
        private String addendumKey;
        private String commodityCd;
        private String fltDepartDt;
        private String memoPostFlg;
        private String merchCatgCd;
        private String orderTypeCd;
        private String processorId;
        private String rvaTimezone;
        private String rvaUniqueId;
        private String salesTaxAmt;
        private String statementDt;
        private String transRefNum;
        private String transTypeCd;
        private String disputeRsnCd;
        private String memberBankId;
        private String rvaStartDate;
        private String sourceCurrCd;
        private String supplierCity;
        private String billingCurrCd;
        private String billingPeriod;
        private String ddaSavingsNbr;
        private String purchIdFormat;
        private String userData1Desc;
        private String userData2Desc;
        private String userData3Desc;
        private String userData4Desc;
        private String userData5Desc;
        private String vmpdLtMtchInd;
        private List<Map<String, Object>> vpaClientRefs;
        private String accountExpDate;
        private String billingAcctNbr;
        private String cardAcceptorId;
        private String disputeStateCd;
        private String enrichedTxnFlg;
        private String exchgRtDcmlNum;
        private String fgnTranExchgRt;
        private String origSupplierNm;
        private String reservedField1;
        private String reservedField2;
        private String reservedField3;
        private String reservedField4;
        private String supplierCtryCd;
        private String supplierVatNum;
        private String cardTransSeqNum;
        private String lineitemMatchId;
        private String origTransRefNum;
        private String pCardTransIsNum;
        private String taxAmtIncludeCd;
        private String origlSuplrCityNm;
        private String origlSuplrCtryCd;
        private String origlSuplrPstlCd;
        private String supplierOrderNum;
        private String supplierPostalCd;
        private String taxAmt2IncludeCd;
        private String destnAirprtCityCd;
        private String rvaClientMessageId;
        private String vpaProxyPoolNumber;
        private String origlSuplrStPrvncCd;
        private String supplierStateProvCd;
        private String visaCommerceBatchId;
        private String issuerDefinedUsageCd;
        private String cardHolderTxnApproval;
        private String visaCommerPymntInstdt;
        private String supplierNameJapaneseKanji;
        private String supplierNameJapaneseKatakana;
    }

    @Data
    public static class Supplier {
        private String atId;
        private String vmid;
        private String matId;
        private String mpsId;
        private String hbcInd;
        private String mailSt;
        private String dsblInd;
        private String dunsNum;
        private String frchsCd;
        private String txpyrNm;
        private String b2MrchNm;
        private String b2MrchSt;
        private String busLglNm;
        private String incStaCd;
        private String mailAddr;
        private String mailCity;
        private String prmSicCd;
        private String secSicCd;
        private String tinTypCd;
        private String wbencInd;
        private String altMrchNm;
        private String altMrchSt;
        private String emailAddr;
        private String expir8ADt;
        private String isoCtryCd;
        private String mailZipCd;
        private String optOutInd;
        private String smlBusInd;
        private String tinMtchCd;
        private String wmnOwnInd;
        private String annlSlsVol;
        private String b2MrchCity;
        private String busSizeInd;
        private String clsfn8AInd;
        private String infoRfslDt;
        private String mrchCatgCd;
        private String mrchFaxNum;
        private String mrchPhnNum;
        private String nonPrftInd;
        private String outOfBusDt;
        private String prmNaicsCd;
        private String secNaicsCd;
        private String txpyrIdNum;
        private String veteranInd;
        private String altMrchCity;
        private String altSuppCtry;
        private String atMrchTypCd;
        private String b2MrchZipCd;
        private String incStaChgDt;
        private String infoRfslInd;
        private String lvl2CertInd;
        private String lvl2CptrInd;
        private String mnrtyVndrCd;
        private String mrchStrAddr;
        private String rsrvnPhnNum;
        private String sbaRgstrInd;
        private String altMrchZipCd;
        private String latestEmpCnt;
        private String lvl3Tier1Ind;
        private String lvl3Tier2Ind;
        private String lvl3Tier3Ind;
        private String lvl3Tier4Ind;
        private String mrchntQualCd;
        private String ownrshpChgDt;
        private String wmnSmlBusInd;
        private String ccdrAdjMrchNm;
        private String ccdrAdjMrchSt;
        private String hubzoneBusInd;
        private String mailZipSffxCd;
        private String mailingCtryCd;
        private String vtrnSmlBusInd;
        private String dVtrnSmlBusInd;
        private String dotDisadBusInd;
        private String dsblVeteranInd;
        private String smlDisadBusInd;
        private String akNativeCorpInd;
        private String b2MrchZipSffxCd;
        private String ccdrAdjMrchCity;
        private String smlBusSrceCd1Nm;
        private String smlBusSrceCd2Nm;
        private String smlBusSrceCd3Nm;
        private String solePrptrLastNm;
        private String airprtDisadvdInd;
        private String altMrchZipSffxCd;
        private String caDeptGenlSvcInd;
        private String certdTexasGsaInd;
        private String level3CaptureInd;
        private String smlBusCertfcn1Dt;
        private String smlBusCertfcn2Dt;
        private String smlBusCertfcn3Dt;
        private String smlBusSrceLvl1Cd;
        private String smlBusSrceLvl2Cd;
        private String smlBusSrceLvl3Cd;
        private String solePrptrFirstNm;
        private String solePrptrMdlInit;
        private String mrchLocnLatitdVal;
        private String smlBusCertfcn1Num;
        private String smlBusCertfcn2Num;
        private String smlBusCertfcn3Num;
        private String vietnamVeteranInd;
        private String airprtDisadvdCd1Nm;
        private String airprtDisadvdCd2Nm;
        private String airprtDisadvdCd3Nm;
        private String airprtDisadvdCd4Nm;
        private String airprtDisadvdCd5Nm;
        private String mrchLocnLongitdVal;
        private String sbaSmlBusRgstryInd;
        private String airprtDisadvdLvl1Cd;
        private String airprtDisadvdLvl2Cd;
        private String airprtDisadvdLvl3Cd;
        private String airprtDisadvdLvl4Cd;
        private String airprtDisadvdLvl5Cd;
        private String certdDnbNmsdcDocInd;
        private String patrActExclpartyind;
        private String airprtDisadvdCtry1Cd;
        private String airprtDisadvdCtry2Cd;
        private String airprtDisadvdCtry3Cd;
        private String airprtDisadvdCtry4Cd;
        private String airprtDisadvdCtry5Cd;
        private String airprtDisadvdTerr1Nm;
        private String airprtDisadvdTerr2Nm;
        private String airprtDisadvdTerr3Nm;
        private String airprtDisadvdTerr4Nm;
        private String airprtDisadvdTerr5Nm;
        private String visaCommerceSellerId;
        private String histBlkCollegeInstInd;
        private String smlBusCertfcnExpirnDt;
        private String smlBusCertfcnExpirn2Dt;
        private String smlBusCertfcnExpirn3Dt;

    }
}
