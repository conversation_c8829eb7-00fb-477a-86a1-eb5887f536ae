package com.fuiou.common.utils;

import org.apache.dubbo.rpc.RpcContext;

/**
 * 远程调用工具类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class RpcUtil {

    private static boolean IS_PRESENT_DUBBO_RPC;

    static {
        try {
            Class.forName("org.apache.dubbo.rpc.RpcContext");
            IS_PRESENT_DUBBO_RPC = true;
        } catch (ClassNotFoundException e) {
            IS_PRESENT_DUBBO_RPC = false;
        }
    }

    /**
     * 是否存在 Dubbo Rpc
     */
    public static boolean isPresentDubboRpc() {
        return IS_PRESENT_DUBBO_RPC;
    }

    /**
     * 获取 Dubbo RpcContext attachment 信息
     */
    public static String getDubboAttachment(String attachment) {
        return RpcContext.getContext().getAttachment(attachment);
    }

}
