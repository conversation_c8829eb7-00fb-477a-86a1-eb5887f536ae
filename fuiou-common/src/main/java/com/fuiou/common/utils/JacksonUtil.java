package com.fuiou.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.databind.type.CollectionLikeType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fuiou.common.constants.DatePattern;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * jackson工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class JacksonUtil {

    private final static ObjectMapper objectMapper;

    static {
        objectMapper = initObjectMapper(new ObjectMapper());
    }

    private JacksonUtil() { }

    /**
     * 初始化 ObjectMapper
     */
    public static ObjectMapper initObjectMapper(ObjectMapper objectMapper) {
        if (Objects.isNull(objectMapper)) {
            objectMapper = new ObjectMapper();
        }
        return doInitObjectMapper(objectMapper);
    }

    /**
     * 初始化 ObjectMapper 执行
     */
    private static ObjectMapper doInitObjectMapper(ObjectMapper objectMapper) {
        // 不将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 不显示为null的字段
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        // 遇到未知属性不抛出JsonMappingException
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // 过滤对象的null属性.
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 忽略transient
        objectMapper.configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true);
        //单引号处理
        objectMapper.configure(JsonReadFeature.ALLOW_SINGLE_QUOTES.mappedFeature(), true);
        return registerModule(objectMapper);
    }

    /**
     * 注册模块
     */
    public static ObjectMapper registerModule(ObjectMapper objectMapper) {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DatePattern.yyyy_MM_dd_HH_mm_ss)));
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DatePattern.yyyy_MM_dd_HH_mm_ss)));
        simpleModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DatePattern.yyyy_MM_dd)));
        simpleModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DatePattern.yyyy_MM_dd)));
        simpleModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(DatePattern.HH_mm_ss)));
        simpleModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(DatePattern.HH_mm_ss)));
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(simpleModule);
        return objectMapper;
    }

    /**
     * 转换Json
     */
    public static String toJson(Object object) {
        if (object instanceof CharSequence) {
            return (String) object;
        }
        try {
            return getObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Json转换为对象
     */
    public static Object parse(String json) {
        Object object = null;
        try {
            object = getObjectMapper().readValue(json, Object.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return object;
    }

    /**
     * Json转换为对象
     */
    public static <T> T parse(String json, Class<T> clazz) {
        T t = null;
        try {
            t = getObjectMapper().readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * Json转换为对象
     */
    public static <T> T parse(byte[] bytes, Class<T> clazz) {
        T t = null;
        try {
            t = getObjectMapper().readValue(bytes, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * Json转换为对象
     */
    public static <T> T parse(String json, TypeReference<T> typeReference) {
        T t = null;
        try {
            t = getObjectMapper().readValue(json, typeReference);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * Json转换为对象
     */
    public static <T> T parse(byte[] bytes, TypeReference<T> typeReference) {
        T t = null;
        try {
            t = getObjectMapper().readValue(bytes, typeReference);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * 将json数据转换成pojo对象list
     */
    public static <T> List<T> parseList(String jsonData, Class<T> beanType) {
        JavaType javaType = getObjectMapper().getTypeFactory().constructParametricType(List.class, beanType);
        try {
            return getObjectMapper().readValue(jsonData, javaType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json数据转换成pojo对象list
     */
    public static <T> List<T> parseList(byte[] bytes, Class<T> beanType) {
        JavaType javaType = getObjectMapper().getTypeFactory().constructParametricType(List.class, beanType);
        try {
            return getObjectMapper().readValue(bytes, javaType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json转成 Map
     */
    public static Map<String, Object> parseMap(byte[] content) {
        return parseMap(content, Object.class);
    }

    /**
     * 将json转成 Map
     */
    public static Map<String, Object> parseMap(String content) {
        return parseMap(content, Object.class);
    }

    /**
     * 将json转成 Map
     */
    public static <V> Map<String, V> parseMap(byte[] content, Class<?> valueClass) {
        return parseMap(content, String.class, valueClass);
    }

    /**
     * 将json转成 Map
     */
    public static <V> Map<String, V> parseMap(String content, Class<?> valueClass) {
        return parseMap(content, String.class, valueClass);
    }

    /**
     * 将json转成 Map
     */
    public static <K, V> Map<K, V> parseMap(byte[] content, Class<?> keyClass, Class<?> valueClass) {
        if (content == null || content.length == 0) {
            return Collections.emptyMap();
        }
        try {
            return getObjectMapper().readValue(content, getMapType(keyClass, valueClass));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json转成 Map
     */
    public static <K, V> Map<K, V> parseMap(String json, Class<?> keyClass, Class<?> valueClass) {
        if (json == null || json.length() == 0) {
            return Collections.emptyMap();
        }
        try {
            return getObjectMapper().readValue(json, getMapType(keyClass, valueClass));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json转成 JsonNode
     */
    public static JsonNode parseTree(String jsonString) {
        try {
            return getObjectMapper().readTree(jsonString);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json字符串转成 JsonNode
     */
    public static JsonNode parseTree(InputStream in) {
        try {
            return getObjectMapper().readTree(in);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将json字符串转成 JsonNode
     */
    public static JsonNode parseTree(byte[] content) {
        try {
            return getObjectMapper().readTree(content);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * jackson 的类型转换
     *
     * @param fromValue   来源对象
     * @param valueType 转换的类型
     * @param <T>         泛型标记
     * @return 转换结果
     */
    public static <T> T convertValue(Object fromValue, Class<T> valueType) {
        return getObjectMapper().convertValue(fromValue, valueType);
    }

    /**
     * jackson 的类型转换
     *
     * @param fromValue   来源对象
     * @param valueType 转换的类型
     * @param <T>         泛型标记
     * @return 转换结果
     */
    public static <T> T convertValue(Object fromValue, JavaType valueType) {
        return getObjectMapper().convertValue(fromValue, valueType);
    }

    /**
     * jackson 的类型转换
     *
     * @param fromValue      来源对象
     * @param valueType 泛型类型
     * @param <T>            泛型标记
     * @return 转换结果
     */
    public static <T> T convertValue(Object fromValue, TypeReference<T> valueType) {
        return getObjectMapper().convertValue(fromValue, valueType);
    }


    /**
     * 封装 map type，keyClass String
     *
     * @param valueClass value 类型
     * @return MapType
     */
    public static MapType getMapType(Class<?> valueClass) {
        return getMapType(String.class, valueClass);
    }

    /**
     * 封装 map type
     *
     * @param keyClass   key 类型
     * @param valueClass value 类型
     * @return MapType
     */
    public static MapType getMapType(Class<?> keyClass, Class<?> valueClass) {
        return getObjectMapper().getTypeFactory().constructMapType(Map.class, keyClass, valueClass);
    }

    /**
     * 封装 map type
     *
     * @param elementClass 集合值类型
     * @return CollectionLikeType
     */
    public static CollectionLikeType getListType(Class<?> elementClass) {
        return getObjectMapper().getTypeFactory().constructCollectionLikeType(List.class, elementClass);
    }

    /**
     * 封装参数化类型
     *
     * <p>
     * 例如： Map.class, String.class, String.class 对应 Map[String, String]
     * </p>
     *
     * @param parametrized     泛型参数化
     * @param parameterClasses 泛型参数类型
     * @return JavaType
     */
    public static JavaType getParametricType(Class<?> parametrized, Class<?>... parameterClasses) {
        return getObjectMapper().getTypeFactory().constructParametricType(parametrized, parameterClasses);
    }


    /**
     * 获取ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

}
