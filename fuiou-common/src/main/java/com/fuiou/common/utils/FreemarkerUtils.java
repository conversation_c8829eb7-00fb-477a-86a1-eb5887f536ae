package com.fuiou.common.utils;

import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年09月14日 14:31
 * @Description 模板文件操作工具
 */
public class FreemarkerUtils {

    private static final String PATH_NAME = "/ftl/";

    /**
     * 封装参数
     *
     * @param object
     * @param templateName
     * @return
     */
    public static String getEmailHtml(Object object, String templateName) {
        String htmlText = "";
        StringWriter result = new StringWriter(1024);
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_30);
        try {
            //加载模板路径
            configuration.setClassForTemplateLoading(FreemarkerUtils.class, PATH_NAME);
            //渲染模板为html
            configuration.getTemplate(templateName).process(object, result);
            return result.toString();
        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
        return htmlText;
    }

    /**
     * 替换subject中的动态参数
     *
     * @param subject
     * @param params
     * @return
     */
    public static String getSubject(String subject, Map<String, String> params) {
        for (Map.Entry<String, String> entry : params.entrySet()) {
            subject = subject.replace("${" + entry.getKey() + "}", StringUtils.isNotBlank(entry.getValue()) ? entry.getValue() : "");
        }
        return subject;
    }

}
