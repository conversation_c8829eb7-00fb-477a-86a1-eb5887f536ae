package com.fuiou.common.api;

import com.fuiou.common.exception.IErrorCode;
import com.fuiou.common.exception.ServiceException;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Objects;

/**
 * API响应结果
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class ApiResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final Integer SUCCESS_CODE = 200;
    public static final String SUCCESS_MSG = "操作成功";
    public static final Integer FAIL_CODE = 500;
    public static final String FAIL_MSG = "操作失败";

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态码
     */
    private String msg;

    /**
     * 状态码
     */
    private T data;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 成功返回
     */
    public static <T> ApiResult<T> success() {
        ApiResult<T> result = new ApiResult<>();
        result.code = SUCCESS_CODE;
        result.success = Boolean.TRUE;
        result.msg = SUCCESS_MSG;
        return result;
    }

    /**
     * 成功返回
     */
    public static <T> ApiResult<T> success(String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.code = SUCCESS_CODE;
        result.success = Boolean.TRUE;
        result.msg = msg;
        return result;
    }

    /**
     * 成功返回
     */
    public static <T> ApiResult<T> data(T data) {
        ApiResult<T> result = new ApiResult<>();
        result.code = SUCCESS_CODE;
        result.success = Boolean.TRUE;
        result.data = data;
        return result;
    }

    /**
     * 成功返回
     */
    public static <T> ApiResult<T> data(T data, String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.code = SUCCESS_CODE;
        result.success = Boolean.TRUE;
        result.data = data;
        result.msg = msg;
        return result;
    }

    /**
     * 失败返回
     */
    public static <T> ApiResult<T> fail(String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.success = Boolean.FALSE;
        result.code = FAIL_CODE;
        result.msg = msg;
        return result;
    }

    /**
     * 失败返回
     */
    public static <T> ApiResult<T> fail(IErrorCode errorCode) {
        return fail(errorCode.getCode(), errorCode.getMsg());
    }

    /**
     * 失败返回
     */
    public static <T> ApiResult<T> fail(Integer code, String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.success = Boolean.FALSE;
        result.code = code;
        result.msg = msg;
        return result;
    }

    /**
     * 失败返回
     */
    public static <T> ApiResult<T> fail(IErrorCode errorCode, T data) {
        return fail(errorCode.getCode(), errorCode.getMsg(), data);
    }

    /**
     * 失败返回
     */
    public static <T> ApiResult<T> fail(Integer code, String msg, T data) {
        ApiResult<T> result = new ApiResult<>();
        result.success = Boolean.FALSE;
        result.code = code;
        result.msg = msg;
        result.data = data;
        return result;
    }

    /**
     * 状态判断返回
     */
    public static ApiResult<Void> status(boolean status) {
        ApiResult<Void> result = new ApiResult<>();
        result.success = status;
        result.code = status ? SUCCESS_CODE : FAIL_CODE;
        result.msg = status ? SUCCESS_MSG : FAIL_MSG;
        return result;
    }

    /**
     * 验证成功并返回数据，异常抛出 {@link ServiceException}
     */
    public T requiredData() {
        if (isSuccess()) {
            return getData();
        }
        throw new ServiceException(code, msg);
    }

    /**
     * 返回数据，异常或值为空将返回默认值
     *
     * @param defaultValue 默认值
     */
    public T optionalData(T defaultValue) {
        T data = getData();
        if (isError() || data == null) {
            return defaultValue;
        }
        return data;
    }

    public boolean isSuccess() {
        return success;
    }

    @Transient
    public boolean isError() {
        return !success;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public Boolean getSuccess() {
        return success;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApiResult<?> apiResult = (ApiResult<?>) o;
        return Objects.equals(code, apiResult.code) &&
            Objects.equals(msg, apiResult.msg) &&
            Objects.equals(data, apiResult.data) &&
            Objects.equals(success, apiResult.success);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, msg, data, success);
    }

    @Override
    public String toString() {
        return "ApiResult{" +
            "code=" + code +
            ", msg='" + msg + '\'' +
            ", data=" + data +
            ", success=" + success +
            '}';
    }

}
