package com.fuiou.common.sensitive;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/20 16:25
 */
public enum DesensitizationEnum {

    // 执行类和脱敏方法名
    PHONE(MaskUtils.class, "maskPhone", new Class[]{String.class, String.class, String.class}),
    NAME(MaskUtils.class, "maskName", new Class[]{String.class, String.class, String.class}),
    ID_CARD(MaskUtils.class, "maskIDCard", new Class[]{String.class, String.class, String.class}),
    ADDRESS(MaskUtils.class, "maskAddress", new Class[]{String.class, String.class, String.class}),
    CUSTOM(MaskUtils.class, "maskCustom", new Class[]{Object.class, String.class, String.class}),
    ;

    private Class<?> clazz;
    private Method method;

    DesensitizationEnum(Class<?> target, String method, Class[] paramTypes) {
        this.clazz = target;
        try {
            this.method = target.getDeclaredMethod(method, paramTypes);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
    }
    public Method getMethod() {
        return method;
    }
}
