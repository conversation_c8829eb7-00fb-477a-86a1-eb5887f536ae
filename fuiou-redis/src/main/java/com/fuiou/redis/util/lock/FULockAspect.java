package com.fuiou.redis.util.lock;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands.SetOption;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 15:55
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings({"unchecked", "all"})
public class FULockAspect {

    /**
     * Lock在redis中的key前缀
     */
    private final String LOCK_PREFIX = "fuiou:lock:";
    /**
     * 缺省锁名称
     */
    private final String DEFAULT_LOCK_NAME = "";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private RedisSerializer<String> serializer = new StringRedisSerializer();

    @Pointcut("@annotation(com.fuiou.redis.util.lock.FULock)")
    public void FULockAspect(){}

    @Around("FULockAspect()")
    public Object lock(final ProceedingJoinPoint joinPoint) throws Throwable {
        FULock lock = ((MethodSignature)joinPoint.getSignature()).getMethod().getAnnotation(FULock.class);
        String lockName = getLockName(joinPoint, lock);
        boolean locked = false;
        long startTime = System.currentTimeMillis();
        long timeoutMillis = lock.timeUnit().toMillis(lock.leaseTime());
        do {
            locked = tryLocking(lockName, lock);
            if (!locked && lock.waitForLock()) {
                // 等待100毫秒后重试
                Thread.sleep(100);
            }
        } while (!locked && lock.waitForLock() && (System.currentTimeMillis() - startTime < timeoutMillis));

        if (locked) {
            try {
                return joinPoint.proceed();
            } finally {
                if (lock.unLockAfterDone()) {
                    unlock(lockName);
                }
            }
        } else {
            log.warn("skip this invoking because of failure to get lock");
            return null;
        }
    }

    /**
     * 获取锁名称
     * @param joinPoint
     * @param lock
     * @return
     */
    private String getLockName(ProceedingJoinPoint joinPoint, FULock lock) {
        String lockName = lock.name();
        if (DEFAULT_LOCK_NAME.equals(lockName)) {
            lockName = getLockNameFromParameter(joinPoint);
        }
        if (DEFAULT_LOCK_NAME.equals(lockName)) {
            lockName = joinPoint.getSignature().toLongString();
        }
        return lockName;
    }

    /**
     * 从横切的方法参数中获取锁的名称，只支持简单对象参数名称，不支持bean对象或bean对象里面的属性作为引用参数
     *
     * @param joinPoint
     * @return 引用的方法参数值
     */
    private String getLockNameFromParameter(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        Annotation[][] paramAnnotations = ((MethodSignature) (joinPoint.getSignature())).getMethod().getParameterAnnotations();
        int paramIndex = -1;
        for (int i = 0; i < paramAnnotations.length; i++) {
            for (int j = 0; j < paramAnnotations[i].length; j++) {
                if (paramAnnotations[i][j] instanceof FULockName) {
                    paramIndex = i;
                    break;
                }
            }
        }
        if (paramIndex > -1) {
            return args[paramIndex].toString();// 返回引用参数的值
        } else {
            return DEFAULT_LOCK_NAME; // 返回缺省锁名称
        }
    }

    /**
     * 尝试获取得锁
     *
     * @param lockName 锁名称
     * @param lock     加锁时的注解参数
     * @return 加锁成功返回true，否则返回false
     */
    private boolean tryLocking(String lockName, FULock lock) {
        String lockKey = LOCK_PREFIX + lockName;
        // RedisCallback方式调用原生接口
        return redisTemplate.execute(new RedisCallback<Boolean>() {
            @Override
            public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                String uuid = UUID.randomUUID().toString();
                byte[] key = serializer.serialize(lockKey);
                connection.set(key, serializer.serialize(uuid), Expiration.from(lock.leaseTime(), lock.timeUnit()), SetOption.SET_IF_ABSENT);
                byte[] value = connection.get(key);
                if (uuid.equals(serializer.deserialize(value))) {
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 释放锁
     *
     * @param lockName 锁名称
     */
    private void unlock(String lockName) {
        String lockKey = LOCK_PREFIX + lockName;
        redisTemplate.delete(lockKey);
    }
}
