package com.fuiou.redis.util.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 15:55
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface FULock {

    /**
     * 锁名称,如果不指定，则为方法签名，可以用"@parameterName"的方式引用横切方法中参数值作为锁名称
     * @return
     */
    String name() default "";

    /**
     * 最大锁时间
     * @return
     */
    long leaseTime();

    /**
     * 锁时间单位
     * @return
     */
    TimeUnit timeUnit();

    /**
     * 是否等待锁释放
     * @return
     */
    boolean waitForLock() default false;

    /**
     * 完成后是否释放锁
     * @return
     */
    boolean unLockAfterDone() default true;
}
