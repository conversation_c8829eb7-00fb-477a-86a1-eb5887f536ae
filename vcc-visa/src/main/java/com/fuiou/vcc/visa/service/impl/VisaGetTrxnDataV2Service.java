package com.fuiou.vcc.visa.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.data.request.ChannelGetTrxnDataV2ReqData;
import com.fuiou.vcc.api.data.response.ChannelGetTrxnDataV2RspData;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.request.GetTrxnDataV2ReqData;
import com.fuiou.vcc.visa.data.response.GetTrxnDataV2RspData;
import com.fuiou.vcc.visa.utils.VisaHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
public class VisaGetTrxnDataV2Service {
    private final static String FILTER_TYPE_BWTEN ="BETWN";
    private final static String FILTER_TYPE_ACCT_NO ="ACCT_NO";
    private final static String FILTER_TYPE_ACCT_LIST ="ACCT_LIST";
    private final static String FILTER_TYPE_ENHN_CAT ="ENHN_CAT";

    public ChannelGetTrxnDataV2RspData getTrxnData(ChannelGetTrxnDataV2ReqData channelReqData) {
        GetTrxnDataV2ReqData getTrxnDataReqData = new GetTrxnDataV2ReqData();
        getTrxnDataReqData.setProcessorId(StaticPropertiesUtil.staticVccAuthProcessorId);
        getTrxnDataReqData.setRegionId(StaticPropertiesUtil.staticVccAuthRegionId);
        getTrxnDataReqData.setCompanyId(StaticPropertiesUtil.staticVccAuthCompanyId);
        getTrxnDataReqData.setIssuerId(StaticPropertiesUtil.staticVccAuthBankId);
        getTrxnDataReqData.setType(channelReqData.getType());
        getTrxnDataReqData.setRejectedSettlements(channelReqData.getRejectedSettlements());

        GetTrxnDataV2ReqData.Pagination pagination=new GetTrxnDataV2ReqData.Pagination();
        pagination.setPageNum(channelReqData.getPageNum());
        pagination.setPageSize(channelReqData.getPageSize());
        getTrxnDataReqData.setPagination(pagination);

        GetTrxnDataV2ReqData.Filters filters = new GetTrxnDataV2ReqData.Filters();

        if(StringUtils.isNoneBlank(channelReqData.getStartDate(),channelReqData.getEndDate())){
            GetTrxnDataV2ReqData.PostingDate postingDate=new GetTrxnDataV2ReqData.PostingDate();
            postingDate.setFilterType(FILTER_TYPE_BWTEN);
            postingDate.setFilterValues(ListUtil.toList(channelReqData.getStartDate(), channelReqData.getEndDate()));
            filters.setPostingDate(postingDate);
        }

        if(StringUtils.isNoneBlank(channelReqData.getStartDateTime(),channelReqData.getEndDateTime())){
            GetTrxnDataV2ReqData.TransactionDateTime transactionDateTime = new GetTrxnDataV2ReqData.TransactionDateTime();
            transactionDateTime.setFilterType(FILTER_TYPE_BWTEN);
            transactionDateTime.setFilterValues(ListUtil.toList(channelReqData.getStartDateTime(), channelReqData.getEndDateTime()));
            filters.setTransactionDateTime(transactionDateTime);
        }

        if(CollUtil.isNotEmpty(channelReqData.getAccountFilterValues())){
            GetTrxnDataV2ReqData.Account account = new GetTrxnDataV2ReqData.Account();
            if(CollUtil.size(channelReqData.getAccountFilterValues())==1){
                account.setFilterType(FILTER_TYPE_ACCT_NO);
            }else {
                account.setFilterType(FILTER_TYPE_ACCT_LIST);
            }
            account.setFilterValues(channelReqData.getAccountFilterValues());
            filters.setAccount(account);
        }

        if(CollUtil.isNotEmpty(channelReqData.getEnhancedDataFilterValues())){
            GetTrxnDataV2ReqData.EnhancedData enhancedData = new GetTrxnDataV2ReqData.EnhancedData();
            enhancedData.setFilterType(FILTER_TYPE_ENHN_CAT);
            enhancedData.setFilterValues(channelReqData.getEnhancedDataFilterValues());
            filters.setEnhancedData(enhancedData);
        }

        if(StringUtils.isNoneBlank(channelReqData.getMinAmount(),channelReqData.getMaxAmount())){
            GetTrxnDataV2ReqData.TransactionAmount transactionAmount = new GetTrxnDataV2ReqData.TransactionAmount();
            transactionAmount.setFilterType(FILTER_TYPE_BWTEN);
            transactionAmount.setFilterValues(ListUtil.toList(channelReqData.getMinAmount(), channelReqData.getMaxAmount()));
            filters.setTransactionAmount(transactionAmount);
        }
        getTrxnDataReqData.setFilters(filters);
        String jsonString = JSONUtil.toJsonStr(getTrxnDataReqData);
        ChannelGetTrxnDataV2RspData rspData = new ChannelGetTrxnDataV2RspData();
        String httpRes = null;
        try {

            httpRes = VisaHttpRequest.postObj(StaticPropertiesUtil.staticVccGetTrxnV2DataUrl, jsonString);
            GetTrxnDataV2RspData virtualAccountRspData = JSONUtil.toBean(httpRes, GetTrxnDataV2RspData.class);
            if (virtualAccountRspData == null || virtualAccountRspData.getMetadata() == null) {
                rspData.setRspCode("9999");
                rspData.setRspDesc("查询异常");
            } else {
                rspData.setMetadata(virtualAccountRspData.getMetadata());
                if (Objects.isNull(virtualAccountRspData.getTransactions())) {
                    rspData.setTransactions(ListUtil.empty());
                } else {
                    rspData.setTransactions(virtualAccountRspData.getTransactions());
                }
            }
        } catch (UtilException e) {
            rspData.setRspCode("5138");
            rspData.setRspDesc("查询异常");
            Map<String, Object> map = JSONUtil.toBean(httpRes, Map.class, true);
            map = JSONUtil.toBean(JSONUtil.toJsonStr(map.get("transactionData")), Map.class, true);

            if (CollectionUtil.isNotEmpty(map)) {
                rspData.setRspDesc(JSONUtil.toJsonStr(map.get("transactionRecords")));
                return rspData;
            }

        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            rspData.setRspCode("5138");
            rspData.setRspDesc("查询异常");
        }
        return rspData;
    }
}
