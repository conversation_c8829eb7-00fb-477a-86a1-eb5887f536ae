package com.fuiou.vcc.visa.constants;

/**
 * Created by renzw on 2019/12/24.
 */
public class Constants {
    public static final String TRANS_TYPE_CONSUME = "XF";
    public static final String TRANS_TYPE_RECHARGE = "CZ";
    public static final String TRANS_ST_INIT = "00";
    public static final String TRANS_ST_FINISH = "01";
    public static final String TRANS_ST_HANDLING = "02";
    public static final String IS_SKIP_VERIFY_SIGN = "1";

    public static final String RSP_ACCOUNT_STATUS_VALID = "01";
    public static final String RSP_ACCOUNT_STATUS_INVALID = "02";
    public static final String RSP_ACCOUNT_STATUS_CANCEL_HANDLING = "03";

    public static final String CARD_STATUS_VALID = "01";
    public static final String CARD_STATUS_INVALID = "02";
    public static final String CARD_STATUS_CANCEL_HANDLING = "05";

    public static final String CARD_TYPE_VISA = "01";
    public static final String CARD_TYPE_MASTER = "02";
    public static final String IS_SUPT_MASTER = "1";

    public static final String CHANNEL_RSP_CODE_SUCCESS = "0000";
    public static final String CHANNEL_RSP_CODE_ERROR = "5138";
    public static final String RECHARGE_DEFAULT_CURRENCY = "CNY";
    public static final String RECHARGE_CURRENCY_TYPE_CARD = "01";
    public static final String CARD_MODEL_SINGLE = "01";
    public static final String CARD_MODEL_MULTIPLY = "02";
    public static final String CARD_MODEL_LOCK_RATE = "03";
    public static final String SUPT_LOCK_ST = "1";

    public static final String ICBC_LUJIAMIN_CARD = "Card_Pool_3"; //VISA使用佳敏工行绑定卡
    public static final String ICBC_JINJING_CARD = "Card_Pool_4";//VISA使用金晶工行绑定卡
    public static final String REQ_JINJING_CARD_BIN = "02";//客户请求开金晶的卡
    public static final String SCAN_PAY_ORDER_ST_SUCCESS = "11";//扫码支付订单状态成功
    public static final String EXCLUDE_CVV = "1";//扫码支付订单状态成功
    public static final String CVV_SALT = "PAS6854@1abd#96";//cvv加密salt


    public static final String REGEX_YYYYMMDD = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|" +
            "((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
            "((0[48]|[2468][048]|[3579][26])00))0229)$";  //匹配日期格式：yyyyMMdd

    public static final String TYPE_TRAN_DATA_BASIC="TRAN";
    public static final String TYPE_TRAN_DATA_TRAN_ENHN="TRAN_ENHN";
    public static final String TYPE_TRAN_DATA_ENHN="ENHN";
}
