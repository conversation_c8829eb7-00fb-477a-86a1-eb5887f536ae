package com.fuiou.vcc.visa.service.impl;

import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.data.request.ChannelUpdAccountReqData;
import com.fuiou.vcc.api.data.response.ChannelUpdAccountRspData;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.assembler.VirtualAccountAssembler;
import com.fuiou.vcc.visa.data.request.account.VirtualAccountReqData;
import com.fuiou.vcc.visa.data.response.account.VirtualAccountRspData;
import com.fuiou.vcc.visa.utils.VisaHttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * visa开户
 * Created by renzw on 2020/7/1.
 */
@Slf4j
public class VisaUpdAccountService {
    public ChannelUpdAccountRspData updVisa(ChannelUpdAccountReqData reqData) {
        ChannelUpdAccountRspData channelBaseRspData = new ChannelUpdAccountRspData();
        try {
            VirtualAccountReqData vccReqData = VirtualAccountAssembler.genUpdateAccount(reqData);
            String jsonString = JSONUtil.toJsonStr(vccReqData);

            String s = VisaHttpRequest.postObj(StaticPropertiesUtil.staticVccOpenCloseUrl, jsonString);
            VirtualAccountRspData virtualAccountRspData = JSONUtil.toBean(s, VirtualAccountRspData.class);
            channelBaseRspData.setResSsn(vccReqData.getMessageId());
            if (!virtualAccountRspData.getResponses().get(0).getResponseCode().equals("00")) {
                channelBaseRspData.setRspCode("9999");
                channelBaseRspData.setRspDesc(virtualAccountRspData.getResponses().get(0).getResponseDescription());
                return channelBaseRspData;
            }
            channelBaseRspData.setExpirationDate(virtualAccountRspData.getExpirationDate());
            return channelBaseRspData;
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            channelBaseRspData.setRspCode("5138");
            channelBaseRspData.setRspDesc("visa修改卡异常");
            return channelBaseRspData;
        }
    }


}
