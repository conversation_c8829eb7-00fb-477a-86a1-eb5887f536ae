package com.fuiou.vcc.visa.service;

import com.fuiou.vcc.api.data.request.*;
import com.fuiou.vcc.api.data.response.*;
import com.fuiou.vcc.api.service.IChannelService;
import com.fuiou.vcc.visa.service.impl.*;
import org.springframework.stereotype.Service;

/**
 * Created by renzw on 2020/6/30.
 */
@Service
public class VisaChannelService implements IChannelService {
    @Override
    public ChannelBaseRspData closeAccount(ChannelCloseAccountReqData reqData) {
        return new VisaCloseAccountService().closeAccount(reqData);
    }


    @Override
    public ChannelGetCvvRspData getCvv(ChannelGetCardReqData reqData) {
        return new VisaGetCvvService().getSecurityCode(reqData.getCardNo(), reqData.getExpirationDate());
    }

    @Override
    public ChannelGetRateRspData getRate(String sourceCurrency, String targetCurrency) {
        return new VisaGetRateService().getRate(sourceCurrency, targetCurrency);
    }

    @Override
    public ChannelOpenAccountRspData openAccount(ChannelOpenAccountReqData reqData) {
        return new VisaOpenAccountService().openAccount(reqData);
    }

    @Override
    public ChannelUpdAccountRspData updAccount(ChannelUpdAccountReqData reqData) {
        return new VisaUpdAccountService().updVisa(reqData);
    }

    @Override
    public ChannelGetCardInfRsqData getCardInf(String cardNo) {
        return new VisaGetCardInfoService().getCardInf(cardNo);
    }

    @Override
    public ChannelGetCardInfRsqData getCardInf(ChannelGetCardReqData reqData) {
        return new VisaGetCardInfoService().getCardInf(reqData.getCardNo());
    }

    @Override
    public ChannelGetAuthorizationData2RspData getAuthorizationData(ChannelGetAuthorizationDatatReqData channelReqData) {
        return new VisaGetAuthorizationData2Service().getAuthorizationData(channelReqData);
    }

    @Override
    public ChannelGetTrxnDatatRspData getTrxnData(ChannelGetTrxnDatatReqData channelReqData) {
        return new VisaGetTrxnDataService().getTrxnData(channelReqData);
    }
}
