package com.fuiou.vcc.visa.data.request;

import lombok.Data;

import java.util.List;

@Data
public class GetAuthorizationData2ReqData {
    private String authEndDateTime;
    private String issuerId;
    private String companyId;
    private String processorId;
    private String regionId;
    private List<String> accountList;
    private String pageSize;
    private List<Filters> filters;
    private String pageNum;
    private String authStartDateTime;

    @Data
    public static class Filters {

        private List<String> filterValues;
        private String filterBy;
    }
}
