package com.fuiou.vcc.visa.data.request;

import lombok.Data;

import java.util.List;

@Data
public class GetAuthorizationDataReqData {
    private AuthIssuerReq authIssuerReq;

    private String authDataFields;

    @Data
    public static class AuthIssuerReq {
        private Metadata metadata;
        private List<BankList> bankList;
    }

    @Data
    public static class Metadata {
        private String startDate;
        private String endDate;
        private String fetchAll;
        private String startIndex;
    }

    @Data
    public static class BankList {
        private String bankId;
        private String processorId;
        private String regionId;
        private List<CompanyList> companyList;
    }

    @Data
    public static class CompanyList {
        private List<String> acctList;
        private String companyId;
    }
}
