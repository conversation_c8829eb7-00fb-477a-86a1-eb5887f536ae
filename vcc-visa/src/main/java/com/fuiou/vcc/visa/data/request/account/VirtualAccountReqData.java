/**
 * Copyright 2019 bejson.com
 */
package com.fuiou.vcc.visa.data.request.account;

import java.util.List;

/**
 * Auto-generated: 2019-12-16 14:38:26
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class VirtualAccountReqData {

    private String accountNumber;
    private RequisitionDetail requisitionDetails;
    private String proxyPoolId;
    private String clientId;
    private String numberOfCards;
    private String messageId;
    private String action;
    private String buyerId;
    private List<OptionalInfo> optionalInfo;

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public RequisitionDetail getRequisitionDetails() {
        return requisitionDetails;
    }

    public void setRequisitionDetails(RequisitionDetail requisitionDetails) {
        this.requisitionDetails = requisitionDetails;
    }

    public void setProxyPoolId(String proxyPoolId) {
        this.proxyPoolId = proxyPoolId;
    }

    public String getProxyPoolId() {
        return proxyPoolId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setNumberOfCards(String numberOfCards) {
        this.numberOfCards = numberOfCards;
    }

    public String getNumberOfCards() {
        return numberOfCards;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getAction() {
        return action;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerId() {
        return buyerId;
    }

    public void setOptionalInfo(List<OptionalInfo> optionalInfo) {
        this.optionalInfo = optionalInfo;
    }

    public List<OptionalInfo> getOptionalInfo() {
        return optionalInfo;
    }

}