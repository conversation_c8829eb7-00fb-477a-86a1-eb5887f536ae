package com.fuiou.vcc.visa.service.impl;


import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.data.request.ChannelCloseAccountReqData;
import com.fuiou.vcc.api.data.response.ChannelBaseRspData;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.assembler.VirtualAccountAssembler;
import com.fuiou.vcc.visa.data.request.account.VirtualAccountReqData;
import com.fuiou.vcc.visa.data.response.account.Responses;
import com.fuiou.vcc.visa.data.response.account.VirtualAccountRspData;
import com.fuiou.vcc.visa.utils.VisaHttpRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * visa开户
 * Created by renzw on 2020/7/1.
 */
@Slf4j
public class VisaCloseAccountService {
    public ChannelBaseRspData closeAccount(ChannelCloseAccountReqData channelReqData) {
        VirtualAccountReqData reqData = VirtualAccountAssembler.genCloseAccount(channelReqData.getCardModel(), channelReqData.getCardNo(), channelReqData.getCardBinConf());
        String jsonString = JSONUtil.toJsonStr(reqData);
        ChannelBaseRspData rspData = new ChannelBaseRspData();
        try {
            String s = VisaHttpRequest.postObj(StaticPropertiesUtil.staticVccOpenCloseUrl, jsonString);
            VirtualAccountRspData virtualAccountRspData = JSONUtil.toBean(s, VirtualAccountRspData.class);
            Responses responses = Optional.ofNullable(virtualAccountRspData).map(VirtualAccountRspData::getResponses).map(x -> x.get(0)).orElse(null);
            if (responses.getResponseCode().equals("00")) {
                rspData.setRspCode("0000");
            } else {
                rspData.setRspCode(responses.getResponseCode());
            }
            rspData.setRspDesc(responses.getResponseDescription());
            rspData.setResSsn(reqData.getMessageId());
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            rspData.setRspCode("5138");
            rspData.setRspDesc("销户异常,请重试");
        }
        return rspData;
    }
}
