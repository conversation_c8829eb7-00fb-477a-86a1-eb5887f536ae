package com.fuiou.vcc.visa.service.impl;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.data.response.ChannelGetRateRspData;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.request.GetRateReqData;
import com.fuiou.vcc.visa.data.response.GetRateRspData;
import com.fuiou.vcc.visa.utils.SsnUtil;
import com.fuiou.vcc.visa.utils.VisaHttpRequest;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * visa开户
 * Created by renzw on 2020/7/1.
 */
@Slf4j
public class VisaGetRateService {
    public ChannelGetRateRspData getRate(String sourceCurrency, String destCurrency) {
        GetRateReqData reqData = new GetRateReqData();
        reqData.setDestinationCurrencyCode(destCurrency);
        reqData.setSourceAmount("10000");
        reqData.setSourceCurrencyCode(sourceCurrency);
        String jsonString = JSONUtil.toJsonStr(reqData);
        ChannelGetRateRspData getRateRspData = new ChannelGetRateRspData();
        try {
            String s = VisaHttpRequest.postObj(StaticPropertiesUtil.staticVccRateUrl, jsonString);
            GetRateRspData rspData = JSONUtil.toBean(s, GetRateRspData.class);
            getRateRspData.setResSsn(SsnUtil.genSsn());
            if (StrUtil.isNotBlank(rspData.getErrorMessage()) && StrUtil.isBlank(rspData.getConversionRate())) {
                getRateRspData.setRspDesc(rspData.getErrorMessage());
                getRateRspData.setRspCode("3001");
                return getRateRspData;
            }
            getRateRspData.setRspCode("0000");
            getRateRspData.setRate(new BigDecimal(rspData.getConversionRate()));
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            getRateRspData.setRspCode("5138");
            getRateRspData.setRspDesc("系统异常,请重试");
        }
        return getRateRspData;
    }
}
