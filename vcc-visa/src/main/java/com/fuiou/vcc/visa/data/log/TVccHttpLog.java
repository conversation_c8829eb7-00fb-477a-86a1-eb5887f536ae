package com.fuiou.vcc.visa.data.log;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TVccHttpLog implements Serializable {
    /**
     * 主键id
     */
    private Integer rowId;

    /**
     * 商户代码
     */
    private String mchntCd;

    /**
     * 请求ip
     */
    private String reqIp;

    /**
     * 请求地址
     */
    private String reqUrl;

    /**
     * 入参
     */
    private String requestBody;

    /**
     * 出参
     */
    private String responseBody;

    /**
     * 请求耗时
     */
    private String duration;

    /**
     * 请求线程标记
     */
    private String uuid;

    /**
     * 保留1
     */
    private String reserved1;

    /**
     * 创建时间
     */
    private Date rowCrtTs;
}
