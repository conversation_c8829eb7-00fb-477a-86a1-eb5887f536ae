package com.fuiou.vcc.visa.utils;

import cn.hutool.core.util.StrUtil;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.log.TVccHttpLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.util.Date;

/**
 * [功能描述：http请求]
 *
 * <AUTHOR>
 * @creation 2018-06-01
 * @email <EMAIL>
 */
@Component
@Slf4j
public class VisaHttpRequest {

    private static Logger logger = LoggerFactory.getLogger(VisaHttpRequest.class);

    private static CloseableHttpClient httpClient = null;

    private static final int TIME_OUT = 200 * 1000;

    private static AmqpProducerUtil staticAmqpProducerUtil = null;

    @Resource
    private AmqpProducerUtil amqpProducerUtil;

    @PostConstruct
    public void setStaticPort() {
        staticAmqpProducerUtil = amqpProducerUtil;
    }


    public static CloseableHttpClient getHttpClient2(String cerPath, String password) throws Exception {
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        logger.info("证书路径：" + cerPath);
        ClassPathResource cpr = new ClassPathResource(cerPath);
        FileInputStream instream = new FileInputStream(cpr.getFile());
        keyStore.load(instream, password.toCharArray());
        SSLContext sslContext = SSLContexts.custom().loadKeyMaterial(keyStore, password.toCharArray()).build();
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"TLSv1.2"},
                null,
                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
        CloseableHttpClient httpClient2 = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        return httpClient2;


    }



    public static String postObj(String url, String parameters) throws Exception {
        String responseString = "";
        httpClient = getHttpClient2(StaticPropertiesUtil.staticVccHttpP12, "123456");//线程池模式
        CloseableHttpResponse response = null;
        HttpPost httpPost = null;
        HttpEntity entity = null;

        try {
            httpPost = new HttpPost(url);
            String base64 = Base64.encodeBase64String((StaticPropertiesUtil.staticVccHttpUserId + ":" + StaticPropertiesUtil.staticVccHttpPassword).getBytes("UTF-8"));
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setHeader("Authorization", "Basic " + base64);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("clientId",StaticPropertiesUtil.staticClientID);
            StringEntity stringEntity = new StringEntity(parameters, "UTF-8");

            httpPost.setEntity(stringEntity);//
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(TIME_OUT).setConnectTimeout(120000)
                    .build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);

            logger.info("请求: " + httpPost.getRequestLine());
            logger.info("请求数据:" + EntityUtils.toString(httpPost.getEntity()));
            long startTime = System.currentTimeMillis();
            response = httpClient.execute(httpPost);
            logger.info("响应状态: " + response.getStatusLine());
            entity = response.getEntity();
            long diffTime = System.currentTimeMillis() - startTime;

            TVccHttpLog tVccHttpLog = new TVccHttpLog();
            tVccHttpLog.setDuration(String.valueOf(diffTime));
            tVccHttpLog.setRequestBody(parameters);
            tVccHttpLog.setUuid(MDC.get("uuid"));
            tVccHttpLog.setReqUrl(url);
            tVccHttpLog.setReqIp(StaticPropertiesUtil.staticNodeIp);
            tVccHttpLog.setRowCrtTs(new Date());
            tVccHttpLog.setMchntCd(StaticPropertiesUtil.staticMchntTheadLocal.get());
            if (entity == null) {
                logger.info("响应数据: response entity is null");
                staticAmqpProducerUtil.createCommercialLog(tVccHttpLog);
                return responseString;
            }
            responseString = EntityUtils.toString(entity, "utf-8");
            logger.info("响应数据:" + StrUtil.removeAllLineBreaks(responseString));
            tVccHttpLog.setResponseBody(responseString);
            staticAmqpProducerUtil.createCommercialLog(tVccHttpLog);
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
        } finally {
            if (null != response) {
                EntityUtils.consume(entity);
                response.close();
            }
            if (null != httpPost) {
                httpPost.releaseConnection();
            }
        }
        return responseString;
    }


}
