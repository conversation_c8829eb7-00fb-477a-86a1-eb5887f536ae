package com.fuiou.vcc.visa.service.impl;

import cn.hutool.json.JSONUtil;
import com.fuiou.vcc.api.data.response.ChannelGetCvvRspData;
import com.fuiou.vcc.api.utils.StaticPropertiesUtil;
import com.fuiou.vcc.visa.data.request.GetSecurityCodeReqData;
import com.fuiou.vcc.visa.data.response.GetSecurityCodeRspData;
import com.fuiou.vcc.visa.utils.SsnUtil;
import com.fuiou.vcc.visa.utils.VisaHttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * visa开户
 * Created by renzw on 2020/7/1.
 */
@Slf4j
public class VisaGetCvvService {
    public ChannelGetCvvRspData getSecurityCode(String accountNo, String expirationDate) {
        GetSecurityCodeReqData reqData = new GetSecurityCodeReqData();
        reqData.setClientId(StaticPropertiesUtil.staticClientID);
        reqData.setBuyerId(StaticPropertiesUtil.staticBuyerId);
        reqData.setMessageId(SsnUtil.genSsn());
        reqData.setAccountNumber(accountNo);
        reqData.setExpirationDate(expirationDate);
        String jsonString = JSONUtil.toJsonStr(reqData);
        ChannelGetCvvRspData rspData = new ChannelGetCvvRspData();
        try {
            String s = VisaHttpRequest.postObj(StaticPropertiesUtil.staticVccCvvUrl, jsonString);
            GetSecurityCodeRspData getSecurityCodeRspData = JSONUtil.toBean(s, GetSecurityCodeRspData.class);
            rspData.setRspCode(getSecurityCodeRspData.getStatusCode());
            if (getSecurityCodeRspData.isSuccess()) {
                rspData.setRspCode("0000");
            }
            rspData.setCvv(getSecurityCodeRspData.getSecurityCode());
            rspData.setRspDesc(getSecurityCodeRspData.getStatusDesc());
            rspData.setResSsn(reqData.getMessageId());
        } catch (Exception e) {
            log.error("[ 异常 ]", e);
            rspData.setRspCode("5138");
            rspData.setRspDesc("系统异常,请重试");
        }
        return rspData;
    }
}
