package com.fuiou.vcc.visa.data.request;

import lombok.Data;

import java.util.List;

@Data
public class GetTrxnDataReqData {

    private String transactionRecordFields;
    private Issuer issuer;


    @Data
    public static class Issuer {
        private List<Bank> bank;
        private Metadata metadata;
    }

    @Data
    public static class Metadata {
        private String startDate;
        private String startTime;
        private String endDate;
        private String endTime;
        private long startIndex;
    }


    @Data
    public static class Bank {
        private String bankId;
        private String processorId;
        private String regionId;
        private List<Company> company;
    }

    @Data
    public static class Company {
        private String companyId;
    }
}
