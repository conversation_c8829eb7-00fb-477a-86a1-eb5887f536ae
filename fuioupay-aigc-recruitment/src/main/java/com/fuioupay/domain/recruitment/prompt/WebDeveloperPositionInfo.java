package com.fuioupay.domain.recruitment.prompt;

/**
 * 前端资深开发工程师岗位信息
 *
 * <AUTHOR>
 */
public class WebDeveloperPositionInfo implements IPositionInfo {

    @Override
    public String getPositionName() {
        return "前端资深开发工程师";
    }

    @Override
    public String getJobResponsibilities() {
        return """
                \n<工作职责要求>
                1.负责公司相关产品的前端开发及上线后的优化与功能答疑工作，包括H5、小程序、Web基础技术研发。
                2.前端代码的优化，样式压缩、合并、请求优化等，确保前端一流的用户体验。
                3.配合需求产品人员进行前端分析与设计工作，保障业务快速、有序迭代，能主动推动和协调落地工作任务。
                4.结合业务需求，积极探索并积累前端开发模式和规范；快速响应和解决系统运行中出现的故障和问题，同时提供优化解决方案、相关版本迭代方案等。
                </工作职责要求>
                \n
                """;
    }

    @Override
    public String getQualificationRequirements() {
        return """
                \n<任职资格要求>
                大专及以上学历，计算机相关专业优先， 3-5年左右前端研发工作经验。
                1.精通各种前端技术，包括HTML5/CSS3/JavaScript（ES5/ES6）/Ajax等。
                2.对前端工程化与模块化开发有一定了解，并有实践经验（如Vue/React/webpack/commonjs等）。
                3.有H5、小程序、web商城项目等实际项目经验者优先。
                4.熟悉一门非前端的语言（如NodeJS/Java/Python）并有实践经验者优先。
                5.对代码有热诚、洁癖，熟悉eslint等规范。
                6.踏实认真，责任心和自驱力强，能独立思考，分析并解决问题。
                </任职资格要求>
                \n
                """;
    }

    @Override
    public String getEmphasis() {
        return """
                \n
                <评分分析维度>
                <第一维度（硬性筛选，淘汰项）>
                1.学历：本科及以上（计算机相关专业加分，其他专业不加分也不减分）。
                2.核心经验年限：≥ 3 年前端开发经验。
                3.最近一份工作/项目经历：必须与公司任职资格要求高度相关（如 H5、小程序、Web 商城等）。
                4.年龄限制：33周岁以内。
                > **备注**：以上任一未达标，直接评分0，返回不匹配。
                </第一维度（硬性筛选，淘汰项）>
                
                <第二维度（打分项，60–80% 权重，聚焦深度与实践）>
                1.（30%）：**精通前端核心技术及底层原理**
                    - JavaScript: 不仅限于ES6+，要求深入理解事件循环(Event Loop)、宏任务/微任务、Promise/async/await的底层实现、作用域链、闭包陷阱、内存管理、敏感信息泄露、弱网环境下的系统稳定和数据幂等、与垃圾回收机制。
                    - CSS: 能熟练运用`Grid/Flex`进行复杂布局，关键信息防遮挡或篡改，深入理解BFC、层叠上下文、浏览器渲染关键路径（CRP）并能进行渲染层面的性能优化。
                    - 浏览器/网络: 必须掌握从URL输入到页面展现的全过程，深刻理解HTTP各版本（1.1, 2, 3）特性与差异、HTTPS、TCP/IP协议，并能解决复杂网络问题（如跨域、缓存策略、安全性），跨域联调，支付回调等。
                2.（25%）：**精通主流框架及架构能力**
                    - 框架掌握度: 要求精通 Vue 或 React 其中一种的源码，能清晰阐述其核心原理（如 Vue 的响应式、依赖收集与派发更新；React 的 Fiber 架构、Hooks 实现机制、Diff 算法），交易场景下组件状态的可靠性和数据幂等性。
                    - 架构设计: 必须具备从0到1独立完成中大型项目（如SaaS平台、复杂中后台）的前端架构设计能力，包括技术选型、工程化体系搭建、状态管理方案（如 Redux-Saga/Pinia 模块化设计）的规划与落地。
                    - 组件化: 具备高复用性、高维护性的业务组件库的设计与开发经验，而不只是使用UI库。
                3.（15%）：**前端工程化与性能优化**
                    - 工程化深度: 要求对 `Webpack/Vite` 有深度性能调优经验，例如能独立编写 `Loader/Plugin`，能主导优化项目构建速度与产物体积，以及安全漏洞检测、依赖包风险评估的经验。
                    - 性能优化实战: 必须有主导线上**核心交易链路**性能优化的成功案例，能熟练运用性能监控工具，从指标（如LCP/FCP/CLS）分析、归因到解决，形成完整的优化闭环。
                4.（10%）：项目经验含金量
                    - 业务复杂度: 要求主导或核心参与过至少一个业务逻辑高度复杂（如在线编辑器、SaaS平台、金融交易系统、营销搭建平台、支付系统、清结算平台、银行风控系统、反洗钱系统、核心账务系统）或数据驱动的项目。
                    - 项目角色: 在项目中担任技术攻坚或核心模块负责人的角色，能清晰阐述所解决的关键技术难题。
                5.（10%）：技术广度与软件工程素养
                    - 跨领域能力: 熟悉 `Node.js` 并有 `BFF` (Backend for Frontend) 中间层开发经验，能独立完成接口开发与部署。
                    - 熟悉一门非前端的语言（如NodeJS/Java/Python）并有实践经验。
                    - 软件工程: 对 Web 安全有深刻理解并有实际的攻防经验（XSS, CSRF, CSP等）；必须在团队中推行并严格遵守代码规范、`Git Flow`，并主导 `Code Review`；防御**支付流程中的常见攻击**（如订单金额篡改、重复提交等）。
                6.（10%）：领导力与稳定性
                    - 沟通协作: 有主导并成功推动跨职能团队（产品、设计、后端、测试、风控）完成复杂技术项目的经验，并有可量化的成果证明。
                    - 工作稳定性: 平均每段工作经历 ≥ 2年，跳槽/空窗期情况。
                </第二维度（打分项，60–80% 权重，聚焦深度与实践）>
                
                <第三维度（加分项，20–40% 权重，筛选卓越人才）>
                1.（20%）：行业与公司背景
                    - 在头部互联网公司（如腾讯、阿里、字节跳动、美团、支付宝、微信支付、清算组织）或知名金融科技、电商公司的核心业务线有从业经验。
                2.（20%）：技术领导力与影响力
                    - 作为前端技术负责人或架构师，主导过团队技术栈的重大升级或演进（例如从Vue 2到3的平滑迁移、引入微前端架构、支付核心链路的架构升级），并有明确的业务收益。
                3.（20%）：顶级项目经验
                    - 主导或核心参与过百万级日活（DAU） C端产品，或支持高复杂度、高实时性（如在线协作、实时数据可视化）的B端产品，能阐述其架构设计与技术挑战。
                4.（15%）：社区技术贡献
                    - 拥有 GitHub Star > 200 的个人开源项目；或为知名开源项目（如 Vue, React, Vite, ECharts 等）贡献过被合并的、有价值的 PR。
                    - 在专业技术社区（如掘金、思否、InfoQ）发表过产生广泛影响力的系列技术文章或深度好文（阅读量 > 2万）。
                5.（15%）：前沿技术与全栈能力
                    - 在 `Node.js` 全栈开发、`Serverless`、`WebAssembly`、`图形学 (WebGL)`、跨端框架 (`React Native/Flutter`) 、`具备国密算法在前端应用的实践经验者`等领域有深入的实践经验和产出。
                6.（10%）：软技能与分享精神
                    - 作为主要讲师，在公司内外组织过5次以上的、有影响力的技术培训或在公开技术大会（如 QCon, ArchSummit）上有过演讲经历，特别是关于Web安全、金融业务知识或前端架构的分享。
                </第三维度（加分项，20–40% 权重，筛选卓越人才）>
                
                <评分说明>
                - 如果技能是 熟悉/了解，只能说他了解，分值范围 10%-30%。
                - 如果技能是 掌握/理解，只能说他会用，分值范围 30%-50%。
                - 如果技能是 熟练/擅长，说明他用得不错，分值范围 40-60%。
                - 如果技能是 精通/深入理解，可以说他已掌握这项技能的底层原理和最佳实践，分值范围 80%-100%。
                </评分说明>
                </评分分析维度>
                \n
                """;
    }
} 