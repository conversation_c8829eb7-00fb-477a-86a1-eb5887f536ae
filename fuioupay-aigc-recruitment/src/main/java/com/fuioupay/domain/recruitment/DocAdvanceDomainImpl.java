package com.fuioupay.domain.recruitment;

import com.aliyun.teautil.models.RuntimeOptions;
import com.fuioupay.common.utils.JacksonUtil;
import com.fuioupay.domain.recruitment.dto.EmailAttachmentDTO;
import com.fuioupay.domain.recruitment.po.DocAdvanceEntity;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.aliyun.docmind_api20220711.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.docmind_api20220711.Client;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Slf4j
@Component
public class DocAdvanceDomainImpl implements DocAdvanceDomain {

    @Value("${aliyun.accesskey.id}")
    String accesskey;
    @Value("${aliyun.accesskey.secret}")
    String accessSecret;
    private Client client;

    // 每隔10s查一次状态
    private static final int POLLING_INTERVAL_SECONDS = 10000;
    // 固定线程数
    private final ExecutorService parserExecutor = Executors.newFixedThreadPool(5);

    @PostConstruct
    public void init() throws Exception {
        Config config = new Config().setAccessKeyId(accesskey).setAccessKeySecret(accessSecret);
        config.endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
        client = new Client(config);
    }

    @Override
    public List<EmailAttachmentDTO> getText(List<EmailAttachmentDTO> attachmentDTOS) {
        List<EmailAttachmentDTO> tempAttachments = new CopyOnWriteArrayList<>(attachmentDTOS);
        List<EmailAttachmentDTO> result = new ArrayList<>();

        Map<String, EmailAttachmentDTO> requestMap = new ConcurrentHashMap<>();

        CountDownLatch latch = new CountDownLatch(tempAttachments.size());
        tempAttachments.forEach(file -> {
            parserExecutor.submit(() -> {
                try {
                    String requestId = startParser(file.getFilename(), file.getContent());
                    // file.setContent(new byte[0]);
                    requestMap.put(requestId, file);
                } catch (Exception e) {
                    log.error("Error processing file " + file.getFilename() + ": " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        });

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            Thread.currentThread().interrupt();
        }

        requestMap.forEach((key, value) -> processParserResult(key, value, result));

        return result;
    }

    /**
     * 处理解析结果，包括轮询状态和获取最终结果
     *
     * @param requestId  请求ID
     * @param attachment 附件信息
     * @param resultList 结果列表
     */
    private void processParserResult(String requestId, EmailAttachmentDTO attachment,
            List<EmailAttachmentDTO> resultList)
    {
        // attachment.setContent(new byte[0]);
        try {
            Map<String, Object> queryResult = pollForParserCompletion(requestId);
            if (queryResult == null) return;

            // 根据解析状态处理结果
            boolean isSuccess = "success".equals(queryResult.get("status"));
            try {
                if (isSuccess) {
                    attachment.setText(getParserResult(requestId, 0,
                            Integer.parseInt(queryResult.get("numberOfSuccessfulParsing").toString())));
                } else {
                    attachment.setText(
                            queryResult.get("message") != null ? queryResult.get("message").toString() : "解析失败");
                }
                attachment.setSuccess(isSuccess);
                resultList.add(attachment);
            } catch (Exception e) {
                log.error("Failed to process result for key " + requestId + ": " + e.getMessage());
            }
        } catch (Exception e) {
            attachment.setText("解析失败");
            attachment.setSuccess(false);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 轮询等待解析完成
     *
     * @param requestId 请求ID
     * @return 解析状态信息Map，如果失败或超时返回null
     */
    private Map<String, Object> pollForParserCompletion(String requestId) {
        int maxAttempts = 30; // 最多尝试30次，总共5分钟

        for (int attempts = 0; attempts < maxAttempts; attempts++) {
            try {
                Map<String, Object> statusResponse = queryParserStatus(requestId);
                // 如果解析成功或失败，返回结果
                if (statusResponse != null) {
                    String status = statusResponse.get("status").toString();
                    if ("success".equals(status) || "Fail".equals(status)) {
                        return statusResponse;
                    }
                }
            } catch (Exception e) {
                log.error("Query parser status error for key ：{}，{}，{}", requestId, e.getMessage(), e);
            }

            try {
                TimeUnit.MILLISECONDS.sleep(POLLING_INTERVAL_SECONDS);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.error(ie.getMessage(), ie);
                throw new RuntimeException("Thread was interrupted during polling", ie);
            }
        }

        return null;
    }

    /**
     * 先上传，然后获取requestId
     *
     * @param fileName
     * @param bytes
     */
    private String startParser(String fileName, byte[] bytes) {
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            SubmitDocParserJobAdvanceRequest advanceRequest = new SubmitDocParserJobAdvanceRequest();
            advanceRequest.fileUrlObject = new ByteArrayInputStream(bytes);
            advanceRequest.fileName = fileName;
            SubmitDocParserJobResponse response = client.submitDocParserJobAdvance(advanceRequest, runtime);
            return response.getBody().getData().getId();
        } catch (Exception e) {
            log.error("Error submitting parser job for file ：{}，{}，{}", fileName, e.getMessage(), e);
            throw new RuntimeException("Failed to submit document parsing job", e);
        }
    }

    private Map<String, Object> queryParserStatus(String requestId) {
        try {
            QueryDocParserStatusRequest resultRequest = new QueryDocParserStatusRequest();
            resultRequest.id = requestId;
            QueryDocParserStatusResponse response = client.queryDocParserStatus(resultRequest);
            Map<String, Object> result = new HashMap<>();

            if (response.getBody() != null && response.getBody().getData() != null) {
                result.put("status", response.getBody().getData().getStatus());
                result.put("numberOfSuccessfulParsing", response.getBody().getData().getNumberOfSuccessfulParsing());
                result.put("message", response.getBody().getMessage());
            }

            String status = (String) result.get("status");
            if ("success".equals(status) || "Fail".equals(status)) {
                return result;
            }
            return null;
        } catch (Exception e) {
            log.error("Error querying parser status for request ID ：{}，{}，{}", requestId, e.getMessage(), e);
            return null;
        }
    }

    private String getParserResult(String requestId, Integer layoutNum, Integer layoutStepSize) {
        try {
            GetDocParserResultRequest resultRequest = new GetDocParserResultRequest();
            resultRequest.id = requestId;
            resultRequest.layoutStepSize = layoutStepSize;
            resultRequest.layoutNum = layoutNum;
            GetDocParserResultResponse response = client.getDocParserResult(resultRequest);

            StringBuilder text = new StringBuilder();
            try {
                DocAdvanceEntity docAdvanceEntity = JacksonUtil.parse(JacksonUtil.toJson(response.getBody()),
                        DocAdvanceEntity.class);
                docAdvanceEntity.getData().getLayouts().forEach(m -> text.append(m.getText()).append("\n"));
            } catch (Exception e) {
                log.error("Error parsing layouts: {}，{}，{}", requestId, e.getMessage(), e);
            }
            return text.toString();
        } catch (Exception e) {
            log.error("Error getting parser result for request ID ：{}，{}，{}", requestId, e.getMessage(), e);
            return "";
        }
    }

}
