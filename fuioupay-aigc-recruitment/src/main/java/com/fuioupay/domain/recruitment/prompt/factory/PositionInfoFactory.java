package com.fuioupay.domain.recruitment.prompt.factory;

import com.fuioupay.domain.recruitment.enums.PositionType;
import com.fuioupay.domain.recruitment.prompt.*;

/**
 * 岗位信息工厂
 * 创建各类岗位信息实例
 *
 * <AUTHOR>
 */
public class PositionInfoFactory {

    /**
     * 根据岗位类型创建岗位信息实例
     */
    public static IPositionInfo createPositionInfo(PositionType positionType) {
        return switch (positionType) {
            case JAVA -> new JavaDeveloperPositionInfo();
            case TEST -> new TestEngineerPositionInfo();
            case WEB -> new WebDeveloperPositionInfo();
            case PRODUCT -> new ProductManagerPositionInfo();
            case ERROR -> null;
        };
    }
} 