package com.fuioupay.domain.recruitment.prompt;

/**
 * 测试工程师岗位信息
 *
 * <AUTHOR>
 */
public class TestEngineerPositionInfo implements IPositionInfo {

    @Override
    public String getPositionName() {
        return "高级软件测试工程师";
    }

    @Override
    public String getJobResponsibilities() {
        return """
                \n
                <工作职责要求>
                1、参与公司产品技术方案整体讨论，根据软件设计需求制定测试计划，设计测试数据和测试用例；
                2、有效地执行测试用例，提交测试报告；
                3、准确地定位并跟踪问题，推动问题及时合理地解决；
                4、完成对产品的**集成测试**与系统测试，具备系统功能测试及API接口测试能力。
                </工作职责要求>
                \n
                """;
    }

    @Override
    public String getQualificationRequirements() {
        return """
                \n
                <任职资格要求>
                1.本科及以上学历，计算机相关专业优先，3-5年工作经验
                2.大型互联网方面实战经验，熟悉接口测试，熟练掌握基本的接口测试工具如Jmeter,soupUl,postman等
                3.熟悉Sql、Mysql，具备SQL语句应用能力等
                4.会基本的Linux命令，能看懂基本的后台日志
                5.具有很强的分析复杂问题和解决复杂问题的能力，有强烈的责任心和使命感，良好的沟通表达能力和团队协作能力:
                6.具有金融行业或互联网行业应用经验者优先。
                </任职资格要求>
                \n
                """;
    }

    @Override
    public String getEmphasis() {
        return """
                \n
                <评分分析维度>
                <第一维度（硬性筛选，淘汰项）>
                - 学历：本科及以上（计算机相关专业加分，其他专业不加分也不减分）。
                - 核心经验年限：≥3 年 测试 或 ≥2 年框架设计。
                - 最近一份工作/项目经历必须跟公司**任职资格要求**相关。
                - 年龄限制：36周岁以内。
                - 性别限制：必须男性。
                > **备注**：以上任一未达标，直接评分0，返回不匹配。
                </第一维度（硬性筛选，淘汰项）>
                
                <第二维度（加分项，20–40% 权重）>
                - （15%）：银行/金融/支付/头部电商行业经验。
                - （15%）：曾在公司担任测试经理/架构师/测试负责人/测试专家/资深测试工程师等角色。
                - （15%）：参与过高并发测试、大流量系统的性能测试，如日交易量超过1万笔，TPS(每秒事务数)达到200以上，QPS达到1000以上，。
                - （15%）：有Java/Python/C++/Go/NodeJs等开发经验，或者开发转测试经验者优先。
                - （10%）：有个人开源项目贡献或在GitHub上有技术贡献、技术社区活跃记录，体现较强的技术热情与影响力。
                - （10%）：大厂/上市公司背景（腾讯、阿里、携程、百度、小米、华为、海康威视、字节、美团等）。
                - （10%）：软技能：培训/讲座、技术分享、发表文章。
                - （10%）：熟悉DevOps（CI/CD）流程、做过测试自动化、搭建过自动化测试框架等。
                </第二维度（加分项，20–40% 权重）>
                
                <评分说明>
                - 如果技能是熟悉/了解，只能说他了解。分值范围10%-30%。
                - 如果技能是掌握/理解，只能说他会用。分值范围30%-50%。
                - 如果技能是熟练/擅长，只能说他会用。分值范围40-60%。
                - 如果技能是精通/深入理解，可以说他掌握了这项技能。分值范围80%-100%。
                </评价说明>
                </评分分析维度>
                \n
                """;
    }
} 