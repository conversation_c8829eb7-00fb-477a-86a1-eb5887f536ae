package com.fuioupay.domain.recruitment.prompt;

import com.fuioupay.domain.AIGCException;
import com.fuioupay.domain.recruitment.enums.PositionType;
import com.fuioupay.domain.recruitment.prompt.factory.PositionInfoFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简历招聘
 * 系统提示词
 *
 * <AUTHOR>
 */
public class ResumeAnalysicPrompt {

    private static final ResumeAnalysicPrompt INSTANCE = new ResumeAnalysicPrompt();

    private final Map<PositionType, IPositionInfo> positionInfoMap = new HashMap<>();

    private ResumeAnalysicPrompt() {
        for (PositionType type : PositionType.values()) {
            if (!type.getIsEffective()) {
                continue;
            }
            registerPositionInfo(type, PositionInfoFactory.createPositionInfo(type));
        }
    }

    public static ResumeAnalysicPrompt getInstance() {
        return INSTANCE;
    }

    public void registerPositionInfo(PositionType type, IPositionInfo positionInfo) {
        positionInfoMap.put(type, positionInfo);
    }

    /**
     * 为指定岗位生成简历分析提示词
     */
    public String getSystemSuggestionPromptForPosition(PositionType positionType) {
        return new PromptBuilder().forPosition(positionType).build();
    }

    /**
     * 为指定岗位生成总结，结论提示词
     */
    public String getSystemSummaryPromptForPosition(PositionType positionType) {
        return new PromptBuilder().forPosition(positionType).buildSummary();
    }

    /**
     * 简历分析提示词构建器
     */
    public static class PromptBuilder {
        // 岗位类型
        private PositionType positionType;
        // 岗位名称
        private String positionName;
        // 任职资格
        private String jobResponsibilities;
        // 岗位要求
        private String qualificationRequirements;
        // 该岗位侧重点
        private String emphasis;

        public PromptBuilder() {
        }

        public PromptBuilder forPosition(PositionType positionType) {
            this.positionType = positionType;
            IPositionInfo info = INSTANCE.positionInfoMap.get(positionType);
            if (info != null) {
                this.positionName = info.getPositionName();
                this.jobResponsibilities = info.getJobResponsibilities();
                this.qualificationRequirements = info.getQualificationRequirements();
                this.emphasis = info.getEmphasis();
            }
            return this;
        }

        public String build() {
            checkParams();
            return INSTANCE.generateSuggestionSystemPrompt(positionName, jobResponsibilities, qualificationRequirements,
                    emphasis);
        }

        public String buildSummary() {
            checkParams();
            return INSTANCE.generateSummarySystemPrompt(positionName, jobResponsibilities, qualificationRequirements,
                    emphasis);
        }

        private void checkParams() {
            if (positionType == null &&
                    (positionName == null || jobResponsibilities == null || qualificationRequirements == null))
            {
                throw new AIGCException(9999, "岗位jd未配置！");
            }
        }
    }

    /**
     * 生成简历分析，系统提示词
     */
    private String generateSuggestionSystemPrompt(String positionName, String jobResponsibilities,
            String qualificationRequirements, String emphasis)
    {
        StringBuilder prompt = new StringBuilder();
        prompt.append("您是一位全球最专业的HR招聘专家，根据用人部门提供的《");
        prompt.append(positionName);
        prompt.append("""
                》的岗位要求：<工作职责要求><任职资格要求><评分分析维度>，对候选人进行评价和打分。
                请输出最详细的，专业的，专家级的分析结果，评分理由不超过 1000 个汉字。
                
                工作年限计算时，如果涉及到至今，就把当前时间代入计算。
                
                当前时间：""");
        prompt.append(LocalDateTime.now());
        prompt.append("\n\n");
        prompt.append(jobResponsibilities);
        prompt.append(qualificationRequirements);
        prompt.append(emphasis);
        prompt.append("""
                
                ## 输出内容
                - 候选人姓名。
                - 评分：0 - 5 分制（0 分最低，5 分最高）。
                - 评分理由：请客观、公正、综合、全面的进行候选人测评。
                
                ## 要求：
                评分大于3分的候选人，请重点关注该岗位的技术深度。
                """);
        return prompt.toString();
    }

    /**
     * 生成总结排名，面试建议，系统提示词
     */
    private String generateSummarySystemPrompt(String positionName, String jobResponsibilities,
            String qualificationRequirements, String emphasis)
    {
        StringBuilder prompt = new StringBuilder();
        prompt.append("您是一位全球最专业的技术专家，根据用人部门提供的《");
        prompt.append(positionName);
        prompt.append("""
                》的岗位要求：<工作职责要求><任职资格要求><评分分析维度>，对候选人进行详细的、专业的、专家级的技术评价。每个候选人的评分理由请严格限制在500个汉字以内。
                输出内容使用<HtmlContent><MatchName><PendingName>标签包裹起来。
                <HtmlContent>内容为全面分析后的层次清晰的Facebook UI风格的且样式靠左展示的Html格式的评分理由，每个候选人用标签包裹。
                <MatchName>内容为分析出的匹配的候选人姓名，英文逗号分隔。
                <PendingName>内容为分析出的待定状态的候选人姓名，英文逗号分隔。
                
                当前时间：""");
        prompt.append(LocalDateTime.now());
        prompt.append("\n\n");
        prompt.append(jobResponsibilities);
        prompt.append(qualificationRequirements);
        prompt.append(emphasis);
        prompt.append("""
                
                ## 输出内容
                - 候选人姓名。
                - 评分。
                - 是否匹配：≥4 分为匹配；4> 且 ≥3 为待定；否则为不匹配。
                - 评分理由。
                
                ## 要求：
                - 评分大于3分的候选人，请重点分析候选人在该岗位上的技术深度是否达标。
                - 你需要严格按照"输出格式示例"的格式仅返回<Root><HtmlContent><MatchName><PendingName>标签包裹的数据，务必不要在标签外增加任何解释性回答或说明。
                - 返回<HtmlContent>内容中，请包含所有候选人，如果不匹配，请分类汇总下，简洁概述，但是不要遗漏任何候选人的姓名。
                
                ## 输出格式示例：
                <Root>
                <HtmlContent>
                <div class="...">
                ...
                </div>
                <style>
                ...
                </style>
                </HtmlContent>
                <MatchName>...</MatchName>
                <PendingName>...</PendingName>
                </Root>
                """);
        return prompt.toString();
    }
}
