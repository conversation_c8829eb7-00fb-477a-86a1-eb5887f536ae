package com.fuioupay.domain.recruitment;

import cn.hutool.core.collection.CollUtil;
import com.fuioupay.domain.recruitment.dto.EmailAttachmentDTO;
import com.fuioupay.domain.recruitment.mapper.EmailAttachmentInfoMapper;
import com.fuioupay.domain.recruitment.po.EmailAttachmentInfo;
import jakarta.mail.search.AndTerm;
import jakarta.mail.search.ComparisonTerm;
import jakarta.mail.search.SearchTerm;
import jakarta.mail.search.SentDateTerm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeUtility;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.BodyPart;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Store;
import jakarta.mail.Folder;
import jakarta.mail.Address;
import jakarta.mail.Session;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 邮箱简历下载
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EmailAttachmentDomainImpl implements EmailAttachmentDomain {

    @Resource
    MailProperties mailProperties;
    @Resource
    EmailAttachmentInfoMapper emailAttachmentInfoMapper;

    @Override
    public List<EmailAttachmentDTO> downloadAttachments(LocalDateTime startTime, LocalDateTime endTime, String to,
            String... subjectFilter)
    {
        List<EmailAttachmentInfo> attachmentsInfo = new ArrayList<>();

        Properties props = new Properties();
        props.put("mail.debug", "true");
        props.put("mail.imaps.partialfetch", "false");
        props.put("mail.imap.ssl.trust", "*");
        props.put("mail.imaps.port", "993");
        props.put("mail.imaps.ssl.enable", "true");
        props.put("mail.imaps.auth", "true");
        props.put("mail.imaps.host", mailProperties.getHost());
        Session session = Session.getInstance(props);
        try (Store store = session.getStore("imaps");) {
            store.connect(mailProperties.getHost(), mailProperties.getUsername(), mailProperties.getPassword());
            try (Folder inbox = store.getFolder("INBOX");) {
                inbox.open(Folder.READ_ONLY);

                SearchTerm searchTerm = buildDateSearchTerm(startTime, endTime);
                Message[] messages = searchTerm != null ? inbox.search(searchTerm) : inbox.getMessages();

                Multipart mp = null;
                BodyPart bp = null;
                String dispo = null, filename = null;
                byte[] content = new byte[0];

                for (Message message : messages) {
                    if (!isMessageMatch(message, startTime, endTime, to, subjectFilter)) continue;

                    mp = (Multipart) message.getContent();
                    for (int i = 0; i < mp.getCount(); i++) {
                        bp = mp.getBodyPart(i);
                        dispo = bp.getDisposition();
                        filename = MimeUtility.decodeText(Optional.ofNullable(bp.getFileName()).orElse(""));

                        // 只取pdf和word
                        if (!isAddAttach(dispo, filename, bp)) continue;

                        content = ((MimeBodyPart) bp).getInputStream().readAllBytes();
                        attachmentsInfo.add(
                                new EmailAttachmentInfo(filename, content.length, content, message.getSubject()));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("Error downloading attachments: " + e.getMessage());
        }
        return emailAttachmentInfoMapper.toDTO(attachmentsInfo);
    }

    // 构建时间范围搜索条件 // 使用搜索条件获取符合条件的邮件
    private SearchTerm buildDateSearchTerm(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null && endTime == null) {
            return null;
        }
        List<SearchTerm> terms = new ArrayList<>();
        if (startTime != null) {
            terms.add(new SentDateTerm(ComparisonTerm.GE,
                    Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant())));
        }
        if (endTime != null) {
            terms.add(
                    new SentDateTerm(ComparisonTerm.LE, Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant())));
        }
        return CollUtil.isEmpty(terms) ? null : new AndTerm(terms.toArray(new SearchTerm[0]));
    }

    private boolean isAddAttach(String dispo, String filename, BodyPart bp) {
        return (Part.ATTACHMENT.equalsIgnoreCase(dispo) || Part.INLINE.equalsIgnoreCase(dispo) || filename != null) &&
                bp instanceof MimeBodyPart && (filename.endsWith(".pdf") || filename.endsWith(".doc") ||
                filename.endsWith(".docx"));
    }

    private boolean isMessageMatch(Message message, LocalDateTime startTime, LocalDateTime endTime, String to,
            String... subjectFilter) throws MessagingException
    {
        if (!message.isMimeType("multipart/*")) return false;

        // 1. 至少有一个条件
        if ((startTime == null || endTime == null) && StringUtils.isBlank(to) &&
                (subjectFilter == null || subjectFilter.length == 0)) return false;

        // 2. 检查主题过滤（如果提供了 subjectFilter）
        if (subjectFilter != null && subjectFilter.length > 0) {
            boolean subjectMatches = false;
            for (String filter : subjectFilter) {
                if (message.getSubject() != null && message.getSubject().contains(filter)) {
                    subjectMatches = true;
                    break;
                }
            }
            if (!subjectMatches) {
                return false;
            }
        }

        // 3. 检查发件人（如果提供了 to）
        if (StringUtils.isNotBlank(to)) {
            Address[] fromAddresses = message.getFrom();
            if (fromAddresses == null || fromAddresses.length == 0) {
                return false;
            }
            boolean senderMatches = Arrays.stream(fromAddresses).anyMatch(addr -> addr.toString().contains(to));
            if (!senderMatches) {
                return false;
            }
        }

        // 4. 检查时间范围（如果提供了 startTime 和 endTime）
        if (startTime != null && endTime != null) {
            LocalDateTime messageDate = LocalDateTime.ofInstant(message.getSentDate().toInstant(),
                    ZoneId.systemDefault());
            if (messageDate.isBefore(startTime) || messageDate.isAfter(endTime)) {
                return false;
            }
        }

        return true;
    }
}