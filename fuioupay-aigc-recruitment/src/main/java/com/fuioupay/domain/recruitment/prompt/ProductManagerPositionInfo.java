package com.fuioupay.domain.recruitment.prompt;

/**
 * 产品经理岗位信息
 *
 * <AUTHOR>
 */
public class ProductManagerPositionInfo implements IPositionInfo {

    @Override
    public String getPositionName() {
        return "产品经理";
    }

    @Override
    public String getJobResponsibilities() {
        return """
                <工作职责要求>
                
                </工作职责要求>
                """;
    }

    @Override
    public String getQualificationRequirements() {
        return """
                <任职资格要求>
                
                </任职资格要求>
                """;
    }

    @Override
    public String getEmphasis() {
        return """
                <该岗位招聘侧重点>
                
                </该岗位招聘侧重点>
                """;
    }
} 