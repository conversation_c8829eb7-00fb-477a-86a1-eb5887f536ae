package com.fuiou.ccs.system.code.generator;

import com.fuiou.code.generator.SimpleAutoGenerator;
import com.fuiou.code.generator.config.DataSourceConfig;
import com.fuiou.code.generator.config.GlobalConfig;
import com.fuiou.code.generator.config.NamingConfig;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.ibatis.datasource.unpooled.UnpooledDataSource;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * db2 代码生成
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class Db2AutoGenerator extends SimpleAutoGenerator {

    private static Map<String, String> staticMap = new HashMap();

    static {
        staticMap.put("**************", "zhaobo");
        staticMap.put("**************", "zhangsl");
        staticMap.put("*************","Xuejing.Yang");
        staticMap.put("**************","kongdz");
        staticMap.put("**************","Jiane");
    }

    /**
     * 列名不做驼峰转化
     */
    @Override
    protected NamingConfig namingConfig() {
        return super.namingConfig().addRemoveTablePrefixs("T_SYS_");
    }

    @SneakyThrows
    @Override
    public GlobalConfig globalConfig() {
        InetAddress ip4 = Inet4Address.getLocalHost();
        System.out.println(ip4.getHostAddress());
        String author = staticMap.get(ip4.getHostAddress()) == null ? "" : staticMap.get(ip4.getHostAddress());
        return new GlobalConfig(author, "1.0.0", "com.fuiou.ccs.system", "")
                .swagger(true).projectName("ccs-system").outputDir("D:\\Downloads\\Generator");
    }


    @Override
    public DataSourceConfig dataSourceConfig() {
        String driverName = "com.ibm.db2.jcc.DB2Driver";
        String url = "*************************************************************;";
        String username = "db2inst1";
        String password = "db2pass1";
        UnpooledDataSource dataSource = new UnpooledDataSource(driverName, url, username, password);
        return new DataSourceConfig(dataSource).schemaName("DB2INST1");
    }

    @Override
    public List<String> includeTables() {
        return Lists.newArrayList("T_SYS_CALENDAR");
    }

    public static void main(String[] args) {
        Db2AutoGenerator autoGenerator = new Db2AutoGenerator();
        autoGenerator.execute();
    }

}
