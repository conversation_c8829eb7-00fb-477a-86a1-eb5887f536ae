package com.fuiou.ccs.system.service;

import com.fuiou.ccs.system.SystemApplication;
import com.fuiou.ccs.system.constants.CcsCacheConstants;
import com.fuiou.redis.util.RedisUtil;
import com.fuiou.sms.client.SmsClient;
import com.fuiou.sms.response.SmsResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @ClassName: CcsPermissionTest
 * @Description:
 * @Author: zhangsl
 * @Date: 2022/6/15 10:00
 */
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SystemApplication.class, webEnvironment = SpringBootTest.WebEnvironment.MOCK)
public class CcsPermissionTest {
    @Autowired
    CcsPermissionService ccsPermissionService;
    @Autowired
    CcsSysUserService sysUserService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    SmsClient smsClient;
    @Autowired
    CcsMenuService ccsMenuService;

    @Test
    public void test01() {
        String redisKey = CcsCacheConstants.CCS_USER_FORGET_PASSWORD_KEY_MOBILE + "zhangsl";
//        String redisKey = CLIENT_CACHE_PREFIX + "ccs";
//        CcsClientDTO redisValue = redisUtil.get(redisKey);
        String redisValue = redisUtil.get(redisKey);
        System.out.println(redisValue);
//        redisUtil.del(redisKey);
        System.out.println(redisUtil.hasKey(redisKey));
    }


    @Test
    public void forgetPassword() {
        System.out.println(sysUserService.forgetPassword("ccs", "zhangsl","15518531188","mobile", "n6fk", "df65d774-9c90-488a-a4af-3dbabdcbcba2"));
    }

    @Test
    public void updatePassword() {
        sysUserService.updatePassword("mobile", "aijy", "810825", "123456");
    }

    @Test
    public void sendSms() {
        SmsResult send = smsClient.send("15221823156", "测试短信功能", "ccs");
        System.out.println(send);
    }

    @Test
    public void deleteMenu() {
        ccsMenuService.deleteMenu(1457589642307100674l);
    }

    @Test
    public void refreshTest(){
        System.out.println(ccsPermissionService.refreshPermissionRules());
    }
}
