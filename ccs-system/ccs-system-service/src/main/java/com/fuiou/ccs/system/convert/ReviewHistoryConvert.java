package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.repository.entity.ReviewHistory;
import com.fuiou.ccs.system.repository.entity.ReviewRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 复核历史 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface ReviewHistoryConvert {

    ReviewHistoryConvert INSTANCE = Mappers.getMapper(ReviewHistoryConvert.class);

    @Mappings({})
    ReviewHistory toEntity(ReviewRecord reviewRecord);

}
