package com.fuiou.ccs.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.system.enums.GroupLogEnums;
import com.fuiou.ccs.system.repository.entity.SysGroupLog;
import com.fuiou.ccs.system.repository.mapper.SysGroupLogMapper;
import com.fuiou.ccs.system.service.SysGroupLogService;
import com.fuiou.ccs.system.vo.SysGroupMemberVO;
import com.fuiou.common.utils.AuthUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作组日志 服务实现类
 *
 * <AUTHOR> @since 1.0.0
 */
@Service
public class SysGroupLogServiceImpl extends ServiceImpl<SysGroupLogMapper, SysGroupLog> implements SysGroupLogService {


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean saveGroupLog(Long groupId, GroupLogEnums.TypeEnums type, String content) {
        return save(createdGroupLog(groupId, type, null, content, LocalDateTime.now(), AuthUtil.getUserId()));
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean batchSaveLog(Long groupId, List<SysGroupMemberVO> list, GroupLogEnums.TypeEnums type, String content) {
        LocalDateTime createTime = LocalDateTime.now();
        if (groupId != null) {
            for (SysGroupMemberVO member : list) {
                this.save(createdGroupLog(groupId, type, member.getGroupMemberId(), content + "【" + member.getEmpName() + "】", createTime, AuthUtil.getUserId()));
            }
        } else {
            for (SysGroupMemberVO member : list) {
                this.save(createdGroupLog(member.getGroupId(), type, member.getGroupMemberId(), content + "【" + member.getEmpName() + "】", createTime, AuthUtil.getUserId()));
            }
        }
        return true;
    }


    private SysGroupLog createdGroupLog(Long groupId, GroupLogEnums.TypeEnums type, Long groupMemberId, String content, LocalDateTime createTime, String creatorId) {
        SysGroupLog newLog = new SysGroupLog();
        newLog.setType(type.getValue());
        if (creatorId == null) {
            newLog.setCreatorId(AuthUtil.getUserId());
        } else {
            newLog.setCreatorId(creatorId);
        }
        newLog.setCreateTime(createTime);
        newLog.setGroupId(groupId);
        newLog.setGroupMemberId(groupMemberId);
        newLog.setContent(content);
        return newLog;
    }

}
