package com.fuiou.ccs.system.controller;

import com.fuiou.ccs.system.convert.SysGroupMemberConvert;
import com.fuiou.ccs.system.param.SysGroupMemberParam;
import com.fuiou.ccs.system.query.SysGroupMemberQuery;
import com.fuiou.ccs.system.repository.entity.SysGroupMember;
import com.fuiou.ccs.system.service.SysGroupMemberService;
import com.fuiou.ccs.system.vo.SysGroupMemberVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工作组成员 前端控制器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"工作组成员相关接口"})
@RequestMapping("/group-Members")
public class SysGroupMemberController extends BaseController {

    final SysGroupMemberService sysGroupMemberService;

    @GetMapping("/groupMemberList")
    @ApiOperation(value = "查询所有工作组成员")
    ApiResult<List<SysGroupMemberVO>> groupMemberList(SysGroupMemberQuery query) {
        return ApiResult.data(sysGroupMemberService.listPage(query));

    }

    @GetMapping("/detailById/{id}")
    @ApiOperation(value = "工作组成员详情")
    @ApiImplicitParam(name = "id", value = "工作组成员ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<SysGroupMemberVO> detailById(@PathVariable("id") Long id) {
        SysGroupMember sysGroupMember = sysGroupMemberService.getById(id);
        return ApiResult.data(SysGroupMemberConvert.INSTANCE.toVO(sysGroupMember));
    }

    @PostMapping("/saveGroupMember")
    @ApiOperation(value = "创建工作组成员")
    ApiResult<Void> saveGroupMember(@RequestBody @Validated(SysGroupMemberParam.Create.class) SysGroupMemberParam sysGroupMemberParam) {
        return ApiResult.status(sysGroupMemberService.saveGroupMember(SysGroupMemberConvert.INSTANCE.toEntity(sysGroupMemberParam)));
    }

}

