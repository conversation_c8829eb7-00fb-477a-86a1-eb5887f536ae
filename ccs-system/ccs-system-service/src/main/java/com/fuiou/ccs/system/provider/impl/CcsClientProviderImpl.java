package com.fuiou.ccs.system.provider.impl;

import com.fuiou.ccs.system.dto.CcsClientDTO;
import com.fuiou.ccs.system.provider.CcsClientProvider;
import com.fuiou.ccs.system.service.CcsClientService;
import com.fuiou.common.api.ApiResult;

import org.apache.dubbo.config.annotation.DubboService;

/**
 * 客户端对外接口实现
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@DubboService
public class CcsClientProviderImpl implements CcsClientProvider {

    final CcsClientService ccsClientService;

    public CcsClientProviderImpl(CcsClientService ccsClientService) {
        this.ccsClientService = ccsClientService;
    }

    @Override
    public ApiResult<CcsClientDTO> getClientByClientId(String clientId) {
        return ApiResult.data(ccsClientService.getClientByClientId(clientId));
    }

}
