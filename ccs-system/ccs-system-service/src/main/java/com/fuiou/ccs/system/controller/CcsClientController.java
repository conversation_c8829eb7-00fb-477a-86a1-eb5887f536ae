package com.fuiou.ccs.system.controller;

import com.fuiou.ccs.system.query.CcsClientQuery;
import com.fuiou.ccs.system.vo.CcsClientVO;
import lombok.AllArgsConstructor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import com.fuiou.web.core.controller.BaseController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.fuiou.common.api.ApiResult;
import com.fuiou.query.util.QueryUtil;
import com.fuiou.ccs.system.repository.entity.CcsClient;
import com.fuiou.ccs.system.param.CcsClientParam;
import com.fuiou.ccs.system.convert.CcsClientConvert;
import com.fuiou.ccs.system.service.CcsClientService;

import java.util.List;
import javax.validation.constraints.Size;

/**
 * 客户端 前端控制器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"客户端相关接口"})
@RequestMapping("/clients")
public class CcsClientController extends BaseController {

    final CcsClientService ccsClientService;

    @GetMapping
    @ApiOperation(value = "查询所有客户端(分页)")
    ApiResult<IPage<CcsClientVO>> page(CcsClientQuery query) {
        QueryWrapper<CcsClient> queryWrapper = QueryUtil.getQueryWrapper(query);
        IPage<CcsClient> page = ccsClientService.page(getPage(), queryWrapper);
        return ApiResult.data(page.convert(CcsClientConvert.INSTANCE::toVO));
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "客户端详情")
    @ApiImplicitParam(name = "id", value = "客户端ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<CcsClientVO> get(@PathVariable("id") Long id) {
        CcsClient ccsClient = ccsClientService.getById(id);
        return ApiResult.data(CcsClientConvert.INSTANCE.toVO(ccsClient));
    }

    @PostMapping
    @ApiOperation(value = "创建客户端")
    ApiResult<Void> save(@RequestBody @Validated(CcsClientParam.Create.class) CcsClientParam ccsClientParam) {
        return ApiResult.status(ccsClientService.save(CcsClientConvert.INSTANCE.toEntity(ccsClientParam)));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改客户端")
    @ApiImplicitParam(name = "id", value = "客户端ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<Void> update(@PathVariable("id") Integer id, @RequestBody @Validated(CcsClientParam.Update.class) CcsClientParam ccsClientParam) {
        ccsClientParam.setId(id);
        ccsClientService.updateById(CcsClientConvert.INSTANCE.toEntity(ccsClientParam));
        return ApiResult.success();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除客户端")
    @ApiImplicitParam(name = "id", value = "客户端ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<Void> delete(@PathVariable("id") Long id) {
        ccsClientService.removeById(id);
        return ApiResult.success();
    }

    @DeleteMapping
    @ApiOperation(value = "批量删除客户端")
    @ApiImplicitParam(name = "ids", value = "客户端ID集合", required = true, dataType = "List<Long>", paramType = "query")
    ApiResult<Void> delete(@RequestParam("ids") @Size(min = 1, message = "客户端ID不能为空") List<Long> ids) {
        ccsClientService.removeByIds(ids);
        return ApiResult.success();
    }

}

