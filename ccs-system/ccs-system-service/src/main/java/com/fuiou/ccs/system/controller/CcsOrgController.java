package com.fuiou.ccs.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.convert.CcsOrgConvert;
import com.fuiou.ccs.system.param.CcsOrgParam;
import com.fuiou.ccs.system.query.CcsOrgQuery;
import com.fuiou.ccs.system.repository.entity.CcsOrg;
import com.fuiou.ccs.system.service.CcsOrgService;
import com.fuiou.ccs.system.vo.CcsOrgVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 机构 前端控制器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"机构相关接口"})
@RequestMapping("/orgs")
public class CcsOrgController extends BaseController {

    final CcsOrgService ccsOrgService;

    @GetMapping("/treeOrg/{type}")
    @ApiImplicitParam(name = "type", value = "树状结构类型(1-展示全部机构树，2-仅展示一级和二级机构树)", required = true, dataType = "Integer", paramType = "path")
    @ApiOperation(value = "组织机构的树状结构")
    ApiResult<List<CcsOrgVO>> treeOrg(@PathVariable("type") Integer type) {
        return ApiResult.data(ccsOrgService.treeOrg(type));
    }

    @GetMapping
    @ApiOperation(value = "查询所有机构(分页)")
    ApiResult<IPage<CcsOrgVO>> page(CcsOrgQuery query) {
        return ApiResult.data(ccsOrgService.orgsListPage(getPage(), query));
    }

    @GetMapping("/orgList")
    @ApiOperation(value = "查询所有机构(不分页)")
    ApiResult<List<CcsOrgVO>> orgList(CcsOrgQuery query) {
        return ApiResult.data(ccsOrgService.orgList(query));
    }

    @GetMapping("/detailById/{id}")
    @ApiOperation(value = "机构详情")
    @ApiImplicitParam(name = "id", value = "机构ID", required = true, dataType = "String", paramType = "path")
    ApiResult<CcsOrgVO> get(@PathVariable("id") String id) {
        CcsOrg ccsOrg = ccsOrgService.getById(id);
        return ApiResult.data(CcsOrgConvert.INSTANCE.toVO(ccsOrg));
    }

    @PostMapping("/saveOrg")
    @ApiOperation(value = "创建机构")
    ApiResult<Void> saveOrg(@RequestBody @Validated(CcsOrgParam.Create.class) CcsOrgParam ccsOrgParam) {
        return ApiResult.status(ccsOrgService.saveOrg(CcsOrgConvert.INSTANCE.toEntity(ccsOrgParam)));
    }

    @PostMapping("/saveOrgList")
    @ApiOperation(value = "创建机构导入(Json)")
    ApiResult<Void> saveOrgList(@RequestBody @Validated(CcsOrgParam.Batch.class) @Size(min = 1, message = "请添加创建机构数据") List<CcsOrgParam> ccsOrgParamList) {
        ccsOrgService.saveOrgList(ccsOrgParamList);
        return ApiResult.success();
    }

    @PostMapping("/updateOrg")
    @ApiOperation("修改机构")
    ApiResult<Void> updateOrg(@RequestBody @Validated(CcsOrgParam.Update.class) CcsOrgParam ccsOrgParam) {
        return ApiResult.status(ccsOrgService.updateOrg(CcsOrgConvert.INSTANCE.toEntity(ccsOrgParam)));
    }

    @DeleteMapping("/deleteOrg/{orgId}")
    @ApiOperation("删除机构")
    @ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String", paramType = "path")
    ApiResult<Void> deleteOrg(@PathVariable("orgId") String orgId) {
        return ApiResult.status(ccsOrgService.deleteOrg(orgId));
    }

    @PostMapping("/exportOrg")
    @ApiOperation(value = "机构数据导出excel")
    void exportOrg(HttpServletResponse response) {
        ccsOrgService.exportOrg(response);
    }
}

