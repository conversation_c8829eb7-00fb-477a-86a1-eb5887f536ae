package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.dto.CcsDictItemDTO;
import com.fuiou.ccs.system.param.CcsDictItemParam;
import com.fuiou.ccs.system.repository.entity.CcsDictItem;
import com.fuiou.ccs.system.vo.CcsDictItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 字典数据 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface CcsDictItemConvert {

    CcsDictItemConvert INSTANCE = Mappers.getMapper(CcsDictItemConvert.class);

    @Mappings({})
    CcsDictItemVO toVO(CcsDictItem ccsDictItem);

    @Mappings({})
    List<CcsDictItemVO> toVO(List<CcsDictItem> ccsDictItemList);

    @Mappings({})
    CcsDictItemDTO toDTO(CcsDictItem ccsDictItem);

    @Mappings({})
    List<CcsDictItemDTO> toDTO(List<CcsDictItem> ccsDictItemList);

    @Mappings({})
    CcsDictItem toEntity(CcsDictItemParam ccsDictItemParam);

    @Mappings({})
    CcsDictItem toEntity(CcsDictItemDTO ccsDictItemDTO);

    @Mappings({})
    CcsDictItem toEntity(CcsDictItemVO ccsDictItemVO);

    @Mappings({})
    List<CcsDictItem> toEntity(List<CcsDictItemDTO> ccsDictItemDTOList);

}
