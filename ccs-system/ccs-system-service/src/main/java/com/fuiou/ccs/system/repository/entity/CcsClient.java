package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户端
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_CLIENT")
public class CcsClient implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户端ID
     */
    @TableField("CLIENT_ID")
    private String clientId;

    /**
     * 客户端密钥
     */
    @TableField("CLIENT_SECRET")
    private String clientSecret;

    /**
     * 资源集合
     */
    @TableField("RESOURCE_IDS")
    private String resourceIds;

    /**
     * 授权范围
     */
    @TableField("SCOPE")
    private String scope;

    /**
     * 授权类型
     */
    @TableField("AUTHORIZED_GRANT_TYPES")
    private String authorizedGrantTypes;

    /**
     * 回调地址
     */
    @TableField("WEB_SERVER_REDIRECT_URI")
    private String webServerRedirectUri;

    /**
     * 权限
     */
    @TableField("AUTHORITIES")
    private String authorities;

    /**
     * 令牌过期秒数
     */
    @TableField("ACCESS_TOKEN_VALIDITY")
    private Integer accessTokenValidity;

    /**
     * 刷新令牌过期秒数
     */
    @TableField("REFRESH_TOKEN_VALIDITY")
    private Integer refreshTokenValidity;

    /**
     * 附加说明
     */
    @TableField("ADDITIONAL_INFORMATION")
    private String additionalInformation;

    /**
     * 自动授权
     */
    @TableField("AUTOAPPROVE")
    private String autoapprove;

    /**
     * 状态（1：启用，0：停用）
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否已删除0(默认)-否  1-是
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Integer deletedStatus;


}
