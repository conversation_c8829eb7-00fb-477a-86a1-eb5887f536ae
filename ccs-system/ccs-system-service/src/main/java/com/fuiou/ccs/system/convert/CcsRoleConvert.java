package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.repository.entity.CcsRole;
import com.fuiou.ccs.system.dto.CcsRoleDTO;
import com.fuiou.ccs.system.vo.CcsRoleVO;
import com.fuiou.ccs.system.param.CcsRoleParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 客服工单角色 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface CcsRoleConvert {

    CcsRoleConvert INSTANCE = Mappers.getMapper(CcsRoleConvert.class);

    @Mappings({})
    CcsRoleVO toVO(CcsRole ccsRole);

    @Mappings({})
    List<CcsRoleVO> toVO(List<CcsRole> ccsRoleList);

    @Mappings({})
    CcsRoleDTO toDTO(CcsRole ccsRole);

    @Mappings({})
    List<CcsRoleDTO> toDTO(List<CcsRole> ccsRoleList);

    @Mappings({})
    CcsRole toEntity(CcsRoleParam ccsRoleParam);

    @Mappings({})
    CcsRole toEntity(CcsRoleDTO ccsRoleDTO);

    @Mappings({})
    CcsRole toEntity(CcsRoleVO ccsRoleVO);

}
