package com.fuiou.ccs.system.repository.mapper;

import com.fuiou.ccs.system.repository.entity.CcsClient;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 客户端 Mapper 接口
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface CcsClientMapper extends BaseMapper<CcsClient> {


    /**
     * 查询认证客户端
     *
     * @param clientId 客户端id
     * @return 认证客户端
     */
    CcsClient selectClientByClientId(@Param("clientId") String clientId);


    /**
     * 插入客户端用户
     *
     * @param clientId 客户端ID
     * @param userId   用户ID
     */
    int insertUser(@Param("clientId") String clientId, @Param("userId") String userId);


    /**
     * 根据用户ID删除用户客户端信息
     * @param userId 用户ID
     */
    void  realDelByUserId(@Param("userId")String userId);

}
