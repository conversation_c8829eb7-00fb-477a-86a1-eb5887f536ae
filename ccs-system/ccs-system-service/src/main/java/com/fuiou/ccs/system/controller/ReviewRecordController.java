package com.fuiou.ccs.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.param.ToReviewParam;
import com.fuiou.ccs.system.query.ReviewQuery;
import com.fuiou.ccs.system.service.ReviewHistoryService;
import com.fuiou.ccs.system.service.ReviewRecordService;
import com.fuiou.ccs.system.vo.ReviewListVO;
import com.fuiou.ccs.system.vo.ReviewVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 复核纪录 前端控制器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"复核纪录相关接口"})
@RequestMapping("/review")
public class ReviewRecordController extends BaseController {

    final ReviewRecordService reviewRecordService;
    final ReviewHistoryService reviewHistoryService;

    @GetMapping("/records")
    @ApiOperation(value = "查询所有待复核纪录(分页)")
    ApiResult<IPage<ReviewListVO>> records(ReviewQuery query) {
        return ApiResult.data(reviewRecordService.pageList(getPage(), query));
    }

    @GetMapping("/history")
    @ApiOperation(value = "查询所有复核历史纪录(分页)")
    ApiResult<IPage<ReviewListVO>> history(ReviewQuery query) {
        return ApiResult.data(reviewHistoryService.pageList(getPage(), query));
    }

    @GetMapping("/records/{id}")
    @ApiOperation(value = "待复核纪录详情")
    @ApiImplicitParam(name = "id", value = "复核纪录ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<ReviewVO> getRecords(@PathVariable("id") Long id) {
        return ApiResult.data(reviewRecordService.info(id));
    }

    @GetMapping("/history/{id}")
    @ApiOperation(value = "复核历史纪录详情")
    @ApiImplicitParam(name = "id", value = "复核纪录ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<ReviewVO> getHistory(@PathVariable("id") Long id) {
        return ApiResult.data(reviewHistoryService.info(id));
    }

    @PostMapping("/toReview")
    @ApiOperation(value = "复核")
    ApiResult<Void> toReview(@RequestBody @Valid ToReviewParam toReviewParam) {
        reviewRecordService.toReview(toReviewParam);
        return ApiResult.success();
    }

}

