package com.fuiou.ccs.system.repository.sequence;

import com.fuiou.ccs.system.repository.mapper.SysSequenceMapper;
import com.fuiou.common.utils.LocalDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.keygen.SelectKeyGenerator;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 系统 DB2 主键生成器
 *
 * 主要用于 T_SYS_ORG，T_SYS_USER，T_SYS_GROUP
 *
 * <AUTHOR>
 * @since  1.0.0
 * @see SelectKeyGenerator
 */
@Repository
public class SysDB2KeyGenerator {

    final SysSequenceMapper sysSequenceMapper;

    public SysDB2KeyGenerator(SysSequenceMapper sysSequenceMapper) {
        this.sysSequenceMapper = sysSequenceMapper;
    }

    /**
     * 生成主键
     *
     * @param sequenceName 序列名称
     * @return 主键值
     */
    public String generatorKey(String sequenceName) {
        SysSequenceEnum sequenceEnum = SysSequenceEnum.getEnum(sequenceName);
        if (sequenceEnum == null) {
            throw new IllegalStateException("sequence not supported：" + sequenceName);
        }
        Long sequence = sysSequenceMapper.selectNextValue(sequenceName);
        return formatSequence(sequenceEnum, sequence);
    }

    /**
     * 生成主键
     *
     * @param sequenceEnum 序列枚举
     * @return 主键值
     */
    public String generatorKey(SysSequenceEnum sequenceEnum) {
        String sequenceName = sequenceEnum.getName();
        Long sequence = sysSequenceMapper.selectNextValue(sequenceName);
        return formatSequence(sequenceEnum, sequence);
    }

    /**
     * 格式化序列
     *
     * @param sequenceEnum 序列枚举
     * @param value        序列值
     * @return 序列
     */
    private String formatSequence(SysSequenceEnum sequenceEnum, Long value) {
        String prefix = sequenceEnum.getPrefix();
        String datePattern = sequenceEnum.getDatePattern();
        String suffixFormat = sequenceEnum.getSuffixFormat();

        String sequence = Optional.ofNullable(prefix).orElse("");
        // 时间格式化拼接
        if (StringUtils.isNotEmpty(datePattern)) {
            LocalDateTime now = LocalDateTime.now();
            sequence += LocalDateUtil.format(now, datePattern);
        }

        // 后缀格式化拼接，不存在就直接拼接在后面
        if (StringUtils.isNotEmpty(suffixFormat)) {
            sequence += String.format(suffixFormat, value);
        } else {
            sequence += value;
        }
        return sequence;
    }


}
