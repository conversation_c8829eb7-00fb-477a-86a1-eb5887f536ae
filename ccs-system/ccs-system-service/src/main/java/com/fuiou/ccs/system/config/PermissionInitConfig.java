package com.fuiou.ccs.system.config;

import com.fuiou.ccs.system.service.CcsPermissionService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 权限初始化配置
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Component
public class PermissionInitConfig implements CommandLineRunner {

    final CcsPermissionService ccsPermissionService;

    public PermissionInitConfig(CcsPermissionService ccsPermissionService) {
        this.ccsPermissionService = ccsPermissionService;
    }

    @Override
    public void run(String... args) throws Exception {
        ccsPermissionService.refreshPermissionRules();
    }

}
