package com.fuiou.ccs.system.common.util;

import com.fuiou.common.domain.AuthUser;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName: PermissionUtil
 * @Description:
 * @Author: zhangsl
 * @Date: 2022/7/25 16:36
 */

@Component
@Data
public class PermissionUtil {

    public static Boolean isAdministrator(AuthUser authUser) {
        //一、非空判断
        if (authUser == null) {
            return false;
        }
        //二、获取角色
        boolean isAdministrator = false;
        //1.获取权限
        List<String> roles = authUser.getRoles();
        //2.判断
        if (roles != null && (roles.contains("JS0001"))) {
            isAdministrator = true;
        }
        return isAdministrator;
    }
}
