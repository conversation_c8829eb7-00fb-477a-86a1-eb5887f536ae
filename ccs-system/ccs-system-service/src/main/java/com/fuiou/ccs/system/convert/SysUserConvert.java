package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.dto.CcsSysUserDTO;
import com.fuiou.ccs.system.dto.CcsAuthUserDTO;
import com.fuiou.ccs.system.param.CcsSysUserParam;
import com.fuiou.ccs.system.repository.entity.CcsSysUser;
import com.fuiou.ccs.system.vo.CcsSysUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户账户 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface SysUserConvert {

    SysUserConvert INSTANCE = Mappers.getMapper(SysUserConvert.class);

    @Mappings({})
    CcsSysUserVO toVO(CcsSysUser sysUser);

    @Mappings({})
    CcsSysUserVO toVO(CcsSysUserDTO sysUserDTO);

    @Mappings({})
    CcsSysUserVO toVO(CcsSysUserParam sysUserParam);

    @Mappings({})
    List<CcsSysUserVO> toVO(List<CcsSysUser> sysUserList);

    @Mappings({})
    CcsSysUserDTO toDTO(CcsSysUser sysUser);

    @Mappings({})
    List<CcsSysUserDTO> toDTO(List<CcsSysUser> sysUserList);

    @Mappings({})
    CcsSysUser toEntity(CcsSysUserParam sysUserParam);

    @Mappings({})
    CcsSysUser toEntity(CcsSysUserDTO sysUserDTO);

    @Mappings({})
    CcsSysUser toEntity(CcsSysUserVO sysUserVO);

    @Mappings({})
    CcsAuthUserDTO toAuthUserDTO(CcsSysUser sysUser);

}
