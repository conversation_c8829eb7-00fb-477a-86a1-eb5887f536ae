package com.fuiou.ccs.system.provider.impl;

import com.fuiou.ccs.system.convert.SysFormDetailConvert;
import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.provider.SysFormDetailProvider;
import com.fuiou.ccs.system.repository.entity.SysFormDetail;
import com.fuiou.ccs.system.service.SysFormDetailService;
import com.fuiou.common.api.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * 表单配置详情接口
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
@RequiredArgsConstructor
public class SysFormDetailProviderImpl implements SysFormDetailProvider {

    final SysFormDetailService sysFormDetailService;

    /**
     * 根据表单表名查询表单配置详情集合
     *
     * @param formName 表名
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    @Override
    public ApiResult<List<SysFormDetailDTO>> querySysFormDetailByFormName(String formName) {
        return ApiResult.data(SysFormDetailConvert.INSTANCE.toDTO(sysFormDetailService.querySysFormDetailByFormName(formName)));
    }

    /**
     * 根据formId获取表单配置详情集合
     *
     * @param formId   表单配置ID
     * @param partFlag true 非预制字段， false 预制字段
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    @Override
    public ApiResult<List<SysFormDetailDTO>> queryFormDetailByFormId(Long formId, boolean partFlag) {
        List<SysFormDetail> sysFormDetailList = sysFormDetailService.queryFormDetailByFormId(formId, partFlag);
        return ApiResult.data(SysFormDetailConvert.INSTANCE.toDTO(sysFormDetailList));
    }

    /**
     * 根据formId查询模板预置字段集合
     *
     * @param formId 表单ID
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    @Override
    public ApiResult<List<SysFormDetailDTO>> queryPresetFormDetailByFormId(Long formId) {
        List<SysFormDetail> sysFormDetailList = sysFormDetailService.queryPresetFormDetailByFormId(formId);
        return ApiResult.data(SysFormDetailConvert.INSTANCE.toDTO(sysFormDetailList));
    }
}
