package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.dto.SysFormLogDTO;
import com.fuiou.ccs.system.param.SysFormLogParam;
import com.fuiou.ccs.system.repository.entity.SysFormLog;
import com.fuiou.ccs.system.vo.SysFormLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 表单配置日志 对象转化器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysFormLogConvert {

    SysFormLogConvert INSTANCE = Mappers.getMapper(SysFormLogConvert.class);

    @Mappings({})
    SysFormLogVO toVO(SysFormLog sysFormLog);

    @Mappings({})
    List<SysFormLogVO> toVO(List<SysFormLog> sysFormLogList);

    @Mappings({})
    SysFormLogDTO toDTO(SysFormLog sysFormLog);

    @Mappings({})
    List<SysFormLogDTO> toDTO(List<SysFormLog> sysFormLogList);

    @Mappings({})
    SysFormLog toEntity(SysFormLogParam sysFormLogParam);

    @Mappings({})
    SysFormLog toEntity(SysFormLogDTO sysFormLogDTO);

    @Mappings({})
    SysFormLog toEntity(SysFormLogVO sysFormLogVO);

}
