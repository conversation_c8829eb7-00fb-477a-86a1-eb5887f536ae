package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.param.SysBusinessParam;
import com.fuiou.ccs.system.query.SysBusinessQuery;
import com.fuiou.ccs.system.repository.entity.SysBusiness;
import com.fuiou.ccs.system.vo.SysBusinessVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 业务表(类型配置) 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysBusinessService extends IService<SysBusiness> {

    /**
     * 类型配置的树状结构
     *
     * @param type       1-展示全部配置，2-展示还未配置过的树形结构，3-展示三层级的配置，4-修改模板时展示类型配置
     * @param workSubIds 工单子类ID拼接
     * @return 类型配置信息
     */
    List<SysBusinessVO> treeBusiness(Integer type, String workSubIds);

    /**
     * 查询所有类型配置(分页)
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页类型配置
     */
    IPage<SysBusinessVO> busListPage(IPage page, SysBusinessQuery query);

    /**
     * 查询所有类型配置(不分页)
     *
     * @param query 查询参数
     * @return 类型配置信息
     */
    List<SysBusinessVO> busList(SysBusinessQuery query);

    /**
     * 查询下一级类型配置的集合
     *
     * @param parentId 上级业务ID
     * @return 型配置信息
     */
    List<SysBusinessVO> busNextList(Long parentId);

    /**
     * 创建类型配置信息
     *
     * @param sysBusiness 类型配置信息
     * @return 创建状态
     */
    void saveBus(SysBusiness sysBusiness);

    /**
     * 创建类型配置导入(Json)
     *
     * @param sysBusinessParamList 类型配置信息list
     */
    void saveBusList(List<SysBusinessParam> sysBusinessParamList);

    /**
     * 删除类型配置
     *
     * @param busId 类型配置id
     * @return 删除状态
     */
    void deleteBus(Long busId);

    /**
     * 修改类型配置
     *
     * @param sysBusiness 类型配置
     * @return 修改状态
     */
    void updateBus(SysBusiness sysBusiness);

    /**
     * 根据业务类型id入参，查询对应的中文名称，封装之后返回
     *
     * @param businessIds
     * @return
     */
    Map<String, String> queryBusinessName(List<Long> businessIds);

    /**
     * 类型配置数据导出excel
     *
     * @param response
     */
    void exportBusiness(HttpServletResponse response);

    List<SysBusinessVO> queryBusinessListByIds(Set<Long> businessIds);
}
