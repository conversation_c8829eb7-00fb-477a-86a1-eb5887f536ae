package com.fuiou.ccs.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.system.client.ToExternalClient;
import com.fuiou.ccs.system.common.excel.SysBusinessExcel;
import com.fuiou.ccs.system.convert.SysBusinessConvert;
import com.fuiou.ccs.system.enums.SysBusinessEnums;
import com.fuiou.ccs.system.exception.SystemErrorCode;
import com.fuiou.ccs.system.exception.SystemServiceException;
import com.fuiou.ccs.system.param.SysBusinessParam;
import com.fuiou.ccs.system.query.SysBusinessQuery;
import com.fuiou.ccs.system.repository.entity.CcsDictItem;
import com.fuiou.ccs.system.repository.entity.SysBusiness;
import com.fuiou.ccs.system.repository.entity.SysFormBusiness;
import com.fuiou.ccs.system.repository.mapper.SysBusinessMapper;
import com.fuiou.ccs.system.service.CcsDictItemService;
import com.fuiou.ccs.system.service.SysBusinessService;
import com.fuiou.ccs.system.service.SysFormBusinessService;
import com.fuiou.ccs.system.vo.SysBusinessVO;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import com.fuiou.excel.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务表(类型配置) 服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysBusinessServiceImpl extends ServiceImpl<SysBusinessMapper, SysBusiness> implements SysBusinessService {

    final CcsDictItemService ccsDictItemService;
    final SysFormBusinessService sysFormBusinessService;
    final ToExternalClient toExternalClient;

    /**
     * 类型配置的树状结构
     *
     * @param type       1-展示全部配置，2-展示还未配置过的树形结构，3-展示三层级的配置，4-修改模板时展示类型配置
     * @param workSubIds 工单子类ID拼接
     * @return 类型配置信息
     */
    @Override
    public List<SysBusinessVO> treeBusiness(Integer type, String workSubIds) {
        List<String> workSubIdList = null;
        if (StringUtils.isNotBlank(workSubIds)) {
            workSubIdList = Arrays.asList(workSubIds.split(","));
        }
        List<SysBusinessVO> queryList = baseMapper.getBusList();
        List<SysBusinessVO> rootList = new ArrayList<>();
        //根节点对象存放到这里
        List<SysBusinessVO> bodyList = new ArrayList<>();
        //其他节点存放到这里，可以包含根节点
        for (SysBusinessVO itemBusiness : queryList) {
            if (itemBusiness != null) {
                if (itemBusiness.getBusLevel() == SysBusinessEnums.BUS_TYPE) {
                    rootList.add(itemBusiness);
                    //所有父节点数据放进去
                } else if (type == 3) {
                    if (itemBusiness.getBusLevel() != SysBusinessEnums.SUB_ELEMENT) {
                        bodyList.add(itemBusiness);
                    }
                } else if (type == 2 || type == 4) {
                    if (itemBusiness.getBusLevel() == SysBusinessEnums.WORK_SUB_TYPE) {
                        //检查已使用过的工单子类
                        LambdaQueryWrapper<SysFormBusiness> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(SysFormBusiness::getWorkSubId, itemBusiness.getBusId()).eq(SysFormBusiness::getDeletedStatus, CommonConstants.NO);
                        int count = sysFormBusinessService.count(wrapper);
                        if (count == 0) {
                            bodyList.add(itemBusiness);
                        }
                    } else if (itemBusiness.getBusLevel() != SysBusinessEnums.SUB_ELEMENT) {
                        //排除子类要素(add二级)
                        bodyList.add(itemBusiness);
                    }
                } else {
                    bodyList.add(itemBusiness);
                }
            }
        }
        //修改模板时展示类型配置
        if (type == 4) {
            assert workSubIdList != null;
            workSubIdList.forEach(busIdStr -> {
                Long busId = Long.parseLong(busIdStr);
                SysBusiness bus = getById(busId);
                bodyList.add(SysBusinessConvert.INSTANCE.toVO(bus));
            });
        }
        List<SysBusinessVO> stc = getTree(rootList, bodyList);
        //组装的树返给前端
        return stc;
    }

    private List<SysBusinessVO> getTree(List<SysBusinessVO> rootList, List<SysBusinessVO> bodyList) {
        if (bodyList != null && !bodyList.isEmpty()) {
            //声明一个map，用来过滤已操作过的数据
            Map<Long, Long> map = new Hashtable<>(bodyList.size());
            rootList.forEach(beanTree -> getChild(beanTree, map, bodyList));
            return rootList;
        } else if (rootList != null) {
            //当数据只有父节点
            return rootList;
        }
        return null;
    }

    private void getChild(SysBusinessVO treeDto, Map<Long, Long> map, List<SysBusinessVO> bodyList) {
        List<SysBusinessVO> childList = new ArrayList();
        bodyList.stream()
                .filter(item -> !map.containsKey(item.getBusId()))
                .filter(item -> item.getParentId().equals(treeDto.getBusId()))
                .forEach(item -> {
                    map.put(item.getBusId(), item.getParentId());
                    getChild(item, map, bodyList);
                    childList.add(item);
                });
        treeDto.setChildren(childList);
        //子数据集
    }

    /**
     * 查询所有类型配置(分页)
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页类型配置
     */
    @Override
    public IPage<SysBusinessVO> busListPage(IPage page, SysBusinessQuery query) {
        return baseMapper.busListPage(page, query);
    }

    /**
     * 查询所有类型配置(不分页)
     *
     * @param query 查询参数
     * @return 类型配置信息
     */
    @Override
    public List<SysBusinessVO> busList(SysBusinessQuery query) {
        return baseMapper.busList(query);
    }

    /**
     * 查询下一级类型配置的集合
     *
     * @param parentId 上级业务ID
     * @return 型配置信息
     */
    @Override
    public List<SysBusinessVO> busNextList(Long parentId) {
        LambdaQueryWrapper<SysBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysBusiness::getParentId, parentId).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
        List<SysBusiness> listBus = list(wrapper);
        if (listBus != null && listBus.size() > 0) {
            return SysBusinessConvert.INSTANCE.toVO(listBus);
        }
        return null;
    }

    /**
     * 创建类型配置信息
     *
     * @param sysBusiness 类型配置信息
     * @return 创建状态
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void saveBus(SysBusiness sysBusiness) {
        if (sysBusiness.getStatus() == null) {
            sysBusiness.setStatus(CommonConstants.YES);
        }
        //检查名称不重复
        checkNameRepeat(sysBusiness);
        Long parentId = sysBusiness.getParentId();
        if (parentId != null && parentId != 0) {
            SysBusiness parentBus = getById(parentId);
            if (parentBus.getBusLevel() == SysBusinessEnums.SUB_ELEMENT) { //只支持1，2，3，4的层次
                throw new SystemServiceException(GlobalErrorCode.BAD_REQUEST.getCode(), "创建类型配置仅支持四级层数");
            }
            if (parentBus.getBusLevel() != null) {
                sysBusiness.setBusLevel(parentBus.getBusLevel() + 1);
            } else {
                //根据父级ID查询不到就抛出错误，参数错误
                throw new SystemServiceException(GlobalErrorCode.BAD_REQUEST.getCode(), "业务表单父级ID请求参数错误");
            }
        } else {
            sysBusiness.setParentId(0L);
            sysBusiness.setBusLevel(SysBusinessEnums.BUS_TYPE);
            //说明是第一层的新增，那就要判断有没有赋值org_code，没赋值抛错
            if (StringUtils.isBlank(sysBusiness.getOrgCode())) {
                throw new SystemServiceException(GlobalErrorCode.BAD_REQUEST.getCode(), "业务类型新增需传组织编码");
            }
        }
        sysBusiness.setBusId(IdWorker.getId());
        save(sysBusiness);
        saveOutSide(sysBusiness);
    }

    /**
     * 四要素新建同步到电商公司
     *
     * @param sysBusiness 类型配置
     */
    private void saveOutSide(SysBusiness sysBusiness) {
        if (sysBusiness.getBusLevel() == SysBusinessEnums.BUS_TYPE) {
            if (sysBusiness.getOrgCode().equals("SHDS")) {
                toExternalClient.businessAddSync(sysBusiness.getBusId(), sysBusiness.getParentId(), sysBusiness.getName(), SysBusinessEnums.BUS_TYPE);
            }
        } else {
            SysBusiness parentBus = getById(sysBusiness.getParentId());
            if (parentBus.getBusLevel() == SysBusinessEnums.WORK_SUB_TYPE) {  //如果父类是第三层级
                //第二层级
                SysBusiness workType = getById(parentBus.getParentId());
                //第一层级
                SysBusiness busType = getById(workType.getParentId());
                if (busType != null && "SHDS".equals(busType.getOrgCode())) {
                    toExternalClient.businessAddSync(sysBusiness.getBusId(), sysBusiness.getParentId(), sysBusiness.getName(), SysBusinessEnums.SUB_ELEMENT);
                }
            } else if (parentBus.getBusLevel() == SysBusinessEnums.WORK_TYPE) { //如果父类是第二层级
                //第一层级
                SysBusiness busType = getById(parentBus.getParentId());
                if (busType != null && "SHDS".equals(busType.getOrgCode())) {
                    toExternalClient.businessAddSync(sysBusiness.getBusId(), sysBusiness.getParentId(), sysBusiness.getName(), SysBusinessEnums.WORK_SUB_TYPE);
                }
            } else if (parentBus.getBusLevel() == SysBusinessEnums.BUS_TYPE) { //如果父类是第一层级
                if ("SHDS".equals(parentBus.getOrgCode())) {
                    toExternalClient.businessAddSync(sysBusiness.getBusId(), sysBusiness.getParentId(), sysBusiness.getName(), SysBusinessEnums.WORK_TYPE);
                }
            }
        }
    }

    /**
     * 四要素修改同步到电商公司
     *
     * @param sysBusiness 类型配置
     */
    private void businessUpdSyncOutSide(SysBusiness sysBusiness, Long createBusinessId) {
        if (sysBusiness.getBusLevel() == SysBusinessEnums.BUS_TYPE) {
            if ("SHDS".equals(sysBusiness.getOrgCode())) {
                sysBusiness.setParentId(createBusinessId);
                toExternalClient.businessUpdSync(sysBusiness.getBusId(), sysBusiness.getName(), sysBusiness.getParentId(), sysBusiness.getBusLevel(), sysBusiness.getStatus());
            }
        } else {
            SysBusiness parentBus = getById(sysBusiness.getParentId());
            if (parentBus.getBusLevel() == SysBusinessEnums.WORK_SUB_TYPE) {  //如果父类是第三层级
                //第二层级
                SysBusiness workType = getById(parentBus.getParentId());
                //第一层级
                SysBusiness busType = getById(workType.getParentId());
                if (busType != null && "SHDS".equals(busType.getOrgCode())) {
                    sysBusiness.setParentId(createBusinessId);
                    toExternalClient.businessUpdSync(sysBusiness.getBusId(), sysBusiness.getName(), sysBusiness.getParentId(), sysBusiness.getBusLevel(), sysBusiness.getStatus());
                }
            } else if (parentBus.getBusLevel() == SysBusinessEnums.WORK_TYPE) { //如果父类是第二层级
                //第一层级
                SysBusiness busType = getById(parentBus.getParentId());
                if (busType != null && "SHDS".equals(busType.getOrgCode())) {
                    toExternalClient.businessUpdSync(sysBusiness.getBusId(), sysBusiness.getName(), sysBusiness.getParentId(), sysBusiness.getBusLevel(), sysBusiness.getStatus());
                }
            } else if (parentBus.getBusLevel() == SysBusinessEnums.BUS_TYPE) { //如果父类是第一层级
                if ("SHDS".equals(parentBus.getOrgCode())) {
                    sysBusiness.setParentId(createBusinessId);
                    toExternalClient.businessUpdSync(sysBusiness.getBusId(), sysBusiness.getName(), sysBusiness.getParentId(), sysBusiness.getBusLevel(), sysBusiness.getStatus());
                }
            }
        }
    }

    /**
     * 类型配置数据导出excel
     *
     * @param response
     */
    @Override
    public void exportBusiness(HttpServletResponse response) {
        List<SysBusinessVO> queryList = baseMapper.getBusList();
        LambdaQueryWrapper<CcsDictItem> ccsWrapper = new LambdaQueryWrapper<>();
        ccsWrapper.eq(CcsDictItem::getDictCode, "BUS_ORG");
        List<CcsDictItem> dictItemList = ccsDictItemService.list(ccsWrapper);
        if (CollectionUtils.isNotEmpty(queryList)) {
            List<SysBusinessExcel> excelList = queryList.stream().map(businessVO -> {
                SysBusinessExcel billExcel = SysBusinessConvert.INSTANCE.toExcel(businessVO);
                Integer busLevel = businessVO.getBusLevel();
                if (busLevel == SysBusinessEnums.SUB_ELEMENT) {
                    billExcel.setSubElementName(businessVO.getName());
                    SysBusiness workSubType = getById(businessVO.getParentId());
                    if (Objects.nonNull(workSubType)) {
                        billExcel.setWorkSubTypeName(workSubType.getName());
                        SysBusiness workType = getById(workSubType.getParentId());
                        billExcel.setWorkTypeName(workType.getName());
                        SysBusiness busType = getById(workType.getParentId());
                        if (Objects.nonNull(busType)) {
                            billExcel.setBusTypeName(busType.getName());
                        }
                    }
                }
                if (busLevel == SysBusinessEnums.WORK_SUB_TYPE) {
                    billExcel.setWorkSubTypeName(businessVO.getName());
                    SysBusiness workType = getById(businessVO.getParentId());
                    if (Objects.nonNull(workType)) {
                        billExcel.setWorkTypeName(workType.getName());
                        SysBusiness busType = getById(workType.getParentId());
                        billExcel.setBusTypeName(busType.getName());
                    }
                }
                if (busLevel == SysBusinessEnums.WORK_TYPE) {
                    billExcel.setWorkTypeName(businessVO.getName());
                    SysBusiness busType = getById(businessVO.getParentId());
                    if (Objects.nonNull(busType)) {
                        billExcel.setBusTypeName(busType.getName());
                    }
                }
                if (busLevel == SysBusinessEnums.BUS_TYPE) {
                    billExcel.setBusTypeName(businessVO.getName());
                    dictItemList.stream().filter(dictItem -> dictItem.getValue().equals(businessVO.getOrgCode())).forEach(dictItem -> billExcel.setOrgName(dictItem.getLabel()));
                }
                return billExcel;
            }).collect(Collectors.toList());
            ExcelUtil.export(response, "类型配置模板", "", excelList, SysBusinessExcel.class);
        }
    }

    @Override
    public List<SysBusinessVO> queryBusinessListByIds(Set<Long> businessIds) {
        return baseMapper.queryBusinessListByIds(businessIds);
    }

    /**
     * 创建类型配置导入(Json)
     *
     * @param sysBusinessParamList 类型配置信息list
     */
    @SuppressWarnings(value = {"checkstyle:methodlength"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void saveBusList(List<SysBusinessParam> sysBusinessParamList) {
        sysBusinessParamList.forEach(businessParam -> {
            //新增是四级子类要素
            if (StringUtils.isNotBlank(businessParam.getSubElementName())) {
                // 1.先确定一级的业务名称
                LambdaQueryWrapper<SysBusiness> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SysBusiness::getName, businessParam.getBusTypeName()).
                        eq(SysBusiness::getBusLevel, SysBusinessEnums.BUS_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                //业务类型
                SysBusiness busType = getOne(wrapper);
                wrapper.clear();
                if (busType == null) {
                    busType = new SysBusiness();
                    busType.setName(businessParam.getBusTypeName());
                    LambdaQueryWrapper<CcsDictItem> ccsWrapper = new LambdaQueryWrapper<>();
                    ccsWrapper.eq(CcsDictItem::getDictCode, "BUS_ORG");
                    List<CcsDictItem> dictItemList = ccsDictItemService.list(ccsWrapper);
                    SysBusiness finalBusType = busType;
                    dictItemList.forEach(dictItem -> {
                        if (dictItem.getLabel().equals(businessParam.getOrgName())) {
                            finalBusType.setOrgCode(dictItem.getValue());
                        }
                    });
                    saveBus(busType);
                    //save工单类型
                    SysBusiness workType = new SysBusiness();
                    workType.setParentId(busType.getBusId());
                    workType.setName(businessParam.getWorkTypeName());
                    saveBus(workType);
                    //find 工单类型
                    wrapper.eq(SysBusiness::getParentId, busType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkTypeName()).
                            eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                    workType = getOne(wrapper);
                    wrapper.clear();
                    //save工单子类
                    SysBusiness workSubType = new SysBusiness();
                    workSubType.setParentId(workType.getBusId());
                    workSubType.setName(businessParam.getWorkSubTypeName());
                    saveBus(workSubType);
                    //find 工单子类
                    wrapper.eq(SysBusiness::getParentId, workType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkSubTypeName()).
                            eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_SUB_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                    workSubType = getOne(wrapper);
                    wrapper.clear();
                    //save子类要素
                    SysBusiness subElement = new SysBusiness();
                    subElement.setParentId(workSubType.getBusId());
                    subElement.setName(businessParam.getSubElementName());
                    saveBus(subElement);
                } else { //业务类型不为null，检查工单类型
                    wrapper.eq(SysBusiness::getParentId, busType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkTypeName()).
                            eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                    //工单类型
                    SysBusiness workType = getOne(wrapper);
                    wrapper.clear();
                    if (workType == null) {
                        //工单类型
                        workType = new SysBusiness();
                        workType.setParentId(busType.getBusId());
                        workType.setName(businessParam.getWorkTypeName());
                        saveBus(workType);
                        //find 工单类型
                        wrapper.eq(SysBusiness::getParentId, busType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkTypeName()).
                                eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                        workType = getOne(wrapper);
                        wrapper.clear();
                        //save工单子类
                        SysBusiness workSubType = new SysBusiness();
                        workSubType.setParentId(workType.getBusId());
                        workSubType.setName(businessParam.getWorkSubTypeName());
                        saveBus(workSubType);
                        //find 工单子类
                        wrapper.eq(SysBusiness::getParentId, workType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkSubTypeName()).
                                eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_SUB_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                        workSubType = getOne(wrapper);
                        wrapper.clear();
                        //save子类要素
                        SysBusiness subElement = new SysBusiness();
                        subElement.setParentId(workSubType.getBusId());
                        subElement.setName(businessParam.getSubElementName());
                        saveBus(subElement);
                    } else { //工单类型不为null，检查工单子类
                        wrapper.eq(SysBusiness::getParentId, workType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkSubTypeName()).
                                eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_SUB_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                        //工单子类
                        SysBusiness workSubType = getOne(wrapper);
                        if (workSubType == null) {
                            workSubType = new SysBusiness();
                            workSubType.setParentId(workType.getBusId());
                            workSubType.setName(businessParam.getWorkSubTypeName());
                            saveBus(workSubType);
                            //find 工单子类
                            wrapper.eq(SysBusiness::getParentId, workType.getBusId()).eq(SysBusiness::getName, businessParam.getWorkSubTypeName()).
                                    eq(SysBusiness::getBusLevel, SysBusinessEnums.WORK_SUB_TYPE).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                            workSubType = getOne(wrapper);
                            wrapper.clear();
                        }
                        //创建子类要素
                        SysBusiness subElement = new SysBusiness();
                        subElement.setParentId(workSubType.getBusId());
                        subElement.setName(businessParam.getSubElementName());
                        saveBus(subElement);
                    }

                }

            }
        });
    }

    /**
     * 删除类型配置
     *
     * @param busId 类型配置id
     * @return 删除状态
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void deleteBus(Long busId) {
        SysBusiness sysBusiness = getById(busId);
        if (sysBusiness.getBusLevel() != SysBusinessEnums.SUB_ELEMENT) {
            LambdaQueryWrapper<SysBusiness> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysBusiness::getParentId, sysBusiness.getBusId()).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
            List<SysBusiness> businessList = list(wrapper);
            if (businessList.size() > 0) {
                throw new SystemServiceException(GlobalErrorCode.NOT_ACCEPTABLE.getCode(), "请先删除当前"
                        + SysBusinessEnums.BusLevelEnums.getInstance(sysBusiness.getBusLevel()).getMsg() + "的" + SysBusinessEnums.BusLevelEnums.getInstance(sysBusiness.getBusLevel() + 1).getMsg());
            }
        }
        //按照模板配置校验
        if (sysBusiness.getBusLevel() == SysBusinessEnums.WORK_SUB_TYPE) { //工单子类
            sysFormBusinessService.judgmentBusinessUsed(null, null, sysBusiness.getBusId());

        } else if (sysBusiness.getBusLevel() == SysBusinessEnums.WORK_TYPE) { //工单类型
            sysFormBusinessService.judgmentBusinessUsed(null, sysBusiness.getBusId(), null);

        } else if (sysBusiness.getBusLevel() == SysBusinessEnums.BUS_TYPE) { //业务类型
            sysFormBusinessService.judgmentBusinessUsed(sysBusiness.getBusId(), null, null);
        }

        SysBusiness sysOrg = new SysBusiness();
        sysOrg.setBusId(busId);
        sysOrg.setStatus(CommonConstants.DISABLED);
        updateById(sysOrg);
    }

    /**
     * 修改类型配置
     *
     * @param sysBusiness 类型配置
     * @return 修改状态
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void updateBus(SysBusiness sysBusiness) {
        SysBusiness oldBusiness = getById(sysBusiness.getBusId());
        //检查名称不重复
        checkNameRepeat(sysBusiness);
        //1.先停用
        SysBusiness updateBusiness = new SysBusiness();
        updateBusiness.setBusId(oldBusiness.getBusId());
        updateBusiness.setStatus(CommonConstants.DISABLED);
        updateById(updateBusiness);

        //2.再新建类型配置
        SysBusiness createBusiness = new SysBusiness();
        createBusiness.setParentId(oldBusiness.getParentId());
        createBusiness.setName(sysBusiness.getName());
        createBusiness.setBusLevel(oldBusiness.getBusLevel());
        createBusiness.setStatus(CommonConstants.ENABLED);
        if (StringUtils.isNotBlank(oldBusiness.getOrgCode())) {
            createBusiness.setOrgCode(oldBusiness.getOrgCode());
        }
        createBusiness.setBusId(IdWorker.getId());
        save(createBusiness);

        try {
            //3.修改模板配置关联到的信息
            businessUpdSyncFormAndFlow(oldBusiness, createBusiness.getBusId());
            //4.四要素新建同步到电商公司
            saveOutSide(createBusiness);
            //5.如果不是子类要素，则更新到电商子类
            if (oldBusiness.getBusLevel() != SysBusinessEnums.SUB_ELEMENT) {
                LambdaQueryWrapper<SysBusiness> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SysBusiness::getParentId, oldBusiness.getBusId()).eq(SysBusiness::getStatus, CommonConstants.ENABLED);
                //原父类的子类集合
                List<SysBusiness> businessList = list(wrapper);
                //6.如果不是子类要素，则需要将原父类ID替换为新的ID
                baseMapper.updateParentId(oldBusiness.getBusId(), createBusiness.getBusId());
                if (CollectionUtils.isNotEmpty(businessList)) {
                    for (SysBusiness item : businessList) {
                        //更新电商公司
                        businessUpdSyncOutSide(item, createBusiness.getBusId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("SysBusinessServiceImpl.updateBus.修改类型配置异常！sysBusiness= {}", sysBusiness);
            log.error("SysBusinessServiceImpl.updateBus.ERROR e.getMessage()= {}, e= {}", e.getMessage(), e);
            throw new SystemServiceException(GlobalErrorCode.BAD_REQUEST.getCode(), e.getMessage());
        }

    }

    private void businessUpdSyncFormAndFlow(SysBusiness business, Long createBusinessId) {
        if (business.getBusLevel() == SysBusinessEnums.WORK_SUB_TYPE) {  //工单子类
            log.info("updateBus.修改类型配置 :原busId:{}, 新busId:{}", business.getBusId(), createBusinessId);
            if (sysFormBusinessService.judgmentBusinessUsed(null, null, business.getBusId())) {
                sysFormBusinessService.updateBusiness(SysBusinessEnums.WORK_SUB_TYPE, business.getBusId(), createBusinessId);
            }
            toExternalClient.updateCcsBusiness(business.getBusId(), createBusinessId, SysBusinessEnums.WORK_SUB_TYPE);

        } else if (business.getBusLevel() == SysBusinessEnums.WORK_TYPE) {  //工单类型
            log.info("updateBus.修改类型配置 :原busId:{}, 新busId:{}", business.getBusId(), createBusinessId);
            if (sysFormBusinessService.judgmentBusinessUsed(null, business.getBusId(), null)) {
                sysFormBusinessService.updateBusiness(SysBusinessEnums.WORK_TYPE, business.getBusId(), createBusinessId);
            }
            toExternalClient.updateCcsBusiness(business.getBusId(), createBusinessId, SysBusinessEnums.WORK_TYPE);

        } else if (business.getBusLevel() == SysBusinessEnums.BUS_TYPE) {  //业务类型
            log.info("updateBus.修改类型配置 :原busId:{}, 新busId:{}", business.getBusId(), createBusinessId);
            if (sysFormBusinessService.judgmentBusinessUsed(business.getBusId(), null, null)) {
                sysFormBusinessService.updateBusiness(SysBusinessEnums.BUS_TYPE, business.getBusId(), createBusinessId);
            }
            toExternalClient.updateCcsBusiness(business.getBusId(), createBusinessId, SysBusinessEnums.BUS_TYPE);
        }
    }

    @Override
    public Map<String, String> queryBusinessName(List<Long> businessIds) {
        int size = businessIds.size();
        int limitSize = 900;
        int toIndex = limitSize;
        List<SysBusinessVO> sysBusinessVOS = new ArrayList<>();
        for (int i = 0; i < size; i += limitSize) {
            if (i + limitSize > size) {
                toIndex = size - i;
            }
            sysBusinessVOS.addAll(baseMapper.queryBusinessName(new HashSet<>(businessIds.subList(i, i + toIndex))));
        }
        return sysBusinessVOS.stream().collect(Collectors.toMap(x -> String.valueOf(x.getBusId()), SysBusinessVO::getName, (x, x1) -> x1));
    }

    /**
     * 检查名称是否重复
     */
    private void checkNameRepeat(SysBusiness sysBusiness) {
        LambdaQueryWrapper<SysBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysBusiness::getName, sysBusiness.getName())
                .eq(SysBusiness::getParentId, sysBusiness.getParentId())
                .eq(SysBusiness::getStatus, CommonConstants.ENABLED)
                .ne(sysBusiness.getBusId() != null, SysBusiness::getBusId, sysBusiness.getBusId());
        if (count(wrapper) > 0) {
            throw new SystemServiceException(SystemErrorCode.SYS_BUSINESS_EXIST);
        }
    }

}
