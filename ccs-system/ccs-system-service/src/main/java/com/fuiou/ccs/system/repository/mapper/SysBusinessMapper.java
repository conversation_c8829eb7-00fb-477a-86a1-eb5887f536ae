package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.query.SysBusinessQuery;
import com.fuiou.ccs.system.repository.entity.SysBusiness;
import com.fuiou.ccs.system.vo.SysBusinessVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 业务表(类型配置) Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysBusinessMapper extends BaseMapper<SysBusiness> {


    /**
     * 类型配置的树状结构
     *
     * @return 类型配置信息
     */
    List<SysBusinessVO> getBusList();

    /**
     * 查询所有类型配置(分页)
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页类型配置
     */
    IPage<SysBusinessVO> busListPage(IPage page, @Param("query") SysBusinessQuery query);

    /**
     * 查询所有类型配置(不分页)
     *
     * @param query 查询参数
     * @return 类型配置信息
     */
    List<SysBusinessVO> busList(@Param("query") SysBusinessQuery query);

    /**
     * 根据业务id集合，查询对应中文名称
     *
     * @param collect
     * @return
     */
    List<SysBusinessVO> queryBusinessName(@Param("collect") Set<Long> collect);

    /**
     * 更新父类ID
     *
     * @param oldId
     * @param newId
     * @return
     */
    int updateParentId(@Param("oldId") Long oldId, @Param("newId") Long newId);

    List<SysBusinessVO> queryBusinessListByIds(@Param("businessIds") Set<Long> businessIds);
}
