package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.repository.entity.SysFormBusiness;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表单业务类型 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysFormBusinessService extends IService<SysFormBusiness> {


    /**
     * 根据表单配置ID返回该表单配置的业务类型VO
     *
     * @param formId 表单模板ID
     * @return 该模板对应的业务类型List
     */
    List<SysFormBusinessVO> getFormBusinessVoByFormId(Long formId);


    /**
     * 进行修改/新增
     *
     * @param usedStatusFlag 表单使用状态
     * @param newList        最新的参数
     * @param oldList        老参数
     */
    String updateFormBusiness(boolean usedStatusFlag, List<SysFormBusinessVO> newList, List<SysFormBusinessVO> oldList);

    /**
     * 判断表单业务类型的参数没有参数错误的，以及没有重复的，另外将他们返回成VO
     *
     * @param newList 参数
     * @return List
     */
    List<SysFormBusinessVO> getVoByList(Long formId, List<SysFormBusiness> newList);


    /**
     * 批量新增
     *
     * @param formId 表单配置ID
     * @param list   新增的参数
     */
    void saveFormBusinessList(Long formId, List<SysFormBusiness> list);

    /**
     * 根据表单id，查询对应配置的业务类型
     *
     * @param formIds 表单配置ID集合
     * @return 表单id为key, 业务类型集合为value
     */
    Map<Long, List<SysFormBusinessVO>> queryFormBusList(List<Long> formIds);

    /**
     * 根据表单ID逻辑删除对应的业务
     *
     * @param formId 表单ID
     */
    void removeByFormId(Long formId);

    /**
     * 根据表单ID真实删除数据库中对应的表单业务数据；
     *
     * @param formId 表单ID
     * @return 成功
     */
    boolean realDelBusByFormId(Long formId);

    /**
     * 根据业务ids，查询数据
     *
     * @param businessIds
     * @return
     */
    List<SysFormBusinessVO> queryBuinessList(Set<Long> businessIds);

    /**
     * 根据条件，查询formBusinessIds集合
     *
     * @param busIds
     * @param workIds
     * @param workSubIds
     * @return
     */
    List<SysFormBusinessVO> queryFormBusinessList(Set<Long> busIds, Set<Long> workIds, Set<Long> workSubIds);


    /**
     * 业务类型改名称时调用此接口
     *
     * @param busId     业务类型ID
     * @param workId    工单类型ID
     * @param workSubId 工单子类ID
     * @return 是否存在表单业务类型对应关系
     */
    boolean judgmentBusinessUsed(Long busId, Long workId, Long workSubId);


    /**
     * 更新表单业务类型关系表
     *
     * @param type          业务类型
     * @param oldBusinessId 老id
     * @param newBusinessId 新增的id
     */
    void updateBusiness(Integer type, Long oldBusinessId, Long newBusinessId);

    /**
     * 根据3要素，查询表单
     *
     * @param busId
     * @param workId
     * @param workSubId
     * @return
     */
    SysFormBusinessVO queryFormBusiness(Long busId, Long workId, Long workSubId);

    /**
     * 根据表单id,查询对应的所有business表ID
     *
     * @param formId
     * @return
     */
    List<Long> queryAllBusinessIdList(Long formId);
}
