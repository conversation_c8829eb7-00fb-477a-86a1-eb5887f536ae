package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 表单配置详情
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_FORM_DETAIL")
public class SysFormDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("FORM_DETAIL_ID")
    private Long formDetailId;

    /**
     * 表单配置ID
     */
    @TableField("FORM_ID")
    private Long formId;

    /**
     * 字段英文名
     */
    @TableField("COLUMNS_NAME")
    private String columnsName;

    /**
     * 字段中文名
     */
    @TableField("CN_COLUMNS_NAME")
    private String cnColumnsName;

    /**
     * 字段长度
     */
    @TableField("COLUMNS_LENGTH")
    private Integer columnsLength;

    /**
     * 字段是否非空;1-非空，0-不非空（默认）
     */
    @TableField("COLUMNS_NOT_NULL")
    private Integer columnsNotNull;

    /**
     * 字段文本框内显示描述
     */
    @TableField("COLUMNS_DESCRIBE")
    private String columnsDescribe;

    /**
     * 表单中对应字段类型
     */
    @TableField("COLUMNS_TYPE")
    private String columnsType;

    /**
     * 字段默认值
     */
    @TableField("COLUMNS_DEFAULT_VALUE")
    private String columnsDefaultValue;

    /**
     * 字段是否查询条件;0-不查询（默认），1-等值查询，2-模糊查询
     */
    @TableField("QUERY_TYPE")
    private Integer queryType;


    /**
     * 字段生成顺序
     */
    @TableField("COLUMNS_ORDER")
    private Integer columnsOrder;


    /**
     * 字典表编码;默认0，1-是字典，不是0,1就代表是字典里的编码（需从字典取值）
     */
    @TableField("DICT_CODE")
    private String dictCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否已删除（0-未删除，默认；1-已删除）
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Integer deletedStatus;

    /**
     * 自动填充0-否（默认），1-是
     */
    @TableField("AUTOMATIC_FILL_IN")
    private Integer automaticFillIn;


    /**
     * 是否外部来源:0-否（默认），1-是
     */
    @TableField("IS_OUTSIDE_SOURCE")
    private Integer outsideSource;

    /**
     * 对外展示字段
     */
    @TableField("OUTSIDE_SOURCE_KEY")
    private String outsideSourceKey;

    /**
     * 是否预置字段：0-否（默认），1-是
     */
    @TableField("IS_PRESET")
    private Integer presetStatus;

    /**
     * 外部来源配置表ID
     */
    @TableField("SOURCE_URL_ID")
    private Long sourceUrlId;

    /**
     * 表头设置：0-不展示（默认）；1-仅表头展示；2-仅导出展示；3-表头和导出都展示
     */
    @TableField("IS_HEADER")
    private Integer headerStatus;


}
