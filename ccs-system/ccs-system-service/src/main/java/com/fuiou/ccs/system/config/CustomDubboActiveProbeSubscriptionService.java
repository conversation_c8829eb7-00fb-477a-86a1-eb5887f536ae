package com.fuiou.ccs.system.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 14:39
 */
// @Slf4j
// @Component
// public class CustomDubboActiveProbeSubscriptionService implements ApplicationListener<ApplicationReadyEvent> {
//     @Override
//     public void onApplicationEvent(ApplicationReadyEvent event) {
//         DubboServiceMetadataRepository dubboServiceMetadataRepository = event.getApplicationContext().getBean(DubboServiceMetadataRepository.class);
//         Set<String> subscribedServices = dubboServiceMetadataRepository.getSubscribedServices();
//         if (!ObjectUtils.isEmpty(subscribedServices)) {
//             DiscoveryClient DiscoveryClient = event.getApplicationContext().getBean(DiscoveryClient.class);
//             for (String subscribedService : subscribedServices) {
//                 log.info("主动探测订阅服务:" + subscribedService);
//                 ServiceInstancesChangedEvent changedEvent = new ServiceInstancesChangedEvent(subscribedService, DiscoveryClient.getInstances(subscribedService));
//                 event.getApplicationContext().publishEvent(changedEvent);
//             }
//         }
//     }
// }
