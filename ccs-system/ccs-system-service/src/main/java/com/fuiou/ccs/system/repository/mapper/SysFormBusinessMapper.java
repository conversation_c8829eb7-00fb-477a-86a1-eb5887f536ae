package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fuiou.ccs.system.repository.entity.SysFormBusiness;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 表单业务类型 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysFormBusinessMapper extends BaseMapper<SysFormBusiness> {

    /**
     * 根据formId，查询工单配置的业务类型
     *
     * @param formId 表单配置模板ID
     * @return List<SysFormBusinessVO>
     */
    List<SysFormBusinessVO> queryFormBusList(@Param("formId") Long formId);

    /**
     * 根据表单ID真实删除数据库中对应的表单业务数据；
     *
     * @param formId 表单配置ID
     */
    void realDelBusByFormId(@Param("formId") Long formId);

    /**
     * 根据业务ids，查询业务类型数据
     *
     * @param businessIds
     * @return
     */
    List<SysFormBusinessVO> queryBuinessList(@Param("businessIds") Set<Long> businessIds);

    /**
     * 根据条件，查询业务类型ids对象集合
     *
     * @param busIds
     * @param workIds
     * @param workSubIds
     * @return
     */
    List<SysFormBusinessVO> queryFormBusinessList(@Param("busIds") Set<Long> busIds, @Param("workIds") Set<Long> workIds, @Param("workSubIds") Set<Long> workSubIds);

    /**
     * @param busId     业务类型ID
     * @param workId    工单类型ID
     * @param workSubId 工单子类ID
     * @return 表单业务类型对应关系
     */
    List<SysFormBusinessVO> queryFormBusListByType(@Param("busId") Long busId, @Param("workId") Long workId, @Param("workSubId") Long workSubId);

    /**
     * 根据条件查询表单
     *
     * @param busId
     * @param workId
     * @param workSubId
     * @return
     */
    SysFormBusinessVO queryFormBusiness(@Param("busId") Long busId,
                                        @Param("workId") Long workId, @Param("workSubId") Long workSubId);

    /**
     * 根据表单ID，查询所有业务类型表ID
     *
     * @param formId
     * @return
     */
    List<SysFormBusinessVO> queryAllBusinessIdList(@Param("formId") Long formId);
}
