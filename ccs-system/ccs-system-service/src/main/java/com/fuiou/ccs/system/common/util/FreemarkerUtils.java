package com.fuiou.ccs.system.common.util;

import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年09月14日 14:31
 * @Description 模板文件操作工具
 */
@Slf4j
public class FreemarkerUtils {


    private static final String PATH_NAME = "/ftl/";

    public static String getEmailHtml(Map<String, Object> map, String templateName) {
        String htmlText = "";
        StringWriter result = new StringWriter(1024);
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_30);
        try {
            //加载模板路径
            configuration.setClassForTemplateLoading(FreemarkerUtils.class, PATH_NAME);
            //渲染模板为html
            configuration.getTemplate(templateName).process(map, result);
            return result.toString();
        } catch (IOException | TemplateException e) {
            e.printStackTrace();
            log.error("FreemarkerUtils.getEmailHtml.加载邮件模板报错；PATH_NAME={} ， templateName={} ； {} ， {}", PATH_NAME, templateName, e.getMessage(), e);
        }
        return htmlText;
    }

}
