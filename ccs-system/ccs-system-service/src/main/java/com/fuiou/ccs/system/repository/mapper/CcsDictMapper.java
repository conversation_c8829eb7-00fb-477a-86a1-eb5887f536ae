package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fuiou.ccs.system.repository.entity.CcsDict;
import org.apache.ibatis.annotations.Mapper;

/**
 * 字典 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CcsDictMapper extends BaseMapper<CcsDict> {

    /**
     * 查询字典编号是否存在
     *
     * @param dictCode 字典编号
     * @return 字典编号
     */
    String selectExists(String dictCode);

}
