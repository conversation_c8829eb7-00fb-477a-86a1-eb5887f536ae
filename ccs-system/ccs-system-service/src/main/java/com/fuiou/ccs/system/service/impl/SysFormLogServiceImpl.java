package com.fuiou.ccs.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.convert.SysFormLogConvert;
import com.fuiou.ccs.system.repository.entity.SysFormLog;
import com.fuiou.ccs.system.repository.mapper.SysFormLogMapper;
import com.fuiou.ccs.system.service.SysFormLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.system.vo.SysFormLogVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 表单配置日志 服务实现类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Service
public class SysFormLogServiceImpl extends ServiceImpl<SysFormLogMapper, SysFormLog> implements SysFormLogService {


    @Override
    public IPage<SysFormLogVO> pageFormLog(Long formId) {
        return baseMapper.pageFormLog(formId);
    }

    /**
     * @param formId 表单配置ID
     * @return 据表单配置ID查询表单配置的所有日志信息
     */
    @Override
    public List<SysFormLogVO> listFormLog(Long formId) {
        QueryWrapper<SysFormLog> queryWrapper = new QueryWrapper<SysFormLog>();
        queryWrapper.eq("FORM_ID", formId);
        return SysFormLogConvert.INSTANCE.toVO(list(queryWrapper));
    }
}
