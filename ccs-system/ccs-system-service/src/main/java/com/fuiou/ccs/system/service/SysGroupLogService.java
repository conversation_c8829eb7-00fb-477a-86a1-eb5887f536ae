package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.enums.GroupLogEnums;
import com.fuiou.ccs.system.repository.entity.SysGroupLog;
import com.fuiou.ccs.system.vo.SysGroupMemberVO;

import java.util.List;

/**
 * 工作组日志 服务类
 *
 * <AUTHOR> @since  1.0.0
 */
public interface SysGroupLogService extends IService<SysGroupLog> {

    /**
     *  新增工作组日志
     * @param groupId 工作组id
     * @param type  操作类型  1：新增，2：修改，3：删除
     * @param content 内容
     * @return 成功或失败
     */
    Boolean saveGroupLog(Long groupId, GroupLogEnums.TypeEnums type, String content);


    /**
     *
     * @param list 新增工作组组成员信息
     * @param type 操作类型  1：新增，2：修改，3：删除
     * @param content 内容
     * @return 成功或失败
     */
    Boolean batchSaveLog(Long groupId, List<SysGroupMemberVO> list,  GroupLogEnums.TypeEnums type, String content);

}
