package com.fuiou.ccs.system.controller;

import com.fuiou.ccs.system.service.CcsMenuService;
import com.fuiou.ccs.system.service.CcsSysUserService;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import com.fuiou.ccs.system.vo.CcsSysUserVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.domain.AuthUser;
import com.fuiou.common.utils.AuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@RestController
@RequiredArgsConstructor
@Api(tags = {"首页相关接口"})
public class IndexController {
    final CcsSysUserService sysUserService;
    final CcsMenuService ccsMenuService;

    @GetMapping("/user-info")
    @ApiOperation(value = "当前用户信息")
    ApiResult<CcsSysUserVO> currUserInfo() {
        return ApiResult.data(sysUserService.getCurrentUserInfo());
    }

    @GetMapping("/routes")
    @ApiOperation(value = "查询用户菜单")
    ApiResult<List<CcsMenuVO>> routes() {
        AuthUser authUser = AuthUtil.getUser();
        return ApiResult.data(ccsMenuService.listRoutes(authUser));
    }
}
