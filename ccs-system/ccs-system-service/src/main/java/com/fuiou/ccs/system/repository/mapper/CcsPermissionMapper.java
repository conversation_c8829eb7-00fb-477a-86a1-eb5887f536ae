package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fuiou.ccs.system.repository.entity.CcsPermission;
import com.fuiou.ccs.system.repository.entity.SysUrlPermissionRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客服工单权限 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CcsPermissionMapper extends BaseMapper<CcsPermission> {


    List<SysUrlPermissionRule> selectPermissionRules();

    void deleteRolePermissionByPermissionId(@Param("permissionId") Long permissionId);
}
