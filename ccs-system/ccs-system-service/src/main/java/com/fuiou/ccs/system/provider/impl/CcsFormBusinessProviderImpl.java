package com.fuiou.ccs.system.provider.impl;

import com.fuiou.ccs.system.provider.CcsFormBuinessProvider;
import com.fuiou.ccs.system.service.SysFormBusinessService;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import com.fuiou.common.api.ApiResult;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Set;

/**
 * 客户端对外接口实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@DubboService
public class CcsFormBusinessProviderImpl implements CcsFormBuinessProvider {

    final SysFormBusinessService sysFormBusinessService;

    public CcsFormBusinessProviderImpl(SysFormBusinessService sysFormBusinessService) {
        this.sysFormBusinessService = sysFormBusinessService;
    }

    @Override
    public ApiResult<List<SysFormBusinessVO>> queryBuinessList(Set<Long> businessIds) {
        return ApiResult.data(sysFormBusinessService.queryBuinessList(businessIds));
    }

    @Override
    public ApiResult<List<SysFormBusinessVO>> queryFormBusinessList(Set<Long> busIds, Set<Long> workIds, Set<Long> workSubIds) {
        return ApiResult.data(sysFormBusinessService.queryFormBusinessList(busIds, workIds, workSubIds));
    }

    @Override
    public ApiResult<SysFormBusinessVO> queryFormBusiness(Long busId, Long workId, Long workSubId) {
        return ApiResult.data(sysFormBusinessService.queryFormBusiness(busId, workId, workSubId));
    }

    @Override
    public ApiResult<List<Long>> queryAllBusinessIdList(Long formId) {
        return ApiResult.data(sysFormBusinessService.queryAllBusinessIdList(formId));
    }
}
