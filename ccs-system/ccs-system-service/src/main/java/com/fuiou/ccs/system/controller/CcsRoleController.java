package com.fuiou.ccs.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.param.CcsRoleParam;
import com.fuiou.ccs.system.query.CcsRoleQuery;
import com.fuiou.ccs.system.service.CcsPermissionService;
import com.fuiou.ccs.system.service.CcsRoleService;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import com.fuiou.ccs.system.vo.CcsPermissionVO;
import com.fuiou.ccs.system.vo.CcsRoleVO;
import com.fuiou.ccs.system.vo.CcsSysUserVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fuiou.ccs.system.constants.CcsConfigConstants.ROLE_LOCK;

/**
 * 客服工单角色 前端控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"角色相关接口"})
@RequestMapping("/roles")
public class CcsRoleController extends BaseController {

    final CcsRoleService ccsRoleService;
    final CcsPermissionService ccsPermissionService;

    /**
     * 分页查询所有角色
     *
     * @param query 查询条件
     * @return 分页查询结果
     */
    @GetMapping
    @ApiOperation(value = "查询所有角色(分页)")
    ApiResult<IPage<CcsRoleVO>> roleListPage(CcsRoleQuery query) {
        return ApiResult.data(ccsRoleService.roleListPage(getPage(), query));
    }

    /**
     * 角色详情
     *
     * @param roleId 角色ID
     * @return 角色详情
     */
    @GetMapping("/{roleId}")
    @ApiOperation(value = "角色详情")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<CcsRoleVO> get(@PathVariable("roleId") Long roleId) {
        CcsRoleVO ccsRoleVO = ccsRoleService.getDetail(roleId);
        return ApiResult.data(ccsRoleVO);
    }

    @PostMapping
    @ApiOperation(value = "创建角色")
    ApiResult<Void> save(@RequestBody @Validated(CcsRoleParam.Create.class) CcsRoleParam ccsRoleParam) {
        return ApiResult.status(ccsRoleService.saveRole(ccsRoleParam, ROLE_LOCK));
    }

    @PutMapping("/{roleId}")
    @ApiOperation("修改角色")
    @ApiImplicitParams({@ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"), @ApiImplicitParam(name = "ccsRoleParam", value = "角色参数", required = true, dataType = "CcsRoleParam", paramType = "body")})
    ApiResult<Void> update(@PathVariable("roleId") Long roleId, @RequestBody @Validated(CcsRoleParam.Update.class) CcsRoleParam ccsRoleParam) {
        ccsRoleParam.setRoleId(roleId);
        return ApiResult.status(ccsRoleService.updateRoleForReview(ccsRoleParam, ROLE_LOCK));
    }

    @DeleteMapping("/{roleId}")
    @ApiOperation("删除角色")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<Void> delete(@PathVariable("roleId") Long roleId) {
        return ApiResult.status(ccsRoleService.deleteRoleForReview(roleId));
    }

    /**
     * 持有某角色的用户列表分页查询
     *
     * @param id       角色ID
     * @param userName 用户中文名/用户名通用-作为查询条件
     * @return
     */
    @GetMapping("/pageListRoleUsers/{id}")
    @ApiOperation(value = "持有角色的用户列表分页查询")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<IPage<CcsSysUserVO>> pageListRoleUsers(@PathVariable("id") Long id, @ApiParam(name = "userName", value = "用户中文名/用户名通用-作为查询条件", required = false) String userName) {
        return ApiResult.data(ccsRoleService.pageListRoleUsers(this.getPage(), id, userName));
    }

    /**
     * 查询某角色下的菜单
     *
     * @param roleId 角色Id
     * @return 该角色所有菜单
     */
    @GetMapping("/{roleId}/menus")
    @ApiOperation(value = "角色菜单")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<List<CcsMenuVO>> menus(@PathVariable("roleId") Long roleId) {
        return ApiResult.data(ccsRoleService.listMenusByRoleId(roleId));
    }

    /**
     * 查询某角色下的权限
     *
     * @param roleId 角色Id
     * @return 该角色所有权限
     */
    @GetMapping("/{roleId}/permissions")
    @ApiOperation(value = "角色权限")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<List<CcsPermissionVO>> permissions(@PathVariable("roleId") Long roleId) {
        return ApiResult.data(ccsRoleService.listPermissionsByRoleId(roleId));
    }

    /**
     * 根据编码查询角色对象
     *
     * @param roleCode 角色编码
     * @return 角色对象
     */
    @GetMapping("/queryRoleName/{roleCode}")
    @ApiOperation("根据编码查询角色名称")
    ApiResult<CcsRoleVO> queryByRoleCode(@PathVariable("roleCode") String roleCode) {
        return ApiResult.data(ccsRoleService.queryNameByRoleCode(roleCode));
    }


}

