package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fuiou.ccs.system.repository.entity.CcsMenu;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客服工单菜单 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CcsMenuMapper extends BaseMapper<CcsMenu> {

    /**
     * 查询当前客户端的菜单
     *
     * @param clientId 客户端ID
     * @return 客户端菜单
     */
    List<CcsMenuVO> selectMenuByClientId(@Param("clientId") String clientId);

    /**
     * 查询用户菜单
     *
     * @param userId   用户ID
     * @param clientId 菜单ID
     * @return 用户菜单
     */
    List<CcsMenuVO> selectMenuByUserId(@Param("userId") String userId, @Param("clientId") String clientId);

    /**
     * 获取持有某菜单的角色ID集合
     *
     * @param menuId 菜单ID
     * @return 角色id集合
     */
    List<Long> selectRolesByMenuId(@Param("menuId") Long menuId);

    /**
     * 删除菜单时，删除关联的角色菜单数据
     *
     * @param menuId 菜单ID
     */
    void deleteRoleMenuByMenuId(@Param("menuId") Long menuId);
}
