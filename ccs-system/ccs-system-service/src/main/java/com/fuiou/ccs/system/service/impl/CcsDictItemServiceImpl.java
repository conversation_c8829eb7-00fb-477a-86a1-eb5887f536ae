package com.fuiou.ccs.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.system.convert.CcsDictItemConvert;
import com.fuiou.ccs.system.dto.CcsDictItemDTO;
import com.fuiou.ccs.system.exception.SystemErrorCode;
import com.fuiou.ccs.system.exception.SystemServiceException;
import com.fuiou.ccs.system.param.CcsDictItemParam;
import com.fuiou.ccs.system.query.CcsDictItemQuery;
import com.fuiou.ccs.system.repository.entity.CcsDictItem;
import com.fuiou.ccs.system.repository.mapper.CcsDictItemMapper;
import com.fuiou.ccs.system.service.CcsDictItemService;
import com.fuiou.ccs.system.vo.CcsDictItemVO;
import com.fuiou.common.constants.CommonConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典数据 服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class CcsDictItemServiceImpl extends ServiceImpl<CcsDictItemMapper, CcsDictItem> implements CcsDictItemService {

    @Override
    public boolean save(CcsDictItemParam cssDictItemParam) {
        CcsDictItem cssDictItem = CcsDictItemConvert.INSTANCE.toEntity(cssDictItemParam);
        //新增时要校验不能有重复的
        checkRepeat(cssDictItem);
        Long dictItemId = IdWorker.getId();
        cssDictItem.setDictItemId(dictItemId);
        return save(cssDictItem);
    }

    @Override
    public boolean updateById(CcsDictItemParam cssDictItemParam) {
        CcsDictItem cssDictItem = CcsDictItemConvert.INSTANCE.toEntity(cssDictItemParam);
        // CcsDictItem oldSysDictItem = baseMapper.selectById(cssDictItemParam);
        if (cssDictItem.getDictItemId() == null) {
            throw new SystemServiceException(SystemErrorCode.DICT_ITEM_ID_NOT_NULL);
        }

        //校验修改时不能重复
        checkRepeat(cssDictItem);
        return updateById(cssDictItem);
    }

    /**
     * 根据 DICT_CODE与VALUE判断新增时不能有重复的
     * 修改时要判断 不等于原来的ID时 DICT_CODE与VALUE不能有重复的
     * 校验是否有重复的
     */
    private void checkRepeat(CcsDictItem cssDictItem) {
        QueryWrapper<CcsDictItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DICT_CODE", cssDictItem.getDictCode())
                .eq("VALUE", cssDictItem.getValue())
                .eq("IS_DELETED", CommonConstants.NO)
                .ne(cssDictItem.getDictItemId() != null, "DICT_ITEM_ID", cssDictItem.getDictItemId());
        if (baseMapper.selectCount(queryWrapper) > 0) {
            throw new SystemServiceException(SystemErrorCode.DICT_ITEM_VALUE_EXISTED);
        }
    }

    @Override
    public List<CcsDictItemDTO> listByDictCode(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return Lists.newArrayListWithCapacity(1);
        }
        return CcsDictItemConvert.INSTANCE.toDTO(baseMapper.selectByDictCode(dictCode));
    }

    @Override
    public Map<String, List<CcsDictItemDTO>> listByDictCode(List<String> dictCodes) {
        List<CcsDictItem> list = baseMapper.selectByDictCodes(dictCodes);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return CcsDictItemConvert.INSTANCE.toDTO(list).stream().collect(Collectors.groupingBy(CcsDictItemDTO::getDictCode));
    }


    @Override
    public List<String> valueListByDictCode(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return null;
        }
        return baseMapper.selectValueByDictCode(dictCode);
    }


    public List<String> checkValue(String dictCode, List<String> list) {
        if (StringUtils.isBlank(dictCode) || CollectionUtils.isEmpty(list)) {
            return null;
        }
        return baseMapper.checkValue(dictCode, list);
    }

    @Override
    public CcsDictItemDTO getByCodeValue(String dictCode, String dictValue, Boolean isContainDelete) {
        return CcsDictItemConvert.INSTANCE.toDTO(baseMapper.selectByCodeValue(dictCode, dictValue, isContainDelete));
    }

    @Override
    public CcsDictItemDTO selectByCodeLabel(String dictCode, String label) {
        return CcsDictItemConvert.INSTANCE.toDTO(baseMapper.selectByCodeLabel(dictCode, label));
    }

    @Override
    public boolean removeByDictCode(String dictCode) {
        if (dictCode != null) {
            baseMapper.deleteByDictCode(dictCode);
        }
        return true;
    }

    @Override
    public boolean removeByDictCodes(List<String> dictCodes) {
        if (CollectionUtils.isNotEmpty(dictCodes)) {
            baseMapper.deleteByDictCodes(dictCodes);
        }
        return true;
    }

    @Override
    public boolean updateDictCode(String oldDictCode, String newDictCode) {
        return baseMapper.updateDictCode(oldDictCode, newDictCode);
    }

    /**
     * 不分页查询
     *
     * @param query 参数  字典编号/字典键值/字典标签
     * @return 根据字典编号/字典键值/字典标签 查询返回List
     */
    @Override
    public List<CcsDictItemVO> listDictItems(CcsDictItemQuery query) {
        QueryWrapper<CcsDictItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(query.getDictCode()), "DICT_CODE", query.getDictCode())
                .eq(StringUtils.isNotEmpty(query.getValue()), "VALUE", query.getValue())
                .eq("IS_DELETED", CommonConstants.NO)
                .eq(StringUtils.isNotEmpty(query.getLabel()), "LABEL", query.getLabel());

        return CcsDictItemConvert.INSTANCE.toVO(baseMapper.selectList(queryWrapper));
    }

}
