package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客服工单菜单
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_MENU")
public class CcsMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("MENU_ID")
    private Long menuId;

    /**
     * 父级ID
     */
    @TableField("PARENT_ID")
    private Long parentId;

    /**
     * 菜单名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 图标
     */
    @TableField("ICON")
    private String icon;

    /**
     * 路由路径
     */
    @TableField("PATH")
    private String path;

    /**
     * 组件路径
     */
    @TableField("COMPONENT")
    private String component;

    /**
     * 显示顺序
     */
    @TableField("SORT")
    private Integer sort;

    /**
     * 状态（1(默认)：启用，0：停用）
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否已被删除：1-是，已被删除   0(默认)-否，状态正常
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Integer deletedStatus;

    /**
     * 是否是菜单  1:是(默认)  0:否
     */
    @TableField("IS_MENU")
    private Integer menuStatus;

    /**
     * 客户端Id
     */
    @TableField("CLIENT_ID")
    private String clientId;
}
