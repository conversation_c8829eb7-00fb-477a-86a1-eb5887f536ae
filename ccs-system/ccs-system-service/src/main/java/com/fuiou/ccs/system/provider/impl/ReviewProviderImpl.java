package com.fuiou.ccs.system.provider.impl;

import com.fuiou.ccs.system.enums.ReviewEnums;
import com.fuiou.ccs.system.provider.ReviewProvider;
import com.fuiou.ccs.system.service.ReviewRecordService;
import com.fuiou.common.api.ApiResult;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/14 9:55
 */
@DubboService
public class ReviewProviderImpl implements ReviewProvider {

    final ReviewRecordService reviewRecordService;

    public ReviewProviderImpl(ReviewRecordService reviewRecordService) {
        this.reviewRecordService = reviewRecordService;
    }

    @Override
    public ApiResult<Void> saveReviewRecord(String businessId, String businessNo, Object oldObj, Object newObj, ReviewEnums.ReviewModule reviewModule, ReviewEnums.OperationEnum operationEnum, String creator) {
        reviewRecordService.saveReviewRecord(businessId, businessNo, oldObj, newObj, reviewModule, operationEnum, creator);
        return ApiResult.success();
    }
}
