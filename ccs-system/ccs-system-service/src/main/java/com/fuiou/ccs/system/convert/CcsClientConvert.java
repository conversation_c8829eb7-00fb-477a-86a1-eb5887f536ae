package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.dto.CcsClientDTO;
import com.fuiou.ccs.system.param.CcsClientParam;
import com.fuiou.ccs.system.repository.entity.CcsClient;
import com.fuiou.ccs.system.vo.CcsClientVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 客户端 对象转化器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CcsClientConvert {

    CcsClientConvert INSTANCE = Mappers.getMapper(CcsClientConvert.class);

    @Mappings({})
    CcsClientVO toVO(CcsClient ccsClient);

    @Mappings({})
    List<CcsClientVO> toVO(List<CcsClient> ccsClientList);

    @Mappings({})
    CcsClientDTO toDTO(CcsClient ccsClient);

    @Mappings({})
    List<CcsClientDTO> toDTO(List<CcsClient> ccsClientList);

    @Mappings({})
    CcsClient toEntity(CcsClientParam ccsClientParam);

    @Mappings({})
    CcsClient toEntity(CcsClientDTO ccsClientDTO);

    @Mappings({})
    CcsClient toEntity(CcsClientVO ccsClientVO);

}
