package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.repository.entity.CcsMenu;
import com.fuiou.ccs.system.dto.CcsMenuDTO;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import com.fuiou.ccs.system.param.CcsMenuParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 客服工单菜单 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface CcsMenuConvert {

    CcsMenuConvert INSTANCE = Mappers.getMapper(CcsMenuConvert.class);

    @Mappings({})
    CcsMenuVO toVO(CcsMenu ccsMenu);

    @Mappings({})
    List<CcsMenuVO> toVO(List<CcsMenu> ccsMenuList);

    @Mappings({})
    CcsMenuDTO toDTO(CcsMenu ccsMenu);

    @Mappings({})
    List<CcsMenuDTO> toDTO(List<CcsMenu> ccsMenuList);

    @Mappings({})
    CcsMenu toEntity(CcsMenuParam ccsMenuParam);

    @Mappings({})
    CcsMenu toEntity(CcsMenuDTO ccsMenuDTO);

    @Mappings({})
    CcsMenu toEntity(CcsMenuVO ccsMenuVO);

}
