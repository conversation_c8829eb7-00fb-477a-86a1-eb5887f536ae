package com.fuiou.ccs.system.review.impl;


import com.fuiou.ccs.system.enums.SysFormEnums;
import com.fuiou.ccs.system.repository.entity.SysForm;
import com.fuiou.ccs.system.repository.entity.SysFormLog;
import com.fuiou.ccs.system.repository.entity.reviewbean.FormReview;
import com.fuiou.ccs.system.review.Reviews;
import com.fuiou.ccs.system.service.SysFormBusinessService;
import com.fuiou.ccs.system.service.SysFormDetailService;
import com.fuiou.ccs.system.service.SysFormLogService;
import com.fuiou.ccs.system.service.SysFormService;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/8 18:12
 */
@Slf4j
@Service
public class FormWorkReviewsImpl implements Reviews {

    @Autowired
    SysFormService sysFormService;

    @Autowired
    SysFormBusinessService sysFormBusinessService;

    @Autowired
    SysFormDetailService sysFormDetailService;

    @Autowired
    SysFormLogService sysFormLogService;

    @Override
    public void ins(String businessId, String oldObj, String newObj) {
        FormReview formReview = JacksonUtil.parse(newObj, FormReview.class);
        //拼接创表SQL
        sysFormService.sendCreatedTableEmail(formReview);
        SysForm sysForm = formReview.getSysForm();
        //改为正常状态
        sysForm.setStatus(CommonConstants.ENABLED);
        //改为复核通过
        sysForm.setCheckedStatus(SysFormEnums.CheckEdEnums.REVIEW_SUCCESS.getValue());
        sysForm.setCreateStatus(CommonConstants.YES);
        sysFormService.updateById(sysForm);
        //新增日志信息
        SysFormLog sysFormLog = SysFormLog.builder()
                .formId(sysForm.getFormId())
                .modifyDescribe("【" + sysForm.getTableName() + "(+" + sysForm.getCnTableName() + ")】表，新增时审核通过，已创建该表单，并通知开发人员")
                .creatorId(AuthUtil.getUserId())
                .createTime(LocalDateTime.now())
                .build();
        sysFormLogService.save(sysFormLog);
    }

    @Override
    public void upd(String businessId, String oldObj, String newObj) {
        FormReview newFormReview = JacksonUtil.parse(newObj, FormReview.class);
        FormReview oldFormReview = JacksonUtil.parse(oldObj, FormReview.class);
        Long formId = oldFormReview.getSysForm().getFormId();
        SysForm oldForm = sysFormService.getById(formId);
        StringBuilder str = new StringBuilder("修改【" + oldForm.getTableName() + "】表;");
        if (!oldForm.getCnTableName().equals(newFormReview.getSysForm().getCnTableName())) {
            //记录日志
            str.append("将表描述由").append(oldForm.getCnTableName()).append("改为").append(newFormReview.getSysForm().getCnTableName()).append(";");
        }
        if (!oldForm.getCallerDictCode().equals(newFormReview.getSysForm().getCallerDictCode())) {
            //记录日志
            str.append("将表来电人类型由").append(oldForm.getCallerDictCode()).append("改为").append(newFormReview.getSysForm().getCallerDictCode()).append(";");
        }
        sysFormService.updateById(newFormReview.getSysForm());
        //未创建的修改，直接调用新增,已创建的修改  可修改，可添加，可以删除列,已使用的 不可删除，可修改，可添加
        boolean usedStatusFlag = CommonConstants.YES == oldForm.getUsedStatus();
        //修改或新增业务类型,并把修改日志记录下来
        String logFormBusinessStr = sysFormBusinessService.updateFormBusiness(usedStatusFlag, newFormReview.getFormBusinessVOList(), oldFormReview.getFormBusinessVOList());
        String logFormDetailStr = sysFormDetailService.updateFormDetailList(usedStatusFlag, oldForm.getTableName(), oldFormReview.getSysForm().getCnTableName(), newFormReview.getSysForm().getCnTableName(), newFormReview.getFormDetailList(), oldFormReview.getFormDetailList());
        //新增日志信息
        SysFormLog sysFormLog = SysFormLog.builder()
                .formId(oldForm.getFormId())
                .modifyDescribe(str + logFormBusinessStr + logFormDetailStr)
                .creatorId(AuthUtil.getUserId())
                .createTime(LocalDateTime.now())
                .build();
        sysFormLogService.save(sysFormLog);
    }

    /**
     * 物理删除
     *
     * @param businessId formId
     */
    @Override
    public void del(String businessId) {
        sysFormService.realDelFormInfo(Long.parseLong(businessId));
    }

    /**
     * 逻辑删除
     *
     * @param businessId formId
     */
    @Override
    public void abolish(String businessId) {
        sysFormService.abolishFormInfo(Long.parseLong(businessId));
    }

    /**
     * 新增时审核失败，逻辑删除数据库表单数据（真实删除会造成编号重复问题）
     *
     * @param businessId 表单ID
     */
    @Override
    public void approvalRejected(String businessId) {
        sysFormService.abolishFormInfo(Long.parseLong(businessId));
    }

    @Override
    public List<String> getNosByName(String name) {
        return sysFormService.getFormCodesByCnTableName(name);
    }
}
