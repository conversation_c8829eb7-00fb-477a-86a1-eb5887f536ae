package com.fuiou.ccs.system.review.impl;

import com.fuiou.ccs.system.exception.SysGlobalError;
import com.fuiou.ccs.system.exception.SysServiceException;
import com.fuiou.ccs.system.review.Reviews;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.flow.api.provider.FlowProcessProvider;
import com.fuiou.flow.api.provider.FlowReviewProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/7 10:25
 */
@Slf4j
@Service
public class RouteReviewsImpl implements Reviews {

    @DubboReference
    FlowReviewProvider flowReviewProvider;
    @DubboReference
    FlowProcessProvider flowProcessProvider;

    @Override
    public void ins(String businessId, String oldObj, String newObj) {
        Long flowId = Long.valueOf(businessId);
        ApiResult<Boolean> apiResult;
        try {
            apiResult = flowReviewProvider.instReviewSuccess(flowId, AuthUtil.getUserId());
        } catch (Exception e) {
            log.error("调用路由服务进行新增成功的复核操作异常! businessId:{}, e:{}", businessId, e);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
        if (!apiResult.getData() || !apiResult.getSuccess()) {
            log.error("调用路由服务进行新增成功的复核操作返回false! businessId:{}, apiResult:{}", businessId, apiResult);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
    }

    @Override
    public void upd(String businessId, String oldObj, String newObj) {
        ApiResult<Boolean> apiResult;
        try {
            apiResult = flowReviewProvider.updateReviewSuccess(newObj, AuthUtil.getUserId());
        } catch (Exception e) {
            log.error("调用路由服务进行修改成功的复核操作异常! businessId:{}, e:{}", businessId, e);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
        if (!apiResult.getData() || !apiResult.getSuccess()) {
            log.error("调用路由服务进行修改成功的复核操作返回false! businessId:{}, apiResult:{}", businessId, apiResult);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
    }

    @Override
    public void del(String businessId) {
        this.abolish(businessId);
    }

    @Override
    public void abolish(String businessId) {
        Long flowId = Long.valueOf(businessId);
        ApiResult<Boolean> apiResult;
        try {
            apiResult = flowReviewProvider.delReviewSuccess(flowId, AuthUtil.getUserId());
        } catch (Exception e) {
            log.error("调用路由服务进行逻辑删除成功的复核操作异常! businessId:{}, e:{}", businessId, e);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
        if (!apiResult.getData() || !apiResult.getSuccess()) {
            log.error("调用路由服务进行逻辑删除成功的复核操作返回false! businessId:{}, apiResult:{}", businessId, apiResult);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
    }

    @Override
    public void approvalRejected(String businessId) {
        Long flowId = Long.valueOf(businessId);
        ApiResult<Boolean> apiResult;
        try {
            apiResult = flowReviewProvider.instReviewFail(flowId, AuthUtil.getUserId());
        } catch (Exception e) {
            log.error("调用路由服务进行新增拒绝的复核操作异常! businessId:{}, e:{}", businessId, e);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
        if (!apiResult.getData() || !apiResult.getSuccess()) {
            log.error("调用路由服务进行新增拒绝的复核操作返回false! businessId:{}, apiResult:{}", businessId, apiResult);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR);
        }
    }

    @Override
    public List<String> getNosByName(String name) {
        ApiResult<List<Long>> apiResult;
        try {
            apiResult = flowProcessProvider.getFlowIdsByName(name, "KF");
        } catch (Exception e) {
            log.error("调用路由服务根据路由名称查编号异常! name:{}, e:{}", name, e);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR.getCode(), "路由复核数据查询失败！");
        }
        if (!apiResult.getSuccess()) {
            log.error("调用路由服务根据路由名称查编号返回false! name:{}, apiResult:{}", name, apiResult);
            throw new SysServiceException(SysGlobalError.REVIEW_ERROR.getCode(), "路由复核数据查询失败！");
        }

        List<String> nos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(apiResult.getData())) {
            nos = apiResult.getData().stream().map(Object::toString).collect(Collectors.toList());
        }
        return nos;
    }

}
