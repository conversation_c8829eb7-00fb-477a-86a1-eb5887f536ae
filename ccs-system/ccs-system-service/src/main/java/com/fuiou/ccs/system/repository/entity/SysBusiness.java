package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 业务表(类型配置)
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_BUSINESS")
public class SysBusiness implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型ID
     */
    @TableId("BUS_ID")
    private Long busId;

    /**
     * 上级业务ID
     */
    @TableField("PARENT_ID")
    private Long parentId;

    /**
     * 业务名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 业务树的级别(1-业务类型，2-工单类型，3-工单子类型)
     */
    @TableField("BUS_LEVEL")
    private Integer busLevel;

    /**
     * 业务状态（1-正常，0-停用）
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 修改日期
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 修改人ID
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 创建日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 关联组织代码
     */
    @TableField("ORG_CODE")
    private String orgCode;

}
