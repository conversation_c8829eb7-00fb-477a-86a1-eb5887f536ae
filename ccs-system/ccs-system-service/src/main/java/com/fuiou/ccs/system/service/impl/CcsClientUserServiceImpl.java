package com.fuiou.ccs.system.service.impl;

import com.fuiou.ccs.system.repository.entity.CcsClientUser;
import com.fuiou.ccs.system.repository.mapper.CcsClientUserMapper;
import com.fuiou.ccs.system.service.CcsClientUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 客户端用户关系 服务实现类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Service
public class CcsClientUserServiceImpl extends ServiceImpl<CcsClientUserMapper, CcsClientUser> implements CcsClientUserService {



}
