package com.fuiou.ccs.system.controller;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fuiou.ccs.system.convert.CcsMenuConvert;
import com.fuiou.ccs.system.param.CcsMenuParam;
import com.fuiou.ccs.system.query.CcsMenuQuery;
import com.fuiou.ccs.system.repository.entity.CcsMenu;
import com.fuiou.ccs.system.service.CcsMenuService;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 客服工单菜单 前端控制器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"菜单相关接口"})
@RequestMapping("/menus")
public class CcsMenuController extends BaseController {

    final CcsMenuService ccsMenuService;

    @GetMapping
    @ApiOperation(value = "查询所有菜单")
    ApiResult<List<CcsMenuVO>> list(CcsMenuQuery query) {
        return ApiResult.data(ccsMenuService.listMenus(query));
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "客服工单菜单详情")
    @ApiImplicitParam(name = "id", value = "客服工单菜单ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<CcsMenuVO> get(@PathVariable("id") Long id) {
        CcsMenu ccsMenu = ccsMenuService.getById(id);
        return ApiResult.data(CcsMenuConvert.INSTANCE.toVO(ccsMenu));
    }

    @PostMapping
    @ApiOperation(value = "创建客服工单菜单")
    ApiResult<Void> save(@RequestBody @Validated(CcsMenuParam.Create.class) CcsMenuParam ccsMenuParam) {
        CcsMenu ccsMenu = CcsMenuConvert.INSTANCE.toEntity(ccsMenuParam);
        String clientId = AuthUtil.getClientId();
        if (StringUtils.isBlank(clientId)) {
            return ApiResult.fail(GlobalErrorCode.UNAUTHORIZED);
        }
        ccsMenu.setClientId(clientId);
        ccsMenu.setMenuId(IdWorker.getId());
        return ApiResult.status(ccsMenuService.save(ccsMenu));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改客服工单菜单")
    @ApiImplicitParam(name = "id", value = "客服工单菜单ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<Void> update(@PathVariable("id") Long id, @RequestBody @Validated(CcsMenuParam.Update.class) CcsMenuParam ccsMenuParam) {
        ccsMenuParam.setMenuId(id);
        CcsMenu ccsMenu = CcsMenuConvert.INSTANCE.toEntity(ccsMenuParam);
        ccsMenuService.updateById(ccsMenu);
        return ApiResult.success();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除客服工单菜单")
    @ApiImplicitParam(name = "id", value = "客服工单菜单ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<Void> delete(@PathVariable("id") Long id) {
        ccsMenuService.deleteMenu(id);
        return ApiResult.success();
    }

    @DeleteMapping
    @ApiOperation(value = "批量删除客服工单菜单")
    @ApiImplicitParam(name = "ids", value = "客服工单菜单ID集合", required = true, dataType = "List<Long>", paramType = "query")
    ApiResult<Void> delete(@RequestParam("ids") @Size(min = 1, message = "客服工单菜单ID不能为空") List<Long> ids) {
        ccsMenuService.removeByIds(ids);
        return ApiResult.success();
    }

}

