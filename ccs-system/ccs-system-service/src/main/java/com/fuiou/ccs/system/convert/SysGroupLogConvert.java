package com.fuiou.ccs.system.convert;

import com.fuiou.ccs.system.repository.entity.SysGroupLog;
import com.fuiou.ccs.system.dto.SysGroupLogDTO;
import com.fuiou.ccs.system.vo.SysGroupLogVO;
import com.fuiou.ccs.system.param.SysGroupLogParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工作组日志 对象转化器
 *
 * <AUTHOR> @since  1.0.0
 */
@Mapper
public interface SysGroupLogConvert {

    SysGroupLogConvert INSTANCE = Mappers.getMapper(SysGroupLogConvert.class);

    @Mappings({})
    SysGroupLogVO toVO(SysGroupLog sysGroupLog);

    @Mappings({})
    List<SysGroupLogVO> toVO(List<SysGroupLog> sysGroupLogList);

    @Mappings({})
    SysGroupLogDTO toDTO(SysGroupLog sysGroupLog);

    @Mappings({})
    List<SysGroupLogDTO> toDTO(List<SysGroupLog> sysGroupLogList);

    @Mappings({})
    SysGroupLog toEntity(SysGroupLogParam sysGroupLogParam);

    @Mappings({})
    SysGroupLog toEntity(SysGroupLogDTO sysGroupLogDTO);

    @Mappings({})
    SysGroupLog toEntity(SysGroupLogVO sysGroupLogVO);

}
