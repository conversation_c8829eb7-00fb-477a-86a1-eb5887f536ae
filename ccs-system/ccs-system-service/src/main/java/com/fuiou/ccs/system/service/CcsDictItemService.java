package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.dto.CcsDictItemDTO;
import com.fuiou.ccs.system.param.CcsDictItemParam;
import com.fuiou.ccs.system.query.CcsDictItemQuery;
import com.fuiou.ccs.system.repository.entity.CcsDictItem;
import com.fuiou.ccs.system.vo.CcsDictItemVO;

import java.util.List;
import java.util.Map;

/**
 * 字典数据 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CcsDictItemService extends IService<CcsDictItem> {


    /**
     * @param cssDictItemParam 新增参数
     * @return 新增字典数据项
     */
    boolean save(CcsDictItemParam cssDictItemParam);

    /**
     * @param cssDictItemParam 修改参数
     * @return 根据数据字典项id修改
     */
    boolean updateById(CcsDictItemParam cssDictItemParam);

    /**
     * 查询字典数据
     *
     * @param dictCode 字典编号
     * @return {@link CcsDictItemDTO}
     */
    List<CcsDictItemDTO> listByDictCode(String dictCode);

    Map<String, List<CcsDictItemDTO>> listByDictCode(List<String> dictCodes);

    /**
     * 根据字典编码查询 value
     *
     * @param dictCode 字典编号
     * @return 字典键值集合
     */
    List<String> valueListByDictCode(String dictCode);

    /**
     * @param dictCode 字典编号
     * @param list     字典键值集合
     * @return 包含的字典键值集合
     */
    List<String> checkValue(String dictCode, List<String> list);

    /**
     * 根据字典编号和字典值获取数据
     *
     * @param dictCode  字典编号
     * @param dictValue 字典值
     * @return {@link CcsDictItemDTO}
     */
    CcsDictItemDTO getByCodeValue(String dictCode, String dictValue, Boolean isContainDelete);

    /**
     * 根据字典编号和字典标签获取数据
     *
     * @param dictCode 字典编号
     * @param label    字典标签
     * @return {@link CcsDictItemDTO}
     */
    CcsDictItemDTO selectByCodeLabel(String dictCode, String label);

    /**
     * 根据字典编号删除字典数据
     *
     * @param dictCode 字典编号
     * @return 是否成功
     */
    boolean removeByDictCode(String dictCode);

    /**
     * 根据字典编号删除字典数据
     *
     * @param dictCodes 字典编号
     * @return 是否成功
     */
    boolean removeByDictCodes(List<String> dictCodes);

    /**
     * 更新字典编号
     *
     * @param oldDictCode 老的字典编号
     * @param newDictCode 新的字典编号
     * @return 是否成功
     */
    boolean updateDictCode(String oldDictCode, String newDictCode);

    /**
     * 不分页查询
     *
     * @param query 参数  字典编号/字典键值/字典标签
     * @return 根据字典编号/字典键值/字典标签 查询
     */
    List<CcsDictItemVO> listDictItems(CcsDictItemQuery query);

}
