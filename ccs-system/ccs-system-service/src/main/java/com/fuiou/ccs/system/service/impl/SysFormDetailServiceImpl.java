package com.fuiou.ccs.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.system.common.util.ExecuteSqlUtil;
import com.fuiou.ccs.system.constants.SysFormConstants;
import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.dto.SysFormMailDTO;
import com.fuiou.ccs.system.enums.DataTypeEnums;
import com.fuiou.ccs.system.enums.MailEnums;
import com.fuiou.ccs.system.enums.ReviewEnums;
import com.fuiou.ccs.system.enums.SysFormEnums;
import com.fuiou.ccs.system.exception.SystemErrorCode;
import com.fuiou.ccs.system.exception.SystemServiceException;
import com.fuiou.ccs.system.repository.entity.SysFormDetail;
import com.fuiou.ccs.system.repository.mapper.SysFormDetailMapper;
import com.fuiou.ccs.system.service.CcsDictItemService;
import com.fuiou.ccs.system.service.SysFormDetailService;
import com.fuiou.ccs.system.service.mail.MailSendService;
import com.fuiou.ccs.system.vo.SysFormDetailVO;
import com.fuiou.common.api.ApiAssert;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 表单配置详情 服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class SysFormDetailServiceImpl extends ServiceImpl<SysFormDetailMapper, SysFormDetail> implements SysFormDetailService {

    @Resource
    MailSendService mailSendService;

    @Resource
    private CcsDictItemService ccsDictItemService;

    @Resource
    ExecuteSqlUtil executeSqlUtil;

    /**
     * 列名不能重复校验
     * <p>
     * 新增时比较，列名不能重复
     * 修改时，未创建，未使用，列名不能重复，可删除列名
     * 使用时，列名不能重复，不可删除列名
     *
     * @param operationEnum  判断是新增还是修改
     * @param usedStatusFlag 表单是否使用 新增时都是false，修改时不一定
     * @param newList        新的详情参数
     * @param oldList        旧的详情参数
     */
    @Override
    public void checkColumnsName(ReviewEnums.OperationEnum operationEnum, boolean usedStatusFlag, List<SysFormDetail> newList, List<SysFormDetail> oldList) {
        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_NOT_NULL, CollectionUtils.isEmpty(newList));
        int newListSize = newList.size();
        //所有列名存起来，后续比较是否有重复的
        Set<String> allColumnName = new HashSet<>();
        if (ReviewEnums.OperationEnum.INS.equals(operationEnum)) {
            //新增直接获取列名称比较
            allColumnName = newList.stream().map(SysFormDetail::getColumnsName).collect(Collectors.toSet());
        } else {
            ApiAssert.isFalse(GlobalErrorCode.BAD_REQUEST.getCode(), "表单配置列请求参数错误", usedStatusFlag && CollectionUtils.isEmpty(oldList));
            //使用中的不能删除 表单列
            ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_NOT_DELETE, usedStatusFlag && newList.size() < oldList.size());
            //大于或等于的时候要比较了
            for (int i = oldList.size() - 1; i >= 0; i--) {
                for (int j = newList.size() - 1; j >= 0; j--) {
                    //将所有的列名
                    allColumnName.add(newList.get(j).getColumnsName());
                    //判断id是否一样
                    boolean formDetailIdFlag = Objects.equals(newList.get(j).getFormDetailId(), oldList.get(i).getFormDetailId());
                    boolean oldNotNullStatus = Objects.equals(oldList.get(i).getColumnsNotNull(), CommonConstants.NO);
                    boolean newNotNullStatus = Objects.equals(newList.get(j).getColumnsNotNull(), CommonConstants.YES);
                    if (formDetailIdFlag) {
                        //判断是否修改为了必填
                        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_NOT_UPDATE.getCode(), "已使用的表单禁止将非必填字段改为必填字段！【" + oldList.get(i).getColumnsName() + "-" + oldList.get(i).getCnColumnsName() + "】", usedStatusFlag && oldNotNullStatus && newNotNullStatus);
                        //这些一样的不用管
                        oldList.remove(i);
                        newList.remove(j);
                        break;
                    }
                    if (newList.get(j).getFormDetailId() == null) {
                        //不一样说明可能是新增的
                        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_NOT_UPDATE.getCode(), "已使用的表单禁止新增必填字段！【" + newList.get(j).getColumnsName() + "-" + newList.get(j).getCnColumnsName() + "】", usedStatusFlag && newNotNullStatus);
                    }
                }
            }
        }
        //不相等说明有重复列，抛出来错误
        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_EXIST.getCode(), "列名不能重复！", newListSize != allColumnName.size());
        //比较完还有数据说明进行了删除，抛出来错误
        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_NOT_DELETE, usedStatusFlag && oldList.size() > 0);
        //判断有无关键字
        List<String> keywordList = ccsDictItemService.checkValue(SysFormEnums.DB_KEYWORD, new ArrayList<>(allColumnName));
        ApiAssert.isFalse(SystemErrorCode.FORM_COLUMN_EXIST_KEYWORD.getCode(), keywordList.toString() + "为关键字，不能命名！", CollectionUtils.isNotEmpty(keywordList));
    }


    /**
     * @param operationEnum 区分是第一次新增还是修改的新增，第一次新增需要给字段生成顺序赋值，修改新增不需要
     * @param formId        表单配置ID
     * @param list          表单配置详情List
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveFormDetailList(ReviewEnums.OperationEnum operationEnum, Long formId, List<SysFormDetail> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<SysFormDetail> addList = Lists.newArrayListWithCapacity(list.size());
            boolean saveFlag = operationEnum.equals(ReviewEnums.OperationEnum.INS);
            int columnsOrder = 0;
            for (SysFormDetail addParam : list) {
                addParam.setFormDetailId(IdWorker.getId());
                //列名存为大写
                addParam.setColumnsName(addParam.getColumnsName());
                //将前台类型映射为数据库的类型
                addParam.setColumnsType(DataTypeEnums.ToDatabaseTypeEnum.getEnum(addParam.getColumnsType()).getValue());
                addParam.setFormId(formId);
                if (saveFlag) {
                    columnsOrder++;
                    addParam.setColumnsOrder(columnsOrder);
                }
                addList.add(addParam);
            }
            saveBatch(addList);
        }
    }

    /**
     * 修改完发邮件给数据库,把修改日志记录下来
     *
     * @param usedStatusFlag 表单是否已经使用
     * @param tableName      数据库表名
     * @param cnTableName    数据库表中文名
     * @param newList        需要修改成的新数据
     * @param oldList        老数据
     */
    @SuppressWarnings(value = {"checkstyle:methodlength", "checkstyle:booleanexpressioncomplexity"})
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String updateFormDetailList(boolean usedStatusFlag, String tableName, String oldCnTableName, String cnTableName, List<SysFormDetail> newList, List<SysFormDetail> oldList) {
        StringBuilder addLogStr = new StringBuilder("新增列：");
        StringBuilder updateLogStr = new StringBuilder("修改列：");
        Long formId = oldList.get(0).getFormId();
        int columnsOrder = oldList.size() + 1;
        StringBuilder addSqlStr = new StringBuilder();
        StringBuilder updateSqlStr = new StringBuilder();
        List<SysFormDetail> addList = Lists.newArrayListWithCapacity(newList.size());
        List<SysFormDetail> updateList = Lists.newArrayListWithCapacity(newList.size());
        for (int i = oldList.size() - 1; i >= 0; i--) {
            for (int j = newList.size() - 1; j >= 0; j--) {
                if (newList.get(j).getFormDetailId() == null) {
                    //说明是新增的列
                    newList.get(j).setColumnsOrder(columnsOrder++);
                    addList.add(newList.get(j));
                    addLogStr.append(newList.get(j).getColumnsName()).append("、");
                    addSqlStr.append("<br/>&nbsp; ALTER &nbsp; TABLE &nbsp; ").append(tableName).append("&nbsp; ADD  &nbsp; \"")
                            .append(newList.get(j).getColumnsName()).append("\" &nbsp; ");
                    if (DataTypeEnums.ToDatabaseTypeEnum.VARCHAR.getType().equals(DataTypeEnums.ToDatabaseTypeEnum.getEnum(newList.get(j).getColumnsType()).getValue())) {
                        addSqlStr.append(DataTypeEnums.ToDatabaseTypeEnum.VARCHAR.getType()).append("(").append(newList.get(j).getColumnsLength()).append(")");
                    } else {
                        addSqlStr.append(newList.get(j).getColumnsType());
                    }
                    //非空设置  目前没有设置默认值的地方，因此设置非空时设置一下默认值 0
                    if (CommonConstants.YES == newList.get(j).getColumnsNotNull()) {
                        addSqlStr.append("  &nbsp;  NOT &nbsp;  NULL ");
                    }
                    //设置默认值
                    if (StringUtils.isNotBlank(newList.get(j).getColumnsDefaultValue())) {
                        addSqlStr.append(" &nbsp;  DEFAULT &nbsp; ").append(newList.get(j).getColumnsDefaultValue());
                    }
                    addSqlStr.append("; <br/> &nbsp;  COMMENT &nbsp; ON &nbsp; COLUMN  &nbsp;")
                            .append(tableName).append(".\"").append(newList.get(j).getColumnsName()).append("\" &nbsp; IS '").append(newList.get(j).getCnColumnsName()).append("'; <br/> ");
                    newList.remove(j);
                    continue;
                }
                //比较类型是否相等
                boolean columnsTypeFlag = DataTypeEnums.ToDatabaseTypeEnum.getEnum(oldList.get(i).getColumnsType()).getValue().equals(DataTypeEnums.ToDatabaseTypeEnum.getEnum(newList.get(j).getColumnsType()).getValue());
                //比较列名是否相等(列名转大写)
                boolean columnsNameFlag = oldList.get(i).getColumnsName().equals(newList.get(j).getColumnsName());
                //比较列名称是否相等
                boolean cnColumnsFlag = oldList.get(i).getCnColumnsName().equals(newList.get(j).getCnColumnsName());
                //判断是否预置的
                boolean presetFlag = CommonConstants.YES == oldList.get(i).getPresetStatus();
                //判断非空字段是否修改了
                boolean notNullFlag = oldList.get(i).getColumnsNotNull().equals(newList.get(j).getColumnsNotNull());
                //判断字段是否修改为了查询条件
                boolean queryTypeFlag = oldList.get(i).getQueryType().equals(newList.get(j).getQueryType());
                //判断字段是否修改了表头设置：0-不展示（默认）；1-仅表头展示；2-仅导出展示；3-表头和导出都展示
                boolean headerFlag = oldList.get(i).getHeaderStatus().equals(newList.get(j).getHeaderStatus());
                boolean sourceUrlFlag = Objects.equals(oldList.get(i).getSourceUrlId(), newList.get(j).getSourceUrlId());
                //判断ID相等不
                boolean formDetailIdFlag = oldList.get(i).getFormDetailId().equals(newList.get(j).getFormDetailId());
                if (formDetailIdFlag && (presetFlag || (columnsNameFlag && columnsTypeFlag && cnColumnsFlag && notNullFlag && queryTypeFlag && headerFlag && sourceUrlFlag))) {
                    //这是不需要修改的列
                    oldList.remove(i);
                    newList.remove(j);
                    break;
                } else if (formDetailIdFlag && !presetFlag && (!columnsNameFlag || !columnsTypeFlag || !cnColumnsFlag || !notNullFlag || !queryTypeFlag || !headerFlag || !sourceUrlFlag)) {
                    //这些是需要修改的列
                    updateList.add(newList.get(j));
                    //接下来去判断具体需要改什么，拼接SQL
                    //修改了列名
                    if (!columnsNameFlag) {
                        updateSqlStr.append(" <br/> \r\n &nbsp;  ALTER &nbsp; TABLE &nbsp; ").append(tableName)
                                .append(" &nbsp;  RENAME  &nbsp; COLUMN  &nbsp; \"").append(oldList.get(i).getColumnsName())
                                .append("\" &nbsp; TO &nbsp; \"").append(newList.get(j).getColumnsName()).append("\";");
                        updateLogStr.append(oldList.get(i).getColumnsName()).append("修改为").append(newList.get(j).getColumnsName());
                    }
                    //修改默认值
//                    if(StringUtils.isNotBlank(oldList.get(i).getColumnsDefaultValue())){
//                        updateSqlStr.append(" <br/>  \r\n  &nbsp;  ALTER &nbsp; TABLE &nbsp; ").append(tableName)
//                                .append(" &nbsp; ALTER &nbsp; COLUMN &nbsp; \"").append(newList.get(j).getColumnsName())
//                                .append("\" &nbsp; SET &nbsp; DEFAULT  &nbsp; ").append(newList.get(j).getColumnsDefaultValue()).append(";");
//                    }else{
//                        updateSqlStr.append(" <br/> \r\n &nbsp;  ALTER &nbsp; TABLE &nbsp; ").append(tableName)
//                                .append(" &nbsp; ALTER &nbsp; COLUMN &nbsp; \"").append(newList.get(j).getColumnsName())
//                                .append("\" &nbsp; SET &nbsp; DEFAULT  &nbsp; NULL ;");
//                    }
                    // 如果修改了是否非空字段
                    if (!notNullFlag) {
                        if (usedStatusFlag && CommonConstants.YES == newList.get(j).getColumnsNotNull()) {
                            //如果已经使用了，就不要设置非空了
                            throw new SystemServiceException(SystemErrorCode.FORM_COLUMN_NOT_UPDATE);
                        }
                        updateSqlStr.append(" <br/> \r\n &nbsp;  ALTER &nbsp; TABLE &nbsp; ").append(tableName)
                                .append("&nbsp; ALTER &nbsp; COLUMN &nbsp;\"").append(newList.get(j).getColumnsName()).append("\"");
                        if (CommonConstants.YES == newList.get(j).getColumnsNotNull()) {
                            //非空设置  修改为非空
                            updateSqlStr.append("  &nbsp; SET &nbsp; NOT &nbsp; NULL &nbsp; ");
                            updateLogStr.append(newList.get(j).getColumnsName()).append("列设置非空约束。");
                        } else {
                            updateSqlStr.append("&nbsp; DROP &nbsp; NOT &nbsp; NULL &nbsp; ; ");
                            updateLogStr.append(newList.get(j).getColumnsName()).append("删除非空约束。");
                        }
                    }

                    //如果修改了字段类型
                    if (!columnsTypeFlag) {
                        updateSqlStr.append(" <br/> \r\n &nbsp;  ALTER &nbsp; TABLE &nbsp; ").append(tableName)
                                .append(" &nbsp;  ALTER &nbsp; COLUMN &nbsp; \"").append(newList.get(j).getColumnsName())
                                .append("\" &nbsp;  SET &nbsp; DATA &nbsp; TYPE &nbsp;");
                        if (DataTypeEnums.ToDatabaseTypeEnum.VARCHAR.getType().equals(DataTypeEnums.ToDatabaseTypeEnum.getEnum(newList.get(j).getColumnsType()).getValue())) {
                            updateSqlStr.append(DataTypeEnums.ToDatabaseTypeEnum.VARCHAR.getType()).append("(").append(newList.get(j).getColumnsLength()).append(");");
                        } else {
                            updateSqlStr.append(newList.get(j).getColumnsType()).append(";");
                        }
                        updateLogStr.append(newList.get(j).getColumnsName()).append("列修改了字段类型。");
                    }

                    if (!queryTypeFlag) {
                        updateLogStr.append(newList.get(j).getColumnsName()).append("列修改了字段查询条件类型。");
                    }
                    if (!headerFlag) {
                        updateLogStr.append(newList.get(j).getColumnsName()).append("列修改了字段的表头设置。");
                    }

                    if (!columnsNameFlag || !columnsTypeFlag || !cnColumnsFlag || !notNullFlag) {
                        updateSqlStr.append(" <br/> \r\n &nbsp;  COMMENT &nbsp; ON &nbsp; COLUMN  &nbsp;")
                                .append(tableName).append(".\"").append(newList.get(j).getColumnsName()).append("\"&nbsp; IS &nbsp; '").append(newList.get(j).getCnColumnsName()).append("'; <br/> ");
                    }
                    oldList.remove(i);
                    break;
                }
            }
        }
        //说明有删除的
        boolean deleteFlag = CollectionUtils.isNotEmpty(oldList);
        StringBuilder deleteLogStr = new StringBuilder();
        //使用中的不能删除,如果使用中还要删除则抛出错误
        if (usedStatusFlag && deleteFlag) {
            throw new SystemServiceException(SystemErrorCode.FORM_COLUMN_NOT_DELETE);
        }
        StringBuilder deleteSqlStr = new StringBuilder("&nbsp; ");
        if (deleteFlag) {
            deleteLogStr.append("删除列：");
            //拼接删除的SQL
            for (SysFormDetail sysFormDetail : oldList) {
                deleteSqlStr.append("&nbsp;<br/>&nbsp; \r\n ALTER &nbsp; TABLE &nbsp; ").append(tableName)
                        .append("&nbsp;  DROP &nbsp; COLUMN &nbsp; \"").append(sysFormDetail.getColumnsName()).append("\";");
                //每次删除字段后都执行一下reorg命令
                deleteSqlStr.append("&nbsp;&nbsp;<br/>&nbsp; \r\n CALL &nbsp; SYSPROC.ADMIN_CMD('REORG  &nbsp; TABLE  &nbsp; ")
                        .append(tableName).append("');");
                deleteLogStr.append(sysFormDetail.getColumnsName()).append("、");
                //修改为删除
                removeById(sysFormDetail);
            }
        }
        //先删除，再修改，最后是添加
        deleteSqlStr.append("<br/><br/>").append(updateSqlStr).append("<br/><br/>").append(addSqlStr);
        //修改表中文名
        boolean cnTableNameFlag = !oldCnTableName.equals(cnTableName);
        if (cnTableNameFlag) {
            deleteSqlStr.append("&nbsp;<br/> \r\n COMMENT &nbsp; ON &nbsp; TABLE &nbsp;").append(tableName)
                    .append("&nbsp; IS &nbsp;'").append(cnTableName).append("';");
        }
        boolean updateFlag = CollectionUtils.isNotEmpty(updateList);
        //修改列信息
        if (updateFlag) {
            updateBatchById(updateList);
            deleteLogStr.append(updateLogStr);
        }
        boolean addFlag = CollectionUtils.isNotEmpty(addList);
        if (CollectionUtils.isNotEmpty(addList)) {
            //新增列
            saveFormDetailList(ReviewEnums.OperationEnum.UPD, formId, addList);
            deleteLogStr.append(addLogStr.deleteCharAt(addLogStr.length() - 1).append("。"));
        }
        //修改字段了/表描述 就发邮件，没修就不用
        if (deleteFlag || cnTableNameFlag || updateFlag || addFlag) {
            deleteSqlStr.append("&nbsp;<br/><br/>&nbsp; \r\n  CALL &nbsp; SYSPROC.ADMIN_CMD('REORG  &nbsp; TABLE  &nbsp; ")
                    .append(tableName).append("');");
            //发送修改的邮件
            sentUpdateFormMail(tableName, cnTableName, deleteSqlStr.toString());
        } else {
            deleteLogStr.append("未修改模板字段。");
        }
        return deleteLogStr.toString();
    }

    /**
     * 发送修改表单的SQL
     *
     * @param tableName   表英文名
     * @param cnTableName 表描述
     * @param sql         修改SQL
     */
    private void sentUpdateFormMail(String tableName, String cnTableName, String sql) {
        List<String> ccEmailList = ccsDictItemService.valueListByDictCode(SysFormConstants.FORM_CC_EMAIL);
        SysFormMailDTO dto = SysFormMailDTO.builder()
                .ccEmail(ccEmailList)
                .tableName(tableName)
                .cnTableName(cnTableName)
                .sql(sql).build();
        mailSendService.sendFormMail(dto, MailEnums.FormMailEnum.FORM_UPDATE);
        //自动执行SQL
        executeSqlUtil.executeSql(sql);
    }

    /**
     * 查询表单配置详情
     *
     * @param formId   formId 表单配置ID
     * @param partFlag 为true 只查询部分数据，不查询预置的
     * @return list   根据表单配置ID查询表单配置详情List
     */
    @Override
    public List<SysFormDetail> queryFormDetailByFormId(Long formId, boolean partFlag) {
        QueryWrapper<SysFormDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("FORM_ID", formId)
                .eq(partFlag, "IS_PRESET", CommonConstants.NO);
        return list(queryWrapper);
    }

    /**
     * 根据表单ID查询模板预置字段
     *
     * @param formId 表单ID
     * @return 返回模板预置字段
     */
    @Override
    public List<SysFormDetail> queryPresetFormDetailByFormId(Long formId) {
        QueryWrapper<SysFormDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("FORM_ID", formId)
                .eq("IS_PRESET", CommonConstants.YES);
        return list(queryWrapper);
    }

    @Override
    public Map<Long, List<SysFormDetailVO>> queryFormDetailIsQueryColumnList(List<Long> formIds, String detailType) {
        Map<Long, List<SysFormDetailVO>> resultMap = new ConcurrentHashMap<>(8);
        if (CollectionUtils.isNotEmpty(formIds)) {
            List<SysFormDetailVO> busList;
            for (Long next : formIds) {
                busList = baseMapper.queryFormDetailIsQueryColumnList(next, detailType);
                if (CollectionUtils.isNotEmpty(busList)) {
                    resultMap.put(next, busList);
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<SysFormDetailDTO> querySysFormDetailDTO(String formName) {
        return baseMapper.querySysFormDetailDTO(formName);
    }

    @Override
    public int countSysFormDetailByFormName(String formName) {
        return baseMapper.countSysFormDetailByFormName(formName);
    }

    @Override
    public void removeByFormId(Long formId) {
        QueryWrapper<SysFormDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("FORM_ID", formId);
        remove(queryWrapper);
    }

    /**
     * 根据表单ID,数据库真实删除表单详情信息数据
     *
     * @param formId 表单ID
     * @return 成功/失败
     */
    @Override
    public boolean realDelDetailByFormId(Long formId) {
        baseMapper.realDelDetailByFormId(formId);
        return true;
    }

    /**
     * 根据表名，查询该表所有字段详情
     */
    @Override
    public List<SysFormDetail> querySysFormDetailByFormName(String formName) {
        return baseMapper.querySysFormDetailByFormName(formName);
    }
}
