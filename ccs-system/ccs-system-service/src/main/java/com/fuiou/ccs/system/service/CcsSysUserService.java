package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.dto.CcsAuthUserDTO;
import com.fuiou.ccs.system.dto.CcsSysUserDTO;
import com.fuiou.ccs.system.dto.CcsSysUserInfo;
import com.fuiou.ccs.system.excel.CcsSysUserExcel;
import com.fuiou.ccs.system.param.CcsSysUserParam;
import com.fuiou.ccs.system.query.CcsSysUserQuery;
import com.fuiou.ccs.system.repository.entity.CcsRole;
import com.fuiou.ccs.system.repository.entity.CcsSysUser;
import com.fuiou.ccs.system.vo.CcsSysUserVO;
import com.fuiou.ccs.system.vo.CcsUserRoleVO;
import com.fuiou.ccs.system.vo.SysGroupAndUserVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户账户 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CcsSysUserService extends IService<CcsSysUser> {


    /**
     * 分页查询用户
     *
     * @param page  分页信息
     * @param query 参数
     * @return 用户信息
     */
    IPage<CcsSysUserVO> usersListPage(IPage page, CcsSysUserQuery query);

    /**
     * 导出查询的用户信息
     *
     * @param query 查询参数
     * @return 根据参数查询用户的信息
     */
    List<CcsSysUserExcel> exportUsers(CcsSysUserQuery query);

    /**
     * 工单指派人时使用
     *
     * @param page              分页信息
     * @param usernameOrEmpName 姓名
     * @return 分页查询用户信息
     */
    IPage<CcsSysUserVO> assignUser(IPage page, String userId, String usernameOrEmpName, String orgId, String orgName);

    /**
     * 根据工作组/用户的 id/名称 查询它们的状态
     *
     * @param id 工作组/用户  id
     * @return 状态/名称
     */
    SysGroupAndUserVO queryGroupOrUserStatus(String id);

    /**
     * 获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    CcsSysUserDTO getUserDetail(String userId);


    /**
     * 获取用户角色ID
     *
     * @param userId 用户ID
     * @return 角色ID
     */
    List<CcsRole> getRolesByUserId(String userId);

    /**
     * 根据用户ID返回所在组ID
     *
     * @param userId 用户ID
     * @return 用户所在工作组ID集合
     */
    List<Long> getGroupIdsByUserId(String userId);


    /**
     * 查询用户角色
     *
     * @param username 用户名
     * @return 用户角色
     */
    List<CcsUserRoleVO> listRolesByUserName(String username);


    /**
     * 保存用户
     *
     * @param param       用户
     * @param roleIds     角色ID
     * @param getGroupIds 工作组ID
     * @return 是否成功
     */
    boolean saveUser(CcsSysUserParam param, List<Long> roleIds, List<Long> getGroupIds);


    /**
     * 根据id作废用户
     * @param userId 用户id
     * @return 是否成功
     */
//    boolean invalidById(String userId);


    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @return 状态
     */
    boolean resetPassword(String userId);

    /**
     * 改密码
     *
     * @param oldPassword 原密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(String oldPassword, String newPassword);


    /**
     * 根据用户登录名解锁
     *
     * @param username 用户登录名
     * @return 是否成功
     */
    Boolean unlocked(String username);

    /**
     * 获取当前登录人信息
     *
     * @return 登录人信息
     */
    CcsSysUserVO getCurrentUserInfo();

    /**
     * 获取认证用户
     *
     * @param clientId 客户端ID
     * @param username 用户名
     * @return {@link CcsAuthUserDTO}
     */
    CcsAuthUserDTO getAuthUserByUsername(String clientId, String username);

    /**
     * 获取认证用户
     *
     * @param clientId 客户端ID
     * @param mobile   手机号
     * @return {@link CcsAuthUserDTO}
     */
    CcsAuthUserDTO getAuthUserByMobile(String clientId, String mobile);


    /**
     * 获取用户授权角色编号
     *
     * @param clientId 客户端ID
     * @param userId   用户id
     * @return 授权角色编号
     */
    List<String> listRoleCodes(String clientId, String userId);

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    CcsSysUserInfo getUserByUserId(String userId);

    /**
     * 批量获取用户信息
     *
     * @param userIds  用户ID
     * @param isDelete 是否删除
     * @return 用户信息
     */
    List<CcsSysUserInfo> getUserListByUserIds(List<String> userIds, Integer isDelete);

    /**
     * 更新用户
     *
     * @param param 参数
     * @return 是否成功
     */
    boolean updateUser(CcsSysUserParam param);


    /**
     * 通过角色编号获取拥有该角色的所有用户
     *
     * @param roleCode 角色编码
     * @return 该角色编码下的用户
     */
    List<CcsSysUserVO> getUserListByRole(String roleCode);

    /**
     * 忘记密码
     *
     * @param clientId   客户端id
     * @param username   用户登录名
     * @param mobile     用户手机号
     * @param type       类型（邮箱/手机号）（email/mobileOut/mobileIn）
     * @param captcha    图片验证码
     * @param captchaKey 图片验证码key
     * @return 成功/失败
     */
    Boolean forgetPassword(String clientId, String username, String mobile, String type, String captcha, String captchaKey);

    /**
     * 修改密码
     *
     * @param type        类型（邮箱/手机号）
     * @param userParam   用户ID/用户登录名
     * @param captcha     短信验证码-短信修改密码时使用
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean updatePassword(String type, String userParam, String captcha, String newPassword);


    /**
     * 更新登录IP
     *
     * @param userId        用户ID
     * @param lastLoginIp   最近登录IP
     * @param lastLoginDate 最近登录时间
     * @return 是否更新成功
     */
    Boolean updateLastLoginInfo(String userId, String lastLoginIp, LocalDateTime lastLoginDate);


    /**
     * 为某用户添加角色
     *
     * @param userId    用户Id
     * @param roleCode  角色编码
     * @param creatorId 添加人ID
     * @return 是否成功
     */
    Boolean addUserRole(String userId, String roleCode, String creatorId);

    /**
     * 为某用户删除角色
     *
     * @param userId   角色Id
     * @param roleCode 角色编码
     * @return 是否成功
     */
    Boolean deleteUserRole(String userId, String roleCode);

    /**
     * 在数据库删除用户相关信息
     *
     * @param userId 用户ID
     */
    void realDelUser(String userId);

    /**
     * 逻辑删除用户
     *
     * @param userId 用户ID
     */
    void abolishUser(String userId);

    /**
     * 根据username获取用户对象
     *
     * @param userName
     * @return
     */
    CcsSysUserVO getCcsSysUserVOByUserName(String userName);

    /***
     * 根据用户姓名查编号
     * @param name 用户姓名
     */
    List<String> getCodesByName(String name);
}
