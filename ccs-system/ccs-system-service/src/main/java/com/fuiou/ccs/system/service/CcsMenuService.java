package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.query.CcsMenuQuery;
import com.fuiou.ccs.system.repository.entity.CcsMenu;
import com.fuiou.ccs.system.vo.CcsMenuVO;
import com.fuiou.common.domain.AuthUser;

import java.util.List;

/**
 * 客服工单菜单 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CcsMenuService extends IService<CcsMenu> {

    /**
     * 列表查询所有菜单
     *
     * @param query 查询条件
     * @return 菜单集合
     */
    List<CcsMenuVO> listMenus(CcsMenuQuery query);

    /**
     * 用户登录时查询菜单
     *
     * @param authUser 当前用户
     * @return 菜单集合
     */
    List<CcsMenuVO> listRoutes(AuthUser authUser);

    /**
     * 删除菜单
     * @param menuId 菜单ID
     */
    void deleteMenu(Long menuId);
}
