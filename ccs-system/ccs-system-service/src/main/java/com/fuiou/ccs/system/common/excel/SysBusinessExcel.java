package com.fuiou.ccs.system.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务类型，导出excel
 *
 * <AUTHOR>
 * @date 2022-10-09
 */
@Data
public class SysBusinessExcel implements Serializable {

    /**
     * 业务类型名称
     */
    @ExcelProperty(value = "业务类型名称")
    private String busTypeName;

    /**
     * 工单类型名称
     */
    @ExcelProperty(value = "工单类型名称")
    private String workTypeName;

    /**
     * 工单子类名称
     */
    @ExcelProperty(value = "工单子类名称")
    private String workSubTypeName;

    /**
     * 子类要素名称
     */
    @ExcelProperty(value = "子类要素名称")
    private String subElementName;

    /**
     * 业务类型的所属公司(仅需第一层级填写)
     */
    @ExcelProperty(value = "业务类型的所属公司(仅需第一层级填写)")
    private String orgName;



}
