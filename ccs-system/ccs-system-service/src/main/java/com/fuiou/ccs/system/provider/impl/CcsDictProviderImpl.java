package com.fuiou.ccs.system.provider.impl;

import com.fuiou.ccs.system.dto.CcsDictItemDTO;
import com.fuiou.ccs.system.provider.CcsDictProvider;
import com.fuiou.ccs.system.service.CcsDictItemService;
import com.fuiou.common.api.ApiResult;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 字典对外接口实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@DubboService
public class CcsDictProviderImpl implements CcsDictProvider {

    final CcsDictItemService sysDictItemService;

    public CcsDictProviderImpl(CcsDictItemService sysDictItemService) {
        this.sysDictItemService = sysDictItemService;
    }

    @Override
    public ApiResult<List<CcsDictItemDTO>> listDictItems(String dictCode) {
        return ApiResult.data(sysDictItemService.listByDictCode(dictCode));
    }

    @Override
    public ApiResult<Map<String, List<CcsDictItemDTO>>> listDictItems(List<String> dictCodes) {
        return ApiResult.data(sysDictItemService.listByDictCode(dictCodes));
    }

    @Override
    public ApiResult<Map<String, CcsDictItemDTO>> getDictItems(String dictCode) {
        return ApiResult.data(sysDictItemService.listByDictCode(dictCode).stream()
                .collect(Collectors.toMap(CcsDictItemDTO::getValue, Function.identity())));
    }

    @Override
    public ApiResult<CcsDictItemDTO> getDictItem(String dictCode, String dictValue, Boolean isContainDelete) {
        return ApiResult.data(sysDictItemService.getByCodeValue(dictCode, dictValue, isContainDelete));
    }

    @Override
    public ApiResult<CcsDictItemDTO> getDictItemByLabel(String dictCode, String label) {
        return ApiResult.data(sysDictItemService.selectByCodeLabel(dictCode, label));
    }

}
