package com.fuiou.ccs.system.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Program: fuiou-cloud
 * @Description: MD5加密
 * @Author: Xuejing.Yang
 * @Create: 2022-04-08
 */
public class MD5Utils {

    private static final MessageDigest MESSAGE_DIGEST;
    /**
     * 默认的密码字符串组合，用来将字节转换成 16 进制表示的字符,apache校验下载的文件的正确性用的就是默认的这个组合
     */
    private static final char[] HEX_DIGITS;

    static {
        HEX_DIGITS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MESSAGE_DIGEST = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(MD5Utils.class.getName() + "初始化失败，MessageDigest不支持MD5Util。", e);
        }
    }

    /**
     * 生成字符串的md5校验值
     *
     * @return
     */

    public static String getMD5String(String s) {
        return getMD5String(s.getBytes(StandardCharsets.UTF_8));

    }

    /**
     * 判断字符串的md5校验码是否与一个已知的md5码相匹配
     * 要校验的字符串
     * 已知的md5校验码
     */

    public static boolean isEqualsToMd5(String password, String md5PwdStr) {
        String s = getMD5String(password);
        return s.equals(md5PwdStr);

    }

    /**
     * 生成文件的md5校验值
     *
     * @param file 文件
     * @return
     * @throws IOException
     */
    public static String getFileMD5String(File file) throws IOException {
        try (InputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int numRead;
            while ((numRead = fis.read(buffer)) > 0) {
                MESSAGE_DIGEST.update(buffer, 0, numRead);
            }
            return bufferToHex(MESSAGE_DIGEST.digest());
        }
    }

    /**
     * 生成字节数组的md5校验值
     */
    public static String getMD5String(byte[] bytes) {
        MESSAGE_DIGEST.update(bytes);
        return bufferToHex(MESSAGE_DIGEST.digest());

    }

    private static String bufferToHex(byte[] bytes) {
        return bufferToHex(bytes, 0, bytes.length);
    }

    private static String bufferToHex(byte[] bytes, int m, int n) {
        StringBuffer stringbuffer = new StringBuffer(2 * n);
        int k = m + n;
        for (int l = m; l < k; l++) {
            appendHexPair(bytes[l], stringbuffer);
        }
        return stringbuffer.toString();
    }

    private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
        char c0 = HEX_DIGITS[(bt & 0xf0) >> 4];
        // 取字节中高 4 位的数字转换, >>>
        // 为逻辑右移，将符号位一起右移,此处未发现两种符号有何不同
        char c1 = HEX_DIGITS[bt & 0xf];
        // 取字节中低 4 位的数字转换
        stringbuffer.append(c0);
        stringbuffer.append(c1);
    }

    /**
     * 将源字符串使用MD5加密为字节数组
     *
     * @param source
     * @return
     */
    public static byte[] encode2bytes(String source) {
        byte[] result = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            md.update(source.getBytes(StandardCharsets.UTF_8));
            result = md.digest();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return result;

    }

    /**
     * 将源字符串使用MD5加密为32位16进制数
     *
     * @param source
     * @return
     */
    public static String encode2hex(String source) {
        byte[] data = encode2bytes(source);
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < data.length; i++) {
            String hex = Integer.toHexString(0xff & data[i]);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();

    }

    /**
     * 验证字符串是否匹配
     *
     * @param unknown 待验证的字符串
     * @param okHex   使用MD5加密过的16进制字符串
     * @return 匹配返回true，不匹配返回false
     */

    public static boolean validate(String unknown, String okHex) {
        return okHex.equals(encode2hex(unknown));
    }

}

