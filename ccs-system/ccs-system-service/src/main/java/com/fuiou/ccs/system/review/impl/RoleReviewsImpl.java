package com.fuiou.ccs.system.review.impl;

import com.fuiou.ccs.system.repository.entity.CcsRole;
import com.fuiou.ccs.system.repository.entity.reviewbean.RoleReview;
import com.fuiou.ccs.system.review.Reviews;
import com.fuiou.ccs.system.service.CcsRoleService;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.fuiou.ccs.system.constants.CcsConfigConstants.ROLE_LOCK;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/7 10:24
 */
@Slf4j
@Service
public class RoleReviewsImpl implements Reviews {
    @Autowired
    CcsRoleService ccsRoleService;

    @Override
    public void ins(String businessId, String oldObj, String newObj) {
        //复核通过，将新增角色的状态修改为1
        //1.获取角色对象
        RoleReview roleReview = JacksonUtil.parse(newObj, RoleReview.class);
        CcsRole ccsRole = roleReview.getCcsRole();
        //2.更新
        ccsRole.setUpdaterId(ccsRole.getCreatorId());
        ccsRole.setStatus(CommonConstants.ENABLED);
        ccsRoleService.updateById(ccsRole);
    }

    @Override
    public void upd(String businessId, String oldObj, String newObj) {
        RoleReview newRoleReview = JacksonUtil.parse(newObj, RoleReview.class);
        ccsRoleService.updateRole(newRoleReview, ROLE_LOCK);
    }

    @Override
    public void del(String businessId) {
        ccsRoleService.deleteRole(Long.valueOf(businessId));
    }

    @Override
    public void abolish(String businessId) {
        //角色的停用和恢复正常可以调用角色的修改接口，该接口实际用不到
    }

    @Override
    public void approvalRejected(String businessId) {
        //复核失败，将新增角色删除
        ccsRoleService.deleteRole(Long.valueOf(businessId));
    }

    @Override
    public List<String> getNosByName(String name) {
        return ccsRoleService.getCodesByName(name);
    }

}
