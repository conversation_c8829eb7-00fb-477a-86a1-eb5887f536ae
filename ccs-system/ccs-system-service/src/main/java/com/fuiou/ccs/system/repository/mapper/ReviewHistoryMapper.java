package com.fuiou.ccs.system.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuiou.ccs.system.query.ReviewQuery;
import com.fuiou.ccs.system.vo.ReviewListVO;
import com.fuiou.ccs.system.vo.ReviewVO;
import com.fuiou.ccs.system.repository.entity.ReviewHistory;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 复核历史 Mapper 接口
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface ReviewHistoryMapper extends BaseMapper<ReviewHistory> {

    List<ReviewListVO> queryReviewHistory(IPage<ReviewListVO> page,
                                          @Param("query") ReviewQuery query);

    ReviewVO queryReviewHistoryInfo(@Param("reviewId") Long reviewId);

}
