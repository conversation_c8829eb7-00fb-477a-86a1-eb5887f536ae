package com.fuiou.ccs.system.controller;

import com.fuiou.ccs.system.repository.entity.SysFormDetail;
import com.fuiou.ccs.system.service.SysFormDetailService;
import com.fuiou.common.api.ApiResult;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 表单配置详情 前端控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Validated
@RestController
@AllArgsConstructor
@Api(tags = {"表单配置详情相关接口"})
@RequestMapping("/formDetails")
public class SysFormDetailController extends BaseController {

    final SysFormDetailService sysFormDetailService;

    @GetMapping("/{formId}")
    @ApiOperation(value = "根据表单配置模板ID查询它所有表单业务类型(不分页-不查询预置的字段)")
    @ApiImplicitParam(name = "formId", value = "表单配置模板ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<List<SysFormDetail>> getByFormId(@PathVariable("formId") Long formId) {
        return ApiResult.data(sysFormDetailService.queryFormDetailByFormId(formId, true));
    }

    @GetMapping("getAllByFormId/{formId}")
    @ApiOperation(value = "根据表单配置模板ID查询它所有表单业务类型(不分页-包含预置的字段)")
    @ApiImplicitParam(name = "formId", value = "表单配置模板ID", required = true, dataType = "Long", paramType = "path")
    ApiResult<List<SysFormDetail>> getAllByFormId(@PathVariable("formId") Long formId) {
        return ApiResult.data(sysFormDetailService.queryFormDetailByFormId(formId, false));
    }

}

