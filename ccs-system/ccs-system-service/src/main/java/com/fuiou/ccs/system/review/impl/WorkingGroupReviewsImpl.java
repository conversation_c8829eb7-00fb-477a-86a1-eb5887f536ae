package com.fuiou.ccs.system.review.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fuiou.ccs.system.convert.SysGroupConvert;
import com.fuiou.ccs.system.convert.SysGroupMemberConvert;
import com.fuiou.ccs.system.enums.GroupLogEnums;
import com.fuiou.ccs.system.repository.entity.SysGroup;
import com.fuiou.ccs.system.repository.entity.SysGroupMember;
import com.fuiou.ccs.system.repository.entity.reviewbean.GroupReview;
import com.fuiou.ccs.system.review.Reviews;
import com.fuiou.ccs.system.service.SysGroupLogService;
import com.fuiou.ccs.system.service.SysGroupMemberService;
import com.fuiou.ccs.system.service.SysGroupService;
import com.fuiou.ccs.system.vo.SysGroupMemberVO;
import com.fuiou.ccs.system.vo.SysGroupVO;
import com.fuiou.common.api.ApiAssert;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/8 18:12
 */
@Slf4j
@Service
public class WorkingGroupReviewsImpl implements Reviews {
    @Lazy
    @Autowired
    SysGroupService sysGroupService;
    @Autowired
    SysGroupMemberService sysGroupMemberService;
    @Autowired
    SysGroupLogService sysGroupLogService;

    @Override
    public void ins(String businessId, String oldObj, String newObj) {
        //复核通过，将新增工作组的状态为1
        //1.获取工作组对象
        GroupReview groupReview = JacksonUtil.parse(newObj, GroupReview.class);
        SysGroupVO sysGroupVO = groupReview.getSysGroup();
        if (sysGroupVO != null) {
            SysGroup sysGroup = SysGroupConvert.INSTANCE.toEntity(sysGroupVO);
            //2.更新
            sysGroup.setUpdaterId(AuthUtil.getUserId());
            sysGroup.setStatus(CommonConstants.ENABLED);
            sysGroupService.updateById(sysGroup);
            sysGroupLogService.saveGroupLog(sysGroup.getGroupId(), GroupLogEnums.TypeEnums.TYPE_SAVE, "新增工作组复核通过");

        }
        if (groupReview.getSysGroupMember() != null && groupReview.getSysGroupMember().size() > 0) {
            List<SysGroupMemberVO> sysGroupMemberVOList = groupReview.getSysGroupMember();
            List<SysGroupMember> sysGroupMemberList = SysGroupMemberConvert.INSTANCE.voToEntity(sysGroupMemberVOList);
            sysGroupMemberList.forEach(item -> {
                item.setStatus(CommonConstants.ENABLED);
                item.setUpdaterId(AuthUtil.getUserId());
            });
            sysGroupMemberService.updateBatchById(sysGroupMemberList);
            ApiAssert.notNull(9999, "工作组为空！", sysGroupVO);
            sysGroupLogService.batchSaveLog(sysGroupVO.getGroupId(), sysGroupMemberVOList, GroupLogEnums.TypeEnums.TYPE_SAVE, "工作组新增人员复核通过");

        }

    }

    /**
     * //一、获取newRoleReview，根据枚举类型判断更新操作
     * //修改工作组信息 newSysGroup
     * //修改工作组成员信息 oldGroupMemberIdList
     * //1.处理需要删除的工作组成员 deleteGroupMemberVOs
     * //1.1获取需要删除的工作组成员 deleteGroupMemberVOs
     * //新工作成员不含该userId,说明需要删除 if (!newGroupMemberIdList.contains(userId)) {
     * //1.2删除工作组成员 deleteGroupMemberIdList
     * //1.3保存日志 sysGroupLogService.batchSaveLog
     * //2.处理需要新增的工作组人员 addGroupMemberVOs
     * //2.1获取需要新增的工作组成员 addGroupMemberVOs
     * //旧工作组成员不含该userId，说明需要新增--注意填充主键ID和updaterId  if (!oldGroupMemberIdList.contains(userId)) {
     * //2.2批量新增工作组成员   sysGroupMemberService.saveBatch
     * //2.3保存日志  sysGroupLogService.batchSaveLog
     */
    @Override
    public void upd(String businessId, String oldObj, String newObj) {
        GroupReview oldGroupReview = JacksonUtil.parse(oldObj, GroupReview.class);
        GroupReview newGroupReview = JacksonUtil.parse(newObj, GroupReview.class);
        SysGroupVO oldGroupVO = oldGroupReview.getSysGroup();
        SysGroupVO newGroupVO = newGroupReview.getSysGroup();
        List<SysGroupMemberVO> oldGroupMemberVOList = oldGroupReview.getSysGroupMember();
        List<SysGroupMemberVO> newGroupMemberVOList = newGroupReview.getSysGroupMember();
        SysGroup newSysGroup = SysGroupConvert.INSTANCE.toEntity(newGroupVO);
        newSysGroup.setUpdaterId(newGroupReview.getUpdaterId());
        newSysGroup.setUpdateTime(LocalDateTime.now());
        sysGroupService.updateById(newSysGroup);
        if (!oldGroupVO.getLeaderId().equals(newSysGroup.getLeaderId())) {
            sysGroupLogService.saveGroupLog(newSysGroup.getGroupId(), GroupLogEnums.TypeEnums.TYPE_UPDATE, "修改工作组组长，组长由【" + oldGroupVO.getLeaderName() + "】，修改为【" + newGroupVO.getLeaderName() + "】复核通过");
        }
        if (!oldGroupVO.getManagerId().equals(newSysGroup.getManagerId())) {
            sysGroupLogService.saveGroupLog(newSysGroup.getGroupId(), GroupLogEnums.TypeEnums.TYPE_UPDATE, "修改工作组组长，组长由【" + oldGroupVO.getManagerName() + "】，修改为【" + newGroupVO.getManagerName() + "】复核通过");
        }
        if (!oldGroupVO.getName().equals(newSysGroup.getName())) {
            sysGroupLogService.saveGroupLog(newSysGroup.getGroupId(), GroupLogEnums.TypeEnums.TYPE_UPDATE, "修改工作组名称，名称由【" + oldGroupVO.getName() + "】，修改为【" + newGroupVO.getName() + "】复核通过");
        }
        List<String> newGroupMemberIdList = newGroupMemberVOList.stream().map(SysGroupMemberVO::getUserId).collect(Collectors.toList());
        List<SysGroupMemberVO> deleteGroupMemberVOs = new ArrayList<>();
        for (SysGroupMemberVO oldGroupMemberVO : oldGroupMemberVOList) {
            if (newGroupMemberIdList.contains(oldGroupMemberVO.getUserId())) {
                continue;
            }
            deleteGroupMemberVOs.add(oldGroupMemberVO);
        }
        List<Long> deleteGroupMemberIdList = deleteGroupMemberVOs.stream().map(SysGroupMemberVO::getGroupMemberId).collect(Collectors.toList());
        sysGroupMemberService.removeByIds(deleteGroupMemberIdList);
        sysGroupLogService.batchSaveLog(oldGroupVO.getGroupId(), deleteGroupMemberVOs, GroupLogEnums.TypeEnums.TYPE_DELETE, "工作组修改人员复核通过，删除");
        // List<SysGroupMemberVO> addGroupMemberVOs = new ArrayList<>();
        List<SysGroupMember> addGroupMembers = new ArrayList<>();
        List<String> oldGroupMemberIdList = oldGroupMemberVOList.stream().map(SysGroupMemberVO::getUserId).collect(Collectors.toList());
        for (SysGroupMemberVO newGroupMemberVO : newGroupMemberVOList) {
            if (oldGroupMemberIdList.contains(newGroupMemberVO.getUserId())) {
                continue;
            }
            newGroupMemberVO.setGroupMemberId(IdWorker.getId());
            // addGroupMemberVOs.add(newGroupMemberVO);
            SysGroupMember addGroupMember = SysGroupMemberConvert.INSTANCE.toEntity(newGroupMemberVO);
            addGroupMember.setStatus(CommonConstants.ENABLED);
            addGroupMember.setUpdaterId(newGroupReview.getUpdaterId());
            addGroupMembers.add(addGroupMember);
        }
        sysGroupMemberService.saveBatch(addGroupMembers);
        sysGroupLogService.batchSaveLog(oldGroupVO.getGroupId(), deleteGroupMemberVOs, GroupLogEnums.TypeEnums.TYPE_SAVE, "工作组修改人员复核通过，新增");
    }

    @Override
    public void del(String businessId) {
        Long groupId = Long.valueOf(businessId);
        LambdaQueryWrapper<SysGroupMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysGroupMember::getGroupId, groupId);
        List<SysGroupMember> groupMembers = sysGroupMemberService.list(wrapper);
        if (groupMembers != null && groupMembers.size() > 0) {
            groupMembers.forEach(item -> sysGroupMemberService.removeById(item));
            sysGroupLogService.batchSaveLog(groupId, SysGroupMemberConvert.INSTANCE.entityToVO(groupMembers), GroupLogEnums.TypeEnums.TYPE_DELETE, "删除工作组复核通过,删除人员");
        }
        sysGroupService.removeById(groupId);
        sysGroupLogService.saveGroupLog(groupId, GroupLogEnums.TypeEnums.TYPE_DELETE, "删除工作组复核通过");
    }

    @Override
    public void abolish(String businessId) {
    }

    @Override
    public void approvalRejected(String businessId) {
        //复核失败，将新增工作组删除
        Long groupId = Long.valueOf(businessId);
        //1.删除工作组主体
        sysGroupService.removeById(groupId);
        //2.删除工作组成员
        LambdaQueryWrapper<SysGroupMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysGroupMember::getGroupId, groupId);
        List<SysGroupMember> groupMembers = sysGroupMemberService.list(wrapper);
        if (groupMembers != null && groupMembers.size() > 0) {
            groupMembers.forEach(item -> sysGroupMemberService.removeById(item));
        }
    }

    @Override
    public List<String> getNosByName(String name) {
        return sysGroupService.getCodesByName(name);
    }
}
