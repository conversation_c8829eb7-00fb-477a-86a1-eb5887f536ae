package com.fuiou.ccs.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "CcsSysUserExcel对象", description = "用户账户导出的对象")
public class CcsSysUserExcel {
    /**
     * 登录名
     */
    @ExcelProperty("登录名")
    private String username;
    /**
     * 员工姓名
     */
    @ExcelProperty("员工姓名")
    private String empName;


    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ExcelProperty("电子邮箱")
    private String email;


    /**
     * 所属机构
     */
    @ExcelProperty("所属机构")
    private String orgName;


    /**
     * 状态（1：正常，0：停用）
     */
    @ExcelProperty("状态")
    private String status;


    @ExcelProperty("所属角色")
    private String rolesName;

    @ExcelProperty("所属工作组")
    private String groupName;



}
