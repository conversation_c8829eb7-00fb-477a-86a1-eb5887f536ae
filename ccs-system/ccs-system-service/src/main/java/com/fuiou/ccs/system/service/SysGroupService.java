package com.fuiou.ccs.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.system.dto.SysGroupDTO;
import com.fuiou.ccs.system.dto.SysGroupMemberDTO;
import com.fuiou.ccs.system.param.SysGroupParam;
import com.fuiou.ccs.system.query.SysGroupQuery;
import com.fuiou.ccs.system.repository.entity.SysGroup;
import com.fuiou.ccs.system.vo.SysGroupVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工作组 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysGroupService extends IService<SysGroup> {

    /**
     * 创建工作组
     *
     * @param groupParam 工作组信息
     * @return 创建状态
     */
    Boolean saveGroup(SysGroupParam groupParam);

    /**
     * 根据用户id,查询该用户归属工作组集合
     *
     * @param userId
     * @return
     */
    List<SysGroupDTO> queryBelongGroupList(String userId);

    /**
     * 修改工作组
     *
     * @param sysGroupParam 工作组
     * @return 是否修改状态
     */
    Boolean updateGroup(SysGroupParam sysGroupParam);

    /**
     * 删除工作组
     *
     * @param id 工作组id
     * @return 是否删除成功
     */
    boolean deleteGroup(Long id);

    /**
     * 根据入参，检查当前任务是否可以指派
     *
     * @param userId                         登录用户，操作指定他人认领的用户
     * @param groupList                      归属组，需要认领的组，集合
     * @param claimTaskFromAssignOtherUserId 被指派去认领的userId
     * @return true-可以指派，false-不可以指派
     */
    boolean checkIsAssignClaim(String userId, List<Long> groupList, String claimTaskFromAssignOtherUserId);

    /**
     * @param groupIds 工作组集合
     * @return 根据工作组ID查询到的工作组个数
     */
    List<SysGroup> countSizeByIds(List<Long> groupIds);

    /**
     * 根据组长，经理userID， 查询其下所有组员集合
     *
     * @param userId
     * @return
     */
    List<SysGroupMemberDTO> queryGroupUserIds(String userId);


    /**
     * @param userId 用户ID
     * @return 返回用户工作组信息
     */
    List<SysGroupVO> getGroupByUserId(String userId);

    /**
     * @param page  分页
     * @param query 查询参数
     * @return 分页查询工作组
     */
    IPage<SysGroupVO> listPage(IPage<Object> page, SysGroupQuery query);

    /**
     * 工作组详情
     *
     * @param id 工作组id
     * @return 工作组信息详情
     */
    SysGroupVO detailById(Long id);

    /**
     * 根据组ids， 获取所有组长userIds集合
     *
     * @param groupIds
     * @return
     */
    List<String> getGroupLeader(List<Long> groupIds);

    /**
     * 根据组id，获取所有组内用户信息
     *
     * @param groupIds
     * @return
     */
    List<String> getGroupAllUserId(List<Long> groupIds);

    /**
     * 根据组id,查询组信息
     *
     * @param groupIds
     * @return
     */
    List<SysGroupDTO> getGroupLeaderAndManager(List<Long> groupIds);

    /**
     * 查询所有组员
     *
     * @param groupIds
     * @return
     */
    List<String> getGroupMemberUserList(List<Long> groupIds);

    /**
     * 工作组数据导出excel
     *
     * @param response
     */
    void exportGroup(HttpServletResponse response);

    List<String> getCodesByName(String name);

    /**
     * 删除用户所在的工作组成员(复核)
     *
     * @param groupId
     * @param userId
     */
    void deleteGroupMember(Long groupId, String userId);

}
