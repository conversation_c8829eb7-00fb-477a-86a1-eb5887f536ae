package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字典数据
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_DICT_ITEM")
public class CcsDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("DICT_ITEM_ID")
    private Long dictItemId;

    /**
     * 字典标签
     */
    @TableField("LABEL")
    private String label;

    /**
     * 字典键值
     */
    @TableField("VALUE")
    private String value;

    /**
     * 字典编号
     */
    @TableField("DICT_CODE")
    private String dictCode;

    /**
     * 字典排序
     */
    @TableField("SORT")
    private Integer sort;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否已删除
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Integer deletedStatus;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;


}
