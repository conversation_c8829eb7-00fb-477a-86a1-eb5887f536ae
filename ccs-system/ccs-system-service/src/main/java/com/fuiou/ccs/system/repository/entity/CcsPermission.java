package com.fuiou.ccs.system.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客服工单权限
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_PERMISSION")
public class CcsPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("PERMISSION_ID")
    private Long permissionId;

    /**
     * 权限名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 菜单ID
     */
    @TableField("MENU_ID")
    private Long menuId;

    /**
     * URL权限标识
     */
    @TableField("URL_PERM")
    private String urlPerm;

    /**
     * 按钮权限标识
     */
    @TableField("BTN_PERM")
    private String btnPerm;

    /**
     * 创建人Id
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 客户端Id
     */
    @TableField("CLIENT_ID")
    private String clientId;

    /**
     * 更新人Id
     */
    @TableField(value = "UPDATER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updaterId;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
