<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsPermission">
        <id column="PERMISSION_ID" property="permissionId" />
        <result column="NAME" property="name" />
        <result column="MENU_ID" property="menuId" />
        <result column="URL_PERM" property="urlPerm" />
        <result column="BTN_PERM" property="btnPerm" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CLIENT_ID" property="clientId" />
        <result column="UPDATER_ID" property="updaterId" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        PERMISSION_ID, NAME, MENU_ID, URL_PERM, BTN_PERM, CREATOR_ID, CREATE_TIME,CLIENT_ID,UPDATER_ID,UPDATE_TIME
    </sql>

    <select id="selectPermissionRules" resultType="com.fuiou.ccs.system.repository.entity.SysUrlPermissionRule">
        SELECT
        SP.URL_PERM, SR.CODE as ROLE_CODE
        FROM T_SYS_PERMISSION SP
         JOIN T_SYS_ROLE_PERMISSION SRP on SP.PERMISSION_ID = SRP.PERMISSION_ID
         JOIN T_SYS_ROLE SR on SRP.ROLE_ID = SR.ROLE_ID AND SR.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
        AND SR.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
    </select>

    <delete id="deleteRolePermissionByPermissionId">
        delete from T_SYS_ROLE_PERMISSION
        where PERMISSION_ID = #{permissionId,jdbcType=BIGINT}
    </delete>
</mapper>
