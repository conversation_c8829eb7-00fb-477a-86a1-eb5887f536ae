<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsClientUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsClientUser">
        <id column="ID" property="id" />
        <result column="CLIENT_ID" property="clientId" />
        <result column="USER_ID" property="userId" />
        <result column="CREATE_TIME" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CLIENT_ID, USER_ID, CREATE_TIME
    </sql>

</mapper>
