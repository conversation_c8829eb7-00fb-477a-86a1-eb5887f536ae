<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.SysGroupMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.SysGroupMember">
        <id column="GROUP_MEMBER_ID" property="groupMemberId" />
        <result column="USER_ID" property="userId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="STATUS" property="status" />
        <result column="UPDATER_ID" property="updaterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        item.GROUP_MEMBER_ID, item.USER_ID, item.GROUP_ID, item.CREATOR_ID, item.CREATE_TIME,item.STATUS,item.UPDATER_ID
    </sql>

   <!-- 根据用户ID删除对应的用户工作组信息-->
    <delete id="realDelByUserId">
        delete from T_SYS_GROUP_MEMBER
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <select id="listPage" resultType="com.fuiou.ccs.system.vo.SysGroupMemberVO">
        select
        <include refid="Base_Column_List"/>,
        user.EMP_NAME as userName
        from T_SYS_GROUP_MEMBER item
        left join T_SYS_USER user on item.USER_ID = user.USER_ID
        where 1 = 1
        <if test="query.groupMemberId!= null and query.groupMemberId != ''">
            AND GROUP_MEMBER_ID = #{query.groupMemberId}
        </if>
        <if test="query.userId != null and query.userId!=''">
            AND USER_ID LIKE CONCAT(#{query.userId},'%')
        </if>
        <if test="query.groupId != null">
            AND GROUP_ID = #{query.groupId}
        </if>
        <if test="query.status!= null ">
            AND STATUS = #{query.status}
        </if>

    </select>

    <select id="listByGroupId" resultType="com.fuiou.ccs.system.vo.SysGroupMemberVO">
        select
        item.GROUP_MEMBER_ID,
        item.USER_ID,
        item.GROUP_ID,
        item.CREATOR_ID,
        item.CREATE_TIME,
        item.STATUS,
        user.STATUS as  userStatus,
        user.IS_DELETED,
        user.IS_CHECKED,
        item.UPDATER_ID,
        user.EMP_NAME as empName,
        user.USERNAME as userName
        from T_SYS_GROUP_MEMBER item
        left join T_SYS_USER user on item.USER_ID = user.USER_ID
        where item.GROUP_ID = #{groupId}
    </select>
</mapper>
