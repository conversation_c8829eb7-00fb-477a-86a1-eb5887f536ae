<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsDictItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsDictItem">
        <id column="DICT_ITEM_ID" property="dictItemId" />
        <result column="LABEL" property="label" />
        <result column="VALUE" property="value" />
        <result column="DICT_CODE" property="dictCode" />
        <result column="SORT" property="sort" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATER_ID" property="updaterId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="deletedStatus" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DICT_ITEM_ID, LABEL, "VALUE", DICT_CODE, SORT, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, IS_DELETED, REMARK
    </sql>

    <!-- 根据字典编号删除字典数据 -->
    <delete id="deleteByDictCode">
        UPDATE T_SYS_DICT_ITEM SET IS_DELETED = 1 WHERE DICT_CODE = #{dictCode}
    </delete>

    <!-- 根据字典编号删除字典数据 -->
    <delete id="deleteByDictCodes">
        UPDATE T_SYS_DICT_ITEM SET IS_DELETED = 1 WHERE DICT_CODE IN
        <foreach collection="dictCodes" item="dictCode" separator="," open="(" close=")">
            #{dictCode}
        </foreach>
    </delete>

    <!-- 更新字典编号 -->
    <update id="updateDictCode">
        UPDATE T_SYS_DICT_ITEM SET DICT_CODE = #{newDictCode} WHERE DICT_CODE = #{oldDictCode}
    </update>

    <!-- 查询字典数据（根据字典编号） -->
    <select id="selectByDictCode" resultMap="BaseResultMap">
        SELECT
        DICT_ITEM_ID, LABEL, "VALUE", DICT_CODE, SORT, REMARK
        FROM T_SYS_DICT_ITEM WHERE DICT_CODE = #{dictCode} AND IS_DELETED =  ${@com.fuiou.common.constants.CommonConstants@NO} ORDER BY SORT
    </select>

    <select id="selectValueByDictCode" resultType="java.lang.String">
        SELECT
           "VALUE"
        FROM T_SYS_DICT_ITEM
        WHERE DICT_CODE = #{dictCode}
        AND IS_DELETED =  ${@com.fuiou.common.constants.CommonConstants@NO}
        ORDER BY SORT
    </select>

    <!--查询动态建表有无包含关键字-->
    <select id="checkValue" resultType="java.lang.String">
        SELECT "VALUE" FROM T_SYS_DICT_ITEM
        WHERE DICT_CODE =  #{dictCode}  AND IS_DELETED =  ${@com.fuiou.common.constants.CommonConstants@NO}
          AND "VALUE" IN  (
        <foreach collection="list" item="items" separator=",">
            #{items}
        </foreach>
        )
    </select>

    <!-- 查询字典数据（字典编号，字典值） -->
    <select id="selectByCodeValue" resultMap="BaseResultMap">
        SELECT
        DICT_ITEM_ID, LABEL, "VALUE", DICT_CODE, SORT, REMARK
        FROM T_SYS_DICT_ITEM WHERE DICT_CODE = #{dictCode}
        AND "VALUE" = #{dictValue}
        <if test="isContainDelete==null or !isContainDelete">
            AND IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
        </if>
    </select>

    <!-- 查询字典数据（字典编号，字典标签） -->
    <select id="selectByCodeLabel" resultMap="BaseResultMap">
        SELECT
        DICT_ITEM_ID, LABEL, "VALUE", DICT_CODE, SORT, REMARK
        FROM T_SYS_DICT_ITEM WHERE DICT_CODE = #{dictCode}
        AND LABEL = #{label} AND IS_DELETED =  ${@com.fuiou.common.constants.CommonConstants@NO}
    </select>

    <select id="selectByDictCodes" resultMap="BaseResultMap">
        SELECT
        DICT_ITEM_ID, LABEL, "VALUE", DICT_CODE, SORT, REMARK, IS_DELETED
        FROM T_SYS_DICT_ITEM
        where 1=2
        <if test="dictCodes!=null and dictCodes.size()!=0">
            or ( DICT_CODE in
            <foreach collection="dictCodes" item="dictCode" open="(" close=")" separator=",">
                #{dictCode}
            </foreach>
            )
        </if>
        ORDER BY DICT_CODE , IS_DELETED, SORT
    </select>

</mapper>
