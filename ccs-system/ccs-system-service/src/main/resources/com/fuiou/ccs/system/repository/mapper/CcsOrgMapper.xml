<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsOrg">
        <id column="ORG_ID" property="orgId" />
        <result column="PARENT_ID" property="parentId" />
        <result column="NAME" property="name" />
        <result column="ORG_LEVEL" property="orgLevel" />
        <result column="STATUS" property="status" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="UPDATER_ID" property="updaterId" />
        <result column="CREATE_TIME" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ORG_ID, PARENT_ID, NAME,ORG_LEVEL, STATUS, UPDATE_TIME, CREATOR_ID, UPDATER_ID, CREATE_TIME
    </sql>

    <select id="getOrgList" resultType="com.fuiou.ccs.system.vo.CcsOrgVO">
        SELECT org.ORG_ID, org.NAME, org.ORG_LEVEL, COALESCE(org.PARENT_ID, '1') AS PARENT_ID, org.STATUS
        FROM T_SYS_ORG org
        WHERE org.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        order by org.ORG_ID asc
    </select>

    <select id="orgsListPage" resultType="com.fuiou.ccs.system.vo.CcsOrgVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SYS_ORG org
        WHERE STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        <if test="query.orgId != null and query.orgId != ''">
            AND ORG_ID = #{query.orgId}
        </if>
        <if test="query.parentId != null and query.parentId != ''">
            AND PARENT_ID = #{query.parentId}
        </if>
        <if test="query.name != null and query.name != ''">
            AND NAME = #{query.name}
        </if>
        order by org.ORG_ID asc
    </select>


    <select id="orgList" resultType="com.fuiou.ccs.system.vo.CcsOrgVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SYS_ORG org
        WHERE STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        <if test="query.orgId != null and query.orgId != ''">
            AND ORG_ID = #{query.orgId}
        </if>
        <if test="query.parentId != null and query.parentId != ''">
            AND PARENT_ID = #{query.parentId}
        </if>
        <if test="query.name != null and query.name != ''">
            AND NAME = #{query.name}
        </if>
        order by org.ORG_ID asc
    </select>

    <select id="queryOrgParentByOrgId" resultType="com.fuiou.ccs.system.vo.CcsOrgVO">
        WITH orgFun(ORG_ID, NAME ,PARENT_ID,ORG_LEVEL, STATUS) AS (
            SELECT o1.ORG_ID
                 ,o1.NAME
                 , o1.PARENT_ID
                 ,o1.ORG_LEVEL
                 , o1.STATUS
            FROM T_SYS_ORG o1
            WHERE o1.ORG_ID = #{orgId}
            UNION ALL
            SELECT o2.ORG_ID
                 ,o2.NAME
                 ,o2.PARENT_ID
                 ,o2.ORG_LEVEL
                 , o2.STATUS
            FROM T_SYS_ORG o2
               , orgFun
            WHERE orgFun.PARENT_ID   = o2.ORG_ID
        )
        SELECT DISTINCT ORG_ID AS orgId, NAME ,ORG_LEVEL AS orgLevel, STATUS as status
        FROM orgFun
        where STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        ORDER BY ORG_LEVEL

    </select>

    <select id="queryLowerOrgByOrgId" resultType="com.fuiou.ccs.system.vo.CcsOrgVO">
        WITH orgFun(ORG_ID,NAME, PARENT_ID, ORG_LEVEL, STATUS) AS (
            SELECT o1.ORG_ID
                 ,o1.NAME
                 , o1.PARENT_ID
                 , o1.ORG_LEVEL
                 , o1.STATUS
            FROM T_SYS_ORG o1
            WHERE o1.ORG_ID = #{orgId}
            UNION ALL
            SELECT o2.ORG_ID
                 ,o2.NAME
                 , o2.PARENT_ID
                 , o2.ORG_LEVEL
                 , o2.STATUS
            FROM T_SYS_ORG o2
               , orgFun
            WHERE orgFun.ORG_ID = o2.PARENT_ID
        )
        SELECT DISTINCT ORG_ID AS orgId, NAME ,ORG_LEVEL AS orgLevel, STATUS as status
        FROM orgFun
        where STATUS  = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
    </select>
    <select id="queryCount" resultType="java.lang.Integer">
        SELECT COUNT(ORG_ID) FROM T_SYS_ORG
    </select>


</mapper>
