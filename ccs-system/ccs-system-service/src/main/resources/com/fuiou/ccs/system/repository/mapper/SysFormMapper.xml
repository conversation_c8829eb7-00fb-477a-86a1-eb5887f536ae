<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.SysFormMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.SysForm">
        <id column="FORM_ID" property="formId"/>
        <result column="TABLE_NAME" property="tableName"/>
        <result column="CN_TABLE_NAME" property="cnTableName"/>
        <result column="IS_CHECKED" property="checkedStatus"/>
        <result column="IS_USED" property="usedStatus"/>
        <result column="IS_CREATE" property="createStatus"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATER_ID" property="updaterId"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETED" property="deletedStatus"/>
        <result column="FORM_CODE" property="formCode"/>
        <result column="CONFIG_JSON" property="configJson"/>
        <result column="STATUS" property="status"/>
        <result column="CALLER_DICT_CODE" property="callerDictCode"/>
        <result column="HIDDEN_STATE" property="hiddenStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FORM_ID,
        TABLE_NAME,
        CN_TABLE_NAME,
        IS_CHECKED,
        IS_USED,
        IS_CREATE,
        CREATOR_ID,
        CREATE_TIME,
        UPDATER_ID,
        UPDATE_TIME,
        IS_DELETED,
        FORM_CODE,
        CONFIG_JSON,
        STATUS,
        HIDDEN_STATE,
        CALLER_DICT_CODE
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultFormTabsVO" type="com.fuiou.ccs.system.vo.SysFormVO">
        <id column="FORM_ID" property="formId"/>
        <result column="FORM_CODE" property="formCode"/>
        <result column="CONFIG_JSON" property="configJson"/>
        <result column="TABLE_NAME" property="tableName"/>
        <result column="CN_TABLE_NAME" property="cnTableName"/>
        <result column="STATUS" property="status"/>
        <!-- tabs顺序 -->
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CALLER_DICT_CODE" property="callerDictCode"/>
    </resultMap>
    <!--分页查询-->
    <select id="pageForm" resultType="com.fuiou.ccs.system.vo.SysFormVO">
        SELECT tsf.FORM_ID,
               tsf.TABLE_NAME,
               tsf.CN_TABLE_NAME,
               tsf.IS_CHECKED as checkedStatus,
               tsf.IS_USED    as usedStatus,
               tsf.IS_CREATE  as createStatus,
               tsf.IS_DELETED,
               tsf.FORM_CODE,
               tsf.CONFIG_JSON,
               tsf.STATUS,
               tsf.CALLER_DICT_CODE,
               tabs.formBusinessNames,
               tabs.workSubIds
        FROM T_SYS_FORM tsf
                 LEFT JOIN
             (select formBus.FORM_ID,
                     LISTAGG(busType.NAME || '-' || workType.NAME || '-' || workSub.NAME, '、') AS formBusinessNames,
                     LISTAGG(workSub.BUS_ID, ',')                                              AS workSubIds
              from T_SYS_FORM_BUSINESS formBus
                       left join T_SYS_BUSINESS busType on busType.BUS_ID = formBus.BUS_ID
                       left join T_SYS_BUSINESS workType on workType.BUS_ID = formBus.WORK_ID
                       left join T_SYS_BUSINESS workSub on workSub.BUS_ID = formBus.WORK_SUB_ID
              WHERE formBus.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
              GROUP BY formBus.FORM_ID
             ) AS tabs ON tabs.FORM_ID = tsf.FORM_ID
        where tsf.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
          AND tsf.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND tsf.HIDDEN_STATE = ${@com.fuiou.common.constants.CommonConstants@NO}
        <if test="query.cnTableName != null and query.cnTableName != ''">
            and tsf.CN_TABLE_NAME like CONCAT('%',CONCAT(#{query.cnTableName}
            , '%'))
        </if>
        <if test="query.formCode != null and query.formCode != ''">
            and tsf.FORM_CODE like CONCAT ('%'
              , CONCAT (#{query.formCode}
              , '%'))
        </if>
        <if test="query.status != null">
            and tsf.STATUS = #{query.status}
        </if>
        <if test="query.createStatus != null">
            and tsf.IS_CREATE = #{query.createStatus}
        </if>
        <if test="query.formId != null and query.formId != ''">
            and tsf.FORM_ID = #{query.formId}
        </if>
        order By tsf.STATUS desc, tsf.FORM_CODE desc
    </select>
    <!-- 不分页查询-->
    <select id="listForm" resultType="com.fuiou.ccs.system.vo.SysFormVO">
        SELECT tsf.FORM_ID,
               tsf.TABLE_NAME,
               tsf.CN_TABLE_NAME,
               tsf.IS_CHECKED as checkedStatus,
               tsf.IS_USED    as usedStatus,
               tsf.IS_CREATE  as createStatus,
               tsf.IS_DELETED,
               tsf.FORM_CODE,
               tsf.CONFIG_JSON,
               tsf.STATUS,
               tsf.CALLER_DICT_CODE,
               tabs.formBusinessNames
        FROM T_SYS_FORM tsf
                 LEFT JOIN
             (select formBus.FORM_ID,
                     LISTAGG(busType.NAME || '-' || workType.NAME || '-' || workSub.NAME, '、') AS formBusinessNames
              from T_SYS_FORM_BUSINESS formBus
                       left join T_SYS_BUSINESS busType on busType.BUS_ID = formBus.BUS_ID
                       left join T_SYS_BUSINESS workType on workType.BUS_ID = formBus.WORK_ID
                       left join T_SYS_BUSINESS workSub on workSub.BUS_ID = formBus.WORK_SUB_ID
              WHERE formBus.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
              GROUP BY formBus.FORM_ID
             ) AS tabs ON tabs.FORM_ID = tsf.FORM_ID
        where tsf.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
          AND tsf.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND tsf.HIDDEN_STATE = ${@com.fuiou.common.constants.CommonConstants@NO}
        <if test="query.cnTableName != null and query.cnTableName != ''">
            and tsf.CN_TABLE_NAME like CONCAT('%',CONCAT(#{query.cnTableName}
            , '%'))
        </if>
        <if test="query.formCode != null and query.formCode != ''">
            and tsf.FORM_CODE like CONCAT ('%'
              , CONCAT (#{query.formCode}
              , '%'))
        </if>
        <if test="query.status != null">
            and tsf.STATUS = #{query.status}
        </if>
        <if test="query.createStatus != null">
            and tsf.IS_CREATE = #{query.createStatus}
        </if>
        <if test="query.formId != null and query.formId != ''">
            and tsf.FORM_ID = #{query.formId}
        </if>
        order By tsf.FORM_CODE desc
    </select>

    <!--根据表名查询数据库中+表单配置表中存在该表名的个数-->
    <select id="countTableName" resultType="java.lang.Integer">
        SELECT (SELECT COUNT(Name) FROM SYSIBM.SYSTABLES WHERE TID  <![CDATA[ <>]]> 0 AND Name = #{tableName})
                   + (SELECT count(TABLE_NAME) FROM T_SYS_FORM WHERE TABLE_NAME = #{tableName})
        FROM sysibm.sysdummy1
    </select>

    <!--查询模板数量-->
    <select id="formCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT FORM_CODE)
        FROM T_SYS_FORM
    </select>

    <!--根据表名，查询相关tabs信息-->
    <select id="querySysFormTabs" resultMap="BaseResultFormTabsVO">
        select f.TABLE_NAME,
               f.FORM_ID,
               f.CN_TABLE_NAME,
               f.FORM_CODE,
               f.CONFIG_JSON,
               f.CALLER_DICT_CODE,
               f.CREATE_TIME,
               f.STATUS
        from T_SYS_FORM f
        where f.IS_CHECKED = ${@com.fuiou.ccs.system.enums.SysFormEnums$CheckEdEnums@REVIEW_SUCCESS.getValue()}
          and f.IS_USED = ${@com.fuiou.ccs.system.enums.SysFormEnums@YES}
          and f.IS_CREATE = ${@com.fuiou.ccs.system.enums.SysFormEnums@YES}
          and f.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
          AND f.HIDDEN_STATE = ${@com.fuiou.common.constants.CommonConstants@NO}
        <if test="formNames != null and formNames.size != 0">
              and f.TABLE_NAME in
            <foreach collection="formNames" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by f.STATUS desc, f.CREATE_TIME asc
    </select>

    <!--    数据库真实删除跟form配置的数据-->
    <delete id="realDelByFormId">
        delete
        from T_SYS_FORM
        where FORM_ID = #{formId}
    </delete>

    <select id="querySysFormByIds" resultType="com.fuiou.ccs.system.vo.SysFormVO">
        select FORM_ID  as  formId,
               TABLE_NAME  as  tableName,
               CN_TABLE_NAME  as  cnTableName,
               FORM_CODE  as  formCode,
               CALLER_DICT_CODE  as  callerDictCode
        from T_SYS_FORM f  where  f.FORM_ID in
        <foreach collection="formIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryFormCodesByCnTableName" resultType="java.lang.String">
        select FORM_CODE from T_SYS_FORM where CN_TABLE_NAME like CONCAT('%', CONCAT(#{cnTableName}, '%'))
    </select>

    <select id="getFormDetil" resultType="com.fuiou.ccs.system.vo.SysFormVO">
        select FORM_ID          as formId,
               TABLE_NAME       as tableName,
               CN_TABLE_NAME    as cnTableName,
               FORM_CODE        as formCode,
               CALLER_DICT_CODE as callerDictCode
        from T_SYS_FORM f
        where f.TABLE_NAME = #{tableName}
    </select>
</mapper>
