<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.SysFormBusinessMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.SysFormBusiness">
        <id column="FORM_BUSINESS_ID" property="formBusinessId"/>
        <result column="FORM_ID" property="formId"/>
        <result column="BUS_ID" property="busId"/>
        <result column="WORK_ID" property="workId"/>
        <result column="WORK_SUB_ID" property="workSubId"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATER_ID" property="updaterId"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETED" property="deletedStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FORM_BUSINESS_ID,
        FORM_ID,
        BUS_ID,
        WORK_ID,
        WORK_SUB_ID,
        CREATOR_ID,
        CREATE_TIME,
        UPDATER_ID,
        UPDATE_TIME,
        IS_DELETED
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapFormBusVO" type="com.fuiou.ccs.system.vo.SysFormBusinessVO">
        <id column="FORM_BUSINESS_ID" property="formBusinessId"/>
        <result column="FORM_ID" property="formId"/>
        <result column="BUS_ID" property="busId"/>
        <result column="WORK_ID" property="workId"/>
        <result column="WORK_SUB_ID" property="workSubId"/>
    </resultMap>

    <!--根据表单id, 查询对应的业务集合-->
    <select id="queryFormBusList" resultMap="BaseResultMapFormBusVO">
        select formBus.FORM_BUSINESS_ID,
               formBus.FORM_ID,
               formBus.BUS_ID,
               busType.NAME     as busName,
               busType.ORG_CODE as busOrgCode,
               formBus.WORK_ID,
               workType.NAME    as workName,
               formBus.WORK_SUB_ID,
               workSub.NAME     as workSubName
        from T_SYS_FORM_BUSINESS formBus
                 left join T_SYS_BUSINESS busType on busType.BUS_ID = formBus.BUS_ID
                 left join T_SYS_BUSINESS workType on workType.BUS_ID = formBus.WORK_ID
                 left join T_SYS_BUSINESS workSub on workSub.BUS_ID = formBus.WORK_SUB_ID
        where formBus.FORM_ID = #{formId}
          and formBus.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
    </select>

    <!-- 根据表单ID真实删除数据库中对应的表单业务数据-->
    <delete id="realDelBusByFormId">
        delete
        from T_SYS_FORM_BUSINESS
        where FORM_ID = #{formId}
    </delete>

    <!--根据id，查询中文名称-->
    <select id="queryBuinessList" resultMap="BaseResultMapFormBusVO">
        select formBus.FORM_BUSINESS_ID,
               formBus.FORM_ID,
               formBus.BUS_ID,
               busType.NAME     as busName,
               busType.ORG_CODE as busOrgCode,
               formBus.WORK_ID,
               workType.NAME    as workName,
               formBus.WORK_SUB_ID,
               workSub.NAME     as workSubName
        from T_SYS_FORM_BUSINESS formBus
                 left join T_SYS_BUSINESS busType on busType.BUS_ID = formBus.BUS_ID
                 left join T_SYS_BUSINESS workType on workType.BUS_ID = formBus.WORK_ID
                 left join T_SYS_BUSINESS workSub on workSub.BUS_ID = formBus.WORK_SUB_ID
        where formBus.FORM_BUSINESS_ID in (
        <foreach collection="businessIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <!--根据参数，查询formBusinessIds对象集合-->
    <select id="queryFormBusinessList" resultMap="BaseResultMapFormBusVO">
        select FORM_BUSINESS_ID, FORM_ID
        from T_SYS_FORM_BUSINESS b
        <where>
            <choose>
                <when test="workSubIds != null and workSubIds.size() != 0">
                    and b.WORK_SUB_ID in
                    <foreach collection="workSubIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="workIds != null and workIds.size() != 0">
                    and  b.WORK_ID in
                    <foreach collection="workIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="busIds != null and busIds.size() != 0">
                    and  b.BUS_ID in
                    <foreach collection="busIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1 = 2
                </otherwise>
            </choose>
        </where>
    </select>


    <select id="queryFormBusListByType" resultType="com.fuiou.ccs.system.vo.SysFormBusinessVO">
        SELECT formBus.FORM_BUSINESS_ID,
               formBus.FORM_ID,
               formBus.BUS_ID,
               formBus.WORK_ID,
               formBus.WORK_SUB_ID,
               tsf.FORM_CODE     as formCode,
               tsf.CN_TABLE_NAME as cnTableName,
               tsf.STATUS        as formStatus
        FROM T_SYS_FORM_BUSINESS as formBus
                 LEFT JOIN T_SYS_FORM tsf ON formBus.FORM_ID = tsf.FORM_ID
        WHERE formBus.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
          AND tsf.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
        <if test="busId != null">

            AND formBus.BUS_ID = #{busId}
        </if>
        <if test="workId != null">
            AND formBus.WORK_ID = #{workId}
        </if>
        <if test="workSubId != null">
            AND formBus.WORK_SUB_ID = #{workSubId}
        </if>
    </select>

    <!--查询表单-->
    <select id="queryFormBusiness" resultType="com.fuiou.ccs.system.vo.SysFormBusinessVO">
        select formBus.FORM_BUSINESS_ID
                ,tsf.TABLE_NAME
        FROM T_SYS_FORM_BUSINESS as formBus
                LEFT JOIN T_SYS_FORM tsf ON formBus.FORM_ID = tsf.FORM_ID
        WHERE formBus.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
            AND tsf.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
            AND formBus.BUS_ID = #{busId}
            AND formBus.WORK_ID = #{workId}
            AND formBus.WORK_SUB_ID = #{workSubId}
    </select>

    <select id="queryAllBusinessIdList" resultType="com.fuiou.ccs.system.vo.SysFormBusinessVO">
        select  b.BUS_ID
                ,b.WORK_ID
                ,b.WORK_SUB_ID
        from  T_SYS_FORM_BUSINESS  b
        where b.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
        and  b.FORM_ID = #{formId}
    </select>
</mapper>
