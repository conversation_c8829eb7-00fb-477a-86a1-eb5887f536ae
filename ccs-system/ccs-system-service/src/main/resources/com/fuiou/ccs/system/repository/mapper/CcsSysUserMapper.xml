<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsSysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsSysUser">
        <id column="USER_ID" property="userId" />
        <result column="EMP_NO" property="empNo" />
        <result column="USER_CODE" property="userCode" />
        <result column="USERNAME" property="username" />
        <result column="PASSWORD" property="password" />
        <result column="EMP_NAME" property="empName" />
        <result column="ORG_ID" property="orgId" />
        <result column="EMAIL" property="email" />
        <result column="MOBILE" property="mobile" />
        <result column="AVATAR" property="avatar" />
        <result column="STATUS" property="status" />
        <result column="REGISTER_DATE" property="registerDate" />
        <result column="LAST_LOGIN_DATE" property="lastLoginDate" />
        <result column="LAST_LOGIN_IP" property="lastLoginIp" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATER_ID" property="updaterId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="deletedStatus" />
        <result column="IS_CHECKED" property="checkedStatus"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseVOResultMap" type="com.fuiou.ccs.system.vo.CcsSysUserVO">
        <id column="USER_ID" property="userId"/>
        <result column="USERNAME" property="username"/>
        <result column="EMP_NO" property="empNo"/>
        <result column="EMP_NAME" property="empName"/>
        <result column="EMAIL" property="email"/>
        <result column="MOBILE" property="mobile"/>
        <result column="AVATAR" property="avatar"/>
        <result column="STATUS" property="status"/>
        <result column="REGISTER_DATE" property="registerDate"/>
        <result column="ORG_ID" property="orgId" />
        <result column="LAST_LOGIN_DATE" property="lastLoginDate"/>
        <result column="LAST_LOGIN_IP" property="lastLoginIp"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        USER_ID,USER_CODE, EMP_NO, USERNAME, PASSWORD, EMP_NAME, ORG_ID, EMAIL, MOBILE, AVATAR, STATUS, REGISTER_DATE, LAST_LOGIN_DATE, LAST_LOGIN_IP, CREATOR_ID, CREATE_TIME, UPDATER_ID, UPDATE_TIME, IS_DELETED,IS_CHECKED
    </sql>

    <sql id="Alias_Column_List">
        su.USER_ID,su.USER_CODE,su.USERNAME,su."PASSWORD",su.EMP_NO, su.EMP_NAME,su.EMAIL,su.MOBILE,su.AVATAR,su."STATUS",su.CREATOR_ID,su.CREATE_TIME,su.UPDATER_ID,
        su.UPDATE_TIME,su.IS_DELETED,su.REGISTER_DATE,su.ORG_ID,su.LAST_LOGIN_DATE,su.LAST_LOGIN_IP,su.IS_CHECKED
    </sql>
    <!--分页查询用户-->
    <select id="usersListPage" resultType="com.fuiou.ccs.system.vo.CcsSysUserVO">
        SELECT su.USER_ID,
        su.USER_CODE AS userCode,
        su.USERNAME AS username,
        su.EMP_NO,
        su.EMP_NAME ,
        su.EMAIL ,
        su.MOBILE ,
        su.AVATAR ,
        su.STATUS ,
        su.REGISTER_DATE ,
        su.ORG_ID ,
        so.name AS orgName,
        su.LAST_LOGIN_DATE,
        su.LAST_LOGIN_IP,
        userRo.rolesName
        FROM T_SYS_USER su
        LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
        AND so.STATUS =${@com.fuiou.common.constants.CommonConstants@ENABLED}
        LEFT JOIN    ( SELECT USER_ID, LISTAGG(roles.NAME, '、') AS rolesName FROM  T_SYS_USER_ROLE as userRole
        LEFT JOIN T_SYS_ROLE roles ON userRole.ROLE_ID = roles.ROLE_ID
        GROUP BY USER_ID ) AS userRo  ON userRo.USER_ID = su.USER_ID
        INNER  JOIN ( SELECT ROLES.USER_ID FROM  T_SYS_USER_ROLE AS ROLES
        <if test="query.roleIds != null ">
            <foreach collection="query.roleIds" item="roleId" open="  WHERE ROLES.ROLE_ID IN(" close=")" separator=",">
                #{roleId}
            </foreach>
        </if>
        GROUP BY ROLES.USER_ID )   AS roleIds ON   roleIds.USER_ID = su.USER_ID
        where su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
        AND su.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        <if test="query.username != null and query.username != ''">
            and su.USERNAME like CONCAT('%',CONCAT(#{query.username},'%'))
        </if>
        <if test="query.usernameOrEmpName != null and query.usernameOrEmpName != ''">
            and ( su.USERNAME like CONCAT('%',CONCAT(#{query.usernameOrEmpName},'%'))  or  su.EMP_NAME like  CONCAT('%',CONCAT(#{query.usernameOrEmpName},'%'))  )
        </if>
        <if test="query.empNo != null and query.empNo != ''">
            and su.EMP_NO like  CONCAT('%',CONCAT(#{query.empNo},'%'))
        </if>
        <if test="query.empName != null and query.empName != ''">
            and su.EMP_NAME like  CONCAT('%',CONCAT(#{query.empName},'%'))
        </if>
        <if test="query.userCode != null and query.userCode != ''">
            and su.USER_CODE like  CONCAT('%',CONCAT(#{query.userCode},'%'))
        </if>
        <if test="query.email != null and query.email != ''">
            and su.EMAIL like  CONCAT('%',CONCAT(#{query.email},'%'))
        </if>
        <if test="query.mobile != null and query.mobile != ''">
            and su.MOBILE like  CONCAT('%',CONCAT(#{query.mobile},'%'))
        </if>
        <if test="query.status != null">
            and su.STATUS = #{query.status}
        </if>
        <if test="query.orgIds != null and query.orgIds.size() > 0">
             and su.ORG_ID IN
            <foreach collection="query.orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
<!--        <if test="query.orgId != null and query.orgId != ''">-->
<!--            and su.ORG_ID = #{query.orgId}-->
<!--        </if>-->
<!--        <if test="query.orgName != null and query.orgName != ''">-->
<!--            and so.NAME   like  CONCAT('%',CONCAT(#{query.orgName},'%'))-->
<!--        </if>-->
        ORDER BY su.STATUS DESC, su.CREATE_TIME DESC
    </select>

    <select id="queryExportUsers" resultType="com.fuiou.ccs.system.excel.CcsSysUserExcel">
        SELECT su.USER_ID,
        su.USER_CODE AS userCode,
        su.USERNAME AS username,
        su.EMP_NO,
        su.EMP_NAME ,
        su.EMAIL ,
        su.MOBILE ,
        su.AVATAR ,
        case when su.STATUS =1 then '正常'
        when su.STATUS =0 then '停用'
        else '' end as STATUS,
        su.REGISTER_DATE ,
        su.ORG_ID ,
        so.name AS orgName,
        su.LAST_LOGIN_DATE,
        su.LAST_LOGIN_IP,
        userRo.rolesName,
        groupInfo.groupName
        FROM T_SYS_USER su
        LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
        AND so.STATUS =${@com.fuiou.common.constants.CommonConstants@ENABLED}
        LEFT JOIN    ( SELECT USER_ID, LISTAGG(roles.NAME, '、') AS rolesName FROM  T_SYS_USER_ROLE as userRole
        LEFT JOIN T_SYS_ROLE roles ON userRole.ROLE_ID = roles.ROLE_ID
        GROUP BY USER_ID ) AS userRo  ON userRo.USER_ID = su.USER_ID
        INNER  JOIN ( SELECT ROLES.USER_ID FROM  T_SYS_USER_ROLE AS ROLES
        <if test="query.roleIds != null ">
            <foreach collection="query.roleIds" item="roleId" open="  WHERE ROLES.ROLE_ID IN(" close=")" separator=",">
                #{roleId}
            </foreach>
        </if>
        GROUP BY ROLES.USER_ID )   AS roleIds ON   roleIds.USER_ID = su.USER_ID
        LEFT JOIN   (SELECT tsgm.USER_ID , LISTAGG(tsg.NAME, '、') AS groupName FROM T_SYS_GROUP_MEMBER tsgm
        INNER JOIN T_SYS_GROUP tsg ON tsgm.GROUP_ID = tsg.GROUP_ID
        WHERE tsgm.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED} AND tsg.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        GROUP BY tsgm.USER_ID )  AS groupInfo ON groupInfo.USER_ID = su.USER_ID
        where su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
        AND su.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        <if test="query.username != null and query.username != ''">
            and su.USERNAME like CONCAT('%',CONCAT(#{query.username},'%'))
        </if>
        <if test="query.usernameOrEmpName != null and query.usernameOrEmpName != ''">
            and ( su.USERNAME like CONCAT('%',CONCAT(#{query.usernameOrEmpName},'%'))  or  su.EMP_NAME like  CONCAT('%',CONCAT(#{query.usernameOrEmpName},'%'))  )
        </if>
        <if test="query.empNo != null and query.empNo != ''">
            and su.EMP_NO like  CONCAT('%',CONCAT(#{query.empNo},'%'))
        </if>
        <if test="query.empName != null and query.empName != ''">
            and su.EMP_NAME like  CONCAT('%',CONCAT(#{query.empName},'%'))
        </if>
        <if test="query.userCode != null and query.userCode != ''">
            and su.USER_CODE like  CONCAT('%',CONCAT(#{query.userCode},'%'))
        </if>
        <if test="query.email != null and query.email != ''">
            and su.EMAIL like  CONCAT('%',CONCAT(#{query.email},'%'))
        </if>
        <if test="query.mobile != null and query.mobile != ''">
            and su.MOBILE like  CONCAT('%',CONCAT(#{query.mobile},'%'))
        </if>
        <if test="query.status != null">
            and su.STATUS = #{query.status}
        </if>
        <if test="query.orgIds != null and query.orgIds.size() > 0">
            and su.ORG_ID IN
            <foreach collection="query.orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        ORDER BY  su.STATUS DESC, su.CREATE_TIME DESC
    </select>
    <select id="assignUser" resultType="com.fuiou.ccs.system.vo.CcsSysUserVO">
        SELECT su.USER_ID,
        su.USERNAME AS username,
        su.EMP_NAME,
        su.ORG_ID ,
        su.STATUS,
        so.name AS orgName
        FROM T_SYS_USER su
        LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
        where su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
        AND su.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        <if test="usernameOrEmpName != null and usernameOrEmpName != ''">
            and ( su.USERNAME like CONCAT('%',CONCAT(#{usernameOrEmpName},'%'))  or  su.EMP_NAME like  CONCAT('%',CONCAT(#{usernameOrEmpName},'%'))  )
        </if>
        <if test="userId != null and userId != ''">
            and su.USER_ID = #{userId}
        </if>
<!--        <if test="orgId != null and orgId != ''">-->
<!--            and su.ORG_ID = #{orgId}-->
<!--        </if>-->
        <if test="orgIds != null and orgIds.size() > 0">
            and su.ORG_ID IN
            <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="orgName != null and orgName != ''">
            and so.NAME   like  CONCAT('%',CONCAT(#{orgName},'%'))
        </if>
        ORDER BY  su.STATUS DESC, su.CREATE_TIME DESC
    </select>


    <select id="queryGroupOrUserStatus" resultType="com.fuiou.ccs.system.vo.SysGroupAndUserVO">
        SELECT   tsu.USER_ID AS id,
                 tsu.EMP_NAME AS name,tsu.STATUS,'1' AS type  FROM T_SYS_USER tsu
        WHERE  tsu.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
            and tsu.USER_ID = #{id}
        UNION
        SELECT   CAST(tsg.GROUP_ID AS VARCHAR(25))  AS id,
                 tsg.NAME,tsg.STATUS ,'2' AS TYPE FROM T_SYS_GROUP tsg
        WHERE    CAST(tsg.GROUP_ID AS VARCHAR(25)) = #{id}
    </select>

    <!--  根据用户id查询详情信息-->
    <select id="getUserDetailById" resultType="com.fuiou.ccs.system.dto.CcsSysUserDTO">
        SELECT su.USER_ID,
               su.USERNAME   AS username,
               su.USER_CODE,
               su.EMP_NO,
               su.EMP_NAME,
               su.EMAIL,
               su.MOBILE,
               su.AVATAR,
               su.STATUS,
               su.REGISTER_DATE,
               su.ORG_ID ,
               so.name AS orgName,
               su.LAST_LOGIN_DATE,
               su.LAST_LOGIN_IP,
               su.IS_CHECKED,
               userRo.rolesName
        FROM T_SYS_USER su
                 LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
                 AND so.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
                 LEFT JOIN    ( SELECT USER_ID, LISTAGG(roles.NAME, '、') AS rolesName
                                       FROM  T_SYS_USER_ROLE as userRole
                                       LEFT JOIN T_SYS_ROLE roles ON userRole.ROLE_ID = roles.ROLE_ID
                                GROUP BY USER_ID ) AS userRo
                     ON userRo.USER_ID = su.USER_ID
       where su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
          AND su.USER_ID = #{userId}
    </select>

    <!-- 查询用户角色 -->
    <select id="getRolesByUserId" resultType="com.fuiou.ccs.system.repository.entity.CcsRole">
        select
            r.ROLE_ID,
            r.NAME,
            r.CODE
        from T_SYS_USER_ROLE ur
        left join T_SYS_ROLE r on r.ROLE_ID = ur.ROLE_ID
        where ur.USER_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <!--查询用户所在用户组ID-->
    <select id="selectGroupIdsByUserId" resultType="java.lang.Long">
        SELECT GROUP_ID
        FROM T_SYS_GROUP_MEMBER
        WHERE  STATUS = ${@com.fuiou.common.constants.CommonConstants@YES}
          AND  USER_ID = #{userId}
    </select>

    <!-- 查询用户角色 -->
    <select id="selectRolesByUserName" resultType="com.fuiou.ccs.system.vo.CcsUserRoleVO">
        SELECT SR.ROLE_ID,
               SR.CLIENT_ID,
               SR."NAME",
               SR.CODE,
               SR.SORT
        FROM T_SYS_USER SU
                 INNER JOIN T_SYS_USER_ROLE SUR ON SU.USERNAME = #{username}
            AND SU.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
            AND SU.IS_DELETED = 0 AND SU.USER_ID = SUR.USER_ID
                 INNER JOIN T_SYS_ROLE SR ON SUR.ROLE_ID = SR.ROLE_ID
            AND SR.CLIENT_ID = #{clientId} AND SR.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
            AND SR.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
        ORDER BY SR.SORT
    </select>

    <!-- 查询用户角色编号 -->
    <select id="selectRoleCodes" resultType="java.lang.String">
        SELECT sr.code
        FROM T_SYS_USER_role sur
                 INNER JOIN T_SYS_ROLE sr ON sur.user_id = #{userId}
            AND sr.CLIENT_ID = #{clientId} AND sur.role_id = sr.role_id
            AND sr.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
            AND sr.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
        ORDER BY sr.SORT
    </select>

    <!-- 查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SYS_USER WHERE USERNAME = #{username}
    </select>

   <!-- 根据手机号查询用户-->
    <select id="selectByMobile" resultType="com.fuiou.ccs.system.repository.entity.CcsSysUser">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SYS_USER WHERE MOBILE = #{mobile}
    </select>

    <!--查询用户表数量-->
    <select id="userCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT USER_CODE) FROM T_SYS_USER
    </select>

    <delete id="deleteRolesByUserId">
        DELETE
        FROM T_SYS_USER_ROLE
        WHERE USER_ID = #{userId}
    </delete>

    <insert id="insertUserRoles">
        INSERT INTO T_SYS_USER_ROLE
        (ROLE_ID, USER_ID, CREATOR_ID) VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{roleId}, #{userId}, #{creatorId})
        </foreach>
    </insert>

    <!--根据登录名查询用户-->
    <select id="selectUserByUsername" resultMap="BaseResultMap">
        SELECT
        <include refid="Alias_Column_List"/>
        FROM T_SYS_USER su
        INNER JOIN T_SYS_CLIENT_user scu ON su.USER_ID = scu.user_id
        WHERE su.username = #{username}
        AND scu.client_id = #{clientId} AND su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
    </select>

    <!-- 查询用户（根据手机号） -->
    <select id="selectUserByMobile" resultMap="BaseResultMap">
        SELECT
        <include refid="Alias_Column_List"/>
        FROM T_SYS_USER su
        INNER JOIN T_SYS_CLIENT_user scu ON su.USER_ID = scu.user_id
        WHERE su.mobile = #{mobile}
        AND scu.client_id = #{clientId} AND su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
    </select>

    <!--根据用户ID查询用户信息-->
    <select id="getUserByUserId" resultType="com.fuiou.ccs.system.dto.CcsSysUserInfo">
        SELECT su.USER_ID,
               su.USERNAME,
               su.USER_CODE,
               su.EMP_NO,
               su.EMP_NAME,
               su.EMAIL,
               su.MOBILE,
               su.STATUS,
               su.ORG_ID ,
               so.name AS orgName,
               su.CREATE_TIME,
               su.LAST_LOGIN_DATE,su.LAST_LOGIN_IP
        FROM T_SYS_USER su
                 LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
                 AND so.STATUS =${@com.fuiou.common.constants.CommonConstants@ENABLED}
        WHERE su.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND su.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
          and su.USER_ID = #{userId}
    </select>


    <!--根据用户ID查询用户信息-->
    <select id="getUserListByUserIds" resultType="com.fuiou.ccs.system.dto.CcsSysUserInfo">
        SELECT su.USER_ID,
               su.USERNAME,
               su.USER_CODE,
               su.EMP_NO,
               su.EMP_NAME,
               su.EMAIL,
               su.MOBILE,
               su.STATUS,
               su.ORG_ID ,
               so.name AS orgName,
               su.CREATE_TIME,
               su.LAST_LOGIN_DATE,su.LAST_LOGIN_IP
        FROM T_SYS_USER su
                 LEFT JOIN T_SYS_ORG so ON so.ORG_ID = su.ORG_ID
            AND so.STATUS =${@com.fuiou.common.constants.CommonConstants@ENABLED}
        WHERE su.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND su.IS_CHECKED = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
        <foreach collection="userIds" item="userId" open=" and su.USER_ID in(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <!--根据角色编号查询用户-->
    <select id="getUserListByRole" resultMap="BaseVOResultMap">
        SELECT su.USER_ID,
               su.EMP_NO,
               su.EMP_NAME,
               su.USERNAME,
               su.EMAIL,
               su.MOBILE,
               su.ORG_ID,
               su.LAST_LOGIN_DATE,
               su.LAST_LOGIN_IP
        FROM T_SYS_USER su
                 LEFT JOIN T_SYS_USER_ROLE sur ON su.USER_ID = sur.USER_ID
                 LEFT JOIN T_SYS_ROLE sr ON sr.ROLE_ID = sur.ROLE_ID
                           AND sr.IS_DELETED = ${@com.fuiou.common.constants.CommonConstants@NO}
        WHERE su.is_deleted = ${@com.fuiou.common.constants.CommonConstants@NO}
          AND su.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND sr.CODE = #{roleCode}
    </select>
    <select id="findAUserHasRole" resultType="int">
        select count(*) from T_SYS_USER_ROLE where  USER_ID =#{userId} and ROLE_ID = #{roleId,jdbcType=BIGINT}
    </select>

    <delete id="deleteUserRole">
        DELETE FROM T_SYS_USER_ROLE
        WHERE USER_ID = #{userId,jdbcType=VARCHAR} AND  ROLE_ID = #{roleId,jdbcType=BIGINT}
    </delete>

    <!--删除用户信息-->
    <delete id="realDelUserById">
      delete from T_SYS_USER
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <!--删除用户角色信息-->
    <delete id="realDelUserRoleByUserId">
        delete from T_SYS_USER_ROLE
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <select id="queryCodesByName" resultType="java.lang.String">
        select USER_CODE from T_SYS_USER where EMP_NAME like CONCAT('%', CONCAT(#{name}, '%'))
    </select>
</mapper>
