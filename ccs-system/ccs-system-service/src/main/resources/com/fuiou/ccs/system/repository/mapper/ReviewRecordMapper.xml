<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.ReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.ReviewRecord">
        <id column="REVIEW_ID" property="reviewId" />
        <result column="REVIEW_NO" property="reviewNo" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="BUSINESS_ID" property="businessId" />
        <result column="BUSINESS_NO" property="businessNo" />
        <result column="OPERATION_TYPE" property="operationType" />
        <result column="STATUS" property="status" />
        <result column="SOURCE_DATA" property="sourceData" />
        <result column="FINAL_DATA" property="finalData" />
        <result column="REMARKS" property="remarks" />
        <result column="CREATOR_ID" property="creatorId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="REVIEWER_ID" property="reviewerId" />
        <result column="REVIEW_TIME" property="reviewTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        REVIEW_ID, REVIEW_NO, BUSINESS_TYPE, BUSINESS_ID, BUSINESS_NO, OPERATION_TYPE, STATUS, SOURCE_DATA, FINAL_DATA, REMARKS, CREATOR_ID, CREATE_TIME, REVIEWER_ID, REVIEW_TIME
    </sql>

    <select id="queryReviewRecords" resultType="com.fuiou.ccs.system.vo.ReviewListVO" parameterType="com.fuiou.ccs.system.query.ReviewQuery">
        SELECT
            srr.REVIEW_ID,
            srr.REVIEW_NO,
            srr.BUSINESS_TYPE,
            srr.BUSINESS_NO,
            srr.OPERATION_TYPE,
            srr.STATUS,
            srr.CREATE_TIME,
            srr.REVIEW_TIME,
            su.EMP_NAME CREATOR_NAME,
            su2.EMP_NAME REVIEWER_NAME
        FROM
            T_SYS_REVIEW_RECORD srr
        LEFT JOIN T_SYS_USER su ON srr.CREATOR_ID = su.USER_ID
        LEFT JOIN T_SYS_USER su2 ON srr.REVIEWER_ID = su2.USER_ID
        <where>
            <if test="query.reviewNo!=null and query.reviewNo!=''">
                srr.REVIEW_NO LIKE CONCAT(#{query.reviewNo},'%')
            </if>
            <if test="query.businessType != null and query.businessType!=''">
                AND srr.BUSINESS_TYPE = #{query.businessType}
            </if>
            <if test="query.businessNO != null and query.businessNO!=''">
                AND srr.BUSINESS_NO LIKE CONCAT(#{query.businessNO},'%')
            </if>
            <if test="query.creatorId != null and query.creatorId!=''">
                AND srr.CREATOR_ID = #{query.creatorId}
            </if>
            <if test="query.creatorName != null and query.creatorName!=''">
                AND su.EMP_NAME LIKE CONCAT('%',CONCAT(#{query.creatorName},'%'))
            </if>
            <if test="query.reviewerId != null and query.reviewerId != ''">
                AND srh.REVIEWER_ID = #{query.reviewerId}
            </if>
            <if test="query.reviewerName != null and query.reviewerName!=''">
                AND su2.EMP_NAME LIKE CONCAT('%',CONCAT(#{query.reviewerName},'%'))
            </if>
            <if test="query.businessNOList != null and query.businessNOList.size() != 0">
                AND srr.BUSINESS_NO in (
                <foreach collection="query.businessNOList" item="busNo" separator=",">
                    #{busNo}
                </foreach>
                )
            </if>
        </where>
        ORDER BY srr.CREATE_TIME desc
    </select>

    <select id="queryReviewRecordInfo" resultType="com.fuiou.ccs.system.vo.ReviewVO">
        SELECT
            srr.REVIEW_ID,
            srr.REVIEW_NO,
            srr.BUSINESS_TYPE,
            srr.BUSINESS_NO,
            srr.OPERATION_TYPE,
            srr.STATUS,
            srr.SOURCE_DATA,
            srr.FINAL_DATA,
            srr.REMARKS,
            srr.CREATE_TIME,
            srr.REVIEW_TIME,
            su.EMP_NAME creator_Name,
            su2.EMP_NAME reviewer_Name
        FROM
            T_SYS_REVIEW_RECORD srr
        LEFT JOIN T_SYS_USER su ON srr.CREATOR_ID = su.USER_ID
        LEFT JOIN T_SYS_USER su2 ON srr.REVIEWER_ID = su2.USER_ID
        WHERE srr.REVIEW_ID = #{reviewId}
    </select>
</mapper>
