<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.SysFormDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.SysFormDetail">
        <id column="FORM_DETAIL_ID" property="formDetailId"/>
        <result column="FORM_ID" property="formId"/>
        <result column="COLUMNS_NAME" property="columnsName"/>
        <result column="CN_COLUMNS_NAME" property="cnColumnsName"/>
        <result column="COLUMNS_LENGTH" property="columnsLength"/>
        <result column="COLUMNS_NOT_NULL" property="columnsNotNull"/>
        <result column="COLUMNS_DESCRIBE" property="columnsDescribe"/>
        <result column="COLUMNS_TYPE" property="columnsType"/>
        <result column="COLUMNS_DEFAULT_VALUE" property="columnsDefaultValue"/>
        <result column="QUERY_TYPE" property="queryType"/>
        <result column="COLUMNS_ORDER" property="columnsOrder"/>
        <result column="DICT_CODE" property="dictCode"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATER_ID" property="updaterId"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETED" property="deletedStatus"/>
        <result column="AUTOMATIC_FILL_IN" property="automaticFillIn"/>
        <result column="IS_OUTSIDE_SOURCE" property="outsideSource"/>
        <result column="OUTSIDE_SOURCE_KEY" property="outsideSourceKey"/>
        <result column="IS_PRESET" property="presetStatus"/>
        <result column="SOURCE_URL_ID" property="sourceUrlId"/>
        <result column="IS_HEADER" property="headerStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FORM_DETAIL_ID,
        FORM_ID,
        COLUMNS_NAME,
        CN_COLUMNS_NAME,
        COLUMNS_LENGTH,
        COLUMNS_NOT_NULL,
        COLUMNS_DESCRIBE,
        COLUMNS_TYPE,
        COLUMNS_DEFAULT_VALUE,
        QUERY_TYPE,
        COLUMNS_ORDER,
        DICT_CODE,
        CREATOR_ID,
        CREATE_TIME,
        UPDATER_ID,
        UPDATE_TIME,
        IS_DELETED,
        AUTOMATIC_FILL_IN,
        IS_OUTSIDE_SOURCE,
        OUTSIDE_SOURCE_KEY,
        IS_PRESET,
        SOURCE_URL_ID,
        IS_HEADER
    </sql>


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapFormDetailVO" type="com.fuiou.ccs.system.vo.SysFormDetailVO">
        <result column="FORM_ID" property="formId"/>
        <result column="COLUMNS_NAME" property="columnsName"/>
        <result column="CN_COLUMNS_NAME" property="cnColumnsName"/>
        <result column="COLUMNS_LENGTH" property="columnsLength"/>
        <result column="COLUMNS_DESCRIBE" property="columnsDescribe"/>
        <result column="COLUMNS_TYPE" property="columnsType"/>
        <result column="COLUMNS_DEFAULT_VALUE" property="columnsDefaultValue"/>
        <result column="QUERY_TYPE" property="queryType"/>
        <result column="COLUMNS_ORDER" property="columnsOrder"/>
        <result column="DICT_CODE" property="dictCode"/>
        <result column="AUTOMATIC_FILL_IN" property="automaticFillIn"/>
        <result column="IS_OUTSIDE_SOURCE" property="outsideSource"/>
        <result column="OUTSIDE_SOURCE_KEY" property="outsideSourceKey"/>
        <result column="IS_PRESET" property="presetStatus"/>
        <result column="SOURCE_URL_ID" property="sourceUrlId"/>
        <result column="IS_HEADER" property="headerStatus"/>
    </resultMap>

    <!--根据表单id,查询对应表单查询字段相关bean集合-->
    <select id="queryFormDetailIsQueryColumnList" resultMap="BaseResultMapFormDetailVO">
        select d.FORM_ID,
               d.COLUMNS_NAME,
               d.CN_COLUMNS_NAME,
               d.COLUMNS_LENGTH,
               d.COLUMNS_DESCRIBE,
               d.COLUMNS_TYPE,
               d.COLUMNS_DEFAULT_VALUE,
               d.QUERY_TYPE,
               d.COLUMNS_ORDER,
               d.DICT_CODE,
               d.AUTOMATIC_FILL_IN,
               d.IS_OUTSIDE_SOURCE,
               d.IS_PRESET,
               d.OUTSIDE_SOURCE_KEY,
               d.SOURCE_URL_ID,
               d.IS_HEADER
        from T_SYS_FORM_DETAIL d
        where d.FORM_ID = #{formId}
          and d.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
        <if test="detailType != null and detailType != ''">
            <choose>
                <when test="detailType.equalsIgnoreCase(@com.fuiou.ccs.system.enums.SysFormEnums@QUERY_TYPE)">
                    and  d.QUERY_TYPE <![CDATA[<>]]> ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
                </when>
                <otherwise>
                    and  d.IS_HEADER in (1,3)
                </otherwise>
            </choose>
        </if>
        order by d.COLUMNS_ORDER asc
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapFormDetailDTO" type="com.fuiou.ccs.system.dto.SysFormDetailDTO">
        <result column="FORM_ID" property="formId"/>
        <result column="COLUMNS_NAME" property="columnsName"/>
        <result column="CN_COLUMNS_NAME" property="cnColumnsName"/>
        <result column="COLUMNS_LENGTH" property="columnsLength"/>
        <result column="COLUMNS_DESCRIBE" property="columnsDescribe"/>
        <result column="COLUMNS_TYPE" property="columnsType"/>
        <result column="COLUMNS_DEFAULT_VALUE" property="columnsDefaultValue"/>
        <result column="QUERY_TYPE" property="queryType"/>
        <result column="COLUMNS_ORDER" property="columnsOrder"/>
        <result column="DICT_CODE" property="dictCode"/>
        <result column="AUTOMATIC_FILL_IN" property="automaticFillIn"/>
        <result column="IS_OUTSIDE_SOURCE" property="outsideSource"/>
        <result column="OUTSIDE_SOURCE_KEY" property="outsideSourceKey"/>
        <result column="IS_PRESET" property="presetStatus"/>
        <result column="SOURCE_URL_ID" property="sourceUrlId"/>
        <result column="IS_HEADER" property="headerStatus"/>
    </resultMap>

    <!--根据表名，查询该表下全部有效字段信息集合-->
    <select id="querySysFormDetailDTO" resultMap="BaseResultMapFormDetailDTO">
        select d.FORM_ID,
               d.COLUMNS_NAME,
               d.CN_COLUMNS_NAME,
               d.COLUMNS_LENGTH,
               d.COLUMNS_DESCRIBE,
               d.COLUMNS_TYPE,
               d.COLUMNS_DEFAULT_VALUE,
               d.QUERY_TYPE,
               d.COLUMNS_ORDER,
               d.DICT_CODE,
               d.AUTOMATIC_FILL_IN,
               d.IS_OUTSIDE_SOURCE,
               d.IS_PRESET,
               d.OUTSIDE_SOURCE_KEY,
               d.SOURCE_URL_ID,
               d.IS_HEADER
        from T_SYS_FORM_DETAIL d
                 inner join T_SYS_FORM f on d.FORM_ID = f.FORM_ID
        where f.TABLE_NAME = #{formName}
          and d.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
          and f.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
        order by d.COLUMNS_ORDER asc
    </select>

    <!--根据表名，查询是否存在配置有效字段-->
    <select id="countSysFormDetailByFormName" resultType="java.lang.Integer">
        select count(*)
        from T_SYS_FORM_DETAIL d
                 inner join t_sys_form f on d.FORM_ID = f.FORM_ID
        where f.TABLE_NAME = #{formName}
          and d.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
          and f.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
    </select>

    <!-- 根据表单ID,数据库真实删除表单详情信息数据-->
    <delete id="realDelDetailByFormId">
        delete
        T_SYS_FORM_DETAIL
        where FORM_ID =
        #{formId}
    </delete>

    <!--根据表名，查询表相关字段-->
    <select id="querySysFormDetailByFormName" resultMap="BaseResultMap">
        select d.FORM_ID,
               d.COLUMNS_NAME,
               d.CN_COLUMNS_NAME,
               d.COLUMNS_LENGTH,
               d.COLUMNS_NOT_NULL,
               d.COLUMNS_DESCRIBE,
               d.COLUMNS_TYPE,
               d.COLUMNS_DEFAULT_VALUE,
               d.DICT_CODE,
               d.AUTOMATIC_FILL_IN,
               d.IS_OUTSIDE_SOURCE,
               d.OUTSIDE_SOURCE_KEY,
               d.IS_PRESET,
               d.SOURCE_URL_ID,
               d.IS_HEADER
        from T_SYS_FORM_DETAIL d
                 inner join T_SYS_FORM f on d.FORM_ID = f.FORM_ID
        where f.TABLE_NAME = #{formName}
          and d.IS_DELETED = ${@com.fuiou.ccs.system.enums.SysFormEnums@NO}
    </select>
</mapper>
