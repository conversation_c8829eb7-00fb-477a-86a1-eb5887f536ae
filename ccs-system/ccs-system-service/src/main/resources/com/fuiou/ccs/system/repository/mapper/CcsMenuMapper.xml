<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.system.repository.mapper.CcsMenuMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.system.repository.entity.CcsMenu">
        <id column="MENU_ID" property="menuId"/>
        <result column="PARENT_ID" property="parentId"/>
        <result column="NAME" property="name"/>
        <result column="ICON" property="icon"/>
        <result column="PATH" property="path"/>
        <result column="COMPONENT" property="component"/>
        <result column="SORT" property="sort"/>
        <result column="STATUS" property="status"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATER_ID" property="updaterId"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETED" property="deletedStatus"/>
        <result column="IS_MENU" property="menuStatus"/>
        <result column="CLIENT_ID" property="clientId"/>
    </resultMap>

    <!-- 通用VO查询映射结果 -->
    <resultMap id="BaseVOResultMap" type="com.fuiou.ccs.system.vo.CcsMenuVO">
        <id column="MENU_ID" property="menuId"/>
        <result column="PARENT_ID" property="parentId"/>
        <result column="NAME" property="name"/>
        <result column="ICON" property="icon"/>
        <result column="PATH" property="path"/>
        <result column="COMPONENT" property="component"/>
        <result column="SORT" property="sort"/>
        <result column="IS_MENU" property="menuStatus"/>
        <result column="CLIENT_ID" property="clientId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MENU_ID
             , PARENT_ID
             , NAME
             , ICON
             , PATH
             , COMPONENT
             , SORT
             , STATUS
             , CREATOR_ID
             , CREATE_TIME
             , UPDATER_ID
             , UPDATE_TIME
             , IS_DELETED
             , IS_MENU
             , CLIENT_ID
    </sql>
    <!-- 通过客户端ID获取所有该客户端菜单 -->
    <select id="selectMenuByClientId" resultMap="BaseVOResultMap">
        SELECT MENU_ID,
               PARENT_ID,
               NAME,
               ICON,
               PATH,
               COMPONENT,
               SORT,
               IS_MENU,
               CLIENT_ID
        FROM T_SYS_MENU
        WHERE CLIENT_ID = #{clientId,jdbcType=VARCHAR}
          AND STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
          AND IS_DELETED = 0
    </select>
    <!-- 获取某用户在当前客户端下的菜单 -->
    <!-- 这里没加 IS_DELETED的条件， 由 T_SYS_USER_ROLE 和 T_SYS_ROLE_MENU 维护关系 -->
    <select id="selectMenuByUserId" resultMap="BaseVOResultMap">
        SELECT SM.MENU_ID,
               SM.CLIENT_ID,
               SM.PARENT_ID,
               SM."NAME",
               SM.ICON,
               SM.PATH,
               SM.COMPONENT,
               SM.SORT,
               SM.IS_MENU
        FROM T_SYS_USER_ROLE SUR
                 INNER JOIN T_SYS_ROLE_MENU SRM ON SUR.USER_ID = #{userId,jdbcType=VARCHAR}
            AND SUR.ROLE_ID = SRM.ROLE_ID
                 INNER JOIN T_SYS_MENU SM ON SRM.MENU_ID = SM.MENU_ID
            AND SM.CLIENT_ID = #{clientId}
            AND SM.STATUS = ${@com.fuiou.common.constants.CommonConstants@ENABLED}
    </select>
    <!-- 获取持有某菜单的角色ID集合 -->
    <select id="selectRolesByMenuId" resultType="java.lang.Long">
        select rm.ROLE_ID
        from T_SYS_ROLE_MENU rm
        where rm.MENU_ID = #{menuId,jdbcType=BIGINT}
    </select>
    <!-- 删除菜单时，删除关联的角色菜单数据 -->
    <delete id="deleteRoleMenuByMenuId">
        delete from T_SYS_ROLE_MENU
        where MENU_ID = #{menuId,jdbcType=BIGINT}
    </delete>
</mapper>
