package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 用户 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysUserQuery对象", description = "用户查询")
public class CcsSysUserQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户名
     */
    @ApiModelProperty(value = "登录名或姓名")
    @QueryField(condition = ConditionEnum.LIKE)
    private String usernameOrEmpName;


    /**
     * 用户名
     */
    @ApiModelProperty(value = "登录名")
    @QueryField(condition = ConditionEnum.LIKE)
    private String username;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号")
    @QueryField(condition = ConditionEnum.LIKE)
    private String userCode;

    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    @QueryField(condition = ConditionEnum.LIKE)
    private String empNo;


    @ApiModelProperty(value = "员工姓名")
    @QueryField(condition = ConditionEnum.LIKE)
    private String empName;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @QueryField(condition = ConditionEnum.LIKE)
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @QueryField(condition = ConditionEnum.LIKE)
    private String mobile;


    /**
     * 状态（1：正常，0：停用）
     */
    @ApiModelProperty(value = "状态（1：正常，0：停用）")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer status;


    @ApiModelProperty(value = "角色id")
    @QueryField(condition = ConditionEnum.EQ)
    private List<Long> roleIds;

//    /**
//     * 注册日期
//     */
//    @ApiModelProperty(value = "注册日期")
//    @QueryField(condition = ConditionEnum.BETWEEN, betweenField = "registerDateEnd")
//    private LocalDate registerDateStart;
//
//    /**
//     * 注册日期结束
//     */
//    @ApiModelProperty(value = "注册日期结束")
//    private LocalDate registerDateEnd;


    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private String orgId;

    /**
     * 根据公司ID查询出包括它在内的所有子级公司/部门
     */
    @ApiModelProperty("公司ID集合，前端赋值无用，后台自己赋值")
    private List<String> orgIds;
//
//    /**
//     * 公司名称
//     */
//    @ApiModelProperty("公司名称")
//    private String orgName;


}
