package com.fuiou.ccs.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 字典数据 数据传输对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "SysDictItemDTO对象", description = "字典数据")
public class CcsDictItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("字典数据ID")
    private Long dictItemId;

    /**
     * 字典标签
     */
    @ApiModelProperty("字典标签")
    private String label;

    /**
     * 字典键值
     */
    @ApiModelProperty("字典键值")
    private String value;

    /**
     * 字典编号
     */
    @ApiModelProperty("字典编号")
    private String dictCode;

    /**
     * 字典排序
     */
    @ApiModelProperty("字典排序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
