package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工作组 视图对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "SysGroupVO对象", description = "工作组")
public class SysGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作组ID
     */
    @ApiModelProperty("工作组ID")
    private Long groupId;

    /**
     * 工作组编号
     */
    @ApiModelProperty("工作组编号")
    private String groupCode;

    /**
     * 工作组名称
     */
    @ApiModelProperty("工作组名称")
    private String name;

    /**
     * 组长ID
     */
    @ApiModelProperty("组长ID")
    private String leaderId;

    @ApiModelProperty("组长名称")
    private String leaderName;

    /**
     * 经理ID
     */
    @ApiModelProperty("经理ID")
    private String managerId;

    @ApiModelProperty("经理名称")
    private String managerName;
    /**
     * 工作组状态（1-正常，0-停用，默认为0）
     */
    @ApiModelProperty("工作组状态（1-正常，0-停用，默认为0）")
    private Integer status;

    @ApiModelProperty("岗位名称")
    private String positionName;

    /**
     * 工作组对象
     */
    @ApiModelProperty("工作组成员对象")
    private List<SysGroupMemberVO> sysGroupMember;

    /**
     * 路由id集合
     */
    @ApiModelProperty("路由id集合")
    private List<Object>   flowIds;
}
