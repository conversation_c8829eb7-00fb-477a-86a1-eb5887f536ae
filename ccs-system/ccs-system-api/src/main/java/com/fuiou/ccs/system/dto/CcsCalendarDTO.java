package com.fuiou.ccs.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 系统日历 数据传输对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "CcsCalendarDTO对象", description = "系统日历")
public class CcsCalendarDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Integer id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate date;

    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty("日")
    private Integer day;

    /**
     * 周几（0：周末）
     */
    @ApiModelProperty("周几（0：周末）")
    private Integer week;

    /**
     * 农历年
     */
    @ApiModelProperty("农历年")
    private String lunarYear;

    /**
     * 农历月
     */
    @ApiModelProperty("农历月")
    private String lunarMonth;

    /**
     * 农历日
     */
    @ApiModelProperty("农历日")
    private String lunarDay;

    /**
     * 节日
     */
    @ApiModelProperty("节日")
    private String holiday;

    /**
     * 节气
     */
    @ApiModelProperty("节气")
    private String solarTerm;

    /**
     * 生肖
     */
    @ApiModelProperty("生肖")
    private String zodiac;

    /**
     * 类型：0-工作日，1-非工作日（周六，周日），2-法定节假日
     */
    @ApiModelProperty("类型：0-工作日，1-非工作日（周六，周日），2-法定节假日")
    private Integer type;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
