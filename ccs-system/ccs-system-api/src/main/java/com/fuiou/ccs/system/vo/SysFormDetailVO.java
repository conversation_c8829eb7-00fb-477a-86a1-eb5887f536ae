package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 表单配置详情 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "TSysFormDetailVO对象", description = "表单配置详情")
public class SysFormDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formDetailId;

    /**
     * 表单配置ID
     */
    @ApiModelProperty("表单配置ID")
    private Long formId;

    /**
     * 字段英文名
     */
    @ApiModelProperty("字段英文名")
    private String columnsName;

    /**
     * 字段中文名
     */
    @ApiModelProperty("字段中文名")
    private String cnColumnsName;

    /**
     * 字段长度
     */
    @ApiModelProperty("字段长度")
    private Integer columnsLength;

    /**
     * 字段是否非空;1-非空，0-不非空（默认）
     */
    @ApiModelProperty("字段是否非空;1-非空，0-不非空（默认）")
    private Integer columnsNotNull;

    /**
     * 字段文本框内显示描述
     */
    @ApiModelProperty("字段文本框内显示描述")
    private String columnsDescribe;

    /**
     * 表单中对应字段类型
     */
    @ApiModelProperty("表单中对应字段类型")
    private String columnsType;

    /**
     * 字段默认值
     */
    @ApiModelProperty("字段默认值")
    private String columnsDefaultValue;

    /**
     * 字段是否查询条件;0-不查询（默认），1-等值查询，2-模糊查询
     */
    @ApiModelProperty("字段是否查询条件;0-不查询（默认），1-等值查询，2-模糊查询")
    private Integer queryType;


    /**
     * 字段生成顺序
     */
    @ApiModelProperty("字段生成顺序")
    private Integer columnsOrder;


    /**
     * 字典表编码;一些表单字段需从字典取值
     */
    @ApiModelProperty("字典表编码;一些表单字段需从字典取值")
    private String dictCode;

    /**
     * 自动填充0-否（默认），1-是 用于判断字段的值是自动填充还是人工填写
     */
    @ApiModelProperty("自动填充0-否（默认），1-是")
    private Integer automaticFillIn;

    /**
     * 是否外部来源:0-否（默认），1-是
     */
    @ApiModelProperty("是否外部来源:0-否（默认），1-是")
    private Integer outsideSource;

    /**
     * 对外展示字段
     */
    @ApiModelProperty("对外展示字段")
    private String outsideSourceKey;

    @ApiModelProperty("外部来源配置表ID")
    private Long sourceUrlId;

    /**
     * 该字段实际填写内容
     */
    @ApiModelProperty("该字段实际填写内容")
    private Object value;

    /**
     * 是否预置字段：0-否（默认），1-是
     */
    @ApiModelProperty("是否预置字段：0-否（默认），1-是")
    private Integer presetStatus;

    /**
     * 是否表头设置：0-不展示（默认）；1-仅表头展示；2-仅导出展示；3-表头和导出都展示
     */
    @ApiModelProperty("表头设置：0-不展示（默认）；1-仅表头展示；2-仅导出展示；3-表头和导出都展示")
    private Integer headerStatus;

}
