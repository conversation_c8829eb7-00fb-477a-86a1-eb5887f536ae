package com.fuiou.ccs.system.provider;

import com.fuiou.ccs.system.vo.SysBusinessVO;
import com.fuiou.common.api.ApiResult;

import java.util.List;
import java.util.Set;

/**
 * 业务类型，对外
 *
 * <AUTHOR>
 */
public interface CcsBusinessProvider {


    /**
     * 根据id，查询中文名称
     *
     * @param businessId
     * @return
     */
    ApiResult<SysBusinessVO> queryBusinessNameById(Long businessId);

    /**
     * 根据id，查询中文名称
     *
     * @param businessIds
     * @return
     */
    ApiResult<List<SysBusinessVO>> queryBusinessListById(Set<Long> businessIds);
}
