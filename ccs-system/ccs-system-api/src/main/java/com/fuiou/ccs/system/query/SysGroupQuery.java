package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作组 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysGroupQuery对象", description = "工作组查询")
public class SysGroupQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 工作组ID
     */
    @ApiModelProperty(value = "工作组ID")
    @QueryField(condition = ConditionEnum.EQ)
    private Long groupId;

    /**
     * 工作组编号
     */
    @ApiModelProperty(value = "工作组编号")
    @QueryField(condition = ConditionEnum.EQ)
    private String groupCode;

    /**
     * 工作组名称
     */
    @ApiModelProperty(value = "工作组名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    /**
     * 组长ID
     */
    @ApiModelProperty(value = "组长ID")
    @QueryField(condition = ConditionEnum.LIKE)
    private String leaderId;

    /**
     * 组长名称
     */
    @ApiModelProperty(value = "组长名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String leaderName;

    /**
     * 经理ID
     */
    @ApiModelProperty(value = "经理ID")
    @QueryField(condition = ConditionEnum.LIKE)
    private String managerId;

    /**
     * 经理名称
     */
    @ApiModelProperty(value = "经理名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String managerName;

    /**
     * 工作组状态（1-正常，0-停用）
     */
    @ApiModelProperty(value = "工作组状态（1-正常，0-停用）")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer status;


}
