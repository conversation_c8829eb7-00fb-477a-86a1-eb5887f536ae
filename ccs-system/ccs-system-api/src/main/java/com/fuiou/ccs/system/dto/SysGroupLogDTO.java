package com.fuiou.ccs.system.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 工作组日志 数据传输对象
 *
 * <AUTHOR> @since  1.0.0
 */
@Data
@ApiModel(value = "SysGroupLogDTO对象", description = "工作组日志")
public class SysGroupLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，工作组日志ID
     */
    @ApiModelProperty("主键，工作组日志ID")
    private Long logId;

    /**
     * 文件ID，关联T_SYS_GROUP表
     */
    @ApiModelProperty("文件ID，关联T_SYS_GROUP表")
    private Long groupId;

    /**
     * 操作类型  1：新增，2：修改，3：删除
     */
    @ApiModelProperty("操作类型  1：新增，2：修改，3：删除")
    private Integer type;

    /**
     * 邮件组成员ID，只有操作的是工作组成员时才会存在值
     */
    @ApiModelProperty("邮件组成员ID，只有操作的是工作组成员时才会存在值")
    private Long groupMemberId;

    /**
     * 日志内容
     */
    @ApiModelProperty("日志内容")
    private String content;

}
