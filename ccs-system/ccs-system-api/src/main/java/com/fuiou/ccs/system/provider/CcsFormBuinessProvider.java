package com.fuiou.ccs.system.provider;

import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import com.fuiou.common.api.ApiResult;

import java.util.List;
import java.util.Set;

/**
 * 工单模板对应业务类型
 */
public interface CcsFormBuinessProvider {


    /**
     * 根据业务类型id,查询业务类型数据
     *
     * @param businessIds
     * @return
     */
    ApiResult<List<SysFormBusinessVO>> queryBuinessList(Set<Long> businessIds);

    /**
     * 根据条件，查询formBusinessIds对象集合
     *
     * @param busIds
     * @param workIds
     * @param workSubIds
     * @return
     */
    ApiResult<List<SysFormBusinessVO>> queryFormBusinessList(Set<Long> busIds, Set<Long> workIds, Set<Long> workSubIds);

    /**
     * 根据条件查询formbusinessId值
     *
     * @param busId
     * @param workId
     * @param workSubId
     * @return
     */
    ApiResult<SysFormBusinessVO> queryFormBusiness(Long busId, Long workId, Long workSubId);

    /**
     * 根据表单id，查询该表单对应的所有业务类型表ID
     *
     * @param formId
     * @return
     */
    ApiResult<List<Long>> queryAllBusinessIdList(Long formId);
}
