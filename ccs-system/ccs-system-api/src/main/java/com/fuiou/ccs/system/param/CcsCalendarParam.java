package com.fuiou.ccs.system.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 系统日历 请求参数对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "CcsCalendarParam对象", description = "系统日历请求参数")
public class CcsCalendarParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Integer id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", required = true)
    @NotNull(message = "日期不能为空", groups = {Create.class, Update.class})
    private LocalDate date;

    /**
     * 年
     */
    @ApiModelProperty(value = "年", required = true)
    @NotNull(message = "年不能为空", groups = {Create.class, Update.class})
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty(value = "月", required = true)
    @NotNull(message = "月不能为空", groups = {Create.class, Update.class})
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty(value = "日", required = true)
    @NotNull(message = "日不能为空", groups = {Create.class, Update.class})
    private Integer day;

    /**
     * 周几（0：周末）
     */
    @ApiModelProperty(value = "周几（0：周末）", required = true)
    @NotNull(message = "周几（0：周末）不能为空", groups = {Create.class, Update.class})
    private Integer week;

    /**
     * 农历年
     */
    @ApiModelProperty(value = "农历年", required = true)
    @NotBlank(message = "农历年不能为空", groups = {Create.class, Update.class})
    @Length(max = 15, message = "农历年不能超过15个字符", groups = {Create.class, Update.class})
    private String lunarYear;

    /**
     * 农历月
     */
    @ApiModelProperty(value = "农历月", required = true)
    @NotBlank(message = "农历月不能为空", groups = {Create.class, Update.class})
    @Length(max = 12, message = "农历月不能超过12个字符", groups = {Create.class, Update.class})
    private String lunarMonth;

    /**
     * 农历日
     */
    @ApiModelProperty(value = "农历日", required = true)
    @NotBlank(message = "农历日不能为空", groups = {Create.class, Update.class})
    @Length(max = 12, message = "农历日不能超过12个字符", groups = {Create.class, Update.class})
    private String lunarDay;

    /**
     * 节日
     */
    @ApiModelProperty(value = "节日")
    @Length(max = 32, message = "节日不能超过32个字符", groups = {Create.class, Update.class})
    private String holiday;

    /**
     * 节气
     */
    @ApiModelProperty(value = "节气")
    @Length(max = 32, message = "节气不能超过32个字符", groups = {Create.class, Update.class})
    private String solarTerm;

    /**
     * 生肖
     */
    @ApiModelProperty(value = "生肖", required = true)
    @NotBlank(message = "生肖不能为空", groups = {Create.class, Update.class})
    @Length(max = 12, message = "生肖不能超过12个字符", groups = {Create.class, Update.class})
    private String zodiac;

    /**
     * 类型：0-工作日，1-非工作日（周六，周日），2-法定节假日
     */
    @ApiModelProperty(value = "类型：0-工作日，1-非工作日（周六，周日），2-法定节假日", required = true)
    @NotNull(message = "类型：0-工作日，1-非工作日（周六，周日），2-法定节假日不能为空", groups = {Create.class, Update.class})
    private Integer type;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 300, message = "备注不能超过300个字符", groups = {Create.class, Update.class})
    private String remark;

    public interface Create {
    }

    public interface Update {
    }

}
