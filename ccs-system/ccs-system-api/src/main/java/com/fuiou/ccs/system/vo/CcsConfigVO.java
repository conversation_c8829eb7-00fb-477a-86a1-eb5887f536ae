package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 配置 视图对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "SysConfigVO对象", description = "配置")
public class CcsConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Integer id;

    /**
     * 参数名
     */
    @ApiModelProperty("参数名")
    private String name;

    /**
     * 参数键
     */
    @ApiModelProperty("参数键")
    private String key;

    /**
     * 参数值
     */
    @ApiModelProperty("参数值")
    private String value;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 是否敏感
     */
    @ApiModelProperty("是否敏感")
    private Integer isSensitive;

}
