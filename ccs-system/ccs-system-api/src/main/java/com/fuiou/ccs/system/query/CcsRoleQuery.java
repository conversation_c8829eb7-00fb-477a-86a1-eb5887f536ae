package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 客服工单角色 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CcsRoleQuery对象", description = "客服工单角色查询")
public class CcsRoleQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    /**
     * 角色英文编号
     */
    @ApiModelProperty(value = "角色英文编号")
    @QueryField(condition = ConditionEnum.LIKE)
    private String code;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer sort;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    @QueryField(condition = ConditionEnum.LIKE)
    private String remark;

    /**
     * 状态：1-正常  0-停用
     */
    @ApiModelProperty(value = "状态：1-正常  0-停用")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer status;

    /**
     * 角色创建人Id
     */
    @ApiModelProperty(value = "角色创建人Id")
    @QueryField(condition = ConditionEnum.EQ)
    private String creatorId;

    /**
     * 客户端Id
     */
    @ApiModelProperty(value = "客户端Id")
    @QueryField(condition = ConditionEnum.EQ)
    private String clientId;
}
