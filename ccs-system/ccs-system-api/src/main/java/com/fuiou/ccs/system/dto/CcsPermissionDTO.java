package com.fuiou.ccs.system.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 客服工单权限 数据传输对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "CcsPermissionDTO对象", description = "客服工单权限")
public class CcsPermissionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long permissionId;

    /**
     * 权限名称
     */
    @ApiModelProperty("权限名称")
    private String name;

    /**
     * 菜单ID
     */
    @ApiModelProperty("菜单ID")
    private Long menuId;

    /**
     * URL权限标识
     */
    @ApiModelProperty("URL权限标识")
    private String urlPerm;

    /**
     * 按钮权限标识
     */
    @ApiModelProperty("按钮权限标识")
    private String btnPerm;

    /**
     * 客户端Id
     */
    @ApiModelProperty("客户端Id")
    private String clientId;
}
