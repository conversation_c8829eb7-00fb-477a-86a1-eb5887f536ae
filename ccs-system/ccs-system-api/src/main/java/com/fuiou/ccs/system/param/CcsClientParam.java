package com.fuiou.ccs.system.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 客户端 请求参数对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "CcsClientParam对象", description = "客户端请求参数")
public class CcsClientParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Integer id;

    /**
     * 客户端ID
     */
    @ApiModelProperty(value = "客户端ID", required = true)
    @NotBlank(message = "客户端ID不能为空", groups = {Create.class, Update.class})
    @Length(max = 32, message = "客户端ID不能超过32个字符", groups = {Create.class, Update.class})
    private String clientId;

    /**
     * 客户端密钥
     */
    @ApiModelProperty(value = "客户端密钥", required = true)
    @NotBlank(message = "客户端密钥不能为空", groups = {Create.class})
    @Length(max = 128, message = "客户端密钥不能超过128个字符", groups = {Create.class, Update.class})
    private String clientSecret;

    /**
     * 资源集合
     */
    @ApiModelProperty(value = "资源集合")
    @Length(max = 256, message = "资源集合不能超过256个字符", groups = {Create.class, Update.class})
    private String resourceIds;

    /**
     * 授权范围
     */
    @ApiModelProperty(value = "授权范围", required = true)
    @Length(max = 256, message = "授权范围不能超过256个字符", groups = {Create.class, Update.class})
    private String scope;

    /**
     * 授权类型
     */
    @ApiModelProperty(value = "授权类型", required = true)
    @NotBlank(message = "授权类型不能为空", groups = {Create.class, Update.class})
    @Length(max = 256, message = "授权类型不能超过256个字符", groups = {Create.class, Update.class})
    private String authorizedGrantTypes;

    /**
     * 回调地址
     */
    @ApiModelProperty(value = "回调地址")
    @Length(max = 256, message = "回调地址不能超过256个字符", groups = {Create.class, Update.class})
    private String webServerRedirectUri;

    /**
     * 权限
     */
    @ApiModelProperty(value = "权限")
    @Length(max = 256, message = "权限不能超过256个字符", groups = {Create.class, Update.class})
    private String authorities;

    /**
     * 令牌过期秒数
     */
    @ApiModelProperty(value = "令牌过期秒数", required = true)
    @NotNull(message = "令牌过期秒数不能为空", groups = {Create.class, Update.class})
    private Integer accessTokenValidity;

    /**
     * 刷新令牌过期秒数
     */
    @ApiModelProperty(value = "刷新令牌过期秒数", required = true)
    @NotNull(message = "刷新令牌过期秒数不能为空", groups = {Create.class, Update.class})
    private Integer refreshTokenValidity;

    /**
     * 附加说明
     */
    @ApiModelProperty(value = "附加说明")
    @Length(max = 512, message = "附加说明不能超过512个字符", groups = {Create.class, Update.class})
    private String additionalInformation;

    /**
     * 自动授权
     */
    @ApiModelProperty(value = "自动授权")
    @Length(max = 256, message = "自动授权不能超过256个字符", groups = {Create.class, Update.class})
    private String autoapprove;

    /**
     * 状态（1：启用，0：停用）
     */
    @ApiModelProperty(value = "状态（1：启用，0：停用）", required = true)
    @NotNull(message = "状态（1：启用，0：停用）不能为空", groups = {Create.class, Update.class})
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 256, message = "备注不能超过256个字符", groups = {Create.class, Update.class})
    private String remark;

    public interface Create { }

    public interface Update { }

}
