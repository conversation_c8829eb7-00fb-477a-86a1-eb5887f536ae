package com.fuiou.ccs.system.enums;

import com.fuiou.common.enums.IEnum;

/**
 * 业务表(类型配置)
 *
 * <AUTHOR>
 */
public class SysBusinessEnums {

    public static final int BUS_TYPE = 1;

    public static final int WORK_TYPE = 2;

    public static final int WORK_SUB_TYPE = 3;

    public static final int SUB_ELEMENT = 4;

    public enum BusLevelEnums implements IEnum {
        BUS_TYPE(SysBusinessEnums.BUS_TYPE, "业务类型"),
        WORK_TYPE(SysBusinessEnums.WORK_TYPE, "工单类型"),
        WORK_SUB_TYPE(SysBusinessEnums.WORK_SUB_TYPE, "工单子类"),
        SUB_ELEMENT(SysBusinessEnums.SUB_ELEMENT, "子类要素");
        final int type;
        final String msg;

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        BusLevelEnums(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param type
         * @return
         */
        public static BusLevelEnums getInstance(Integer type) {
            if (type != null) {
                for (BusLevelEnums enums : BusLevelEnums.values()) {
                    if (enums.getValue() == type) {
                        return enums;
                    }
                }
            }
            throw new NullPointerException("BusLevelEnums.status传入值在枚举类中不存在");
        }
    }


}
