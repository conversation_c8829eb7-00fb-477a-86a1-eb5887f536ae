package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户角色信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "SysUserRoleVO对象", description = "用户角色")
public class CcsUserRoleVO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private String roleId;

    /**
     * 角色编号
     */
    @ApiModelProperty("角色编号")
    private String roleCode;

    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * 角色显示顺序
     */
    @ApiModelProperty("角色显示顺序")
    private Integer sort;

}
