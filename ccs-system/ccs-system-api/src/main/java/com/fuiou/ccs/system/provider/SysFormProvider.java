package com.fuiou.ccs.system.provider;

import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.vo.SysFormVO;
import com.fuiou.common.api.ApiResult;

import java.util.List;
import java.util.Set;

/**
 * 表单接口
 *
 * <AUTHOR>
 */
public interface SysFormProvider {

    /**
     * 根据入参表名，查询该表，对应的相关信息
     *
     * @param formNames 表名集合，传null则查全部使用中的表信息
     * @return
     */
    ApiResult<List<SysFormVO>> querySysFormTabs(List<String> formNames);

    /**
     * 根据入参表名，查询该表，对应的所有有效字段信息
     *
     * @param formName
     * @return
     */
    ApiResult<List<SysFormDetailDTO>> querySysFormDetailDTO(String formName);


    /**
     * 根据formId查询表单配置
     *
     * @param formId formId
     * @return
     */
    ApiResult<SysFormVO> querySysFormById(Long formId);

    ApiResult<List<SysFormVO>> querySysFormByIds(Set<Long> formIds);

    /**
     * 更新表单使用状态
     * @param formId
     */
    ApiResult<Void> updateFormUsedStatus(Long formId);

    /**
     * 根据工单三要素获取表单配置(包括非预制配置详情集合)
     *
     * @param busId     业务类型
     * @param workId    工单类型
     * @param workSubId 工单子类
     * @return  ApiResult<SysFormVO>
     */
    ApiResult<SysFormVO> queryFormDataByThreeElement(Long busId, Long workId, Long workSubId);

    ApiResult<SysFormVO> querySysForm(String formName);
}
