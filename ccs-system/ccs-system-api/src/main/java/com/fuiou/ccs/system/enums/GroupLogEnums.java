package com.fuiou.ccs.system.enums;

import com.fuiou.common.enums.IEnum;

import java.util.Arrays;

/**
 * 群组日志常量
 */
public class GroupLogEnums {
    /**
     * type:1-新增;2-修改；3-删除
     */
    public static final Integer TYPE_SAVE = 1;
    public static final Integer TYPE_UPDATE = 2;
    public static final Integer TYPE_DELETE = 3;

    /**
     * 类型
     */
    public enum TypeEnums implements IEnum {
        TYPE_SAVE(1, "新增"),
        TYPE_UPDATE(2, "修改"),
        TYPE_DELETE(3, "删除");
        final int type;
        final String msg;

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        TypeEnums(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param type
         * @return
         */
        public static GroupLogEnums.TypeEnums getInstance(Integer type) {
            return Arrays.stream(GroupLogEnums.TypeEnums.values()).filter(x -> x.getValue() == type).findFirst().orElseThrow(() -> new NullPointerException(
                    "GroupLogEnums传入值在枚举类中不存在"));
        }
    }
}
