package com.fuiou.ccs.system.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/27 11:21
 */
public interface ReviewEnums {

    enum ReviewModule {

        USER("user", "员工"),
        ROLE("role", "角色"),
        ROUTE("route", "路由"),
        WORKING_GROUP("working_group", "工作组"),
        FORM_WORK("form_work", "模板");

        final String type;
        final String desc;

        ReviewModule(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public String getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

    enum OperationEnum {
        INS(1, "新增"),
        UPD(2, "修改"),
        ABOLISH(3, "逻辑删除"),
        DEL(4, "物理删除");

        final Integer type;
        final String desc;

        OperationEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

    enum StatusEnum {
        TO_BE_REVIEWED(1, "待复核"),
        APPROVED(2, "复核通过"),
        REVIEW_FAILED(3, "复核拒绝"),
        OBSOLETE(4, "作废");

        StatusEnum(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        Integer status;
        String desc;

        public Integer getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }
    }

}
