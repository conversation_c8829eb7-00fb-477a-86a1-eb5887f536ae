package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 配置 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysConfigQuery对象", description = "配置查询")
public class CcsConfigQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数名
     */
    @ApiModelProperty(value = "参数名")
    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键")
    @QueryField(condition = ConditionEnum.LIKE)
    private String key;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    @QueryField(condition = ConditionEnum.LIKE)
    private String value;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @QueryField(condition = ConditionEnum.LIKE)
    private String remark;

    /**
     * 是否敏感
     */
    @ApiModelProperty(value = "是否敏感")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer isSensitive;


}
