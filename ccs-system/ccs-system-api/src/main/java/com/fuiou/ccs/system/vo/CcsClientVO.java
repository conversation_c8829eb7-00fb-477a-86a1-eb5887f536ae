package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户端 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "CcsClientVO对象", description = "客户端")
public class CcsClientVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Integer id;

    /**
     * 客户端ID
     */
    @ApiModelProperty("客户端ID")
    private String clientId;

    /**
     * 资源集合
     */
    @ApiModelProperty("资源集合")
    private String resourceIds;

    /**
     * 授权范围
     */
    @ApiModelProperty("授权范围")
    private String scope;

    /**
     * 授权类型
     */
    @ApiModelProperty("授权类型")
    private String authorizedGrantTypes;

    /**
     * 回调地址
     */
    @ApiModelProperty("回调地址")
    private String webServerRedirectUri;

    /**
     * 权限
     */
    @ApiModelProperty("权限")
    private String authorities;

    /**
     * 令牌过期秒数
     */
    @ApiModelProperty("令牌过期秒数")
    private Integer accessTokenValidity;

    /**
     * 刷新令牌过期秒数
     */
    @ApiModelProperty("刷新令牌过期秒数")
    private Integer refreshTokenValidity;

    /**
     * 附加说明
     */
    @ApiModelProperty("附加说明")
    private String additionalInformation;

    /**
     * 自动授权
     */
    @ApiModelProperty("自动授权")
    private String autoapprove;

    /**
     * 状态（1：启用，0：停用）
     */
    @ApiModelProperty("状态（1：启用，0：停用）")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
