package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 表单配置日志 视图对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "TSysFormLogVO对象", description = "表单配置日志")
public class SysFormLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formLogId;

    /**
     * 表单配置ID
     */
    @ApiModelProperty("表单配置ID")
    private Long formId;

    /**
     * 修改描述JSON
     */
    @ApiModelProperty("修改描述JSON")
    private String modifyDescribe;

}
