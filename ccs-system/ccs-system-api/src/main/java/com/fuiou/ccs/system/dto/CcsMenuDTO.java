package com.fuiou.ccs.system.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 客服工单菜单 数据传输对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "CcsMenuDTO对象", description = "客服工单菜单")
public class CcsMenuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单Id
     */
    private Long menuId;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private Long parentId;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String name;

    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String icon;

    /**
     * 路由路径
     */
    @ApiModelProperty("路由路径")
    private String path;

    /**
     * 组件路径
     */
    @ApiModelProperty("组件路径")
    private String component;

    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    private Integer sort;

    /**
     * 状态（1(默认)：启用，0：停用）
     */
    @ApiModelProperty("状态（1(默认)：启用，0：停用）")
    private Integer status;

    /**
     * 是否是菜单  1:是(默认)  0:否
     */
    @ApiModelProperty("是否是菜单  1:是(默认)  0:否")
    private Integer menuStatus;

    /**
     * 客户端Id
     */
    @ApiModelProperty("客户端Id")
    private String clientId;
}
