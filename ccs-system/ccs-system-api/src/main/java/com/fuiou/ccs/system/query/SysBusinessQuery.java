package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 业务表(类型配置) 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysBusinessQuery对象", description = "业务表(类型配置)查询")
public class SysBusinessQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型配置ID")
    @QueryField(condition = ConditionEnum.EQ)
    private Long busId;

    /**
     * 上级业务ID
     */
    @ApiModelProperty(value = "上级业务ID")
    @QueryField(condition = ConditionEnum.EQ)
    private Long parentId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    /**
     * 业务树的级别(1-业务类型，2-工单类型，3-工单子类型)
     */
    @ApiModelProperty(value = "业务树的级别(1-业务类型，2-工单类型，3-工单子类型)")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer busLevel;


    /**
     * 关联组织代码
     */
    @ApiModelProperty(value = "关联组织代码")
    @QueryField(condition = ConditionEnum.EQ)
    private String orgCode;

}
