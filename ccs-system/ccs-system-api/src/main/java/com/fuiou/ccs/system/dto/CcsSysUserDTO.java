package com.fuiou.ccs.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 数据传输对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "SysUserDTO对象", description = "用户")
public class CcsSysUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("userId")
    private String userId;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号")
    private String userCode;

    /**
     * 员工工号
     */
    @ApiModelProperty("员工工号")
    private String empNo;

    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名")
    private String empName;


    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 头像地址
     */
    @ApiModelProperty("头像地址")
    private String avatar;

    /**
     * 状态（1：正常，0：停用）
     */
    @ApiModelProperty("状态（1：正常，0：停用）")
    private Integer status;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<String> roles;

    /**
     * 角色Id
     */
    @ApiModelProperty("角色Id")
    private List<Long> roleIds;

    @ApiModelProperty("角色名称")
    private String rolesName;

    @ApiModelProperty("用户所在组集合Id集合")
    private List<Long> groupIds;

    @ApiModelProperty("公司层级集合")
    private List<String> orgIds;

    /**
     * 注册日期
     */
    @ApiModelProperty("注册日期")
    private LocalDate registerDate;

    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private String orgId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String orgName;


    /**
     * 最近登录时间
     */
    @ApiModelProperty("最近登录时间")
    private LocalDateTime lastLoginDate;

    /**
     * 最近登录IP
     */
    @ApiModelProperty("最近登录IP")
    private String lastLoginIp;


    /**
     * 复核状态（0-待复核，默认；1-复核通过；2-复核不通过
     */
    @ApiModelProperty("复核状态（0-待复核，默认；1-复核通过；2-复核不通过")
    private Integer checkedStatus;

    @ApiModelProperty("路由编号集合")
    private List<Object> flowIds;

    @ApiModelProperty("当前用户待处理任务个数")
    private Long nowNums;

}
