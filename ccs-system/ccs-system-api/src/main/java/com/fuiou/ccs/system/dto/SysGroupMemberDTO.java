package com.fuiou.ccs.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作组成员 数据传输对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "SysGroupItemDTO对象", description = "工作组成员")
public class SysGroupMemberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作组成员ID
     */
    @ApiModelProperty("工作组成员ID")
    private Long groupMemberId;


    /**
     * 工作组成员的用户ID
     */
    @ApiModelProperty("工作组成员的用户ID")
    private String userId;

    /**
     * 所属工作组ID
     */
    @ApiModelProperty("所属工作组ID")
    private Long groupId;

    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private String creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 工作组成员状态（1-正常，0-停用，默认为0）
     */
    @ApiModelProperty("工作组成员状态（1-正常，0-停用，默认为0）")
    private Integer status;

}
