package com.fuiou.ccs.system.enums;

import com.fuiou.common.enums.IEnum;

/**
 * <AUTHOR>
 * @date 2021年12月07日 13:35
 * @Description 序列枚举值
 */
public interface SequenceEnums {
    /**
     * 序列刷新周期枚举值
     */
    enum LoopTypeEnums implements IEnum {

        YEAR(1, "年"),
        MONTH(2, "月"),
        DAY(3, "日");

        final int type;
        final String msg;

        LoopTypeEnums(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }
    }


    /**
     * 需要序列的业务枚举
     */
    enum ServiceType implements IEnum {
        WORK_ORDER(1, "WO", "工单新增序列编号", LoopTypeEnums.YEAR);

        final int type;
        final String msg;
        final String desc;
        final LoopTypeEnums loopType;

        ServiceType(int type, String msg, String desc, LoopTypeEnums loopType) {
            this.type = type;
            this.msg = msg;
            this.desc = desc;
            this.loopType = loopType;
        }

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        public String getDesc() {
            return desc;
        }

        public Integer getLoopType() {
            return loopType.getValue();
        }
    }
}
