package com.fuiou.ccs.system.enums;

import com.fuiou.common.enums.IEnum;

/**
 * 日历类型：0-工作日，1-非工作日（周六，周日），2-法定节假日
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum CcsCalendarTypeEnum implements IEnum {

    /**
     * 工作日
     */
    WORKING_DAY(0),
    /**
     * 非工作日（周六，周日）
     */
    NON_WORKING_DAY(1),
    /**
     * 法定节假日
     */
    STATUTORY_HOLIDAYS(2);

    final int type;

    CcsCalendarTypeEnum(int type) {
        this.type = type;
    }

    @Override
    public int getValue() {
        return type;
    }

}
