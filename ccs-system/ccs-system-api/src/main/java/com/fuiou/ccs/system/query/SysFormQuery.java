package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 表单配置 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TSysFormQuery对象", description = "表单配置查询")
public class SysFormQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formId;

    /**
     * 数据库表英文名
     */
    @ApiModelProperty(value = "数据库表英文名")
    @QueryField(condition = ConditionEnum.EQ)
    private String tableName;

    /**
     * 模板编号（MB0001）
     */
    @ApiModelProperty("模板编号（自动生成，例如MB0001）")
    private String formCode;

    /**
     * 数据库表中文名
     */
    @ApiModelProperty(value = "数据库表中文名")
    @QueryField(condition = ConditionEnum.LIKE)
    private String cnTableName;

    /**
     * 复核状态（0-待复核，默认；1-复核通过；2-复核不通过
     */
    @ApiModelProperty(value = "复核状态（0-待复核，默认；1-复核通过；2-复核不通过")
    @QueryField(condition = ConditionEnum.EQ, column = "IS_CHECKED")
    private Integer checkedStatus;

    /**
     * 表是否已使用（0-未使用，默认；1-已使用）
     */
    @ApiModelProperty(value = "表是否已使用（0-未使用，默认；1-已使用）")
    @QueryField(condition = ConditionEnum.EQ, column = "IS_USED")
    private Integer usedStatus;

    /**
     * 表是否已创建（0-未创建，默认；1-已创建）
     */
    @ApiModelProperty(value = "表是否已创建（0-未创建，默认；1-已创建）")
    @QueryField(condition = ConditionEnum.EQ, column = "IS_CREATE")
    private Integer createStatus;

    /**
     * 状态：1-正常（默认），0-停用
     */
    @ApiModelProperty("状态：1-正常（默认），0-停用")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer status;

}
