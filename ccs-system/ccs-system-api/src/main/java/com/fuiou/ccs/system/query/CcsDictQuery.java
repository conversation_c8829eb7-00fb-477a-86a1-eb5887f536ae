package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 字典 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SysDictQuery对象", description = "字典查询")
public class CcsDictQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    @QueryField(condition = ConditionEnum.LIKE)
    private String name;

    /**
     * 字典编号
     */
    @ApiModelProperty(value = "字典编号")
    @QueryField(condition = ConditionEnum.LIKE)
    private String code;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @QueryField(condition = ConditionEnum.LIKE)
    private String remark;


}
