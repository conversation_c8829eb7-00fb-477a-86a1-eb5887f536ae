package com.fuiou.ccs.system.constants;

/**
 * 表单配置常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SysFormConstants {

    /**
     * 字典配置中存放的，抄送人邮箱编码
     */
    public static final String FORM_CC_EMAIL = "FORM_CC_EMAIL";

    /**
     * 动态模板的主键
     */
    public static final String PRIMARY_KEY = "FORM_INFO_ID";
    /**
     * 工单模板必配字段
     * 修改时，记得检查该类，做同步调整 SysFormEventVO
     */
    public static final String FORM_TEMPLATE = "[{\"columnsName\":\"FORM_INFO_ID\",\"cnColumnsName\":\"主键\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"}"
            + ",{\"columnsName\":\"FORM_ID\",\"cnColumnsName\":\"表单ID\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"FORM_BUSINESS_ID\",\"cnColumnsName\":\"表单类型ID\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            +           // ",{\"columnsName\":\"BUS_ID\",\"cnColumnsName\":\"业务类型\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"1\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"+
            // ",{\"columnsName\":\"WORK_ID\",\"cnColumnsName\":\"工单类型\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"1\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"+
            // ",{\"columnsName\":\"WORK_SUB_ID\",\"cnColumnsName\":\"工单子类\",\"columnsNotNull\":\"1\",\"columnsType\":\"BIGINT\",\"queryType\":\"1\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"+
            ",{\"columnsName\":\"SUB_ELEMENT\",\"cnColumnsName\":\"子类要素\",\"columnsNotNull\":\"0\",\"columnsType\":\"BIGINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"DICT_CODE\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"ASSOCIATED_INFO_ID\",\"cnColumnsName\":\"关联工单ID\",\"columnsNotNull\":\"0\",\"columnsType\":\"BIGINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"CALL_NUMBER\",\"cnColumnsName\":\"来电号码\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"CALLER_NAME\",\"cnColumnsName\":\"来电人称呼\",\"columnsNotNull\":\"0\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"CALLER_TYPE\",\"cnColumnsName\":\"来电人类型\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"DICT_CODE\":\"1\",\"automaticFillIn\":\"0\"},"
            +            //UI说去掉此字段",{\"columnsName\":\"CALLED_BACK\",\"cnColumnsName\":\"联系电话\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"+
            ",{\"columnsName\":\"FORM_EVENT_CODE\",\"cnColumnsName\":\"事件编号\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"EVENT_LEVEL\",\"cnColumnsName\":\"事件级别\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"200\" ,\"queryType\":\"2\",\"presetStatus\":\"1\" ,\"DICT_CODE\":\"EVENT_LEVEL\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"EVENT_DATE\",\"cnColumnsName\":\"事件日期\",\"columnsNotNull\":\"1\",\"columnsType\":\"DATE\",\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"EVENT_STATUS\",\"cnColumnsName\":\"事件状态\",\"columnsNotNull\":\"1\",\"columnsType\":\"SMALLINT\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"DICT_CODE\":\"EVENT_STATUS\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"EVENT_DESC\",\"cnColumnsName\":\"事件描述\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"9000\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"CREATOR_ID\",\"cnColumnsName\":\"创建人\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"25\" ,\"queryType\":\"2\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            +            //风控是否可查看 1-可查看，0-不可查看
            ",{\"columnsName\":\"RISK_TO_VIEW\",\"cnColumnsName\":\"风控是否可查看\",\"columnsNotNull\":\"1\",\"columnsType\":\"SMALLINT\",\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"},"
            + ",{\"columnsName\":\"CREATE_TIME\",\"cnColumnsName\":\"创建时间\",\"columnsNotNull\":\"1\",\"columnsType\":\"TIMESTAMP\",\"columnsDefaultValue\":\" CURRENT TIMESTAMP \" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"UPDATE_TIME\",\"cnColumnsName\":\"修改时间\",\"columnsNotNull\":\"0\",\"columnsType\":\"TIMESTAMP\",\"columnsDefaultValue\":\" CURRENT TIMESTAMP \" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"UPDATER_ID\",\"cnColumnsName\":\"修改人\",\"columnsNotNull\":\"0\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"25\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"PROCESS_KEY\",\"cnColumnsName\":\"路由实例ID\",\"columnsNotNull\":\"1\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"50\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            + ",{\"columnsName\":\"FILE_ID\",\"cnColumnsName\":\"附件ID\",\"columnsNotNull\":\"0\",\"columnsType\":\"VARCHAR\",\"columnsLength\":\"1100\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"},"
            +      //去掉",{\"columnsName\":\"IS_URGENT\",\"cnColumnsName\":\"是否加急\",\"columnsNotNull\":\"1\",\"columnsType\":\"SMALLINT\",\"columnsDefaultValue\":\"0\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"0\"}," +
            ",{\"columnsName\":\"IS_DELETED\",\"cnColumnsName\":\"是否删除\",\"columnsNotNull\":\"1\",\"columnsType\":\"SMALLINT\",\"columnsDefaultValue\":\"0\" ,\"queryType\":\"0\",\"presetStatus\":\"1\",\"automaticFillIn\":\"1\"}]";

}
