package com.fuiou.ccs.system.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 配置 请求参数对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "SysConfigParam对象", description = "配置请求参数")
public class CcsConfigParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Integer id;

    /**
     * 参数名
     */
    @ApiModelProperty(value = "参数名", required = true)
    @NotBlank(message = "参数名不能为空", groups = {Create.class, Update.class})
    @Length(max = 64, message = "参数名不能超过64个字符", groups = {Create.class, Update.class})
    private String name;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键", required = true)
    @NotBlank(message = "参数键不能为空", groups = {Create.class, Update.class})
    @Length(max = 50, message = "参数键不能超过50个字符", groups = {Create.class, Update.class})
    private String key;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值", required = true)
    @NotBlank(message = "参数值不能为空", groups = {Create.class, Update.class})
    @Length(max = 64, message = "参数值不能超过64个字符", groups = {Create.class, Update.class})
    private String value;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 250, message = "备注不能超过250个字符", groups = {Create.class, Update.class})
    private String remark;

    /**
     * 是否敏感
     */
    @ApiModelProperty(value = "是否敏感", required = true)
    @NotNull(message = "是否敏感不能为空", groups = {Create.class, Update.class})
    private Integer isSensitive;

    public interface Create { }

    public interface Update { }

}
