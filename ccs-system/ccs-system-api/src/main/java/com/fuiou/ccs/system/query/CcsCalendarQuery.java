package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 系统日历 查询对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CcsCalendarQuery对象", description = "系统日历查询")
public class CcsCalendarQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @QueryField(condition = ConditionEnum.BETWEEN, betweenField = "dateEnd")
    private LocalDate dateStart;

    /**
     * 日期结束
     */
    @ApiModelProperty(value = "日期结束")
    private LocalDate dateEnd;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty(value = "日")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer day;

    /**
     * 周几（0：周末）
     */
    @ApiModelProperty(value = "周几（0：周末）")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer week;

    /**
     * 农历年
     */
    @ApiModelProperty(value = "农历年")
    @QueryField(condition = ConditionEnum.LIKE)
    private String lunarYear;

    /**
     * 农历月
     */
    @ApiModelProperty(value = "农历月")
    @QueryField(condition = ConditionEnum.LIKE)
    private String lunarMonth;

    /**
     * 农历日
     */
    @ApiModelProperty(value = "农历日")
    @QueryField(condition = ConditionEnum.LIKE)
    private String lunarDay;

    /**
     * 节日
     */
    @ApiModelProperty(value = "节日")
    @QueryField(condition = ConditionEnum.LIKE)
    private String holiday;

    /**
     * 节气
     */
    @ApiModelProperty(value = "节气")
    @QueryField(condition = ConditionEnum.LIKE)
    private String solarTerm;

    /**
     * 生肖
     */
    @ApiModelProperty(value = "生肖")
    @QueryField(condition = ConditionEnum.LIKE)
    private String zodiac;

    /**
     * 类型：0-工作日，1-非工作日（周六，周日），2-法定节假日
     */
    @ApiModelProperty(value = "类型：0-工作日，1-非工作日（周六，周日），2-法定节假日")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer type;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @QueryField(condition = ConditionEnum.LIKE)
    private String remark;


}
