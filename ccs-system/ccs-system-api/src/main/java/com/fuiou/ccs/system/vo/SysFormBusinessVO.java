package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 表单业务类型 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "TSysFormBusinessVO对象", description = "表单业务类型")
public class SysFormBusinessVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formBusinessId;

    /**
     * 表单配置ID
     */
    @ApiModelProperty("表单配置ID")
    private Long formId;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private Long busId;


    private String busOrgCode;
    /**
     * 业务类型名称
     */
    @ApiModelProperty("业务类型名称")
    private String busName;

    /**
     * 工单类型
     */
    @ApiModelProperty("工单类型")
    private Long workId;

    /**
     * 工单类型名称
     */
    @ApiModelProperty("工单类型名称")
    private String workName;

    /**
     * 工单子类
     */
    @ApiModelProperty("工单子类")
    private Long workSubId;


    /**
     * 工单子类名称
     */
    @ApiModelProperty("工单子类名称")
    private String workSubName;

    /**
     * 子类要素
     */
    @ApiModelProperty("子类要素")
    private Long subElement;


    /**
     * 子类要素名称
     */
    @ApiModelProperty("子类要素名称")
    private String subElementName;

    /**
     * T_SYS_FORM 表单状态
     */
    @ApiModelProperty("主表状态:1-正常（默认），0-停用")
    private Integer formStatus;


    @ApiModelProperty("模板编号")
    private String formCode;

    /**
     * 数据库表中文名
     */
    @ApiModelProperty("模板数据库表中文名")
    private String cnTableName;

    /**
     * 数据库表名
     */
    @ApiModelProperty("数据库表名")
    private  String  tableName;
}
