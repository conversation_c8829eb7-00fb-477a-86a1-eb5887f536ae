package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 表单配置 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "TSysFormVO对象", description = "表单配置")
public class SysFormVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formId;

    /**
     * 模板编号（MB0001）
     */
    @ApiModelProperty("模板编号")
    private String formCode;

    /**
     * 数据库表英文名
     */
    @ApiModelProperty("数据库表英文名")
    private String tableName;

    /**
     * 数据库表中文名
     */
    @ApiModelProperty("数据库表中文名")
    private String cnTableName;

    /**
     * 配置json信息
     */
    @ApiModelProperty("配置json信息")
    private String configJson;

    /**
     * 复核状态（0-待复核，默认；1-复核通过；2-复核不通过
     */
    @ApiModelProperty("复核状态（0-待复核，默认；1-复核通过；2-复核不通过")
    private Integer checkedStatus;

    /**
     * 表是否已使用（0-未使用，默认；1-已使用）
     */
    @ApiModelProperty("表是否已使用（0-未使用，默认；1-已使用）")
    private Integer usedStatus;

    /**
     * 表是否已创建（0-未创建，默认；1-已创建）
     */
    @ApiModelProperty("表是否已创建（0-未创建，默认；1-已创建）")
    private Integer createStatus;

    /**
     * 状态：1-正常（默认），0-停用
     */
    @ApiModelProperty("状态：1-正常（默认），0-停用")
    private Integer status;

    /**
     * 模板是否隐藏：0-否；1-隐藏
     */
    @ApiModelProperty("是否隐藏：0-否；1-隐藏")
    private Integer hiddenStatus;


    /**
     * 表单创建时间
     */
    @ApiModelProperty("表单创建时间")
    private LocalDateTime createTime;

    /**
     * 表单业务类型
     */
    @ApiModelProperty("表单业务类型")
    private List<SysFormBusinessVO> formBusinessList;

    /**
     * 表单业务类型,名称拼接，分页查询用
     */
    @ApiModelProperty("表单业务类型,名称拼接，分页查询用")
    private String formBusinessNames;

    /**
     * 表单配置详情
     */
    @ApiModelProperty("表单配置详情")
    private List<SysFormDetailVO> formDetailList;

    /**
     * 表单配置日志
     */
    @ApiModelProperty("表单配置日志")
    private List<SysFormLogVO> formLogList;

    /**
     * 是否已删除（0-未删除，默认；1-已删除）
     */
    @ApiModelProperty("是否已删除")
    private Integer deletedStatus;

    /**
     * 新增工单时候需要前端返回该字段，所以在SysFormVO中增加formBusinessId用于前端接收
     */
    @ApiModelProperty("表单业务类型主键")
    private Long formBusinessId;

    @ApiModelProperty(value = "来电人类型(字典取值,动态配置)")
    private String callerDictCode;

    @ApiModelProperty(value = "工单子类ID拼接，分页查询用")
    private String workSubIds;

    /**
     * 动态表头字段集合
     */
    @ApiModelProperty("动态表头字段集合")
    private List<SysFormDetailVO> headerDetailList;

    /**
     * 当前工单对应的所有业务id
     */
    @ApiModelProperty("当前工单对应的所有业务id")
    private List<Long> ccsAllbusIdListByFormId;
}
