package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 机构 视图对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "SysOrgVO对象", description = "机构")
public class CcsOrgVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private String orgId;

    /**
     * 上级机构ID
     */
    @ApiModelProperty("上级机构ID")
    private String parentId;

    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String name;

    /**
     * 机构树中的级别（上级机构的级别+1）
     */
    @ApiModelProperty("机构树中的级别（上级机构的级别+1）")
    private Integer orgLevel;

    /**
     * 机构状态（1-正常，0-停用）
     */
    @ApiModelProperty("机构状态（1-正常，0-停用）")
    private Integer status;

    @ApiModelProperty("子集")
    private List<CcsOrgVO> children;

}
