package com.fuiou.ccs.system.provider;

import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.common.api.ApiResult;

import java.util.List;

/**
 * 表单配置详情接口
 *
 * <AUTHOR>
 */
public interface SysFormDetailProvider {

    /**
     * 根据表单表名查询表单配置详情集合
     *
     * @param formName 表名
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    ApiResult<List<SysFormDetailDTO>> querySysFormDetailByFormName(String formName);

    /**
     * 根据formId获取表单配置详情集合
     *
     * @param formId   表单配置ID
     * @param partFlag true 非预制字段， false 预制字段
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    ApiResult<List<SysFormDetailDTO>> queryFormDetailByFormId(Long formId, boolean partFlag);

    /**
     * 根据formId查询模板预置字段集合
     *
     * @param formId 表单ID
     * @return ApiResult<List < SysFormDetailDTO>>
     */
    ApiResult<List<SysFormDetailDTO>> queryPresetFormDetailByFormId(Long formId);
}
