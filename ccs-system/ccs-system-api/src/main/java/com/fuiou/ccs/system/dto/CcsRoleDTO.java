package com.fuiou.ccs.system.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 客服工单角色 数据传输对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "CcsRoleDTO对象", description = "客服工单角色")
public class CcsRoleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long roleId;

    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String name;

    /**
     * 角色英文编号
     */
    @ApiModelProperty("角色英文编号")
    private String code;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remark;

    /**
     * 状态：1-正常  0-停用
     */
    @ApiModelProperty("状态：1-正常  0-停用")
    private Integer status;

    /**
     * 客户端Id
     */
    @ApiModelProperty("客户端Id")
    private String clientId;


}
