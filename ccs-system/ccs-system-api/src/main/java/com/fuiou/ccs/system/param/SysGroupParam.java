package com.fuiou.ccs.system.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 工作组 请求参数对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "SysGroupParam对象", description = "工作组请求参数")
public class SysGroupParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作组ID
     */
    @ApiModelProperty(value = "工作组ID")
    @NotNull(message = "工作组ID不能为空", groups = {Update.class})
    private Long groupId;

    /**
     * 工作组编号
     */
    @ApiModelProperty(value = "工作组编号")
    @NotNull(message = "工作组编号不能为空", groups = {Update.class})
    private String groupCode;

    /**
     * 工作组名称
     */
    @ApiModelProperty(value = "工作组名称", required = true)
    @NotBlank(message = "工作组名称不能为空", groups = {Create.class, Update.class})
    @Length(max = 33, message = "工作组名称不能超过33个中文字符", groups = {Create.class, Update.class})
    private String name;

    /**
     * 组长ID
     */
    @ApiModelProperty(value = "组长ID")
    @Length(max = 25, message = "组长ID不能超过25个字符", groups = {Create.class, Update.class})
    private String leaderId;

    /**
     * 经理ID
     */
    @ApiModelProperty(value = "经理ID")
    @Length(max = 25, message = "经理ID不能超过25个字符", groups = {Create.class, Update.class})
    private String managerId;

    /**
     * 工作组状态（1-正常，0-停用，默认0）
     */
    @ApiModelProperty(value = "工作组状态（1-正常，0-停用，默认0）")
    private Integer status;
    
    /**
     * 工作组对象
     */
    @Valid
    @ApiModelProperty("工作组成员对象")
    private List<SysGroupMemberParam> sysGroupMember;

    /**
     *新增或修改时，所有工作组成员ids
     */
    @ApiModelProperty("新增或修改时，所有工作组成员ids")
    String userIds;

    public interface Create { }

    public interface Update { }

}
