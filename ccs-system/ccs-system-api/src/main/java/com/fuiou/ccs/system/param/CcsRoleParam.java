package com.fuiou.ccs.system.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 客服工单角色 请求参数对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "CcsRoleParam对象", description = "客服工单角色请求参数")
public class CcsRoleParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @NotNull(message = "更新时，主键ID不能为空", groups = {Update.class})
    private Long roleId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空", groups = {Create.class, Update.class})
    @Length(max = 10, message = "角色名称不能超过10个中文汉字", groups = {Create.class, Update.class})
    private String name;

    /**
     * 角色英文编号
     */
    @ApiModelProperty(value = "角色英文编号")
    @Length(max = 30, message = "角色英文编号不能超过30个字符", groups = {Create.class, Update.class})
    private String code;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    @Length(max = 20, message = "备注信息不能超过20个汉字", groups = {Create.class, Update.class})
    private String remark;

    /**
     * 状态：1-正常  0-停用
     */
    @ApiModelProperty(value = "状态：1-正常  0-停用  更新时需要")
    @NotNull(message = "状态：1-正常  0-停用不能为空", groups = { Update.class})
    private Integer status;

    /**
     * 菜单ID集合
     */
    @ApiModelProperty("菜单ID集合")
    private List<Long> menuIdList;

    /**
     * 权限ID集合
     */
    @ApiModelProperty("权限ID集合")
    private List<Long> permissionIdList;

    public interface Create { }

    public interface Update { }

}
