package com.fuiou.ccs.system.query;

import com.fuiou.query.annotation.Query;
import com.fuiou.query.annotation.QueryField;
import com.fuiou.query.enums.ConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作组日志 查询对象
 *
 * <AUTHOR> @since 1.0.0
 */
@Data
@Query
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GroupLogQuery对象", description = "工作组日志查询")
public class GroupLogQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID，关联T_SYS_GROUP表
     */
    @ApiModelProperty(value = "文件ID，关联T_SYS_GROUP表")
    @QueryField(condition = ConditionEnum.EQ)
    private Long groupId;

    /**
     * 操作类型  1：新增，2：修改，3：删除
     */
    @ApiModelProperty(value = "操作类型  1：新增，2：修改，3：删除")
    @QueryField(condition = ConditionEnum.EQ)
    private Integer type;

    /**
     * 邮件组成员ID，只有操作的是工作组成员时才会存在值
     */
    @ApiModelProperty(value = "邮件组成员ID，只有操作的是工作组成员时才会存在值")
    @QueryField(condition = ConditionEnum.EQ)
    private Long groupMemberId;

    /**
     * 日志内容
     */
    @ApiModelProperty(value = "日志内容")
    @QueryField(condition = ConditionEnum.LIKE)
    private String content;


}
