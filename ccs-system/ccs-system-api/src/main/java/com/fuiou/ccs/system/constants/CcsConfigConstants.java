package com.fuiou.ccs.system.constants;

/**
 * 系统配置常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class CcsConfigConstants {

    /**
     * 用户初始密码
     */
    public static final String INIT_PASSWORD = "sys.user.init-password";

    /**
     * 默认初始密码
     */
    public static final String DEFAULT_INIT_PASSWORD = "123456";

    /**
     * 基础角色编号
     */
    public static final String BASIC_ROLE_CODE = "sys.role.basic-code";

    /**
     * 默认初始密码
     */
    public static final String DEFAULT_BASIC_ROLE_CODE = "Basic";

    /**
     * 修改/添加角色时，需要的分布式锁名称
     */
    public static final String ROLE_LOCK = "ccs_role_lock";

    /**
     * 超级管理员角色编号
     */
    public static final String ADMIN_ROLE_CODE = "JS0001";

    /**
     * 业务管理员角色编号
     */
    public static final String BUSINESS_ADMIN_ROLE_CODE = "JS0005";

    /**
     * 业客服处理人角色编号
     */
    public static final String CUSTOMER_SERVICE_ROLE_CODE = "JS0009";

}
