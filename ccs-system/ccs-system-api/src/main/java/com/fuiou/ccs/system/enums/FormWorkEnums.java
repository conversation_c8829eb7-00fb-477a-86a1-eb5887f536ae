package com.fuiou.ccs.system.enums;

import com.fuiou.common.enums.IEnum;

/**
 * 工单数据表枚举
 *
 * <AUTHOR>
 * @date 2022-6-14
 */
public interface FormWorkEnums {

    enum EventStatusEnums implements IEnum {
        APPROVE(1, "审批中"),
        VISIT(2, "待回访"),
        OVER(3, "已结束");
        final int type;
        final String msg;

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        EventStatusEnums(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param type
         * @return
         */
        public static EventStatusEnums getInstance(Integer type) {
            if (type != null) {
                for (EventStatusEnums enums : EventStatusEnums.values()) {
                    if (enums.getValue() == type) {
                        return enums;
                    }
                }
            }
            throw new NullPointerException("EventStatusEnums.status传入值在枚举类中不存在");
        }
    }

    enum StopStatusEnums implements IEnum {
        NORMAL(0, "正常"),
        PAUSE(1, "已终止");
        final int type;
        final String msg;

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        StopStatusEnums(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param type
         * @return
         */
        public static StopStatusEnums getInstance(Integer type) {
            if (type != null) {
                for (StopStatusEnums enums : StopStatusEnums.values()) {
                    if (enums.getValue() == type) {
                        return enums;
                    }
                }
            }
            throw new NullPointerException("StopStatusEnums.stopStatus传入值在枚举类中不存在");
        }
    }
}
