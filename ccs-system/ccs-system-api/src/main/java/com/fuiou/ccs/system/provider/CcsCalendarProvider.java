package com.fuiou.ccs.system.provider;

import com.fuiou.common.api.ApiResult;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 日历对外接口
 *
 * <AUTHOR>
 */
public interface CcsCalendarProvider {

    /**
     * @return com.fuiou.common.api.ApiResult<java.time.LocalDateTime>
     * @desc //延长指定num时间后的工作时间
     * 去掉节假日+周末（0小时），只算工作日（工作日24小时）
     * @params [startTime, num, chronoUnit]
     * @date 2022-12-19
     */
    ApiResult<LocalDateTime> getExtendTimeToWorkTime(LocalDateTime startTime, long num, ChronoUnit chronoUnit);

}
