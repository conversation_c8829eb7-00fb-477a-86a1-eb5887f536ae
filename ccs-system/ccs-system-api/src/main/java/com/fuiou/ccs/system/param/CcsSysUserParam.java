package com.fuiou.ccs.system.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 请求参数对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "SysUserParam对象", description = "用户请求参数")
public class CcsSysUserParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("userId")
    @NotBlank(message = "userId不能为空", groups = Update.class)
    private String userId;


    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true)
    @NotBlank(message = "用户名不能为空", groups = {Create.class, Update.class})
    @Length(max = 32, message = "用户名不能超过32个字符", groups = {Create.class, Update.class})
    private String username;


    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    @NotBlank(message = "员工编号不能为空", groups = Update.class)
    @Length(max = 8, message = "员工编号不能超过8个字符")
    private String userCode;

    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号", required = false)
    @Length(max = 8, message = "员工工号不能超过8个字符")
    private String empNo;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名", required = true)
    @NotBlank(message = "员工姓名不能为空", groups = {Create.class, Update.class})
    @Length(max = 36, message = "员工姓名不能超过36个字符", groups = {Create.class, Update.class})
    private String empName;



    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @NotBlank(message = "邮箱不能为空", groups = {Create.class, Update.class})
    @Length(max = 50, message = "邮箱不能超过50个字符", groups = {Create.class, Update.class})
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空", groups = {Create.class, Update.class})
    @Length(max = 16, message = "手机号不能超过16个字符", groups = {Create.class, Update.class})
    private String mobile;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    @Length(max = 128, message = "头像地址不能超过128个字符", groups = {Create.class, Update.class})
    private String avatar;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色")
    @Size(min = 1, message = "角色不能为空", groups = {Create.class, Update.class})
    private List<Long> roleIds;

    /**
     * 工作组
     */
    @ApiModelProperty(value = "工作组")
    //@Size(min = 1, message = "工作组不能为空", groups = {Create.class, Update.class})
    private List<Long> groupIds;

    /**
     * 状态（1：正常，0：停用）
     */
    @ApiModelProperty(value = "状态（1：正常，0：停用）", required = true)
    @NotNull(message = "状态（1：正常，0：停用）不能为空", groups = {Update.class})
    private Integer status;

    /**
     * 注册日期
     */
    @ApiModelProperty(value = "注册日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate registerDate;

    /**
     * 最近登录时间
     */
    @ApiModelProperty(value = "最近登录时间")
    private LocalDateTime lastLoginDate;

    /**
     * 最近登录IP
     */
    @ApiModelProperty(value = "最近登录IP")
    @Length(max = 50, message = "最近登录IP不能超过50个字符", groups = {CcsSysUserParam.Update.class})
    private String lastLoginIp;


    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    @Length(max = 25, message = "公司ID不能超过25个字符", groups = {CcsSysUserParam.Create.class, CcsSysUserParam.Update.class})
    private String orgId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    @NotNull(message = "公司名称不能为空", groups = {Update.class})
    private String orgName;


    public interface Create { }

    public interface Update { }

}
