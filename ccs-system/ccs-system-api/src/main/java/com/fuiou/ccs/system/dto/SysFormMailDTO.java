package com.fuiou.ccs.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Program: fuiou-cloud
 * @Description: 给用户发送邮件
 * @Author: Xuejing.Yang
 * @Create: 2022-03-03
 */
@Data
@Builder
public class SysFormMailDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("收件人姓名")
    private String empName;

    @ApiModelProperty("待执行SQL")
    private String sql;

    @ApiModelProperty("表名（英文）")
    private String tableName;

    @ApiModelProperty("中文表名")
    private String cnTableName;

    /**
     * 收件人邮箱
     */
    @ApiModelProperty("收件人邮箱")
    private String email;

    @ApiModelProperty("抄送人邮箱")
    private List<String> ccEmail;

    @ApiModelProperty("链接地址")
    private String url;

}
