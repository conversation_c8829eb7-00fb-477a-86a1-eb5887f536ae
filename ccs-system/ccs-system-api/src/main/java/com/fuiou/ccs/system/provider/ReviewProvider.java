package com.fuiou.ccs.system.provider;

import com.fuiou.ccs.system.enums.ReviewEnums;
import com.fuiou.common.api.ApiResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/14 9:29
 */
public interface ReviewProvider {

    /**
     * 保存复核数据
     * @param businessId 业务数据ID 必传
     * @param businessNo 业务编号 必传 用于页面展示
     * @param oldObj 旧对象  -- 新增时传空 （修改、删除时必传）
     * @param newObj 新对象  -- 删除时传空 （新增、修改时必传）
     * @param reviewModule 需要复核的模块 必传
     * @param operationEnum 操作类型 必传
     * @param creator 创建人 跨服务调用必传
     */
    ApiResult<Void> saveReviewRecord(String businessId, String businessNo, Object oldObj, Object newObj, ReviewEnums.ReviewModule reviewModule, ReviewEnums.OperationEnum operationEnum, String creator);

}
