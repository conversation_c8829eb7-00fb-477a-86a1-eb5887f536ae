package com.fuiou.ccs.system.constants;

/**
 * 系统缓存常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class CcsCacheConstants {

    /**
     * 客户端缓存 Key
     */
    public static final String CLIENT_CACHE_PREFIX = "system.client:";

    /**
     * 部门缓存 Key
     */
    public static final String DEPT_CACHE_PREFIX = "system.dept:";

    /**
     * 客服工单系统用户忘记密码-(EMAIL) KEY 前缀
     */
    public static final String CCS_USER_FORGET_PASSWORD_KEY_EMAIL = "fuiou:ccs:email:forgetPassword:";

    /**
     * 客服工单系统用户忘记密码-(MOBILE) KEY 前缀
     */
    public static final String CCS_USER_FORGET_PASSWORD_KEY_MOBILE = "fuiou:ccs:mobile:forgetPassword:";

    /**
     * 客服工单系统用户忘记密码次数 KEY 前缀
     */
    public static final String CCS_USER_FORGET_PASSWORD_COUNT = "fuiou:ccs:count:forgetPassword:";

    /**
     * redis登录失败锁定key前缀
     */
    public static final String CCS_REDIS_LOGIN_FAILED_LOCK_KEY = "fuiou:ccs:auth:loginFailed:";

    /**
     * 邮件短连接  KEY前缀
     */
    public static final String CCS_SHORT_LINK_CACHE_KEY = "fuiou:ccs:shortLink:";


}
