package com.fuiou.ccs.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SysGroupAndUserVO对象", description = "工作或者用户信息")
public class SysGroupAndUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工作组/用户ID")
    private String id;

    /**
     * 工作组/用户名称
     */
    @ApiModelProperty("工作组/用户名称")
    private String name;

    @ApiModelProperty("状态（1-正常，0-停用，默认为0）")
    private Integer status;

    @ApiModelProperty("类型：1-用户，2-工作组")
    private Integer type;

}
