package com.fuiou.mybatis.core.handler;

import com.fuiou.common.constants.DatePattern;
import com.fuiou.common.utils.LocalDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.LocalDateTimeTypeHandler;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * db2 LocalDateTime 类型转换
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class DB2LocalDateTimeTypeHandler extends LocalDateTimeTypeHandler {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType)
        throws SQLException {
        if (parameter == null) {
            ps.setObject(i, parameter);
        } else {
            ps.setObject(i, LocalDateUtil.format(parameter, DatePattern.yyyy_MM_dd_HH_mm_ss_SSS));
        }
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String dateStr = rs.getString(columnName);
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return LocalDateUtil.parseDateTime(dateStr.substring(0, 19));
    }

}
