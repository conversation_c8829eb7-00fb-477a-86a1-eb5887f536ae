package com.fuiou.mybatis.core.handler;

import com.fuiou.common.utils.LocalDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.LocalDateTypeHandler;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;

/**
 * db2 LocalDate 类型转换
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class DB2LocalDateTypeHandler extends LocalDateTypeHandler {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDate parameter, JdbcType jdbcType)
        throws SQLException {
        if (parameter == null) {
            ps.setObject(i, parameter);
        } else {
            ps.setObject(i, LocalDateUtil.format(parameter));
        }
    }

    @Override
    public LocalDate getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String dateStr = rs.getString(columnName);
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return LocalDateUtil.parseDate(dateStr.substring(0, 10));
    }

}
