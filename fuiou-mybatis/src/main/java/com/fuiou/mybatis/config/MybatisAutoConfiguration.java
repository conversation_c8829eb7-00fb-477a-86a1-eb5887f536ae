package com.fuiou.mybatis.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.fuiou.mybatis.core.handler.DB2LocalDateTimeTypeHandler;
import com.fuiou.mybatis.core.handler.DB2LocalDateTypeHandler;
import com.fuiou.mybatis.core.handler.DB2LocalTimeTypeHandler;
import com.fuiou.mybatis.core.handler.DefaultMetaObjectHandler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.Properties;

/**
 * MyBaits 配置类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Configuration
@MapperScan(value = "${mybatis-plus.base-package:com.fuiou}", annotationClass = Mapper.class,
    lazyInitialization = "${mybatis.lazy-initialization:false}")
public class MybatisAutoConfiguration implements ConfigurationCustomizer {

    @Value("${mybatis-plus.configuration.database-id:}")
    private String databaseId;

    /**
     * 分页拦截器
     */
    @Bean
    @ConditionalOnMissingBean(MybatisPlusInterceptor.class)
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return mybatisPlusInterceptor;
    }

    /**
     * 默认字段插入
     */
    @Bean
    @ConditionalOnMissingBean(MetaObjectHandler.class)
    public MetaObjectHandler defaultMetaObjectHandler() {
        return new DefaultMetaObjectHandler();
    }

    /**
     * 多数据库支持
     */
    @Bean
    public DatabaseIdProvider getDatabaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("DB2", "db2");
        properties.setProperty("SQL Server", "sqlserver");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }

    @Override
    public void customize(MybatisConfiguration configuration) {
        if ("db2".equalsIgnoreCase(databaseId)) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            typeHandlerRegistry.register(DB2LocalDateTimeTypeHandler.class);
            typeHandlerRegistry.register(DB2LocalDateTypeHandler.class);
            typeHandlerRegistry.register(DB2LocalTimeTypeHandler.class);
        }
    }

}
