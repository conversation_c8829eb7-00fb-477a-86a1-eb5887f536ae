#!/bin/bash

# AI智能咖啡厅项目 Nginx 管理脚本
# 作者: AI Assistant
# 日期: $(date)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_IP="***************"
SSL_DIR="/etc/nginx/ssl"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
APP_NAME="ai-smartcafe"

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# 检查nginx状态
check_nginx_status() {
    log "检查Nginx状态..."
    systemctl status nginx --no-pager
}

# 检查SSL证书
check_ssl_certificate() {
    log "检查SSL证书..."
    if [ -f "$SSL_DIR/${APP_NAME}.crt" ] && [ -f "$SSL_DIR/${APP_NAME}.key" ]; then
        log "SSL证书存在"
        openssl x509 -in "$SSL_DIR/${APP_NAME}.crt" -text -noout | grep -E "(Subject:|Not After)"
    else
        warn "SSL证书不存在"
    fi
}

# 测试nginx配置
test_nginx_config() {
    log "测试Nginx配置..."
    nginx -t
}

# 重新加载nginx
reload_nginx() {
    log "重新加载Nginx配置..."
    test_nginx_config
    systemctl reload nginx
    log "Nginx配置已重新加载"
}

# 重启nginx
restart_nginx() {
    log "重启Nginx服务..."
    systemctl restart nginx
    log "Nginx服务已重启"
}

# 查看nginx日志
show_nginx_logs() {
    log "显示Nginx日志 (按 Ctrl+C 退出):"
    echo -e "${BLUE}=== 访问日志 ===${NC}"
    tail -f /var/log/nginx/ai-smartcafe.access.log &
    echo -e "${BLUE}=== 错误日志 ===${NC}"
    tail -f /var/log/nginx/ai-smartcafe.error.log
}

# 查看端口监听
check_ports() {
    log "检查端口监听状态..."
    echo -e "${BLUE}=== 相关端口监听状态 ===${NC}"
    netstat -tln | grep -E ':(80|443|3000)' || echo "未找到相关端口"
}

# 生成新的SSL证书
generate_ssl_certificate() {
    log "生成新的SSL证书..."
    mkdir -p "$SSL_DIR"
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$SSL_DIR/${APP_NAME}.key" \
        -out "$SSL_DIR/${APP_NAME}.crt" \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=AI-SmartCafe/OU=IT/CN=$SERVER_IP"
    log "SSL证书已生成"
}

# 测试HTTPS连接
test_https() {
    log "测试HTTPS连接..."
    echo -e "${BLUE}=== 测试HTTP重定向 ===${NC}"
    curl -I "http://$SERVER_IP" || warn "HTTP连接测试失败"
    
    echo -e "${BLUE}=== 测试HTTPS连接 ===${NC}"
    curl -k -I "https://$SERVER_IP" || warn "HTTPS连接测试失败"
}

# 显示访问信息
show_access_info() {
    log "访问信息:"
    echo -e "${GREEN}HTTP访问地址:  http://$SERVER_IP${NC}"
    echo -e "${GREEN}HTTPS访问地址: https://$SERVER_IP${NC}"
    echo -e "${YELLOW}注意: 由于使用自签名证书，浏览器会显示安全警告，请选择"继续访问"${NC}"
}

# 安装nginx和依赖
install_nginx() {
    log "安装Nginx和依赖..."
    apt update
    apt install -y nginx certbot python3-certbot-nginx
    systemctl enable nginx
    log "Nginx安装完成"
}

# 主函数
main() {
    log "AI智能咖啡厅项目 - Nginx管理"
    
    # 解析命令行参数
    case "${1:-status}" in
        "install")
            install_nginx
            ;;
        "status")
            check_nginx_status
            check_ports
            ;;
        "ssl-check")
            check_ssl_certificate
            ;;
        "ssl-generate")
            generate_ssl_certificate
            ;;
        "test")
            test_nginx_config
            ;;
        "reload")
            reload_nginx
            ;;
        "restart")
            restart_nginx
            ;;
        "logs")
            show_nginx_logs
            ;;
        "test-https")
            test_https
            ;;
        "info")
            show_access_info
            ;;
        "ports")
            check_ports
            ;;
        *)
            echo "用法: $0 {install|status|ssl-check|ssl-generate|test|reload|restart|logs|test-https|info|ports}"
            echo ""
            echo "  install      - 安装Nginx和依赖"
            echo "  status       - 查看Nginx状态 (默认)"
            echo "  ssl-check    - 检查SSL证书"
            echo "  ssl-generate - 生成新的SSL证书"
            echo "  test         - 测试Nginx配置"
            echo "  reload       - 重新加载Nginx配置"
            echo "  restart      - 重启Nginx服务"
            echo "  logs         - 查看Nginx日志"
            echo "  test-https   - 测试HTTP/HTTPS连接"
            echo "  info         - 显示访问信息"
            echo "  ports        - 查看端口监听状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 