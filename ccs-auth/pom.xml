<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ccs-auth</artifactId>
    <parent>
        <artifactId>ccs-cloud</artifactId>
        <groupId>com.fuiou.ccs</groupId>
        <version>1.0.0</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- fuiou framework dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-captcha</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-crypto</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>

        <!-- fuiou cloud dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-cloud-common</artifactId>
            <version>${fuiou-cloud-common.version}</version>
        </dependency>

        <!-- fuiou service api dependency -->
        <dependency>
            <groupId>com.fuiou.ccs</groupId>
            <artifactId>ccs-system-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- spring security oauth2 dependency -->
        <dependency>
            <groupId>org.springframework.security.oauth.boot</groupId>
            <artifactId>spring-security-oauth2-autoconfigure</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-security-config</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-security-web</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
            <version>5.2.15.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-security-oauth2-core</artifactId>
                    <groupId>org.springframework.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>spring-security-core</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-config</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-web</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>spring-security-oauth2-core</artifactId>
            <groupId>org.springframework.security</groupId>
            <version>5.2.15.RELEASE</version>
        </dependency>

        <!-- 密码加密需要 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <!-- knife4j swagger dependency -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
        </dependency>

        <!-- spring boot dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <finalName>ccs-auth</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <excludes>
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>