security:
  oauth2:
    authorization:
      realm: 1qazse4
      token-key-access: permitAll()
      check-token-access: isAuthenticated()
      # keytool -genkey -alias jwt -keyalg RSA -keypass KUsIBmqf -keystore jwt.jks -storepass Cdbih0dO
      # 获取公钥：需要openssl软件 http://slproweb.com/download/Win64OpenSSL_Light-1_1_1k.exe 安装后需要配环境变量
      # 命令：keytool -list -rfc --keystore jwt.jks | openssl x509 -inform pem -pubkey
      jwt:
        key-alias: jwt
        key-password: KUsIBmqf
        key-store: jwt.jks
        key-store-password: Cdbih0dO

spring:
  cloud:
    nacos:
      server-addr: *************:9848
      username: nacos
      password: 1qazse4
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: 95c726fe-a383-4862-a134-d7d3416bcf7e
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: 95c726fe-a383-4862-a134-d7d3416bcf7e

dubbo:
  scan:
    # dubbo 服务扫描基准包
    base-packages: com.fuiou.ccs.auth
  cloud:
    subscribed-services: ccs-system
  protocol:
    # # dubbo 协议端口（ -1 表示自增端口，从 20880 开始）
    name: dubbo
    port: -1
  application:
    name: ccs-auth
  registry:
    address: nacos://*************:9848?namespace=95c726fe-a383-4862-a134-d7d3416bcf7e
    username: nacos
    password: 1qazse4
    simplified: true
