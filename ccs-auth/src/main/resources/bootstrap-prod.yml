security:
  oauth2:
    authorization:
      realm: 1qazse4
      token-key-access: permitAll()
      check-token-access: isAuthenticated()
      # keytool -genkey -alias jwtProd -keyalg RSA -keypass uNPcgk5L -keystore jwtProd.jks -storepass nW78ILQq
      # 获取公钥：需要openssl软件 http://slproweb.com/download/Win64OpenSSL_Light-1_1_1k.exe 安装后需要配环境变量
      # 命令：keytool -list -rfc --keystore jwtProd.jks | openssl x509 -inform pem -pubkey
      jwt:
        key-alias: jwtProd
        key-password: uNPcgk5L
        key-store: jwtProd.jks
        key-store-password: nW78ILQq

spring:
  cloud:
    nacos:
      server-addr: **********:8848
      username: nacos
      password: VDDGnYjdMPNIyBkw
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d

dubbo:
  scan:
    # dubbo 服务扫描基准包
    base-packages: com.fuiou.ccs.auth
  cloud:
    subscribed-services: ccs-system
  protocol:
    # # dubbo 协议端口（ -1 表示自增端口，从 20880 开始）
    name: dubbo
    port: -1
  application:
    name: ccs-auth
  registry:
    address: nacos://**********:8848?namespace=a522c8a7-9355-48a5-8c00-27dfafe1796d
    username: nacos
    password: VDDGnYjdMPNIyBkw
    simplified: true
