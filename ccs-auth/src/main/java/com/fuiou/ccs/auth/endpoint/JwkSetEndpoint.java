package com.fuiou.ccs.auth.endpoint;

import com.nimbusds.jose.jwk.JWKSet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 获取RSA公钥接口
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Api(tags = "Jwk相关接口")
@RestController
public class JwkSetEndpoint {

    final JWKSet jwkSet;

    public JwkSetEndpoint(JWKSet jwkSet) {
        this.jwkSet = jwkSet;
    }

    @GetMapping("/rsa/public_key")
    @ApiOperation(value = "获取验签公钥")
    public Map<String, Object> rsaPublicKey() {
        return this.jwkSet.toJSONObject();
    }

}
