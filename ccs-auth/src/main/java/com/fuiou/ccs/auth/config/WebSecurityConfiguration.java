package com.fuiou.ccs.auth.config;

import com.fuiou.ccs.auth.config.properties.SecurityProperties;
import com.fuiou.ccs.auth.handler.RestAccessDeniedHandler;
import com.fuiou.ccs.auth.handler.RestAuthenticationEntryPoint;
import com.fuiou.ccs.auth.nopassword.NoPasswordAuthenticationProvider;
import com.fuiou.ccs.auth.service.impl.AuthUserDetailsServiceImpl;
import com.fuiou.ccs.auth.service.impl.ClientDetailsServiceImpl;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.core.GrantedAuthorityDefaults;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsByNameServiceWrapper;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.argon2.Argon2PasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

/**
 * Spring Security安全配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableConfigurationProperties({SecurityProperties.class})
public class WebSecurityConfiguration extends WebSecurityConfigurerAdapter {

    /**
     * 需要把AuthenticationManager主动暴露出来
     * 以便在授权服务器{@link AuthorizationServerConfiguration}中可以使用它来完成用户名、密码的认证
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * 密码编码器，使用算法 argon2
     * https://github.com/xitu/gold-miner/blob/master/TODO1/password-hashing-pbkdf2-scrypt-bcrypt-and-argon2.md
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new Argon2PasswordEncoder();
    }

    /**
     * 去除 ROLE_ 前缀
     */
    @Bean
    public GrantedAuthorityDefaults grantedAuthorityDefaults() {
        return new GrantedAuthorityDefaults("");
    }

    /**
     * 客户端详情服务类
     */
    @Bean
    public ClientDetailsService clientDetailsService() {
        return new ClientDetailsServiceImpl();
    }

    @Bean
    @Override
    protected UserDetailsService userDetailsService() {
        return new AuthUserDetailsServiceImpl();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new RestAuthenticationEntryPoint();
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return new RestAccessDeniedHandler();
    }

    public NoPasswordAuthenticationProvider noPasswordAuthenticationProvider() {
        return new NoPasswordAuthenticationProvider(userDetailsService());
    }

    /**
     * 预验证身份认证器
     * <p>
     * 预验证是指身份已经在其他地方（第三方）确认过
     * 预验证器的目的是将第三方身份管理系统集成到具有Spring安全性的Spring应用程序中，在本项目中，用于JWT令牌过期后重刷新时的验证
     * 此时只要检查用户是否有停用、锁定、密码过期、账号过期等问题，如果没有，可根据JWT令牌的刷新过期期限，重新给客户端发放访问令牌
     */
    @Bean
    public PreAuthenticatedAuthenticationProvider preAuthenticatedAuthenticationProvider() {
        PreAuthenticatedAuthenticationProvider preAuthenticatedAuthenticationProvider =
                new PreAuthenticatedAuthenticationProvider();

        UserDetailsByNameServiceWrapper<PreAuthenticatedAuthenticationToken> userDetailsByNameServiceWrapper =
                new UserDetailsByNameServiceWrapper<>(userDetailsService());

        preAuthenticatedAuthenticationProvider
                .setPreAuthenticatedUserDetailsService(userDetailsByNameServiceWrapper);

        return preAuthenticatedAuthenticationProvider;
    }

    /**
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        // 禁用 CSRF，因为不使用 Session
        httpSecurity.csrf().disable();
        // 禁用 HTTP 基本身份验证
        httpSecurity.httpBasic().disable();
        // 禁用请求头控制
        httpSecurity.headers()
                // 防止 iframe 造成跨域
                .frameOptions()
                // 移除静态资源目录的安全控制，避免Spring Security默认禁止HTTP缓存的行为
                .and().cacheControl().disable();
        // 不创建 Session
        httpSecurity.sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        // 认证请求处理
        httpSecurity.authorizeRequests()
                // 放开 actuator 端点
                .requestMatchers(EndpointRequest.toAnyEndpoint()).permitAll()
                // 放开公钥请求
                .antMatchers("/rsa/public_key").permitAll()
                // 放开认证请求，包括token、captcha、logout...
                .antMatchers("/oauth/**").permitAll()
                // 验证码请求
                // .antMatchers("/oauth/captcha").permitAll()
                // 退出登录
                // .antMatchers("/oauth/logout").permitAll()
                // 放开knife4j相关请求 https://gitee.com/xiaoym/knife4j/issues/I1Q5X6
                .antMatchers("/webjars/**", "/doc.html", "/swagger-resources/**", "/v2/api-docs").permitAll()
                .anyRequest().authenticated();

        // 异常处理
        httpSecurity.exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint())
                .accessDeniedHandler(accessDeniedHandler());
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService()).passwordEncoder(passwordEncoder())
                .and().authenticationProvider(noPasswordAuthenticationProvider());

        auth.authenticationProvider(preAuthenticatedAuthenticationProvider());
    }

}
