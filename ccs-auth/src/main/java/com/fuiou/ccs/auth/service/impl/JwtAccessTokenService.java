package com.fuiou.ccs.auth.service.impl;

import com.fuiou.ccs.auth.config.properties.SecurityProperties;
import com.fuiou.common.constants.AuthConstants;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.redis.util.RedisUtil;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.AccessTokenConverter;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenStore;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Jwt 访问令牌服务
 * <p>
 * 在此服务中提供了令牌如何存储、携带哪些信息、如何签名、持续多长时间等相关内容的定义
 * 令牌服务应当会被授权服务器注册验证Endpoint时候调用到
 *
 * <AUTHOR>
 * @since  1.0.0
 **/
public class JwtAccessTokenService extends DefaultTokenServices {

    final RedisUtil redisUtil;
    final SecurityProperties securityProperties;

    /**
     * 构建JWT令牌，并进行默认的配置
     */
    public JwtAccessTokenService(RedisUtil redisUtil,
                                 TokenStore tokenStore,
                                 TokenEnhancer tokenEnhancer,
                                 ClientDetailsService clientDetailsService,
                                 AuthenticationManager authenticationManager,
                                 SecurityProperties securityProperties) {
        this.redisUtil = redisUtil;
        this.securityProperties = securityProperties;
        // 设置令牌的持久化容器
        // 令牌持久化有多种方式，单节点服务可以存放在Session中，集群可以存放在Redis中
        // 而JWT是后端无状态、前端存储的解决方案，Token的存储由前端完成
        setTokenStore(tokenStore);
        // 令牌支持的客户端详情
        setClientDetailsService(clientDetailsService);
        // 定义令牌的额外支持
        setTokenEnhancer(tokenEnhancer);
        // 设置验证管理器，在鉴权的时候需要用到
        setAuthenticationManager(authenticationManager);
        // access_token有效期，单位：秒
        setAccessTokenValiditySeconds((int) securityProperties.getTokenTimeout().getSeconds());
        // refresh_token的有效期，单位：秒, 默认30天
        // 这决定了客户端选择“记住当前登录用户”的最长时效，即失效前都不用再请求用户赋权了
        setRefreshTokenValiditySeconds((int) securityProperties.getRefreshTokenTimeout().getSeconds());
        // 是否支持refresh_token，默认false
        setSupportRefreshToken(true);
        // 是否复用refresh_token，默认为true
        // 如果为false，则每次请求刷新都会删除旧的refresh_token，创建新的refresh_token
        setReuseRefreshToken(true);
    }

    @Override
    public OAuth2AccessToken createAccessToken(OAuth2Authentication authentication) throws AuthenticationException {
        OAuth2AccessToken accessToken = super.createAccessToken(authentication);
        if (accessToken != null && !securityProperties.isConcurrentLogin()) {
            String clientId = authentication.getOAuth2Request().getClientId();
            // 不允许并发登录，则将jwt信息存入redis中
            cacheJwt(clientId, accessToken);
        }
        return accessToken;
    }

    /**
     * 缓存jwt信息到redis
     */
    private void cacheJwt(String clientId, OAuth2AccessToken accessToken) {
        int expiresIn = accessToken.getExpiresIn();
        Map<String, Object> additionalInformation = accessToken.getAdditionalInformation();
        String userId = (String) additionalInformation.get(AuthConstants.USER_ID_KEY);
        Object jti = additionalInformation.get(AccessTokenConverter.JTI);
        String jwtRedisKey = AuthUtil.getJwtRedisKey(clientId, userId);
        try {
            redisUtil.set(jwtRedisKey, jti, expiresIn, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
