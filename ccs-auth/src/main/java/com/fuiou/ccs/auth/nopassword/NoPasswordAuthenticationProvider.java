package com.fuiou.ccs.auth.nopassword;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/31 11:40
 */
public class NoPasswordAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    public NoPasswordAuthenticationProvider(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        UserDetails userDetails = userDetailsService.loadUserByUsername(authentication.getPrincipal().toString());
        if (userDetails == null) {
            throw new UsernameNotFoundException("该账号不存在");
        }
        NoPasswordAuthenticationToken authenticationResult = new NoPasswordAuthenticationToken(userDetails, null,
            userDetails.getAuthorities());
        authenticationResult.setDetails(authentication.getDetails());
        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return NoPasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
