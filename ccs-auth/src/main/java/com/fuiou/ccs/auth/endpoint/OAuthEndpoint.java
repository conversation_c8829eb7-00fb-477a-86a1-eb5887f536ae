package com.fuiou.ccs.auth.endpoint;

import com.fuiou.ccs.auth.service.AuthUserDetailsService;
import com.fuiou.ccs.auth.service.CaptchaService;
import com.fuiou.ccs.auth.service.ShortLinkService;
import com.fuiou.ccs.auth.vo.NormalLinkVO;
import com.fuiou.ccs.system.constants.CcsUserConstants;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.domain.AuthUser;
import com.fuiou.common.utils.AuthUtil;
import com.fuiou.crypto.service.RSACryptoService;
import com.fuiou.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.security.Principal;
import java.util.Map;

import static com.fuiou.ccs.system.constants.CcsUserConstants.MOBILE_IN;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@Api(tags = "认证相关接口")
@RequestMapping("/oauth")
public class OAuthEndpoint {


    final RedisUtil redisUtil;

    final TokenEndpoint tokenEndpoint;

    final CaptchaService captchaService;

    final RSACryptoService rsaCryptoService;

    final ShortLinkService shortLinkService;

    final AuthUserDetailsService authUserDetailsService;


    public OAuthEndpoint(RedisUtil redisUtil, TokenEndpoint tokenEndpoint,
                         CaptchaService captchaService, RSACryptoService rsaCryptoService,
                         ShortLinkService shortLinkService, AuthUserDetailsService authUserDetailsService) {
        this.redisUtil = redisUtil;
        this.tokenEndpoint = tokenEndpoint;
        this.captchaService = captchaService;
        this.rsaCryptoService = rsaCryptoService;
        this.shortLinkService = shortLinkService;
        this.authUserDetailsService = authUserDetailsService;
    }

    /**
     * 获取 token 的请求
     *
     * @see TokenEndpoint#postAccessToken(Principal, Map)
     */
    @PostMapping("/token")
    @ApiOperation(value = "OAuth2认证")
    @ApiImplicitParams({@ApiImplicitParam(name = "grant_type", defaultValue = "password", value = "授权模式", required = true), @ApiImplicitParam(name = "client_id", value = "Oauth2客户端ID（新需放置在请求头）", required = true), @ApiImplicitParam(name = "client_secret", value = "Oauth2客户端秘钥（需放置在请求头）", required = true), @ApiImplicitParam(name = "username", defaultValue = "admin", value = "用户名"), @ApiImplicitParam(name = "password", defaultValue = "123456", value = "密码"), @ApiImplicitParam(name = "shortLinkCode", value = "短链code"), @ApiImplicitParam(name = "refresh_token", value = "刷新token")})
    public Object postAccessToken(@ApiIgnore Principal principal, @ApiIgnore @RequestParam
            Map<String, String> parameters) throws HttpRequestMethodNotSupportedException {
        log.info("开始认证！认证方式：{}", parameters.get("grant_type"));
        if ("shortLink".equals(parameters.get("grant_type"))) {
            // 在这里区分认证方式的原因是不想增加自定义身份校验过滤器
            String shortLinkCode = parameters.get("shortLinkCode");
            NormalLinkVO normalLinkVO = shortLinkService.getNormalLinkInfo(shortLinkCode);
            parameters.remove("username");
            parameters.put("username", normalLinkVO.getUsername());
            ResponseEntity<OAuth2AccessToken> responseEntity = tokenEndpoint.postAccessToken(principal, parameters);
            normalLinkVO.setTokenInfo(responseEntity.getBody());
            return normalLinkVO;
        } else {
            // 密码 rsa 解码
            String password = parameters.get("password");
            if (StringUtils.isNotBlank(password)) {
                password = rsaCryptoService.decryptToString(password);
                parameters.put("password", password);
            }
            return tokenEndpoint.postAccessToken(principal, parameters);
        }
    }

    /**
     * 验证码
     */
    @GetMapping("/captcha")
    @ApiOperation(value = "验证码")
    @ApiImplicitParam(name = "type", defaultValue = "gif", value = "验证码类型")
    public ApiResult<Map<String, Object>> captcha(@RequestParam(required = false, defaultValue = "gif") String type) {
        return ApiResult.data(captchaService.create(type));
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    @ApiOperation(value = "退出登录")
    public ApiResult<Void> logout() {
        AuthUser authUser = AuthUtil.getUser();
        if (authUser == null) {
            return ApiResult.success("退出登录成功");
        }

        String clientId = authUser.getClientId();
        String userId = authUser.getUserId();
        try {
            String jwtRedisKey = AuthUtil.getJwtRedisKey(clientId, userId);
            redisUtil.del(jwtRedisKey);
        } catch (Exception e) {
            return ApiResult.fail("退出登录失败");
        }

        return ApiResult.success("退出登录成功");
    }


    @PostMapping("/forgetPassword")
    @ApiOperation(value = "忘记密码")
    @ApiImplicitParams({@ApiImplicitParam(name = "clientId", value = "客户端ID(td)", required = true), @ApiImplicitParam(name = "username", defaultValue = "admin", value = "用户名", required = true), @ApiImplicitParam(name = "type", value = "类型（email/mobileOut/mobileIn）", required = true), @ApiImplicitParam(name = "captcha", value = "图片验证码", required = false), @ApiImplicitParam(name = "captchaKey", value = "图片验证码的key", required = false), @ApiImplicitParam(name = "mobile", value = "用户手机号", required = true)})
    public ApiResult<Boolean> forgetPassword(@RequestParam Map<String, String> parameters) {
        if (StringUtils.isEmpty(parameters.get("username")) || StringUtils.isEmpty(parameters.get("type"))
                || StringUtils.isEmpty(parameters.get("clientId"))) {
            return ApiResult.fail("用户名、类型、客户端ID不能为空 ");
        }
        if (!MOBILE_IN.equals(parameters.get("type"))) {
            if (StringUtils.isEmpty(parameters.get("mobile"))) {
                return ApiResult.fail("用户手机号不能为空 ");
            }
            if (StringUtils.isEmpty(parameters.get("captcha")) || StringUtils.isEmpty(parameters.get("captchaKey"))) {
                return ApiResult.fail("图片验证码及其key不能为空 ");
            }
        }
        return ApiResult.data(authUserDetailsService.forgetPassword(parameters.get("clientId"), parameters.get("username"), parameters.get("mobile"), parameters.get("type"), parameters.get("captcha"), parameters.get("captchaKey")));
    }

    @PostMapping("/updatePassword")
    @ApiOperation(value = "用户忘记密码时，更改密码")
    @ApiImplicitParams({@ApiImplicitParam(name = "password", value = "用户密码（传参加密）", required = true), @ApiImplicitParam(name = "userParam", defaultValue = "用户id/登录名", value = "用户参数", required = true), @ApiImplicitParam(name = "type", value = "类型（email/mobile）", required = true), @ApiImplicitParam(name = "captcha", value = "短信验证码-当通过mobile修改密码时，必须", required = false)})
    public ApiResult<Void> updatePassword(@RequestParam Map<String, String> parameters) {
        if (StringUtils.isEmpty(parameters.get("type")) || StringUtils.isEmpty(parameters.get("userParam"))
                || StringUtils.isEmpty(parameters.get("password"))) {
            return ApiResult.fail("类型、用户参数、密码不能为空 ");
        }
        if (CcsUserConstants.MOBILE.equals(parameters.get("type")) && StringUtils.isEmpty(parameters.get("captcha"))) {
            return ApiResult.fail("验证码不能为空 ");
        }
        return ApiResult.status(authUserDetailsService.updatePassword(parameters.get("type"), parameters.get("userParam"), parameters.get("captcha"), rsaCryptoService.decryptToString(parameters.get("password"))));
    }

}
