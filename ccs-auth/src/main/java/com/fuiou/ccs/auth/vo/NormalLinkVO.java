package com.fuiou.ccs.auth.vo;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/4 14:08
 */
@Builder
@Data
public class NormalLinkVO {

    private String username;

    private String longLink;

    private Map<String, Object> params;

    private Object tokenInfo;

    @Tolerate
    public NormalLinkVO() {
    }
}
