package com.fuiou.ccs.auth.granter;

import com.fuiou.ccs.auth.dao.UserDetailsDao;
import com.fuiou.common.constants.AuthConstants;
import com.fuiou.common.utils.WebUtil;
import com.fuiou.redis.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 验证码的TokenGranter
 *
 * <AUTHOR>
 * @since 2020-7-21
 */
public class CaptchaTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "captcha";

    private final AuthenticationManager authenticationManager;

    private RedisUtil redisUtil;

    private UserDetailsDao userDetailsDao;

    public CaptchaTokenGranter(AuthenticationManager authenticationManager,
                               AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService,
                               OAuth2RequestFactory requestFactory, RedisUtil redisUtil, UserDetailsDao userDetailsDao) {
        this(authenticationManager, tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.redisUtil = redisUtil;
        this.userDetailsDao = userDetailsDao;
    }

    protected CaptchaTokenGranter(AuthenticationManager authenticationManager, AuthorizationServerTokenServices tokenServices,
                                  ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        HttpServletRequest request = WebUtil.getRequest();
        // 增加验证码判断
        String captchaKey = request.getHeader(AuthConstants.CAPTCHA_KEY_HEADER);
        String captchaCode = request.getHeader(AuthConstants.CAPTCHA_CODE_HEADER);
        if (StringUtils.isBlank(captchaCode)) {
            throw new UserDeniedAuthorizationException("请输入验证码");
        }
        String captchaRedisKey = AuthConstants.CAPTCHA_CACHE_KEY + captchaKey;
        String captchaRedisCode = redisUtil.get(captchaRedisKey);
        // 缓存中的验证码为空，代表已过期
        if (StringUtils.isBlank(captchaRedisCode)) {
            throw new UserDeniedAuthorizationException("验证码已过期");
        }
        // 无论验证码是否输入正确，只要校验过就要删除
        redisUtil.del(captchaRedisKey);
        // 判断验证码是否正确
        if (!StringUtils.equalsIgnoreCase(captchaCode, captchaRedisCode)) {
            throw new UserDeniedAuthorizationException("验证码错误");
        }
        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
        String username = parameters.get("username");
        String password = parameters.get("password");
        parameters.remove("password");
        Authentication userAuth = new UsernamePasswordAuthenticationToken(username, password);
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        try {
            userAuth = authenticationManager.authenticate(userAuth);
            userDetailsDao.resetFailAttempts(username);
        } catch (AccountStatusException ase) {
            //covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        } catch (BadCredentialsException bce) {
            userDetailsDao.updateFailAttempts(username);
            throw new InvalidGrantException(bce.getMessage());
        } catch (InternalAuthenticationServiceException ex) {
            throw new UserDeniedAuthorizationException(ex.getMessage());
        }
        // If the username/password are wrong the spec says we should send 400/invalid grant
        if (userAuth == null || !userAuth.isAuthenticated()) {
            throw new InvalidGrantException("Could not authenticate user: " + username);
        }
        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }

}
