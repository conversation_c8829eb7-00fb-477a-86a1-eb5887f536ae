package com.fuiou.ccs.auth.service.impl;

import com.fuiou.ccs.auth.client.AuthClientDetailsClient;
import com.fuiou.ccs.system.dto.CcsClientDTO;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.utils.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 客户端详情
 *
 * <AUTHOR>
 * @see org.springframework.security.oauth2.provider.ClientDetailsService
 * @since 1.0.0
 */
public class ClientDetailsServiceImpl implements ClientDetailsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientDetailsServiceImpl.class);

    @Autowired
    private AuthClientDetailsClient authClientDetailsClient;

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws InvalidClientException {
        CcsClientDTO ccsClient = null;
        try {
            ccsClient = authClientDetailsClient.getClientByClientId(clientId);
        } catch (Exception e) {
            LOGGER.warn("获取客户端信息失败：客户端ID = {}，异常信息 = {}", clientId, e.getMessage());
            throw new InvalidClientException("获取客户端信息失败");
        }
        return buildClientDetails(ccsClient);
    }

    /**
     * 构建 ClientDetails
     */
    private ClientDetails buildClientDetails(CcsClientDTO ccsClient) {
        if (!Objects.equals(CommonConstants.ENABLED, ccsClient.getStatus())) {
            throw new InvalidClientException("该客户端已停用");
        }
        BaseClientDetails details = new BaseClientDetails(ccsClient.getClientId(), ccsClient.getResourceIds(),
                ccsClient.getScope(), ccsClient.getAuthorizedGrantTypes(), ccsClient.getAuthorities(), ccsClient.getWebServerRedirectUri());
        details.setClientSecret(ccsClient.getClientSecret());
        Integer accessTokenValidity = ccsClient.getAccessTokenValidity();
        if (accessTokenValidity != null) {
            details.setAccessTokenValiditySeconds(accessTokenValidity);
        }
        Integer refreshTokenValidity = ccsClient.getRefreshTokenValidity();
        if (refreshTokenValidity != null) {
            details.setRefreshTokenValiditySeconds(refreshTokenValidity);
        }
        String json = ccsClient.getAdditionalInformation();
        if (StringUtils.hasText(json)) {
            try {
                Map<String, Object> additionalInformation = JacksonUtil.parseMap(json);
                details.setAdditionalInformation(additionalInformation);
            } catch (Exception e) {
                LOGGER.warn("Could not decode JSON for additional information: " + details, e);
            }
        }
        String scopes = ccsClient.getAutoapprove();
        if (StringUtils.hasText(scopes)) {
            details.setAutoApproveScopes(StringUtils.commaDelimitedListToSet(scopes));
        }
        return details;
    }

}
