package com.fuiou.ccs.auth.enums;

/**
 * 认证类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum AuthType {

    PASSWORD(1, "密码"),
    MOBILE(2, "手机"),
    SOCIAL(3, "社交账号");

    final int type;

    final String name;

    AuthType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "AuthType{"
                + "type=" + type
                + ", name='" + name + '\''
                + '}';
    }

}
