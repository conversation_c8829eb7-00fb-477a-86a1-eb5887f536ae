package com.fuiou.ccs.auth.support;

import com.fuiou.ccs.auth.userdetails.AuthUserDetails;
import com.fuiou.common.constants.AuthConstants;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Jwt Token信息扩展
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class JwtTokenEnhancer implements TokenEnhancer {

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Authentication userAuthentication = authentication.getUserAuthentication();
        if (userAuthentication == null) {
            return accessToken;
        }

        Optional.ofNullable((AuthUserDetails) userAuthentication.getPrincipal()).ifPresent(userDetail -> {
            final Map<String, Object> additionInfo = new HashMap<>(12);
            additionInfo.put(AuthConstants.USER_ID_KEY, userDetail.getUserId());
            additionInfo.put(AuthConstants.COMPANY_ID_KEY, userDetail.getOrgId());
            additionInfo.put(AuthConstants.EMP_NO_KEY, userDetail.getEmpNo());
            additionInfo.put(AuthConstants.EMP_NAME_KEY, userDetail.getEmpName());
            additionInfo.put(AuthConstants.AUTH_TYPE_KEY, userDetail.getAuthType());
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionInfo);
        });

        return accessToken;
    }

}
