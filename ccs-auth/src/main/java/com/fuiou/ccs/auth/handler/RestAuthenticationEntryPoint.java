package com.fuiou.ccs.auth.handler;

import com.fuiou.ccs.auth.exception.AuthErrorCode;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.WebUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 认证失败 rest 返回
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class RestAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestAuthenticationEntryPoint.class);

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        LOGGER.error("Responding with unauthorized error. Message:{}, url:{}", authException.getMessage(), request.getRequestURI());
        WebUtil.writeJsonResponse(response, HttpServletResponse.SC_UNAUTHORIZED, ApiResult.fail(AuthErrorCode.AUTHENTICATION_FAIL));
    }

}
