package com.fuiou.ccs.auth.client;

import com.fuiou.ccs.system.dto.CcsAuthUserDTO;
import com.fuiou.ccs.system.exception.SystemServiceException;
import com.fuiou.ccs.system.provider.SysUserProvider;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 认证用户详情
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class AuthUserDetailsClient {

    @DubboReference
    private SysUserProvider sysUserProvider;

    /**
     * 获取认证用户
     *
     * @param username 用户名
     */
    public CcsAuthUserDTO getAuthUserByUsername(String clientId, String username) {
        return sysUserProvider.getAuthUserByUsername(clientId, username).requiredData();
    }

    /**
     * 获取认证用户
     *
     * @param mobile 手机号
     */
    public CcsAuthUserDTO getAuthUserByMobile(String clientId, String mobile) {
        return sysUserProvider.getAuthUserByMobile(clientId, mobile).requiredData();
    }

    public Boolean forgetPassword(String clientId, String username, String mobile, String type, String captcha, String captchaKey) {
        try {
            log.info("忘记密码操作传参：clientId：{}, username：{},mobile：{},type：{},captcha:{},captchaKey:{}", clientId, username, mobile, type, captcha, captchaKey);
            ApiResult<Boolean> apiResult = sysUserProvider.forgetPassword(clientId, username, mobile, type, captcha, captchaKey);
            if (!apiResult.getSuccess()) {
                log.error("忘记密码操作失败!");
                throw new SystemServiceException(apiResult.getCode(), apiResult.getMsg());
            }
            return apiResult.getData();
        } catch (Exception e) {
            log.error("忘记密码操作传参异常，e:{}", e);
        }
        return false;
    }

    public boolean updatePassword(String type, String userParam, String captcha, String newPassword) {
        try {
            log.info("修改密码操作传参：type：{}, userParam：{},captcha:{}", type, userParam, captcha);
            ApiResult<Void> apiResult = sysUserProvider.updatePassword(type, userParam, captcha, newPassword);
            if (!apiResult.getSuccess()) {
                log.error("修改密码操作失败!");
                throw new SystemServiceException(apiResult.getCode(), apiResult.getMsg());
            }
            return apiResult.getSuccess();
        } catch (Exception e) {
            log.error("修改密码操作传参异常，e:{}", e);
        }
        return false;
    }

    public void updateLastLoginInfo(String userId, String lastLoginIp, LocalDateTime lastLoginDate) {
        try {
            log.info("修改用户最近登录IP，最近登录时间传参：userId：{}, lastLoginIp：{},lastLoginDate：{}", userId, lastLoginIp, lastLoginDate);
            ApiResult<Void> apiResult = sysUserProvider.updateLastLoginInfo(userId, lastLoginIp, lastLoginDate);
            if (!apiResult.getSuccess()) {
                log.error("修改用户最近登录IP，最近登录时间失败!");
                throw new SystemServiceException(apiResult.getCode(), apiResult.getMsg());
            }
        } catch (Exception e) {
            log.error("修改用户最近登录IP，最近登录时间传参异常，e:{}", e);
            throw new SystemServiceException(GlobalErrorCode.SYSTEM_BUSY.getCode(), "登录失败!");
        }
    }
}
