package com.fuiou.ccs.auth.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.ccs.auth.service.ShortLinkService;
import com.fuiou.ccs.auth.vo.NormalLinkVO;
import com.fuiou.ccs.system.dto.CcsShortLinkDto;
import com.fuiou.redis.util.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.stereotype.Service;

import static com.fuiou.ccs.system.constants.CcsCacheConstants.CCS_SHORT_LINK_CACHE_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/4 14:06
 */
@Slf4j
@AllArgsConstructor
@Service
public class ShortLinkServiceImpl implements ShortLinkService {

    private final RedisUtil redisUtil;

    @Override
    public NormalLinkVO getNormalLinkInfo(String shortLinkCode) {
        if (StringUtils.isBlank(shortLinkCode)) {
            throw new UserDeniedAuthorizationException("请输入短链码！");
        }
        String shortLinkRedisKey = CCS_SHORT_LINK_CACHE_KEY + shortLinkCode;
        if (!redisUtil.hasKey(shortLinkRedisKey)) {
            throw new UserDeniedAuthorizationException("链接已失效，请手动登录到系统中查看相关内容！");
        }
        String shortUrlDtoStr = redisUtil.get(shortLinkRedisKey);
        CcsShortLinkDto shortLinkDto = JSONObject.parseObject(shortUrlDtoStr, CcsShortLinkDto.class);
        return NormalLinkVO.builder()
                .username(shortLinkDto.getAccountNum())
                .longLink(shortLinkDto.getLongUrl())
                .params(shortLinkDto.getParams())
                .build();
    }
}
