package com.fuiou.ccs.auth.exception;

import com.fuiou.common.exception.IErrorCode;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public enum AuthErrorCode implements IErrorCode {

    /**
     * 用户名或密码错误
     */
    USER_PASSWORD_ERROR(10001, "用户名或密码错误"),
    /**
     * 没有该用户
     */
    USER_NOT_EXIST(10002, "账号不存在"),
    /**
     * 账号已禁用
     */
    USER_DISABLED(10003, "账号已禁用"),
    /**
     * 该用户已在其它地方登录
     */
    USER_MAX_LOGIN(10004, "该账号已在其它地方登录"),
    /**
     * 用户账号过期
     */
    USER_ACCOUNT_EXPIRED(10005, "账号已过期"),
    /**
     * 用户登录失败
     */
    AUTHENTICATION_FAIL(10006, "认证失败"),
    /**
     * 加密方式不支持
     */
    ENCRYPTION_NOT_SUPPORTED(10007, "加密方式不支持"),
    /**
     * 认证请求已过期
     */
    AUTH_REQUEST_EXPIRED(10008, "认证请求已过期");


    final int code;

    final String msg;

    AuthErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}
