package com.fuiou.ccs.auth.service.impl;

import com.fuiou.ccs.auth.config.properties.SecurityProperties;
import com.fuiou.ccs.auth.service.CaptchaService;
import com.fuiou.captcha.factory.ArithmeticCaptchaFactory;
import com.fuiou.captcha.factory.CaptchaFactory;
import com.fuiou.captcha.service.CaptchaFactoryService;
import com.fuiou.common.api.ApiAssert;
import com.fuiou.common.constants.AuthConstants;
import com.fuiou.common.exception.enums.GlobalErrorCode;
import com.fuiou.redis.util.RedisUtil;
import com.wf.captcha.base.Captcha;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码实现类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Service
public class CaptchaServiceImpl implements CaptchaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CaptchaServiceImpl.class);

    final RedisUtil redisUtil;

    final SecurityProperties securityProperties;

    final CaptchaFactoryService captchaFactoryService;

    public CaptchaServiceImpl(RedisUtil redisUtil,
                              SecurityProperties securityProperties,
                              CaptchaFactoryService captchaFactoryService) {
        this.redisUtil = redisUtil;
        this.securityProperties = securityProperties;
        this.captchaFactoryService = captchaFactoryService;
    }

    @Override
    public Map<String, Object> create(String type) {
        CaptchaFactory captchaFactory = captchaFactoryService.createFactory(type);
        ApiAssert.notNull(GlobalErrorCode.BAD_REQUEST, captchaFactory);

        int width = 200, height = 48, length = 4;
        if (captchaFactory instanceof ArithmeticCaptchaFactory) {
            // 算数长度为2
            length = 2;
        }
        // 创建验证码
        Captcha captcha = captchaFactory.createCaptcha(width, height, length);

        // 将验证码存入缓存中
        String captchaKey = UUID.randomUUID().toString();
        String captchaCode = captcha.text().toLowerCase();
        String captchaRedisKey = AuthConstants.CAPTCHA_CACHE_KEY + captchaKey;
        Integer captchaTimeout = securityProperties.getCaptchaTimeout();
        try {
            // 存入redis并设置过期时间为30分钟
            redisUtil.setnx(captchaRedisKey, captchaCode, captchaTimeout, TimeUnit.MINUTES);
        } catch (Exception e) {
            LOGGER.warn(e.getMessage(), e);
            ApiAssert.failure(GlobalErrorCode.INTERNAL_SERVER_ERROR);
        }
        LOGGER.info("生成验证码：{key: {}, code: {}}", captchaKey, captchaCode);

        // 返回验证码的key和图片
        HashMap<String, Object> data = new HashMap<>(2);
        data.put("key", captchaKey);
        data.put("image", captcha.toBase64());
        return data;
    }

}
