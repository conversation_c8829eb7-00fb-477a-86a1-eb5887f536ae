package com.fuiou.ccs.auth.service;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 认证用户详情 服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuthUserDetailsService extends UserDetailsService {

    /**
     * 根据手机号查询用户
     */
    UserDetails loadUserByMobile(String mobile) throws UsernameNotFoundException;

    /**
     * 忘记密码
     *
     * @param clientId   客户端ID
     * @param username   登录用户名
     * @param mobile     登录用户手机号
     * @param type       类型 (手机号/邮箱）
     * @param captcha    图片验证码
     * @param captchaKey 图片验证码key
     * @return 用户忘记密码 ，通过手机号/邮箱 去修改用户密码
     */
    Boolean forgetPassword(String clientId, String username, String mobile, String type, String captcha, String captchaKey);

    /**
     * 忘记密码时，修改密码
     *
     * @param type        类型 (手机号/邮箱）
     * @param userParam   用户id/用户登录名
     * @param captcha     短信验证码-短信修改密码时使用
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean updatePassword(String type, String userParam, String captcha, String newPassword);


}
