package com.fuiou.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fuiou.excel.support.ExcelImporter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Excel 导入监听器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class ImportListener<T> extends AnalysisEventListener<T> {

    /**
     * 默认批量2048
     */
    private int batchCount = 2048;
    /**
     * 数据集合
     */
    private List<T> dataList = new ArrayList<>(256);
    /**
     * 数据导入类
     */
    private final ExcelImporter<T> importer;

    public ImportListener(final ExcelImporter<T> importer) {
        this.importer = importer;
    }

    public ImportListener(ExcelImporter<T> importer, int batchCount) {
        this.importer = importer;
        this.batchCount = batchCount;
    }

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        dataList.add(data);
        if (dataList.size() >= batchCount) {
            importer.save(dataList);
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        importer.save(dataList);
        dataList.clear();
    }

    public int getBatchCount() {
        return batchCount;
    }

    public void setBatchCount(int batchCount) {
        this.batchCount = batchCount;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    public ExcelImporter<T> getImporter() {
        return importer;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImportListener)) return false;
        ImportListener<?> that = (ImportListener<?>) o;
        return batchCount == that.batchCount && Objects.equals(dataList, that.dataList) && Objects.equals(importer, that.importer);
    }

    @Override
    public int hashCode() {
        return Objects.hash(batchCount, dataList, importer);
    }

    @Override
    public String toString() {
        return "ImportListener{" +
            "batchCount=" + batchCount +
            ", list=" + dataList +
            ", importer=" + importer +
            '}';
    }

}
