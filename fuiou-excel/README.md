# Excel 组件

Excel 组件，根据`alibaba easyexcel`封装了导入、导出操作，核心类：`ExcelUtil`



## 导入

### 前端

```js
export function uploadFile(data) {
  return axios({
    url: '/fuiou-xxx/import',
    method: 'post',
    headers: {
      'Authorization': getToken(),
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

const formData = new FormData()
formData.append('file', file)

uploadFile(formData).then(ret => {
    const {success, msg} = ret.data
    if (success) {
      return Message({
        message: msg,
        type: 'success'
      })
    } else {
      return Message({
        message: msg,
        type: 'error'
      })
    }
  })
```

### 后端

``` java
@PostMapping("/import")
@ApiOperation("excel导入")
ApiResult<Void> upload(MultipartFile file) {
    if (file == null) {
        return ApiResult.fail("文件格式错误");
    }
    
    String fileName = file.getName();
    if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
        return ApiResult.fail("文件格式错误");
    }
    
    try (InputStream inputStream = file.getInputStream()) {
        List<xxxExcel> list = ExcelUtil.read(inputStream, xxxExcel.class)
        // TODO excel对象转业务对象... 
        // TODO 数据校验 -> 数据存入数据库
    } catch (IOException e) {
        LOGGER.warn(e.getMessage(), e)
        return ApiResult.fail("导入失败");
    }
    
    return ApiResult.success("导入成功")
}
```



## 导出

### 前端

```js
// 下载文件方法
export function downloadFile(url, params) {
    let urlParams = 'access_token=' + getToken()
    if (params) {
        urlParams = urlParams + '&' + qs.stringify(params)
    }

    if (!url) return
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url + '?' + urlParams
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link);
    //window.URL.revokeObjectURL(link.href)
}

const queryParams = {...this.queryParams}
// 设置不分页
queryParams.searchCount = false

// 下载导出接口的文件
downloadFile('/api/fuiou-xxx/export', queryParams)

```

### 后端

``` java
@GetMapping("/xxx/export")
@ApiOperation(value = "xxx导出")
void exportPageByStatistics(xxxQuery query, HttpServletResponse response) {
    List<xxxVO> records = page(query).getData().getRecords();
    List<xxxExcel> excelList = xxxConvert.INSTANCE.toExcel(records);
    ExcelUtil.export(response, "文件名", "Sheet页名", excelList, xxxExcel.class);
}
```

