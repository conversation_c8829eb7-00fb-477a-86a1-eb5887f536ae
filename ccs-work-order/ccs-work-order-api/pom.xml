<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ccs-work-order</artifactId>
        <groupId>com.fuiou.ccs</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ccs-work-order-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-common</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-query</artifactId>
            <version>${fuiou-framework.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>