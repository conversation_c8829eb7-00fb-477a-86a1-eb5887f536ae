package com.fuiou.ccs.workorder.api.provider;

import com.fuiou.common.api.ApiResult;

/**
 * 客服工单，对外通知接口
 *
 * <AUTHOR>
 */
public interface ToExternalProvider {

    /**
     * 客服工单业务类型，增加 同步给到外部接口
     *
     * @param busId    业务类型ID
     * @param parentId 上级业务ID
     * @param name     业务名称（修改仅修改名称）
     * @param busLevel 业务树的级别(1-业务类型，2-工单类型，3-工单子类型,4-子类要素)
     * @return
     */
    ApiResult<Boolean> businessAddSync(Long busId, Long parentId, String name, Integer busLevel);

    /**
     * 客服工单业务类型，修改 同步给到外部接口
     *
     * @param busId    业务类型ID
     * @param parentId 上级业务ID
     * @param name     业务名称（修改仅修改名称）
     * @param busLevel 业务树的级别(1-业务类型，2-工单类型，3-工单子类型 4-子类要素)
     * @param status   业务状态（1-正常，0-停用）
     * @return
     */
    ApiResult<Boolean> businessUpdSync(Long busId, String name, Long parentId, Integer busLevel, Integer status);

}
