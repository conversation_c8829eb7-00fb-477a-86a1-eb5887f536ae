package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 历史工单审批日志
 */
@Data
@ApiModel(value = "HistoryLogVO", description = "历史审批日志")
public class HistoryLogVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 审批日志
     */
    private List<EventLogVO> eventLogVOS;
    /**
     * 查看交流意见
     */
    private List<CcsExchangeVO> exchangeVOS;


}
