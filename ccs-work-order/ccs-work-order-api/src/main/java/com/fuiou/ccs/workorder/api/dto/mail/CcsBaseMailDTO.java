package com.fuiou.ccs.workorder.api.dto.mail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "BaseMailDto对象", description = "发送邮件模板数据封装bean")
public class CcsBaseMailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮件模板类型
     */
    @ApiModelProperty("邮件模板类型")
    private String templateShow;

    /**
     * 邮件模板中邮件短链接，按照模板中的顺序来封装集合顺序
     */
    @ApiModelProperty("邮件模板中邮件短链接，按照模板中的顺序来封装集合顺序")
    private List<String> mailUrls;

    /**
     * 驳回dto集合
     */
    @ApiModelProperty("驳回dto集合")
    private List<CcsRejectDto> rejectDtos;

    public CcsRejectDto getInstance() {
        return new CcsRejectDto();
    }

    /**
     * 收件人姓名
     */
    @ApiModelProperty("收件人姓名")
    private String toEmpName;
}
