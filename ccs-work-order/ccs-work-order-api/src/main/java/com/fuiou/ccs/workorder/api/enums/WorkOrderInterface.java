package com.fuiou.ccs.workorder.api.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/28 17:28
 */
public interface WorkOrderInterface {

    /**
     * 工单详情页面，从哪个菜单跳转过去的
     */
    enum WorkDetailVuePageType {

        PENDING("pending", "待处理菜单跳转详情页"),
        VISIT("visit", "回访工单跳转详情页"),
        ALL("all", "全部工单跳转详情页");

        String name;

        String desc;

        WorkDetailVuePageType(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param name
         * @return
         */
        public static WorkDetailVuePageType getInstance(String name) {
            return Arrays.stream(WorkDetailVuePageType.values()).filter(x -> StringUtils.isNotBlank(name) && x.getName().equalsIgnoreCase(name)).findFirst().orElseThrow(() -> new NullPointerException(
                    "WorkDetailVuePageType.name传入值在枚举类中不存在"));
        }
    }


    enum InterfaceNameEnums {
        FUNW_WorkOrder("fuNWWorkOrder", "网络-获取保单信息接口"),
        FUPay_GetOrder("fuPayGetOrder", "支付-信用卡/跨境/基金模板接口"),
        FUPay_GetMchnt("fuPayGetMchnt", "支付-商户业务模板接口/个人支付业务模板接口"),
        FUPay_GetYfk("fuPayGetYfk", "支付-预付卡业务模板接口"),
        FUPay_GetRefund("fuPayGetRefund", "支付-申请/查询退款模板接口"),
        FUPay_GetCancelRefund("fuPayGetCancelRefund", "支付-撤销退款模板接口");

        String name;

        String desc;

        InterfaceNameEnums(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }
    }

}
