package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 外部来源字段信息 视图对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "OutsideSourceColumnVO对象", description = "外部来源字段信息")
public class OutsideSourceColumnVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键字段ID
     */
    @ApiModelProperty("主键字段ID")
    private Long columnId;

    /**
     * 字段英文名
     */
    @ApiModelProperty("字段英文名")
    private String columnName;

    /**
     * 字段中文名
     */
    @ApiModelProperty("字段中文名")
    private String cnColumnName;

    /**
     * 字段是否非空;1-非空，0-不非空（默认）
     */
    @ApiModelProperty("字段是否非空;1-非空，0-不非空（默认）")
    private Integer columnsNotNull;

    /**
     * 表单中对应字段类型
     */
    @ApiModelProperty("表单中对应字段类型")
    private String columnsType;

    /**
     * 外部来源数据的URL的ID
     */
    @ApiModelProperty("外部来源数据的URL的ID")
    private Long sourceUrlId;

}
