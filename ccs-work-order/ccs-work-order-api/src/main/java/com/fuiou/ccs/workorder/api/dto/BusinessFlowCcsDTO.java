package com.fuiou.ccs.workorder.api.dto;

import com.fuiou.ccs.workorder.api.enums.EventEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 路由对应的业务关键数据展示
 *
 * <AUTHOR>
 * @date 2022-5-6
 */
@Data
@ApiModel(value = "BusinessFlowFinDTO", description = "路由对应的业务关键数据展示")
public class BusinessFlowCcsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务表ID
     */
    @ApiModelProperty("业务表ID")
    private Long busId;

    /**
     * 审批事件表ID
     */
    @ApiModelProperty("审批事件表ID")
    private Long eventId;

    /**
     * 业务类别
     */
    @ApiModelProperty("业务类别")
    private String busType;

    /**
     * 业务编号
     */
    @ApiModelProperty("业务编号")
    private String busCode;

    /**
     * 审批事件表状态
     */
    @ApiModelProperty("审批事件表状态")
    private Integer eventStatus;

    /**
     * 审批事件表状态名
     */
    @ApiModelProperty("审批事件表状态名")
    private String eventStatusName;

    public String getEventStatusName() {
        if (eventStatus != null) {
            return EventEnums.StatusEnums.getInstance(eventStatus).getMsg();
        }
        return eventStatusName;
    }

    /**
     * 发起人
     */
    @ApiModelProperty("发起人")
    private String creatorId;

    /**
     * 发起人姓名
     */
    @ApiModelProperty("发起人姓名")
    private String creatorEmpName;

    /**
     * 发起时间
     */
    @ApiModelProperty("发起时间")
    private LocalDateTime createTime;

    /**
     * 流程实例ID
     */
    @ApiModelProperty("流程实例ID")
    private String processInstanceId;

    /**
     * 路由KEY
     */
    @ApiModelProperty("路由KEY")
    private String processKey;
}
