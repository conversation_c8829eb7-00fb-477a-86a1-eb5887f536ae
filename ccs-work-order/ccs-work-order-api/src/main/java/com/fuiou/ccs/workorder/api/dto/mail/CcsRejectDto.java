package com.fuiou.ccs.workorder.api.dto.mail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "RejectDto对象", description = "驳回对象bean")
public class CcsRejectDto implements Serializable {

    private static final long serialVersionUID = 2L;

    /**
     * 驳回人
     */
    @ApiModelProperty("驳回人")
    private String rejectEmpName;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String rejectReason;

    /**
     * 驳回时间
     */
    @ApiModelProperty("驳回时间")
    private String rejectTime;
}
