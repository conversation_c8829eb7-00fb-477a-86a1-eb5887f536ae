package com.fuiou.ccs.workorder.api.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Tolerate;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 客服工单业务提交入参
 *
 * <AUTHOR>
 * @date 2022-8-1
 */
@Data
@Builder(toBuilder = true)
@EqualsAndHashCode
@ApiModel(value = "WorkOrderParam", description = "客服工单业务提交入参")
public class WorkOrderParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Tolerate
    public WorkOrderParam() {
    }

    @ApiModelProperty(value = "表单配置ID", required = true)
    @NotNull(message = "表单配置ID不能为空", groups = {Create.class, Query.class})
    private Long formId;

    @ApiModelProperty(value = "公共字段Map", required = true)
    @NotNull(message = "公共字段Map不能为空", groups = {Create.class})
    private Map<String, Object> commonParam;

    @ApiModelProperty(value = "非公共字段Map", required = true)
    @NotNull(message = "非公共字段Map不能为空", groups = {Create.class})
    private Map<String, Object> presetParam;

    @ApiModelProperty(value = "查询外部来源数据入参Map")
    @NotNull(message = "查询外部来源数据入参Map不能为空", groups = {Query.class})
    private Map<String, Object> requestMap;

    @ApiModelProperty(value = "外部来源urlId(返回多个外部链接集合从其中选择的符合的urlId)")
    @NotNull(message = "外部来源urlId不能为空", groups = {Query.class})
    private Long sourceUrlId;

    @ApiModelProperty(value = "是否新建完结工单，默认否")
    private Boolean isFinishWorkFlag;

    public interface Create {

    }

    public interface Query {

    }
}
