package com.fuiou.ccs.workorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 回访
 */
@Data
@Builder(toBuilder = true)
@EqualsAndHashCode
@ApiModel(value = "VisitDTO", description = "回访")
public class VisitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事件ID", required = true)
    private Long eventId;

    @ApiModelProperty(value = "返回结果", required = true)
    private String msg;


}
