package com.fuiou.ccs.workorder.api.vo;

import com.fuiou.ccs.workorder.api.enums.EventEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客服工单事件审批 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "EventVO对象", description = "客服工单事件审批")
public class CcsEventVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @ApiModelProperty("id主键")
    private Long eventId;

    /**
     * 事件类型，所选路由表中的租户id
     */
    @ApiModelProperty("事件类型，所选路由表中的租户id")
    private String eventType;

    /**
     * 工单数据表名
     */
    @ApiModelProperty("工单数据表名")
    private String formName;

    /**
     * 工单数据表id
     */
    @ApiModelProperty("工单数据表id")
    private Long formInfoId;

    /**
     * 路由key
     */
    @ApiModelProperty("路由key")
    private String processKey;

    /**
     * 路由流程实例id
     */
    @ApiModelProperty("路由流程实例id")
    private String processInstanceId;

    /**
     * 审批状态：1、已发起(默认)，2、审批中，3，已驳回，4-重新提交，5-审批通过(待回访)，6-工单完结
     */
    @ApiModelProperty("审批状态：1、已发起(默认)，2、审批中，3，已驳回，4-重新提交，5-审批通过(待回访)，6-工单完结")
    private Integer status;

    /**
     * 审批状态：1、已发起(默认)，2、审批中，3，已驳回，4-重新提交，5-审批通过(待回访)，6-工单完结
     */
    @ApiModelProperty("审批状态：1、已发起(默认)，2、审批中，3，已驳回，4-重新提交，5-审批通过(待回访)，6-工单完结")
    private String statusName;

    public String getStatusName() {
        if (status != null) {
            return EventEnums.StatusEnums.getInstance(status).getMsg();
        }
        return statusName;
    }

    /**
     * 终止状态：0-正常（默认），1-已暂停(可恢复)，2-已作废（不能恢复）
     */
    @ApiModelProperty("终止状态：0-正常（默认），1-已暂停(可恢复)，2-已作废（不能恢复）")
    private Integer stopStatus;

    /**
     * 终止状态：0-正常（默认），1-已暂停(可恢复)，2-已作废（不能恢复）
     */
    @ApiModelProperty("终止状态：0-正常（默认），1-已暂停(可恢复)，2-已作废（不能恢复）")
    private String stopStatusName;

    public String getStopStatusName() {
        if (stopStatus != null) {
            return EventEnums.StopStatusEnums.getInstance(stopStatus).getMsg();
        }
        return stopStatusName;
    }

    /**
     * 当前任务实际已认领处理人
     */
    @ApiModelProperty("当前任务实际已认领处理人")
    private String claimUserId;

    /**
     * 当前任务实际已认领处理人
     */
    @ApiModelProperty("当前任务实际已认领处理人")
    private String claimUserEmpName;

    /**
     * 是否加急节点：0-否（默认），1-是
     */
    @ApiModelProperty("是否加急节点：0-否（默认），1-是")
    private Integer hurryStatus;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateId;

    /**
     * 当前任务是否需要认领：0-不需要（默认），1-需要
     */
    @ApiModelProperty(value = "当前任务是否需要认领：0-不需要（默认），1-需要")
    private Integer claimStatus;

    /**
     * 当前节点任务办理审批人为组任务时，记录，多个组逗号分隔
     */
    @ApiModelProperty(value = "当前节点任务办理审批人为组任务时，记录，多个组逗号分隔")
    private String groupApproves;

    /**
     * 当前任务节点为单人/多人审批节点时，记录，多个审批人逗号分隔
     */
    @ApiModelProperty(value = "当前任务节点为单人/多人审批节点时，记录，多个审批人逗号分隔")
    private String userApproves;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorEmpName;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateEmpName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTimeStr;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 当前路由流程，当前任务节点名称
     */
    @ApiModelProperty("当前路由流程，当前任务节点名称")
    private String processTaskName;

    /**
     * 当前节点所有的任务id集合
     */
    @ApiModelProperty("当前节点所有的任务id集合")
    private String taskIds;

    /**
     * 如果当前节点任务ID只有一个的话，直接放到这个值里面
     */
    @ApiModelProperty("如果当前节点任务ID只有一个的话，直接放到这个值里面")
    private String currentTaskId;

    /**
     * 当前节点任务活动ID
     */
    @ApiModelProperty("当前节点任务活动ID")
    private String activityId;

    /**
     * 回访用户user_id， 单选
     */
    @ApiModelProperty("回访用户user_id,单选")
    private String visitUserId;

    /**
     * 回访用户姓名
     */
    @ApiModelProperty("回访用户姓名")
    private String visitEmpName;

    /**
     * 回访组ID，单选
     */
    @ApiModelProperty("回访组ID，单选")
    private Long visitGroupId;

    /**
     * 回访组名称，单选
     */
    @ApiModelProperty("回访组名称")
    private String visitGroupName;

    /**
     * 如果选择了工单回访，记录新的回访工单的数据表ID
     */
    @ApiModelProperty("如果选择了工单回访，记录新的回访工单的数据表ID")
    private Long visitFormInfoId;

    /**
     * 是否选了回访人：0-没选（默认），1-选了
     */
    @ApiModelProperty("是否选了回访人：0-没选（默认），1-选了")
    private Integer visitUserStatus;

    /**
     * 回访结果：1-情况相符客户满意，2-情况相符客户不满意，3-情况不相符客户满意，4-情况不相符客户不满意，5-客户未接通
     */
    @ApiModelProperty("回访结果：1-情况相符客户满意，2-情况相符客户不满意，3-情况不相符客户满意，4-情况不相符客户不满意，5-客户未接通")
    private Integer visitResult;

    /**
     * 工单回访，处理方式：0-工单完结，1-重新调查，2-记录结果暂不处理
     */
    @ApiModelProperty("工单回访，处理方式：0-工单完结，1-重新调查，2-记录结果暂不处理")
    private Integer visitStatus;

    /**
     * 当前节点是否为上一节点处理人指派认领的：0-否（默认），1-是
     */
    @ApiModelProperty("当前节点是否为上一节点处理人指派认领的：0-否（默认），1-是")
    private Integer currentNodeAssignedStatus;

    /**
     * 锁定人
     */
    @ApiModelProperty("锁定人")
    private String lockUserId;

    /**
     * 锁定人
     */
    @ApiModelProperty("锁定人")
    private String lockUserEmpName;

    /**
     * 锁定时间
     */
    @ApiModelProperty("锁定时间")
    private LocalDateTime lockTime;

    /**
     * 锁定状态：默认0-未锁定，1-已锁定
     */
    @ApiModelProperty("锁定状态：默认0-未锁定，1-已锁定")
    private Integer lockStatus;

    /**
     * 动态工单数据bean，导出excel工具类
     */
    @ApiModelProperty(value = "动态工单数据bean，导出excel工具类")
    private SysFormEventVO sysFormEventVO;

    /**
     * 封装界面按钮是否展示
     */
    @ApiModelProperty(value = "封装界面按钮是否展示")
    private EventPermissionVO eventPermissionVO;

    /**
     * 可以指派的组id，逗号分隔
     */
    @ApiModelProperty(value = "可以指派的组id，逗号分隔")
    private String assignGroupIds;

    /**
     * 动态表头字段映射集合
     */
    @ApiModelProperty(value = "动态表头字段映射集合")
    private Map<String, Object> headerColumns;

    /**
     * 审批日志列表
     */
    @ApiModelProperty(value = "审批日志列表")
    private List<EventLogVO> eventLogVOList;

    /**
     * 意见交流记录
     */
    @ApiModelProperty(value = "意见交流记录")
    private List<CcsExchangeVO> exchangeVOList;
}
