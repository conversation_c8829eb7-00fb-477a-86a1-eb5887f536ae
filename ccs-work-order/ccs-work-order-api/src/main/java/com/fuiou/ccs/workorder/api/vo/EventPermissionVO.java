package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 封装界面按钮是否展示
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "EventPermissionVO", description = "封装界面按钮是否展示")
public class EventPermissionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待处理列表--认领按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理列表--认领按钮是否显示：false-不显示，true-显示")
    private boolean claimFlag;

    /**
     * 待处理列表--指派按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理列表--指派按钮是否显示：false-不显示，true-显示")
    private boolean assignFlag;

    /**
     * 待处理列表--取消认领按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理列表--取消认领按钮是否显示：false-不显示，true-显示")
    private boolean cancelClaimFlag;

    // =======待处理工单详情页按钮：发起人撤销、管理员暂停和恢复、审批人提交按钮、发起人重新提交按钮，补充信息提交按钮
    /**
     * 待处理工单详情页按钮--发起人撤销是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--发起人撤销是否显示：false-不显示，true-显示")
    private boolean sponsorCancel;

    /**
     * 发起人是否可直接完结
     */
    @ApiModelProperty(value = "发起人是否可直接完结")
    private boolean sponsorOver;

    /**
     * 待处理工单详情页按钮--管理员暂停是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--管理员暂停是否显示：false-不显示，true-显示")
    private boolean adminPause;

    /**
     * 待处理工单详情页按钮--管理员恢复是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--管理员恢复是否显示：false-不显示，true-显示")
    private boolean adminRestart;

    /**
     * 待处理工单详情页按钮--审批通过是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--审批通过是否显示：false-不显示，true-显示")
    private boolean approveComplete;

    /**
     * 待处理工单详情页按钮--审批驳回是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--审批驳回是否显示：false-不显示，true-显示")
    private boolean approveReject;

    /**
     * 待处理工单详情页按钮--发起人重新提交是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "待处理工单详情页按钮--发起人重新提交是否显示：false-不显示，true-显示")
    private boolean sponsorRestartSubmit;

    //=========================
    /**
     * 待处理列表--指派按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "回访列表--指派回访人按钮是否显示：false-不显示，true-显示")
    private boolean visitAssignFlag;

    /**
     * 待处理列表--认领按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "回访列表--认领按钮是否显示：false-不显示，true-显示")
    private boolean visitClaimFlag;

    /**
     * 待处理列表--取消认领按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "回访列表--取消认领按钮是否显示：false-不显示，true-显示")
    private boolean visitCancelClaimFlag;

    /**
     * 待处理列表--指派按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "回访列表--指派他人认领按钮是否显示：false-不显示，true-显示")
    private boolean visitAssignOtherUserFlag;

    /**
     * 待处理列表--指派按钮是否显示：false-不显示，true-显示
     * 默认false
     */
    @ApiModelProperty(value = "回访列表--提交按钮是否显示：false-不显示，true-显示")
    private boolean visitSubmit;


}
