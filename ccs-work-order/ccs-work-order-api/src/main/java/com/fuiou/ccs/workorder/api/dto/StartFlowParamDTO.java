package com.fuiou.ccs.workorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.Map;

/**
 * 路由启动入参，针对批量处理
 *
 * <AUTHOR>
 * @date 2022-4-13
 */
@Builder(toBuilder = true)
@Data
@ApiModel(value = "StartFlowParamDto", description = "路由启动入参，针对批量处理")
public class StartFlowParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Tolerate
    public StartFlowParamDTO() {
    }

    /**
     * 流程实例key
     */
    @ApiModelProperty("流程实例key")
    private String processKey;

    /**
     * 【动态表】工单数据表表名
     */
    @ApiModelProperty("【动态表】工单数据表表名")
    private String formName;

    /**
     * 业务表对应类型
     */
    @ApiModelProperty("工单数据表ID")
    private Long formInfoId;

    /**
     * 租户类型
     */
    @ApiModelProperty("租户类型")
    private String eventType;

    /**
     * 启动路由初始入参集合
     */
    @ApiModelProperty("启动路由初始入参集合")
    private Map<String, Object> varParams;

    /**
     * 发起人姓名
     */
    @ApiModelProperty("发起人姓名")
    private String userEmpName;

    /**
     * 发起人userId
     */
    @ApiModelProperty("发起人userId")
    private String userId;

    /**
     * 是否加急任务：0-否（默认），1-是
     */
    @ApiModelProperty("是否加急任务：0-否（默认），1-是")
    private Integer hurryStatus;

}
