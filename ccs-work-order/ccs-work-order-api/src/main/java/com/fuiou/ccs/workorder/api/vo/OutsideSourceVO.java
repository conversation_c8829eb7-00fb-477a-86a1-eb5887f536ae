package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 外部来源配置 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "OutsideSourceVO对象", description = "外部来源配置")
public class OutsideSourceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键外部来源数据的URL的ID
     */
    @ApiModelProperty("主键外部来源数据的URL的ID")
    private Long sourceUrlId;

    /**
     * 请求描述
     */
    @ApiModelProperty("请求描述")
    private String methodDesc;

    /**
     * 封装入参方法名
     */
    @ApiModelProperty("封装入参方法名")
    private String methodName;

    /**
     * 外部来源字段信息集合
     */
    @ApiModelProperty("外部来源字段信息集合")
    private List<OutsideSourceColumnVO> outsideSourceColumnList;

}
