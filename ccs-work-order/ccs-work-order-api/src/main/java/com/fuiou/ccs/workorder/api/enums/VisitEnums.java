package com.fuiou.ccs.workorder.api.enums;

import com.fuiou.common.enums.IEnum;

import java.util.Arrays;

/**
 * 审批事件表枚举集合
 */
public interface VisitEnums {

    /**
     * ===================回访结果==================
     */
    // enum VisitResult implements IEnum {
    //     OPTION_MATCH_SATISFIED_CUSTOMER(1, "情况相符客户满意"),
    //     OPTION_MATCH_SATISFIED_NOT_CUSTOMER(2, "情况相符客户不满意"),
    //     OPTION_NOT_MATCH_SATISFIED_CUSTOMER(3, "情况不相符客户满意"),
    //     OPTION_NOT_MATCH_SATISFIED_NOT_CUSTOMER(4, "情况不相符客户不满意"),
    //     CUSTOMER_NOT_CONNECTED(5, "客户未接通"),
    //     ;
    //     final int type;
    //     final String msg;
    //
    //     @Override
    //     public int getValue() {
    //         return type;
    //     }
    //
    //     public String getMsg() {
    //         return msg;
    //     }
    //
    //     VisitResult(int type, String msg) {
    //         this.type = type;
    //         this.msg = msg;
    //     }
    //
    //     /**
    //      * 获取枚举是否存在，并返回
    //      *
    //      * @param type
    //      * @return
    //      */
    //     public static VisitResult getInstance(Integer type) {
    //         return Arrays.stream(VisitResult.values()).filter(x -> x.getValue() == type).findFirst().orElseThrow(() -> new NullPointerException(
    //                 "VisitResult.type.传入值在枚举类中不存在"));
    //     }
    // }

    /**
     * ===================工单回访，处理方式==================
     */
    enum VisitStatus implements IEnum {
        OVER(0, "工单完结"),
        REPEAT_REVIEW(1, "重新调查"),
        NOT_DEAL_WITH(2, "记录结果暂不处理");
        final int type;
        final String msg;

        @Override
        public int getValue() {
            return type;
        }

        public String getMsg() {
            return msg;
        }

        VisitStatus(int type, String msg) {
            this.type = type;
            this.msg = msg;
        }

        /**
         * 获取枚举是否存在，并返回
         *
         * @param type
         * @return
         */
        public static VisitStatus getInstance(Integer type) {
            return Arrays.stream(VisitStatus.values()).filter(x -> x.getValue() == type).findFirst().orElseThrow(() -> new NullPointerException(
                    "VisitStatus.type.传入值在枚举类中不存在"));
        }
    }
}
