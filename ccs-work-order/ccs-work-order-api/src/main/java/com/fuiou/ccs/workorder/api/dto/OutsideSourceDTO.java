package com.fuiou.ccs.workorder.api.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 外部来源配置 数据传输对象
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@ApiModel(value = "OutsideSourceDTO对象", description = "外部来源配置")
public class OutsideSourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键外部来源数据的URL的ID
     */
    @ApiModelProperty("主键外部来源数据的URL的ID")
    private Long sourceUrlId;

    /**
     * 请求描述
     */
    @ApiModelProperty("请求描述")
    private String methodDesc;

    /**
     * 封装入参方法名
     */
    @ApiModelProperty("封装入参方法名")
    private String methodName;

}
