package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExternalDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码
     */
    @ApiModelProperty("返回码")
    private Integer code;

    /**
     * 对返回码的文本描述内容
     */
    @ApiModelProperty("对返回码的文本描述内容")
    private String desc;

    /**
     * 数据集合
     */
    @ApiModelProperty("数据集合")
    private Object data;

    /**
     * 返回是否成功
     */
    @ApiModelProperty("返回是否成功")
    private Boolean success;

}
