package com.fuiou.ccs.workorder.api.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Tolerate;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 回访结果提交入参
 *
 * <AUTHOR>
 * @date 2022-7-29
 */
@Data
@Builder(toBuilder = true)
@EqualsAndHashCode
@ApiModel(value = "VisitParam", description = "回访结果提交入参")
public class VisitParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Tolerate
    public VisitParam() {
    }

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID", required = true)
    @NotNull(message = "事件ID不能为空", groups = {Create.class})
    private Long eventId;

    /**
     * 回访结果：1-情况相符客户满意，2-情况相符客户不满意，3-情况不相符客户满意，4-情况不相符客户不满意，5-客户未接通
     */
    @ApiModelProperty(value = "回访结果：1-情况相符客户满意，2-情况相符客户不满意，3-情况不相符客户满意，4-情况不相符客户不满意，5-客户未接通", required = true)
    @NotNull(message = "回访结果不能为空", groups = {Create.class})
    private Integer visitResult;

    /**
     * 工单回访，处理方式：0-工单完结，1-重新调查，2-记录结果暂不处理
     */
    @ApiModelProperty(value = "工单回访，处理方式：0-工单完结，1-重新调查，2-记录结果暂不处理", required = true)
    @NotNull(message = "工单回访，处理方式不能为空", groups = {Create.class})
    private Integer visitStatus;

    /**
     * 发起路由key入参
     */
    @ApiModelProperty("发起路由key入参")
    private String processKey;

    /**
     * 新路由选择的事件等级
     */
    @ApiModelProperty("新路由选择的事件等级")
    private Integer eventLevel;

    /**
     * 2023-04-19客服增加需求，批量认领——》批量驳回，批量通过================
     */
    /**
     * 批量操作时传入的事件ID集合
     * key-eventId，value-工单事件编号
     */
    @ApiModelProperty(value = "批量操作时传入的事件ID集合")
    private Map<String, String> batchEventIdSet;

    /**
     * 备注，必填
     */
    @ApiModelProperty(value = "备注，必填", required = true)
    private String comments;

    /**
     * 2023-5-8客服二期需求，增加
     * 1，回访结果要增加备注框，3项结果都要填写，非必填
     * 2，重新调查：
     * 2.1，增加重选3要素的功能，及事件等级，及重选路由
     * 2.2，增加事件描述填写功能，新生成的工单替换掉原工单的事件描述
     * 2.3，重选的路由，必须满足同一个表单
     */
    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private Long busId;

    /**
     * 工单类型
     */
    @ApiModelProperty("工单类型")
    private Long workId;

    /**
     * 工单子类
     */
    @ApiModelProperty("工单子类")
    private Long workSubId;

    /**
     * 子类要素
     */
    @ApiModelProperty("子类要素")
    private Long subElementId;

    /**
     * 事件描述
     */
    @ApiModelProperty("事件描述")
    private String eventDesc;

    public interface Create {

    }

}





