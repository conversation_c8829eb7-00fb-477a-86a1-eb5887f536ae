package com.fuiou.ccs.workorder.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单业务数据备注信息记录 视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ApiModel(value = "ExchangeVO对象", description = "工单业务数据备注信息记录")
public class CcsExchangeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 备注信息，正文内容（html标签格式）
     */
    @ApiModelProperty("备注信息，正文内容（html标签格式）")
    private String exchangeContent;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorEmpName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 其他系统传过来的交流意见保存，记录用户名
     */
    @ApiModelProperty(value = "其他系统传过来的交流意见保存，记录用户名")
    private String outEmpName;

    /**
     * 工单表ID
     */
    @ApiModelProperty(value = "工单表ID")
    private Long formInfoId;

    /**
     * 当前处理人
     */
    @ApiModelProperty(value = "当前处理人")
    private String assignee;

    /**
     * 多选用户
     */
    @ApiModelProperty(value = "多选用户")
    private String candidateusers;

    /**
     * 候选组
     */
    @ApiModelProperty(value = "候选组")
    private String candidategroups;

    /**
     * 当前任务节点名称
     */
    @ApiModelProperty(value = "当前任务节点名称")
    private String nodeName;

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID")
    private Long eventId;

    /**
     * 当前事件状态
     */
    @ApiModelProperty(value = "当前事件状态")
    private Integer eventStatus;

}
