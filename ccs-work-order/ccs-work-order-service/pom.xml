<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ccs-work-order</artifactId>
        <groupId>com.fuiou.ccs</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ccs-work-order-service</artifactId>

    <dependencies>
        <!-- fuiou cloud dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-file-api</artifactId>
            <version>${fuiou-file-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-cloud-common</artifactId>
            <version>${fuiou-cloud-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou.ccs</groupId>
            <artifactId>ccs-work-order-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou.ccs</groupId>
            <artifactId>ccs-system-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- fuiou-framework dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-framework-all</artifactId>
            <version>${fuiou-framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fuiou</groupId>
                    <artifactId>fuiou-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fuiou</groupId>
                    <artifactId>fuiou-rabbitmq</artifactId>
                </exclusion>
                <!--                <exclusion>-->
                <!--                    <groupId>com.fuiou</groupId>-->
                <!--                    <artifactId>fuiou-mail</artifactId>-->
                <!--                </exclusion>-->
            </exclusions>
        </dependency>

        <!-- fuiou crypto dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-crypto</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>

        <!-- spring boot dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- spring security crypto dependency -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>5.2.15.RELEASE</version>
        </dependency>

        <!-- alibaba druid dependency -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- code generator dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>code-generator</artifactId>
            <version>1.0.0</version>
            <scope>test</scope>
        </dependency>

        <!-- knife4j swagger dependency -->
        <!-- 使用gateway管理时打开此jar包引用-->
        <!--        <dependency>-->
        <!--            <groupId>com.github.xiaoymin</groupId>-->
        <!--            <artifactId>knife4j-micro-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>

        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.10.9</version>
        </dependency>

        <!-- fuiou crypto dependency -->
        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-crypto</artifactId>
            <version>${fuiou-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fuiou.ccs</groupId>
            <artifactId>ccs-system-api</artifactId>
            <version>${ccs-system-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fuiou</groupId>
            <artifactId>fuiou-sk-cipher</artifactId>
            <version>2.1</version>
        </dependency>

        <!--junit测试-->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <version>1.6.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>5.6.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>ccs-work-order</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <mainClass>com.fuiou.ccs.workorder.WordOrderApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>db2</id>
            <dependencies>
                <dependency>
                    <groupId>com.ibm.db2</groupId>
                    <artifactId>jcc</artifactId>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>mysql8</id>
            <dependencies>
                <dependency>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>
