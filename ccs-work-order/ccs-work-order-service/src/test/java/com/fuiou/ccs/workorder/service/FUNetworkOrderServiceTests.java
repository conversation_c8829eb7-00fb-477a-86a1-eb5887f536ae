package com.fuiou.ccs.workorder.service;

import com.fuiou.ccs.workorder.WordOrderApplication;
import com.fuiou.ccs.workorder.repository.entity.Event;
//import com.fuiou.ccs.workorder.thirdparty.FUNetworkService;
import com.fuiou.ccs.workorder.thirdparty.WorkOrderStrategyContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/12 11:38
 */
@ActiveProfiles("test")
@SpringBootTest(classes = WordOrderApplication.class, webEnvironment = SpringBootTest.WebEnvironment.MOCK)
public class FUNetworkOrderServiceTests {

    @Autowired
    private WorkOrderStrategyContext workOrderStrategyContext;

    @Test
    public void getOrderInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("policyNo", "PWVI21110110900000000050");
        Map<String, Object> resultMap = workOrderStrategyContext.getStrategy("fuNetworkOrder").excute(map);
        System.out.println(resultMap);
    }


    public static void main(String[] args) {

        long l = System.currentTimeMillis() / 1000;

        System.out.println(l);

    }


    public void fun() {

        List<Event> list = new ArrayList<>(8);
        Map<Long, List<Event>> collect = list.stream().collect(Collectors.groupingBy(Event::getEventId));

        Map<String, Object> param = new HashMap<>(8);

        Set<String> strings = param.keySet();
        List<Long> b = new ArrayList<>(8);

        int con = 0;

        for (Long a : collect.keySet()) {
            List<Event> events = collect.get(a);
            con = 0;
            for (Event e : events) {
                con = 1;
                if (!strings.contains(e.getProcessTaskName())) {
                    con = 2;
                    break;
                }
            }

            if (con == 1) {
                b.add(a);
            }
        }


    }


}
