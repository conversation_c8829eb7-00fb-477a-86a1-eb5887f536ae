package com.fuiou.ccs.workorder.code.generator;

import com.fuiou.code.generator.SimpleAutoGenerator;
import com.fuiou.code.generator.config.DataSourceConfig;
import com.fuiou.code.generator.config.GlobalConfig;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * mysql 代码生成
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public class MysqlAutoGenerator extends SimpleAutoGenerator {

    @Override
    public GlobalConfig globalConfig() {
        // TODO 记得改作者、版本、模块名等信息
        return new GlobalConfig("xxx", "1.0.0", "com.fuiou.flow.modules", "xxx")
                .swagger(true).outputDir("D:\\Downloads\\Generator");
    }

    @Override
    public DataSourceConfig dataSourceConfig() {
        String url = "*******************************************************************************************************************************************************************";
        String username = "root";
        String password = "root";
        return new DataSourceConfig(url, username, password);
    }

    @Override
    public List<String> includeTables() {
        return Lists.newArrayList("", "");
    }

    public static void main(String[] args) {
        MysqlAutoGenerator autoGenerator = new MysqlAutoGenerator();
        autoGenerator.execute();
    }

}
