spring:
  cloud:
    nacos:
      server-addr: *************:9848
      username: nacos
      password: 1qazse4
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: dd4a6df7-e29f-48ee-a551-fd695921bd2c
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: dd4a6df7-e29f-48ee-a551-fd695921bd2c

dubbo:
  cloud:
    subscribed-services:
  protocol:
    # # dubbo 协议端口（ -1 表示自增端口，从 20880 开始）
    port: -1
    name: dubbo
  application:
    name: ccs-workorder
  registry:
    address: nacos://*************:9848?namespace=dd4a6df7-e29f-48ee-a551-fd695921bd2c
    username: nacos
    password: 1qazse4
    simplified: true
