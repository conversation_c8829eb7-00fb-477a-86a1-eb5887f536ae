spring:
  cloud:
    nacos:
      server-addr: *************:9848
      username: nacos
      password: 1qazse4
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: 95c726fe-a383-4862-a134-d7d3416bcf7e
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: 95c726fe-a383-4862-a134-d7d3416bcf7e

dubbo:
  cloud:
    subscribed-services: ccs-system
  protocol:
    # # dubbo 协议端口（ -1 表示自增端口，从 20880 开始）
    port: -1
    name: dubbo
  application:
    name: ccs-workorder
  registry:
    address: nacos://*************:9848?namespace=95c726fe-a383-4862-a134-d7d3416bcf7e
    username: nacos
    password: 1qazse4
    simplified: true

