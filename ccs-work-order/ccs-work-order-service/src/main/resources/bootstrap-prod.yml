spring:
  cloud:
    nacos:
      server-addr: **********:8848
      username: nacos
      password: VDDGnYjdMPNIyBkw
      # 配置加载顺序 shared-configs > extension-configs > data id (spring.application.name)
      config:
        file-extension: yml
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d
        # 共享配置，默认
        shared-configs:
          - data-id: ccs-cloud.yml
            group: GLOBALE_GROUP
            refresh: false
      discovery:
        namespace: a522c8a7-9355-48a5-8c00-27dfafe1796d

dubbo:
  cloud:
    subscribed-services:
  protocol:
    # # dubbo 协议端口（ -1 表示自增端口，从 20880 开始）
    port: -1
    name: dubbo
  application:
    name: ccs-workorder
  registry:
    address: nacos://**********:8848?namespace=a522c8a7-9355-48a5-8c00-27dfafe1796d
    username: nacos
    password: VDDGnYjdMPNIyBkw
    simplified: true
