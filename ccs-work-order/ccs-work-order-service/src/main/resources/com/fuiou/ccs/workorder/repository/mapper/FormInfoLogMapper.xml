<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.FormInfoLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.FormInfoLog">
        <id column="LOG_ID" property="logId"/>
        <result column="FORM_NAME" property="formName"/>
        <result column="FORM_INFO_ID" property="formInfoId"/>
        <result column="CONTENT" property="content"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        LOG_ID,
        FORM_NAME,
        FORM_INFO_ID,
        CONTENT,
        CREATOR_ID,
        CREATE_TIME
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapVO" type="com.fuiou.ccs.workorder.api.vo.FormInfoLogVO">
        <result column="CONTENT" property="content"/>
        <result column="CREATOR_EMP_NAME" property="creatorEmpName"/>
        <result column="CREATE_TIME" property="createTime"/>
    </resultMap>

    <!--根据表名，表数据id,查询工单日志表数据集合-->
    <select id="queryFormInfoLogList" resultMap="BaseResultMapVO">
        select l.CREATE_TIME, l.CONTENT, u.EMP_NAME as CREATOR_EMP_NAME
        from T_FORM_INFO_LOG l
                     left join t_sys_user u on l.CREATOR_ID = u.USER_ID
        where l.FORM_NAME = #{formName}
          and l.FORM_INFO_ID = #{formInfoId}
    </select>
</mapper>
