<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.ExchangeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.Exchange">
        <id column="EXCHANGE_ID" property="exchangeId"/>
        <result column="FORM_NAME" property="formName"/>
        <result column="FORM_INFO_ID" property="formInfoId"/>
        <result column="EXCHANGE_CONTENT" property="exchangeContent"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="OUT_EMP_NAME" property="outEmpName"/>
        <result column="ASSIGNEE" property="assignee"/>
        <result column="CANDIDATEUSERS" property="candidateusers"/>
        <result column="CANDIDATEGROUPS" property="candidategroups"/>
        <result column="NODE_NAME" property="nodeName"/>
        <result column="EVENT_ID" property="eventId"/>
        <result column="EVENT_STATUS" property="eventStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        EXCHANGE_ID,
        FORM_NAME,
        FORM_INFO_ID,
        EXCHANGE_CONTENT,
        CREATOR_ID,
        CREATE_TIME,
        OUT_EMP_NAME,
        ASSIGNEE,
        CANDIDATEUSERS,
        CANDIDATEGROUPS,
        NODE_NAME,
        EVENT_ID,
        EVENT_STATUS
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapVO" type="com.fuiou.ccs.workorder.api.vo.CcsExchangeVO">
        <result column="EXCHANGE_CONTENT" property="exchangeContent"/>
        <result column="CREATOR_EMP_NAME" property="creatorEmpName"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="OUT_EMP_NAME" property="outEmpName"/>
        <result column="FORM_INFO_ID" property="formInfoId"/>
        <result column="ASSIGNEE" property="assignee"/>
        <result column="CANDIDATEUSERS" property="candidateusers"/>
        <result column="CANDIDATEGROUPS" property="candidategroups"/>
        <result column="NODE_NAME" property="nodeName"/>
        <result column="EVENT_ID" property="eventId"/>
        <result column="EVENT_STATUS" property="eventStatus"/>
    </resultMap>

    <!--根据工单数据表名， 工单数据表id，查询意见交流记录-->
    <select id="queryExchangeList" resultMap="BaseResultMapVO">
        select c.CREATE_TIME, c.EXCHANGE_CONTENT, u.EMP_NAME as CREATOR_EMP_NAME,
        c.OUT_EMP_NAME,c.NODE_NAME,c.EVENT_STATUS,c.EVENT_ID,c.ASSIGNEE,c.CANDIDATEUSERS,c.CANDIDATEGROUPS
        from T_EXCHANGE c
                     left join T_SYS_USER u on c.CREATOR_ID = u.USER_ID
        where c.FORM_NAME = #{formName}
          and c.FORM_INFO_ID = #{formInfoId}
    </select>

    <select id="queryExchangeListByInfoId" resultMap="BaseResultMapVO">
        select c.CREATE_TIME, c.EXCHANGE_CONTENT, nvl(c.OUT_EMP_NAME,u.EMP_NAME) as CREATOR_EMP_NAME,c.FORM_INFO_ID
        ,c.NODE_NAME,c.EVENT_STATUS,c.EVENT_ID,c.ASSIGNEE,c.CANDIDATEUSERS,c.CANDIDATEGROUPS
        from T_EXCHANGE c
        left join T_SYS_USER u on c.CREATOR_ID = u.USER_ID
        where c.FORM_INFO_ID  in
        <foreach collection="subList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
</mapper>
