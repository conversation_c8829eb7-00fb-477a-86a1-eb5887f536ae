<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.SequenceMapper">


    <select id="getCcsSeq" resultType="com.fuiou.ccs.workorder.repository.entity.CcsSeq">
        SELECT s.VALUE, s.version
        FROM T_SYS_SEQ s
        WHERE s.KEY = #{key}
          AND s.SEQ_DATE = #{seqDate}
    </select>

    <insert id="insertCcsSeq">
        insert into T_SYS_SEQ(SEQ_DATE, KEY, VALUE, DESC, VERSION, MOUDLE_TYPE)
        values (#{seqDate}, #{key}, 1, #{desc}, #{version}, #{moudleType})
    </insert>

    <update id="updateCcsSeq">
        update T_SYS_SEQ s
        set s.VALUE  = s.VALUE + 1,
            s.VERSION=#{newVersion}
        where s.KEY = #{key}
          AND s.SEQ_DATE = #{seqDate}
          and s.VERSION = #{oldVersion}
    </update>
</mapper>