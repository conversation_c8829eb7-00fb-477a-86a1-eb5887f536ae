<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.EventMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.Event">
        <id column="EVENT_ID" property="eventId"/>
        <result column="EVENT_TYPE" property="eventType"/>
        <result column="FORM_NAME" property="formName"/>
        <result column="FORM_INFO_ID" property="formInfoId"/>
        <result column="PROCESS_KEY" property="processKey"/>
        <result column="PROCESS_INSTANCE_ID" property="processInstanceId"/>
        <result column="STATUS" property="status"/>
        <result column="STOP_STATUS" property="stopStatus"/>
        <result column="CLAIM_USER_ID" property="claimUserId"/>
        <result column="HURRY_STATUS" property="hurryStatus"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CLAIM_STATUS" property="claimStatus"/>
        <result column="GROUP_APPROVES" property="groupApproves"/>
        <result column="USER_APPROVES" property="userApproves"/>
        <result column="PROCESS_TASK_NAME" property="processTaskName"/>
        <result column="TASK_IDS" property="taskIds"/>
        <result column="CURRENT_TASK_ID" property="currentTaskId"/>
        <result column="ACTIVITY_ID" property="activityId"/>
        <result column="VISIT_USER_ID" property="visitUserId"/>
        <result column="VISIT_FORM_INFO_ID" property="visitFormInfoId"/>
        <result column="VISIT_USER_STATUS" property="visitUserStatus"/>
        <result column="VISIT_RESULT" property="visitResult"/>
        <result column="VISIT_STATUS" property="visitStatus"/>
        <result column="VISIT_GROUP_ID" property="visitGroupId"/>
        <result column="CURRENT_NODE_ASSIGNED_STATUS" property="currentNodeAssignedStatus"/>
        <result column="LOCK_USER_ID" property="lockUserId"/>
        <result column="LOCK_TIME" property="lockTime"/>
        <result column="LOCK_STATUS" property="lockStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        e.EVENT_ID,
        e.EVENT_TYPE,
        e.FORM_NAME,
        e.FORM_INFO_ID,
        e.PROCESS_KEY,
        e.PROCESS_INSTANCE_ID,
        e.STATUS,
        e.STOP_STATUS,
        e.CLAIM_USER_ID,
        e.HURRY_STATUS,
        e.CREATOR_ID,
        e.UPDATE_ID,
        e.CREATE_TIME,
        e.UPDATE_TIME,
        e.CLAIM_STATUS,
        e.GROUP_APPROVES,
        e.USER_APPROVES,
        e.PROCESS_TASK_NAME,
        e.TASK_IDS,
        e.CURRENT_TASK_ID,
        e.ACTIVITY_ID,
        e.VISIT_USER_ID,
        e.VISIT_FORM_INFO_ID,
        e.VISIT_USER_STATUS,
        e.VISIT_RESULT,
        e.VISIT_STATUS,
        e.VISIT_GROUP_ID,
        e.CURRENT_NODE_ASSIGNED_STATUS,
        e.LOCK_USER_ID,
        e.LOCK_TIME,
        e.LOCK_STATUS
    </sql>

    <!--查询当前用户参与的待审批数据相关的tabs-->
    <select id="selectFormNameList" resultType="java.lang.String">
        select distinct e.FORM_NAME
        from T_EVENT e
        where e.STOP_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()}
          and e.STATUS in (${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@APPLY_OVERING.getValue()})
          and (e.CLAIM_USER_ID = #{userId} or ((e.CLAIM_USER_ID is null or e.CLAIM_USER_ID = '') and
        ( instr(e.USER_APPROVES, #{userId}) <![CDATA[ > ]]>  0
        <foreach collection="groups" item="item" separator=" or " open=" or ">
            instr(e.GROUP_APPROVES
                 , #{item}) <![CDATA[ > ]]> 0
        </foreach>
        )))
    </select>

    <!--根据路由key，租户id，入参，查询流程中的业务数据-->
    <select id="queryFormListByFlow" resultType="com.fuiou.ccs.workorder.api.dto.BusinessFlowCcsDTO">
        select e.EVENT_TYPE
             , e.STATUS
             , e.FORM_INFO_ID
             , e.FORM_NAME
             , e.CREATOR_ID
             , e.GROUP_APPROVES
             , e.USER_APPROVES
             , e.CLAIM_STATUS
             , e.CLAIM_USER_ID
             , e.PROCESS_TASK_NAME
             , e.CURRENT_TASK_ID
             , e.ACTIVITY_ID
        from T_EVENT e
        where e.PROCESS_KEY = #{processKey}
          and e.EVENT_TYPE = #{tenantId}
          and e.STOP_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StopStatusEnums@NORMAL}
          and e.STATUS in (${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()},
                           ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@APPLY_OVERING.getValue()})
    </select>

    <!--待处理工单，列表表头展示字段-->
    <resultMap id="SysFormEventVO" type="com.fuiou.ccs.workorder.api.vo.SysFormEventVO">
        <id column="F_FORM_INFO_ID" property="FORM_INFO_ID"/>
        <result column="F_FORM_EVENT_CODE" property="FORM_EVENT_CODE"/>
        <result column="F_FORM_ID" property="FORM_ID"/>
        <result column="F_CALL_NUMBER" property="CALL_NUMBER"/>
        <result column="F_CALLER_NAME" property="CALLER_NAME"/>
        <result column="F_CALLER_TYPE" property="CALLER_TYPE"/>
        <result column="F_EVENT_DATE" property="EVENT_DATE"/>
        <result column="F_EVENT_STATUS" property="EVENT_STATUS"/>
        <result column="F_EVENT_DESC" property="EVENT_DESC"/>
        <result column="F_FORM_BUSINESS_ID" property="FORM_BUSINESS_ID"/>
        <result column="F_EVENT_LEVEL" property="EVENT_LEVEL"/>
        <result column="F_BUS_NAME" property="BUS_NAME"/>
        <result column="F_WORK_NAME" property="WORK_NAME"/>
        <result column="F_WORK_SUB_NAME" property="WORK_SUB_NAME"/>
        <result column="F_SUB_ELEMENT" property="SUB_ELEMENT_ID"/>
        <result column="F_EVENT_LEVEL_NAME" property="EVENT_LEVEL_NAME"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapVO" type="com.fuiou.ccs.workorder.api.vo.CcsEventVO">
        <id column="EVENT_ID" property="eventId"/>
        <result column="EVENT_TYPE" property="eventType"/>
        <result column="FORM_NAME" property="formName"/>
        <result column="FORM_INFO_ID" property="formInfoId"/>
        <result column="PROCESS_KEY" property="processKey"/>
        <result column="PROCESS_INSTANCE_ID" property="processInstanceId"/>
        <result column="STATUS" property="status"/>
        <result column="STOP_STATUS" property="stopStatus"/>
        <result column="CLAIM_USER_ID" property="claimUserId"/>
        <result column="HURRY_STATUS" property="hurryStatus"/>
        <result column="UPDATE_ID" property="updateId"/>
        <result column="CLAIM_STATUS" property="claimStatus"/>
        <result column="GROUP_APPROVES" property="groupApproves"/>
        <result column="USER_APPROVES" property="userApproves"/>
        <result column="CREATOR_ID" property="creatorId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="PROCESS_TASK_NAME" property="processTaskName"/>
        <result column="TASK_IDS" property="taskIds"/>
        <result column="CURRENT_TASK_ID" property="currentTaskId"/>
        <result column="ACTIVITY_ID" property="activityId"/>
        <result column="CREATOR_EMP_NAME" property="creatorEmpName"/>
        <result column="CLAIM_USER_EMP_NAME" property="claimUserEmpName"/>
        <result column="VISIT_USER_ID" property="visitUserId"/>
        <result column="VISIT_EMP_NAME" property="visitEmpName"/>
        <result column="VISIT_GROUP_ID" property="visitGroupId"/>
        <result column="VISIT_GROUP_NAME" property="visitGroupName"/>
        <result column="VISIT_FORM_INFO_ID" property="visitFormInfoId"/>
        <result column="VISIT_USER_STATUS" property="visitUserStatus"/>
        <result column="VISIT_RESULT" property="visitResult"/>
        <result column="VISIT_STATUS" property="visitStatus"/>
        <result column="VISIT_USER_EMP_NAME" property="visitEmpName"/>
        <result column="CURRENT_NODE_ASSIGNED_STATUS" property="currentNodeAssignedStatus"/>
        <result column="LOCK_USER_ID" property="lockUserId"/>
        <result column="LOCK_USER_EMP_NAME" property="lockUserEmpName"/>
        <result column="LOCK_TIME" property="lockTime"/>
        <result column="LOCK_STATUS" property="lockStatus"/>
        <collection property="sysFormEventVO" resultMap="SysFormEventVO"/>
        <collection property="headerColumns" ofType="java.util.Map" autoMapping="true" javaType="map">
        </collection>
    </resultMap>

    <!--查询路由流程中的，待处理工单分页数据-->
    <select id="toBeProcessedPageList" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>,
                                               ${contantColumns},
                                               i.LABEL     as F_EVENT_LEVEL_NAME,
                                               cu.EMP_NAME as CREATOR_EMP_NAME,
                                               eu.EMP_NAME as CLAIM_USER_EMP_NAME
    from T_EVENT e
             inner join CTDBCRTD.${eventQuery.formNameE} f on e.FORM_INFO_ID = f.FORM_INFO_ID
             left join T_SYS_USER cu on e.CREATOR_ID = cu.USER_ID
             left join T_SYS_USER eu on e.CLAIM_USER_ID = eu.USER_ID
             left join T_SYS_DICT_ITEM i
                       on i.DICT_CODE = '${@com.fuiou.ccs.workorder.api.enums.DictCode@EVENT_LEVEL}' and
                          i.VALUE = f.EVENT_LEVEL
        <where>
            <include refid="Query_Event_Column_toBeProcessedPageList"/>
            <if test="eventQuery != null">
                <if test="eventQuery.formBusinessIds != null and eventQuery.formBusinessIds.size() != 0">
                    and f.FORM_BUSINESS_ID in (
                    <foreach collection="eventQuery.formBusinessIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="eventQuery.subElementIds != null and eventQuery.subElementIds.size() != 0">
                    and f.SUB_ELEMENT in (
                    <foreach collection="eventQuery.subElementIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="sqlSegment != null and sqlSegment != ''">
                AND ${sqlSegment}
            </if>
        </where>
        <choose>
            <when test="orderItems != null and orderItems.size() != 0">
                order by

                <foreach collection="orderItems" item="item" separator=",">
                    ${item.column}
                    <choose>
                        <when test="item.asc">

                             asc

                        </when>
                        <otherwise>

                             desc
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                order by e.LOCK_STATUS asc, e.HURRY_STATUS desc, e.UPDATE_TIME desc
            </otherwise>
        </choose>
    </select>

    <!--拼接审批事件表，查询条件列-->
    <sql id="Query_Event_Column_toBeProcessedPageList">
        <if test="stopStatus != null">
            AND e.STOP_STATUS = #{stopStatus}
        </if>
        <include refid="event_query_columns">
        </include>
        <if test="userId != null and userId != ''">
                        and (e.CLAIM_USER_ID = #{userId} or ((e.CLAIM_USER_ID is null or e.CLAIM_USER_ID = '') and
                                 ( instr(e.USER_APPROVES , #{userId}) <![CDATA[ > ]]>  0
        <foreach collection="groups" item="item" separator=" or " open=" or ">
            instr(e.GROUP_APPROVES, #{item}) <![CDATA[ > ]]> 0
        </foreach>))
            <if test="groupLeaderIds !=null and groupLeaderIds.size()!=0">
                <foreach collection="groupLeaderIds" item="item" separator=" or " open=" or ">
                    instr(e.GROUP_APPROVES, #{item}) <![CDATA[ > ]]> 0
                </foreach>
            </if>
            )
        </if>
    </sql>

    <!-- 事件审批条件 -->
    <sql id="event_query_columns">
        <if test="eventQuery != null">
            <if test="eventQuery.eventTypeE != null and eventQuery.eventTypeE != ''">
                AND e.EVENT_TYPE = #{eventQuery.eventTypeE}

            </if>
            <if test="eventQuery.formNameE != null and eventQuery.formNameE != ''">

                                AND e.FORM_NAME = #{eventQuery.formNameE}

            </if>
            <if test="eventQuery.processKeyE != null and eventQuery.processKeyE != ''">

                                AND e.PROCESS_KEY = #{eventQuery.processKeyE}

            </if>
            <if test="eventQuery.statusE != null">

                                AND e.STATUS = #{eventQuery.statusE}

            </if>
            <if test="eventQuery.stopStatusE != null">

                                AND e.STOP_STATUS = #{eventQuery.stopStatusE}

            </if>
            <if test="eventQuery.hurryStatusE != null">

                                AND e.HURRY_STATUS = #{eventQuery.hurryStatusE}

            </if>
            <if test="eventQuery.creatorIdE != null and eventQuery.creatorIdE != ''">

                                AND e.CREATOR_ID = #{eventQuery.creatorIdE}

            </if>
            <if test="eventQuery.claimUserIdE != null and eventQuery.claimUserIdE != ''">

                                AND (e.CLAIM_USER_ID = #{eventQuery.claimUserIdE}
                                or  (instr(e.VISIT_USER_ID , #{eventQuery.claimUserIdE}) <![CDATA[ > ]]> 0 and e.STATUS =
                                ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}))

            </if>
            <if test="eventQuery.dealGroupId != null and eventQuery.dealGroupId != ''">

                                and ((e.STATUS not in
                                (${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()},
                                ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()})
                                and (e.CLAIM_USER_ID is null or  e.CLAIM_USER_ID ='') and  instr(e.GROUP_APPROVES,
                                #{eventQuery.dealGroupId})
                                <![CDATA[ > ]]> 0)
                                or  (e.STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}
                                and (e.VISIT_USER_ID  is null or  e.VISIT_USER_ID ='') and e.VISIT_GROUP_ID = #{eventQuery.dealGroupId}))

            </if>
            <if test="eventQuery.claimStatusE != null">
                <if test="eventQuery.claimStatusE">

                                        and  (e.CLAIM_USER_ID is not null  and  e.CLAIM_USER_ID != '')

                </if>
                <if test="!eventQuery.claimStatusE">

                                        and  (e.CLAIM_USER_ID is null or  e.CLAIM_USER_ID = '')

                </if>
            </if>
            <if test="eventQuery.eventIds != null">

                                AND e.EVENT_ID in

                <foreach collection="eventQuery.eventIds" item="item" close=")" open="(" separator=",">
                    #{item}

                </foreach>
            </if>
            <if test="eventQuery.createTimeStart != null and eventQuery.createTimeEnd != null">

                                AND e.CREATE_TIME between #{eventQuery.createTimeStart} and #{eventQuery.createTimeEnd}

            </if>
            <if test="eventQuery.updateTimeStart != null">

                                and e.UPDATE_TIME between #{eventQuery.updateTimeStart} and #{eventQuery.updateTimeEnd}

            </if>
            <if test="eventQuery.isDirectOver != null and eventQuery.isDirectOver">

                                and (e.PROCESS_KEY is null or e.PROCESS_KEY = '')

            </if>
            <if test="eventQuery.isDirectOver != null and !eventQuery.isDirectOver">

                                and (e.PROCESS_KEY is not null and e.PROCESS_KEY!='')
            </if>
        </if>
    </sql>

    <!--查询路由流程中的，查询全部工单数据-->
    <select id="allEventPageList" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>,
                                               ${contantColumns},
                                               i.LABEL     as F_EVENT_LEVEL_NAME,
                                               cu.EMP_NAME as CREATOR_EMP_NAME,
                                               eu.EMP_NAME as CLAIM_USER_EMP_NAME,
                                               vu.EMP_NAME as VISIT_EMP_NAME,
                                               g.NAME      as VISIT_GROUP_NAME
    from T_EVENT e
             inner join CTDBCRTD.${eventQuery.formNameE} f on e.FORM_INFO_ID = f.FORM_INFO_ID
             left join T_SYS_USER cu on e.CREATOR_ID = cu.USER_ID
             left join T_SYS_USER vu on e.VISIT_USER_ID = vu.USER_ID
             left join T_SYS_USER eu on e.CLAIM_USER_ID = eu.USER_ID
             left join T_SYS_GROUP g on e.VISIT_GROUP_ID = g.GROUP_ID
             left join T_SYS_DICT_ITEM i
                       on i.DICT_CODE = '${@com.fuiou.ccs.workorder.api.enums.DictCode@EVENT_LEVEL}' and
                          i.VALUE = f.EVENT_LEVEL
        <where>
            <include refid="event_query_columns"/>
            <if test="eventQuery != null">
                <if test="eventQuery.formBusinessIds != null and eventQuery.formBusinessIds.size() != 0">
                    and f.FORM_BUSINESS_ID in (
                    <foreach collection="eventQuery.formBusinessIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="eventQuery.subElementIds != null and eventQuery.subElementIds.size() != 0">
                    and f.SUB_ELEMENT in (
                    <foreach collection="eventQuery.subElementIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="sqlSegment != null and sqlSegment != ''">
                AND ${sqlSegment}
            </if>
        </where>
        <choose>
            <when test="orderItems != null and orderItems.size() != 0">
                order by

                <foreach collection="orderItems" item="item" separator=",">
                    ${item.column}
                    <choose>
                        <when test="item.asc">

                            asc

                        </when>
                        <otherwise>

                            desc
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                order by e.HURRY_STATUS desc, e.UPDATE_TIME desc
            </otherwise>
        </choose>
    </select>

    <!--查询路由流程中的，查询全部工单数据-->
    <select id="allEventPageListTotal" resultType="java.lang.Integer">
        select count(*)
        from CTDBCRTD.${eventQuery.formNameE} f
                 inner join T_EVENT e on e.FORM_INFO_ID = f.FORM_INFO_ID
        <where>
            <include refid="event_query_columns"/>
            <if test="eventQuery != null">
                <if test="eventQuery.formBusinessIds != null and eventQuery.formBusinessIds.size() != 0">
                    and f.FORM_BUSINESS_ID in (
                    <foreach collection="eventQuery.formBusinessIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="eventQuery.subElementIds != null and eventQuery.subElementIds.size() != 0">
                    and f.SUB_ELEMENT in (
                    <foreach collection="eventQuery.subElementIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="sqlSegment != null and sqlSegment != ''">
                AND ${sqlSegment}
            </if>
        </where>
    </select>

    <!--查询路由流程中的，查询当前用户，待回访的分页数据-->
    <select id="toBeVisitPageList" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>,
                                               ${contantColumns},
                                               i.LABEL     as F_EVENT_LEVEL_NAME,
                                               cu.EMP_NAME as CREATOR_EMP_NAME,
                                               eu.EMP_NAME as CLAIM_USER_EMP_NAME,
                                               vu.EMP_NAME as VISIT_EMP_NAME,
                                               g.NAME      as VISIT_GROUP_NAME
    from T_EVENT e
             inner join CTDBCRTD.${eventQuery.formNameE} f on e.FORM_INFO_ID = f.FORM_INFO_ID
             left join T_SYS_USER cu on e.CREATOR_ID = cu.USER_ID
             left join T_SYS_USER vu on e.VISIT_USER_ID = vu.USER_ID
             left join T_SYS_USER eu on e.CLAIM_USER_ID = eu.USER_ID
             left join T_SYS_GROUP g on e.VISIT_GROUP_ID = g.GROUP_ID
             left join T_SYS_DICT_ITEM i
                       on i.DICT_CODE = '${@com.fuiou.ccs.workorder.api.enums.DictCode@EVENT_LEVEL}' and
                          i.VALUE = f.EVENT_LEVEL
        <where>
            <include refid="Query_Event_Column_toBeVisitPageList"/>
            <if test="eventQuery != null">
                <if test="eventQuery.formBusinessIds != null and eventQuery.formBusinessIds.size() != 0">
                    and f.FORM_BUSINESS_ID in (
                    <foreach collection="eventQuery.formBusinessIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="eventQuery.subElementIds != null and eventQuery.subElementIds.size() != 0">
                    and f.SUB_ELEMENT in (
                    <foreach collection="eventQuery.subElementIds" separator="," item="item">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="sqlSegment != null and sqlSegment != ''">
                AND ${sqlSegment}
            </if>
        </where>
        <choose>
            <when test="orderItems != null and orderItems.size() != 0">
                order by
                <foreach collection="orderItems" item="item" separator=",">
                    ${item.column}
                    <choose>
                        <when test="item.asc">
                            asc
                        </when>
                        <otherwise>
                            desc
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                order by e.HURRY_STATUS desc, e.UPDATE_TIME desc
            </otherwise>
        </choose>
    </select>

    <!--拼接审批事件表，查询条件列-->
    <sql id="Query_Event_Column_toBeVisitPageList">
        AND e.STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}
        AND e.STOP_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()}

        <include refid="event_query_columns">
        </include>
        <if test="!isJs0001 and userId != null and userId != ''">
                        AND ( instr(e.VISIT_USER_ID , #{userId}) <![CDATA[ > ]]> 0
            <foreach collection="groups" item="item" separator=" or " open=" or ">
                instr(e.VISIT_GROUP_ID , #{item}) <![CDATA[ > ]]> 0
            </foreach> )
        </if>
    </sql>

    <!--根据工单表名，工单表id，查询工单表数据-->
    <select id="queryFormDetailInfo" resultType="java.util.Map">
        select f.*
        from CTDBCRTD.${formName} f
        where f.FORM_INFO_ID = #{formInfoId}
    </select>

    <!--根据条件，更新当前数据状态-->
    <update id="updateStatusToFormData">
        update CTDBCRTD.${formName} f
        set f.UPDATER_ID   = #{userId},
            f.UPDATE_TIME  = current_timestamp,
            f.EVENT_STATUS = #{status}
        where f.FORM_INFO_ID = #{formInfoId}
    </update>

    <!--根据入参，查询涉及到的表名，表id -->
    <select id="queryEventIdByParams" resultType="com.fuiou.ccs.workorder.api.dto.CcsEventDTO">
        select distinct e.EVENT_ID as eventId, e.FORM_NAME as formName
        from T_EVENT e
                 left join T_EVENT_LOG el on e.EVENT_ID = el.EVENT_ID
                 left join T_EXCHANGE h on h.FORM_NAME = e.FORM_NAME and h.FORM_INFO_ID = e.FORM_INFO_ID
                 left join T_FORM_INFO_LOG fl on fl.FORM_NAME = e.FORM_NAME and fl.FORM_INFO_ID = e.FORM_INFO_ID
        where
        <choose>
            <when test="formName != null and formName != ''">
                (1 = 2 or e.FORM_NAME = #{formName}) and
            </when>
            <otherwise>
                1 = 2
                   or
            </otherwise>
        </choose>
        (1 = 2
        <foreach collection="userIds" item="userId" separator=" or " open=" or ">
            e.CREATOR_ID = #{userId}
                or e.UPDATE_ID = #{userId}
                or instr(e.USER_APPROVES, #{userId}) <![CDATA[ > ]]> 0
                or el.USER_ID = #{userId}
                or el.PROXY_USER_ID = #{userId}
                or instr(el.USER_APPROVES, #{userId}) <![CDATA[ > ]]> 0
                or h.CREATOR_ID = #{userId}
                or fl.CREATOR_ID = #{userId}
        </foreach>
        <foreach collection="groups" item="item" separator=" or " open=" or ">
            instr(e.GROUP_APPROVES, #{item}) <![CDATA[ > ]]> 0 or instr(el.GROUP_APPROVES, #{item}) <![CDATA[ > ]]> 0
        </foreach>
        )
    </select>

    <!--根据用户，查询待回访数据表名-->
    <select id="selectVisitFormNames" resultType="java.lang.String">
        select distinct e.FORM_NAME
        from T_EVENT e
        where e.STOP_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()}
          and e.STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}
            <if test="isJs0001==null or !isJs0001">
                and (instr(e.VISIT_USER_ID, #{userId}) <![CDATA[ > ]]> 0
                <foreach collection="groups" item="item">
                    or e.VISIT_GROUP_ID = #{item}
                </foreach>
                )
            </if>
    </select>

    <!--更新审批表回访状态-->
    <update id="updateVisitUserStatusEvent">
        update T_EVENT e  set e.VISIT_USER_ID = #{event.visitUserId},
        <if test="event.visitGroupId != null and event.visitGroupId != ''">
            e.VISIT_GROUP_ID = #{event.visitGroupId},
        </if>
        <if test="event.visitUserStatus != null">
            e.VISIT_USER_STATUS = #{event.visitUserStatus},
        </if>
        e.UPDATE_ID   = #{event.updateId},
        e.UPDATE_TIME = current timestamp
        where e.EVENT_ID = #{event.eventId}
<!--          and instr(e.VISIT_USER_ID, #{event.updateId}) <![CDATA[ > ]]> 0-->
<!--          and e.VISIT_USER_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums@NO}-->
    </update>

    <!--根据eventId，查询事件表数据-->
    <select id="selectEventById" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List">
        </include>,
                      u.EMP_NAME  as CREATOR_EMP_NAME,
                      eu.EMP_NAME as CLAIM_USER_EMP_NAME,
                      vu.EMP_NAME as VISIT_USER_EMP_NAME,
                      g.NAME      as VISIT_GROUP_NAME
    from T_EVENT e
             left join T_SYS_USER u on e.CREATOR_ID = u.USER_ID
             left join T_SYS_USER eu on e.CLAIM_USER_ID = eu.USER_ID
             left join T_SYS_USER vu on e.VISIT_USER_ID = vu.USER_ID
             left join T_SYS_GROUP g on e.VISIT_GROUP_ID = g.GROUP_ID
    where e.EVENT_ID = #{eventId}
    </select>

    <!--根据表数据，和表名，查询所有历史关联ids-->
    <select id="queryHistoryWorkOrderFormIds" resultType="java.lang.Long">
        select f.FORM_INFO_ID
        from CTDBCRTD.${formName} f
        where f.FORM_EVENT_CODE != #{nowCode}
          and f.FORM_EVENT_CODE like #{subCode} || '%'
    </select>

    <!--根据条件，查询全部工单数据-->
    <select id="queryAllEventPageList" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>,
                                               ${contantColumns},
                                               i.LABEL     as F_EVENT_LEVEL_NAME,
                                               cu.EMP_NAME as CREATOR_EMP_NAME,
                                               eu.EMP_NAME as CLAIM_USER_EMP_NAME,
                                               vu.EMP_NAME as VISIT_EMP_NAME,
                                               g.NAME      as VISIT_GROUP_NAME
    from T_EVENT e
             inner join CTDBCRTD.${formName} f on e.FORM_INFO_ID = f.FORM_INFO_ID
             left join T_SYS_USER cu on e.CREATOR_ID = cu.USER_ID
             left join T_SYS_USER vu on e.VISIT_USER_ID = vu.USER_ID
             left join T_SYS_USER eu on e.CLAIM_USER_ID = eu.USER_ID
             left join T_SYS_GROUP g on e.VISIT_GROUP_ID = g.GROUP_ID
             left join T_SYS_DICT_ITEM i
                       on i.DICT_CODE = '${@com.fuiou.ccs.workorder.api.enums.DictCode@EVENT_LEVEL}' and
                          i.VALUE = f.EVENT_LEVEL
        <include refid="queryAllEventPage_column">
        </include>
        <choose>
            <when test="orderItems != null and orderItems.size() != 0">
                order by
                <foreach collection="orderItems" item="item" separator=",">
                    ${item.column}
                    <choose>
                        <when test="item.asc">
                            asc
                        </when>
                        <otherwise>
                            desc
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                order by e.HURRY_STATUS desc, e.UPDATE_TIME desc
            </otherwise>
        </choose>
    </select>

    <!--根据条件，查询全部工单数据-->
    <select id="queryAllEventPageListTotal" resultType="java.lang.Integer">
        select count(*)
        from T_EVENT e
                 inner join CTDBCRTD.${formName} f on e.FORM_INFO_ID = f.FORM_INFO_ID
        <include refid="queryAllEventPage_column">
        </include>
    </select>

    <sql id="queryAllEventPage_column">
        <where>
            <if test="eventIds != null and eventIds.size() != 0">
                and e.EVENT_ID in

                <foreach collection="eventIds" item="item" separator="," open="(" close=")">
                    #{item}

                </foreach>
            </if>
            <if test="allEventQuery.statusE != null">

                                and e.STATUS = #{allEventQuery.statusE}

            </if>
            <if test="allEventQuery.stopStatusE != null">

                                 and e.STOP_STATUS = #{allEventQuery.stopStatusE}

            </if>
            <if test="allEventQuery.creatorIdE != null and allEventQuery.creatorIdE != ''">

                                and e.CREATOR_ID = #{allEventQuery.creatorIdE}

            </if>
            <if test="allEventQuery.claimUserIdE != null and allEventQuery.claimUserIdE != ''">

                                AND (e.CLAIM_USER_ID = #{allEventQuery.claimUserIdE}
                                or  (instr(e.VISIT_USER_ID , #{allEventQuery.claimUserIdE}) <![CDATA[ > ]]> 0  and e.STATUS =
                                ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}))

            </if>
            <if test="allEventQuery.dealGroupId != null and allEventQuery.dealGroupId != ''">

                                and ((e.STATUS not in
                                (${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()},
                                ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()})
                                and (e.CLAIM_USER_ID is null or  e.CLAIM_USER_ID ='') and  instr(e.GROUP_APPROVES,
                                #{allEventQuery.dealGroupId})
                                <![CDATA[ > ]]> 0)
                                or  (e.STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$StatusEnums@PASS_VISIT.getValue()}
                                and  (e.VISIT_USER_ID is null or  e.VISIT_USER_ID ='') and e.VISIT_GROUP_ID =
                                #{allEventQuery.dealGroupId}))

            </if>
            <if test="allEventQuery.createTimeStart != null">

                                and e.CREATE_TIME between #{allEventQuery.createTimeStart} and #{allEventQuery.createTimeEnd}

            </if>
            <if test="allEventQuery.updateTimeStart != null">

                                and e.UPDATE_TIME between #{allEventQuery.updateTimeStart} and #{allEventQuery.updateTimeEnd}

            </if>
            <if test="allEventQuery.claimStatusE != null">
                <if test="allEventQuery.claimStatusE">

                                        and  (e.CLAIM_USER_ID is not null  and  e.CLAIM_USER_ID != '')

                </if>
                <if test="!allEventQuery.claimStatusE">

                                        and  (e.CLAIM_USER_ID is null or  e.CLAIM_USER_ID = '')

                </if>
            </if>
            <if test="allEventQuery.callNumber != null and allEventQuery.callNumber != ''">

                                and f.CALL_NUMBER = #{allEventQuery.callNumber}

            </if>
            <if test="allEventQuery.formEventCode != null and allEventQuery.formEventCode != ''">

                                and f.FORM_EVENT_CODE like '%' ||#{allEventQuery.formEventCode} || '%'

            </if>
            <if test="allEventQuery.eventLevel != null">

                                and f.EVENT_LEVEL = #{allEventQuery.eventLevel}

            </if>
            <if test="allEventQuery.eventDesc != null and allEventQuery.eventDesc != ''">

                and f.EVENT_DESC like   '%' ||#{allEventQuery.eventDesc}|| '%'

            </if>
            <if test="allEventQuery.formBusinessIds != null and allEventQuery.formBusinessIds.size() != 0">

                                and f.FORM_BUSINESS_ID in (

                <foreach collection="allEventQuery.formBusinessIds" separator="," item="item">
                    #{item}

                </foreach>

                                )

            </if>
            <if test="allEventQuery.subElementIds != null and allEventQuery.subElementIds.size() != 0">

                                and f.SUB_ELEMENT in (

                <foreach collection="allEventQuery.subElementIds" separator="," item="item">
                    #{item}

                </foreach>

                                )

            </if>
            <if test="allEventQuery.riskToView != null">

                                and f.RISK_TO_VIEW = #{allEventQuery.riskToView}

            </if>
            <if test="allEventQuery.isDirectOver != null and allEventQuery.isDirectOver">

                                and (e.PROCESS_KEY is null or e.PROCESS_KEY = '')

            </if>
            <if test="allEventQuery.isDirectOver != null and !allEventQuery.isDirectOver">

                                and (e.PROCESS_KEY is not null and e.PROCESS_KEY!='')
            </if>
        </where>
    </sql>

    <!--根据条件，先查询下所有数据-->
    <select id="queryAllEventFormNameList" resultType="java.lang.String">
        select distinct e.FORM_NAME
        from T_EVENT e
    </select>

    <!--根据事件id，查询工单数据-->
    <select id="querySysFormInfo" resultMap="SysFormEventVO">
        select f.FORM_EVENT_CODE as F_FORM_EVENT_CODE
        from CTDBCRTD.${formName} f
        where f.FORM_INFO_ID = #{formInfoId}
    </select>

    <update id="lockEvent">
        update T_EVENT e
        set e.LOCK_STATUS  = 1,
            e.LOCK_TIME    = current timestamp,
            e.LOCK_USER_ID = #{userId}
        where  e.EVENT_ID in
        <foreach collection="eventList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and e.LOCK_STATUS = 0
    </update>

    <update id="unLockEvent">
        update T_EVENT e
        set e.LOCK_STATUS  = 0,
            e.LOCK_TIME    = current timestamp,
            e.LOCK_USER_ID = ''
        where  e.EVENT_ID in
        <foreach collection="eventList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and e.LOCK_USER_ID = #{userId}
    </update>

    <select id="queryLockUserInfoByEventId" resultMap="BaseResultMapVO">
        select e.LOCK_TIME, e.LOCK_STATUS,e.LOCK_USER_ID, u.EMP_NAME as LOCK_USER_EMP_NAME
        from T_EVENT e
                 left join T_SYS_USER u on e.LOCK_USER_ID = u.USER_ID
        where e.EVENT_ID = #{eventId}
    </select>

    <select id="getNowTaskNumsByUserId" resultType="java.lang.Long">
        select count(e.EVENT_ID)  from T_EVENT e
        <where>
            e.STOP_STATUS = ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()}
            and e.STATUS <![CDATA[<>]]> ${@com.fuiou.ccs.workorder.api.enums.EventEnums$<EMAIL>()}
            and (
            e.CLAIM_USER_ID = #{userId}
            or instr(e.USER_APPROVES, #{userId}) <![CDATA[ > ]]> 0
            or instr(e.VISIT_USER_ID, #{userId}) <![CDATA[ > ]]> 0
            )
        </where>
    </select>

    <select id = "checkMeData" resultType="java.lang.Long">
        select distinct event_id from t_event e
        where e.EVENT_ID in
        <foreach collection="eventIds" item="eventId" separator="," open="(" close=")">
            #{eventId}
        </foreach>
        and e.FORM_NAME LIKE CONCAT(#{tableName}, '%')
        and e.CLAIM_USER_ID = #{userId}
    </select>
</mapper>
