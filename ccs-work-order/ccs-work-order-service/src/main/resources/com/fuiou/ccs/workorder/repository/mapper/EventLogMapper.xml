<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.EventLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.EventLog">
        <id column="LOG_ID" property="logId"/>
        <result column="EVENT_ID" property="eventId"/>
        <result column="USER_ID" property="userId"/>
        <result column="DEAL_STATUS" property="dealStatus"/>
        <result column="ACTIVITY_ID" property="activityId"/>
        <result column="TIMEOUT_STATUS" property="timeoutStatus"/>
        <result column="CUT_OFF_TIME" property="cutOffTime"/>
        <result column="DEAL_TIME" property="dealTime"/>
        <result column="DESC" property="desc"/>
        <result column="PROXY_USER_ID" property="proxyUserId"/>
        <result column="GROUP_APPROVES" property="groupApproves"/>
        <result column="USER_APPROVES" property="userApproves"/>
        <result column="IS_BATCH" property="batch"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        l.LOG_ID,
        l.EVENT_ID,
        l.USER_ID,
        l.DEAL_STATUS,
        l.ACTIVITY_ID,
        l.TIMEOUT_STATUS,
        l.CUT_OFF_TIME,
        l.DEAL_TIME,
        nvl(l.DESC,'') as DESC,
        l.PROXY_USER_ID,
        l.GROUP_APPROVES,
        l.USER_APPROVES,
        nvl(l.NODE_NAME,'') as NODE_NAME,
        l.IS_BATCH
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapVO" type="com.fuiou.ccs.workorder.api.vo.EventLogVO">
        <id column="LOG_ID" property="logId"/>
        <result column="EVENT_ID" property="eventId"/>
        <result column="USER_ID" property="userId"/>
        <result column="DEAL_STATUS" property="dealStatus"/>
        <result column="ACTIVITY_ID" property="activityId"/>
        <result column="TIMEOUT_STATUS" property="timeoutStatus"/>
        <result column="CUT_OFF_TIME" property="cutOffTime"/>
        <result column="DEAL_TIME" property="dealTime"/>
        <result column="DESC" property="desc"/>
        <result column="PROXY_USER_ID" property="proxyUserId"/>
        <result column="GROUP_APPROVES" property="groupApproves"/>
        <result column="USER_APPROVES" property="userApproves"/>
        <result column="NODE_NAME" property="nodeName"/>
        <result column="USER_EMP_NAME" property="userEmpName"/>
        <result column="PROXY_USER_EMP_NAME" property="proxyUserEmpName"/>
        <result column="IS_BATCH" property="batch"/>
    </resultMap>

    <!-- 查询审批日志 -->
    <select id="queryEventLogList" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>,
           u.EMP_NAME  as USER_EMP_NAME,
           pu.EMP_NAME as PROXY_USER_EMP_NAME
    from T_EVENT_LOG l
             left join T_SYS_USER u on l.USER_ID = u.USER_ID
             left join t_sys_user pu on l.PROXY_USER_ID = pu.USER_ID
    where l.EVENT_ID = #{eventId}
    order by l.DEAL_TIME asc
    </select>

    <!--最早的一条审批日志-->
    <select id="queryActivityIdByEaryTime" resultType="java.time.LocalDateTime">
        select l.DEAL_TIME
        from T_EVENT_LOG l
        where l.EVENT_ID = #{eventId}
          and l.ACTIVITY_ID = #{activityId}
        order by l.DEAL_TIME asc
            FETCH FIRST 1 ROWS ONLY
    </select>

    <!--查询最近一条审批通过的日志activityId-->
    <select id="queryActivityId" resultMap="BaseResultMapVO">
        select l.ACTIVITY_ID, l.USER_ID, u.EMP_NAME as USER_EMP_NAME, l.GROUP_APPROVES
        from T_EVENT_LOG l
                 left join T_SYS_USER u on l.USER_ID = u.USER_ID
        where l.EVENT_ID = #{eventId}
          and l.DEAL_STATUS in (${@com.fuiou.ccs.workorder.api.enums.EventLogEnums$<EMAIL>()},
                                ${@com.fuiou.ccs.workorder.api.enums.EventLogEnums$<EMAIL>()})
          and l.ACTIVITY_ID != #{activityId}
        <if test="dealTime != null">
            and l.DEAL_TIME <![CDATA[<]]> #{dealTime}
        </if>
        order by l.DEAL_TIME desc
            FETCH FIRST 1 ROWS ONLY
    </select>

    <!--是否存在某用户办理记录-->
    <select id="queryEventLogByUser" resultType="java.lang.Integer">
        select count(*)
        from T_EVENT_LOG l
                 left join T_EVENT e on l.EVENT_ID = e.EVENT_ID
        where l.EVENT_ID = #{eventId}
          and (l.USER_ID = #{userId} or e.CLAIM_USER_ID = #{userId})
    </select>

    <select id="queryEventLogListByBranchOffice" resultMap="BaseResultMapVO">
        select l.EVENT_ID,l.DEAL_TIME,u.EMP_NAME as USER_EMP_NAME,l.DESC
        from T_EVENT_LOG  l
        left join  T_SYS_USER u  on  l.USER_ID = u.USER_ID
        where l.EVENT_ID in
        <foreach collection="subList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND l.DEAL_STATUS IN (1, 2)
        AND instr(l.GROUP_APPROVES, '1586927033467817986') = 0
        AND instr(l.GROUP_APPROVES, '1586927474504118273') = 0
        AND instr(l.GROUP_APPROVES, '1587265140226543618') = 0
        AND (l.USER_ID != 'electricityUserToExternal' and l.USER_ID != 'branchCompanyExternal')
    </select>
</mapper>
