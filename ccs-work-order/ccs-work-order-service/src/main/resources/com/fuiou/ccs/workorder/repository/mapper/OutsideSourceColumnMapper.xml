<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.OutsideSourceColumnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.OutsideSourceColumn">
        <id column="COLUMN_ID" property="columnId" />
        <result column="COLUMN_NAME" property="columnName" />
        <result column="COLUMNS_NOT_NULL" property="columnsNotNull" />
        <result column="COLUMNS_TYPE" property="columnsType" />
        <result column="SOURCE_URL_ID" property="sourceUrlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COLUMN_ID, COLUMN_NAME, COLUMNS_NOT_NULL, COLUMNS_TYPE, SOURCE_URL_ID
    </sql>

</mapper>
