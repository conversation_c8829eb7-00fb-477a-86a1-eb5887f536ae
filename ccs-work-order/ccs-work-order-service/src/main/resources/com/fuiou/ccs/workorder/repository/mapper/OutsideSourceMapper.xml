<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.ccs.workorder.repository.mapper.OutsideSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fuiou.ccs.workorder.repository.entity.OutsideSource">
        <id column="SOURCE_URL_ID" property="sourceUrlId" />
        <result column="METHOD_DESC" property="methodDesc" />
        <result column="METHOD_NAME" property="methodName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SOURCE_URL_ID, METHOD_DESC, METHOD_NAME
    </sql>

    <select id="queryAllUrlId" resultType="java.lang.Long">
        SELECT SOURCE_URL_ID FROM T_OUTSIDE_SOURCE
    </select>
</mapper>
