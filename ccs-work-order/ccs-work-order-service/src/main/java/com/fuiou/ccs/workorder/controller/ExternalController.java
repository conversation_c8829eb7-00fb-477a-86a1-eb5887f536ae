package com.fuiou.ccs.workorder.controller;

import com.fuiou.ccs.workorder.api.dto.CcsExchangeDTO;
import com.fuiou.ccs.workorder.api.param.CaseOverParam;
import com.fuiou.ccs.workorder.external.FromExternalService;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.web.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 对外接口以及接收数据接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"对外接口以及接收数据接口"})
@RequestMapping("/external")
public class ExternalController extends BaseController {

    final ApplicationContext applicationContext;
    private FromExternalService fromExternalService;

    public void setFromExternalService(String type) {
        this.fromExternalService = (FromExternalService) applicationContext.getBean(type + "Fromexternalservice");
    }

    @PostMapping("/{type}/caseOver")
    @ApiOperation(value = "一线申请结案，通知客服工单平台")
    ApiResult<Void> caseOver(@PathVariable String type, @RequestBody CaseOverParam caseOverParam) {
        setFromExternalService(type);
        log.info("ExternalController.caseOver.请求入参： caseOverParam = {} ", JacksonUtil.toJson(caseOverParam));
        return ApiResult.status(fromExternalService.caseOver(caseOverParam));
    }

    @PostMapping("/{type}/backInfo")
    @ApiOperation(value = "一线交流信息回传，录入客服工单平台意见交流信息")
    ApiResult<Void> backInfo(@PathVariable String type, @RequestBody CcsExchangeDTO exchangeDTO) {
        setFromExternalService(type);
        log.info("ExternalController.caseOver.请求入参： exchangeDTO = {} ", JacksonUtil.toJson(exchangeDTO));
        return ApiResult.status(fromExternalService.backInfo(exchangeDTO));
    }
}

