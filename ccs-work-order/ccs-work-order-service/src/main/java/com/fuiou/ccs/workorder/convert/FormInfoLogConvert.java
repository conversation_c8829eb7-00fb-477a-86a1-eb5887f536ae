package com.fuiou.ccs.workorder.convert;

import com.fuiou.ccs.workorder.api.dto.FormInfoLogDTO;
import com.fuiou.ccs.workorder.api.vo.FormInfoLogVO;
import com.fuiou.ccs.workorder.repository.entity.FormInfoLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单数据表，修改日志记录 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface FormInfoLogConvert {

    FormInfoLogConvert INSTANCE = Mappers.getMapper(FormInfoLogConvert.class);

    @Mappings({})
    FormInfoLogVO toVO(FormInfoLog formInfoLog);

    @Mappings({})
    List<FormInfoLogVO> toVO(List<FormInfoLog> formInfoLogList);

    @Mappings({})
    FormInfoLogDTO toDTO(FormInfoLog formInfoLog);

    @Mappings({})
    List<FormInfoLogDTO> toDTO(List<FormInfoLog> formInfoLogList);

    @Mappings({})
    FormInfoLog toEntity(FormInfoLogDTO formInfoLogDTO);

    @Mappings({})
    FormInfoLog toEntity(FormInfoLogVO formInfoLogVO);

}
