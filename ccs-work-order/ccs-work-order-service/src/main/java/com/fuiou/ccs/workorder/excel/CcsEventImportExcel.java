package com.fuiou.ccs.workorder.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fuiou.ccs.workorder.api.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 待处理导入
 *
 * <AUTHOR>
 */
@Data
public class CcsEventImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @StopAnalyzeParseAnnotation(index = 0, msg = "处理备注为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @NotNullField(index = 0, msg = "处理备注不能为空")
    @StringLengthField(index = 0, msg = "处理备注长度不能超过 1300个字符=400个汉字", length = 1300, zhLength = 1300)
    @ExcelProperty(value = "处理备注", index = 0)
    private String comments;


    @StopAnalyzeParseAnnotation(index = 1, msg = "ID列为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @NotNullField(index = 1, msg = "ID不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @LongParseField(index = 1, msg = "ID转换Long类型失败")
    @ExcelProperty(value = "ID", index = 1)
    private Long eventId;

    @StopAnalyzeParseAnnotation(index = 2, msg = "更新时间列为空，解析截止。如该列存在空白，请删除此空白行再进行导入")
    @NotNullField(index = 2, msg = "更新时间不能为空，请使用导出后的模板导入，不可擅自修改表格禁用部分")
    @LocalDateTimeParseField(index = 2, msg = "日期，时间格式转换错误！")
    @ExcelProperty(value = "更新时间", index = 2)
    private LocalDateTime updateTime;

    @ExcelProperty(value = "事件编号", index = 3)
    private String eventCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CcsEventImportExcel that = (CcsEventImportExcel) o;
        return Objects.equals(eventId, that.eventId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(eventId);
    }
}
