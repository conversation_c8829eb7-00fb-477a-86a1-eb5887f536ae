package com.fuiou.ccs.workorder.provider;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.workorder.api.dto.mail.CcsRepeatMailDTO;
import com.fuiou.ccs.workorder.api.provider.SendMessageProvider;
import com.fuiou.ccs.workorder.client.CcsGroupClient;
import com.fuiou.ccs.workorder.repository.entity.Event;
import com.fuiou.ccs.workorder.service.EventMailService;
import com.fuiou.ccs.workorder.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 对应调用，邮件发送接口
 */
@Slf4j
@DubboService
public class SendMessageProviderImpl implements SendMessageProvider {

    @Autowired
    EventService eventService;
    @Autowired
    EventMailService eventMailService;
    @Autowired
    CcsGroupClient ccsGroupClient;

    /**
     * 根据eventId，发送催办邮件+短信
     *
     * @param businessId
     */
    @Override
    public void sendMailAndSms(String businessId) {
        log.info("SendMessageProviderImpl.sendMailAndSms.businessId = {}", businessId);
        String logId = UUID.randomUUID().toString();
        CcsRepeatMailDTO ccsRepeatMailDTO = eventMailService.buildComeOnTimeoutTemplate();
        Event mailEvent = eventService.getById(Long.valueOf(businessId));
        /**
         * 【即将逾期提醒】该节点发送即将逾期工单处理提醒  （待开发评估）
         * 路由中配置超时时间和即将逾期通知时间，  超时时间不可小于即将逾期通知时间。
         * 1.即将逾期节点为配置的一个人，则发送给该用户；
         * 2.即将逾期节点为组，则通知给该组认领的人，若无人认领，通知给组长；
         * 3.即将逾期节点为指定人，则通知给被指定人。
         */
        /*
         * 1，先找认领人，认领人为空的话，在找组长；不为空的话，直接发送
         *   */
        String toUserId = mailEvent.getClaimUserId();
        List<String> groupLeader = new ArrayList<>();
        if (StringUtils.isNotBlank(mailEvent.getGroupApproves())) {
            List<Long> groupIds = Arrays.stream(mailEvent.getGroupApproves().split(",")).map(Long::valueOf).collect(Collectors.toList());
            groupLeader = ccsGroupClient.getGroupLeader(groupIds);
            if (CollectionUtils.isNotEmpty(groupLeader)) {
                if (StringUtils.isNotBlank(toUserId)) {
                    groupLeader.add(toUserId);
                }
            }
        }
        eventMailService.sendMailToFlowProcess("system", Long.valueOf(businessId), toUserId, ccsRepeatMailDTO, logId, groupLeader);
    }
}
