package com.fuiou.ccs.workorder.client;

import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.provider.SysFormDetailProvider;
import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 表单配置详情接口调用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CcsFormDetailClient {

    @DubboReference
    SysFormDetailProvider sysFormDetailProvider;

    /**
     * 根据表名查询表单配置详情list(字段)
     *
     * @param formName 表单配置ID
     * @return List<SysFormDetailDTO>
     */
    public List<SysFormDetailDTO> querySysFormDetailByFormName(String formName) {
        if (StringUtils.isBlank(formName)) {
            log.error("SysFormDetailClient.querySysFormDetailByFormName.根据表名查询表单配置详情list(字段)，接口入参为空！formName=null.error=NullPointException");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<List<SysFormDetailDTO>> result;
        try {
            result = sysFormDetailProvider.querySysFormDetailByFormName(formName);
            log.info("SysFormDetailClient.querySysFormDetailByFormName.根据表名查询表单配置详情list(字段)，接口返回，入参： formName={} ，返回：result={} ", formName, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysFormDetailClient.querySysFormDetailByFormName.根据表名查询表单配置详情list(字段)，异常，入参： formName={} ，报错：error={}", formName, e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysFormDetailClient.querySysFormDetailByFormName.根据表名查询表单配置详情list(字段)，入参：formName={} ， 返回：{}", formName, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据formId获取表单配置详情集合
     *
     * @param formId   表单配置ID
     * @param partFlag true 非预制字段， false 预制字段
     * @return List<SysFormDetailDTO>
     */
    public List<SysFormDetailDTO> queryFormDetailByFormId(Long formId, boolean partFlag) {
        if (formId == null) {
            log.error("SysFormDetailClient.queryFormDetailByFormId.根据formId获取表单非预制配置详情集合，接口入参为空！formId=null.error=NullPointException");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<List<SysFormDetailDTO>> result;
        try {
            result = sysFormDetailProvider.queryFormDetailByFormId(formId, partFlag);
            log.info("SysFormDetailClient.queryFormDetailByFormId.根据formId获取表单非预制配置详情集合，接口返回，入参： formId={} ，返回：result={} ", formId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysFormDetailClient.queryFormDetailByFormId.根据formId获取表单非预制配置详情集合，异常，入参： formId={} ，报错：error={}", formId, e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysFormDetailClient.queryFormDetailByFormId.根据formId获取表单非预制配置详情集合，入参：formId={} ， 返回：{}", formId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据formId查询模板预置字段集合
     *
     * @param formId 表单配置ID
     * @return List<SysFormDetailDTO>
     */
    public List<SysFormDetailDTO> queryPresetFormDetailByFormId(Long formId) {
        if (formId == null) {
            log.error("SysFormDetailClient.queryPresetFormDetailByFormId.根据formId查询模板预置字段集合，接口入参为空！formId=null.error=NullPointException");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<List<SysFormDetailDTO>> result;
        try {
            result = sysFormDetailProvider.queryPresetFormDetailByFormId(formId);
            log.info("SysFormDetailClient.queryPresetFormDetailByFormId.根据formId查询模板预置字段集合，接口返回，入参： formId={} ，返回：result={} ", formId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysFormDetailClient.queryPresetFormDetailByFormId.根据formId查询模板预置字段集合，异常，入参： formId={} ，报错：error={}", formId, e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysFormDetailClient.queryPresetFormDetailByFormId.根据formId查询模板预置字段集合，入参：formId={} ， 返回：{}", formId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

}
