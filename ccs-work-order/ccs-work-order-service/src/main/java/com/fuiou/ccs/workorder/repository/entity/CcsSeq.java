package com.fuiou.ccs.workorder.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022.7.15 10:37
 * @Description 序列生成
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_SYS_SEQ")
public class CcsSeq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期，规则
     */
    @TableField("SEQ_DATE")
    private String seqDate;

    /**
     * key
     */
    @TableField("KEY")
    private String key;

    /**
     * 值
     */
    @TableField("VALUE")
    private int value;

    /**
     * 描述
     */
    @TableField("DESC")
    private String desc;

    /**
     * 版本号
     */
    @TableField("VERSION")
    private String version;

    /**
     * 模块类别
     */
    @TableField("MOUDLE_TYPE")
    private String moudleType;


}
