package com.fuiou.ccs.workorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.workorder.api.vo.FormInfoLogVO;
import com.fuiou.ccs.workorder.repository.entity.FormInfoLog;
import com.fuiou.ccs.workorder.repository.mapper.FormInfoLogMapper;
import com.fuiou.ccs.workorder.service.FormInfoLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工单数据表，修改日志记录 服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class FormInfoLogServiceImpl extends ServiceImpl<FormInfoLogMapper, FormInfoLog> implements FormInfoLogService {

    @Override
    public List<FormInfoLogVO> queryFormInfoLogList(String formName, Long formInfoId) {
        return baseMapper.queryFormInfoLogList(formName, formInfoId);
    }
}
