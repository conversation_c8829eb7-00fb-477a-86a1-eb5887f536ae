package com.fuiou.ccs.workorder.appender;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * Adjust MyBatis log level if log file size exceeds 10GB
 *
 * <AUTHOR>
 */
public class MybatisLogLevelAdjustingAppender extends RollingFileAppender<ILoggingEvent> {

    /*限制文件大小为10G*/
    private static final long MAX_LOG_SIZE = 10L * 1024 * 1024 * 1024;
    /**
     * 限制指定类的内容输出
     */
    private Logger logger = (Logger) LoggerFactory.getLogger("com.fuiou.mybatis.config.LocalSlf4jImpl");

    @Override
    protected void append(ILoggingEvent event) {
        super.append(event);
        File logFile = new File(getFile());
        if (logFile.exists() && logFile.length() >= MAX_LOG_SIZE) {
            logger.setLevel(Level.ERROR);
        }
    }
}
