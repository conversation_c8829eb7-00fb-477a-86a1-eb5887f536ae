package com.fuiou.ccs.workorder.support;

import com.fuiou.ccs.workorder.api.dto.mail.CcsBaseMailDTO;
import com.fuiou.ccs.workorder.api.dto.mail.EventWorkOrderMailDTO;
import com.fuiou.ccs.workorder.api.enums.MailTemplateEnums;
import com.fuiou.ccs.workorder.service.BaseMailService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客服工单审批流程中邮件发送，封装主题，邮件正文操作类
 *
 * <AUTHOR>
 * @date 2022-9-16
 */
@Service("eventWorkOrderMailSupport")
public class EventWorkOrderMailSupport implements BaseMailService<EventWorkOrderMailDTO> {
    /**
     * 封装主题
     *
     * @param billMailDto
     * @return
     */
    @Override
    public Map<String, String> buildSubject(EventWorkOrderMailDTO billMailDto) {
        return new ConcurrentHashMap<>(16);
    }

    /**
     * 封装模板内容
     *
     * @param billMailDto
     * @param templateType
     * @param eventId
     * @param userId
     * @return
     */
    @Override
    public CcsBaseMailDTO buildContent(EventWorkOrderMailDTO billMailDto, MailTemplateEnums templateType, Long eventId, String userId) {
        EventWorkOrderMailDTO bean = new EventWorkOrderMailDTO();
        BeanUtils.copyProperties(billMailDto, bean);
        bean.setTemplateShow(templateType.getValue());
        return bean;
    }
}
