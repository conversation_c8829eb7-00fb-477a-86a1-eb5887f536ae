package com.fuiou.ccs.workorder.query;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 所有工单数据查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AllEventQuery", description = "所有工单数据查询")
public class AllEventQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来电号码
     */
    @ApiModelProperty("来电号码")
    private String callNumber;

    /**
     * 事件编号
     */
    @ApiModelProperty("事件编号")
    private String formEventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty("事件等级")
    private Integer eventLevel;

    /**
     * 创建时间开始
     */
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @ApiModelProperty("更新时间开始")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @ApiModelProperty("更新时间结束")
    private LocalDateTime updateTimeEnd;

    /**
     * 2022-10-21 客服增加
     * 事件表状态搜索
     * 审批状态：1、已发起(默认)，2、审批中，3，已驳回，4-重新提交，5-审批通过（待回访），6-工单完结
     */
    @ApiModelProperty("事件状态")
    private Integer statusE;

    /**
     * 2022-11-8 客服增加
     * 事件表状态搜索
     * 终止状态：0-正常，1-已终止，2-已撤销
     */
    @ApiModelProperty("事件终止状态")
    private Integer stopStatusE;

    /**
     * 2022-11-8 客服增加
     * 创建人搜索
     */
    @ApiModelProperty(value = "创建人")
    private String creatorIdE;

    /**
     * 2022-11-8 客服增加
     * 当前处理人搜索
     */
    @ApiModelProperty("当前处理人")
    private String claimUserIdE;

    /**
     * 是否被认领
     */
    @ApiModelProperty("是否被认领")
    private Boolean claimStatusE;

    /**
     * 全部工单-全部工单：* 导出接口，将当前页总条数传过来
     */
    @ApiModelProperty("全部工单-全部工单： 导出接口，将当前列表总条数传过来")
    private Integer exportSize;

    /**
     * 风控是否可查看 1-可查看，0-不可查看
     */
    @ApiModelProperty("风控是否可查看 1-可查看，0-不可查看")
    private Integer riskToView;

    /**
     * 当前处理组ID
     */
    @ApiModelProperty("当前处理组ID")
    private String dealGroupId;

    /**
     * 是否直接完结，false-否，true-是
     */
    @ApiModelProperty("是否直接完结，默认查全部：false-否，true-是")
    private Boolean isDirectOver;

    /* 事件描述 */
    private String eventDesc;

    //================================

    /**
     * 工单子类--》工单类型--》业务类型
     * 按照优先级取值
     * 逗号分隔
     * 前台传入后，封装参数formBusinessIds
     */
    @ApiModelProperty("业务类型")
    private String busId;

    /**
     * 工单子类--》工单类型--》业务类型
     * 按照优先级取值
     * 逗号分隔
     * 前台传入后，封装参数formBusinessIds
     */
    @ApiModelProperty("工单类型")
    private String workId;

    /**
     * 工单子类--》工单类型--》业务类型
     * 按照优先级取值
     * 逗号分隔
     * 前台传入后，封装参数formBusinessIds
     */
    @ApiModelProperty("工单子类")
    private String workSubId;

    /**
     * 子类要素--》工单子类--》工单类型--》业务类型
     * 按照优先级取值
     * 逗号分隔
     * 前台传入后，封装参数formBusinessIds
     */
    @ApiModelProperty("子类要素")
    private String subElementId;

    /**
     * ==========================================
     * 后台根据入参封装得查询条件，前台无需传值
     * 后台封装
     * 表单类型配置ids
     */
    private Set<Long> formBusinessIds;

    /**
     * 后台根据入参封装得查询条件，前台无需传值
     * 后台封装
     * 子类要素集合
     */
    private Set<Long> subElementIds;

    /**
     * true-为空，false-不为空
     *
     * @return
     */
    @SuppressWarnings(value = {"checkstyle:booleanexpressioncomplexity"})
    public boolean checkIsNull() {
        if (StringUtils.isBlank(callNumber) && eventLevel == null && createTimeStart == null && createTimeEnd == null && statusE == null && stopStatusE == null
                && claimStatusE == null && riskToView == null && StringUtils.isBlank(formEventCode) && StringUtils.isBlank(creatorIdE) && StringUtils.isBlank(claimUserIdE) && StringUtils.isBlank(busId) && StringUtils.isBlank(workId) && StringUtils.isBlank(workSubId) && StringUtils.isBlank(dealGroupId)
                && updateTimeStart == null && updateTimeEnd == null && isDirectOver == null && eventDesc == null) {
            return true;
        }
        return false;
    }
}
