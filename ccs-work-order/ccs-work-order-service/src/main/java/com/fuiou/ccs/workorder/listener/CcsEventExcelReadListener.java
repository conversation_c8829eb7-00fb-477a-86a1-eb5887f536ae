package com.fuiou.ccs.workorder.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.workorder.api.annotation.*;
import com.fuiou.ccs.workorder.common.util.ObjectCastUtils;
import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.ccs.workorder.exception.ExcelStopAnalyzeException;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 表格解析
 *
 * @param <T> 导入excel-bean
 * <AUTHOR>
 */
@Slf4j
public class CcsEventExcelReadListener<T> extends AnalysisEventListener<T> {

    /**
     * key= sheet名
     * value = list数据
     */
    @Getter
    private final Map<String, List<T>> allSheetData = new HashMap<>(8);

    /**
     * When analysis one row trigger invoke function.
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(T data, AnalysisContext context) throws CcsServiceException, ExcelStopAnalyzeException {
        Integer currentRowNum = context.getCurrentRowNum();
        Field[] declaredFields = data.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            extractedByStopAnalyzeParse(data, field, currentRowNum);
            extractedByNonNull(data, field, currentRowNum);
            extractedByLongParse(data, field, currentRowNum);
            extractedByString(data, field, currentRowNum);
            extractedByLocalDateTime(data, field, currentRowNum);
        }
        putData(context.readSheetHolder().getReadSheet().getSheetName(), data);
    }

    private void extractedByLocalDateTime(T data, Field field, Integer currentRowNum) {
        LocalDateTimeParseField localDateTimeParseField = field.getAnnotation(LocalDateTimeParseField.class);
        if (Objects.nonNull(localDateTimeParseField)) {
            try {
                Object obj = field.get(data);
                LocalDateTime.parse(String.valueOf(obj));
            } catch (IllegalAccessException e) {
                log.error("extractedByLocalDateTime.IllegalAccessException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9910, e.getMessage());
            } catch (DateTimeParseException e) {
                log.error("extractedByLocalDateTime.DateTimeParseException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9911, "第" + currentRowNum + "行" + localDateTimeParseField.index() + "列：" + localDateTimeParseField.msg());
            }
        }
    }

    private void putData(String sheetName, T data) {
        List<T> beforeDatas = allSheetData.putIfAbsent(sheetName, Lists.newArrayList(data));
        if (CollectionUtils.isNotEmpty(beforeDatas)) {
            beforeDatas.add(data);
            allSheetData.put(sheetName, beforeDatas);
        }
    }

    private void extractedByString(T data, Field field, Integer currentRowNum) {
        StringLengthField stringLengthField = field.getAnnotation(StringLengthField.class);
        if (Objects.nonNull(stringLengthField)) {
            try {
                String o = (String) field.get(data);
                int length = stringLengthField.length();
                if (!Objects.isNull(o) && o.length() > length) {
                    throw new CcsServiceException(9901, "第" + currentRowNum + "行" + stringLengthField.index() + "列：" + stringLengthField.msg() + length + "字");
                }
                int zhLength = stringLengthField.zhLength();
                if (!Objects.isNull(o) && checkCommentLengthIsOut(o, zhLength)) {
                    throw new CcsServiceException(9902, "第" + currentRowNum + "行" + stringLengthField.index() + "列：" + stringLengthField.msg() + zhLength + "汉字");
                }
            } catch (IllegalAccessException e) {
                log.error("extractedByString.IllegalAccessException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9903, e.getMessage());
            }
        }
    }

    /**
     * 计算汉字长度是否超出
     */
    private boolean checkCommentLengthIsOut(String comments, Integer length) {
        if (StringUtils.isNotBlank(comments)) {
            try {
                int wordCountCode = ObjectCastUtils.getWordCountCode(comments, "UTF-8");
                if (wordCountCode >= length) {
                    return true;
                }
            } catch (UnsupportedEncodingException e) {
                log.error("extractedByString.UnsupportedEncodingException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(CcsGlobalError.UTF_CHANGE_IS_FAIL);
            }
        }
        return false;
    }

    private void extractedByLongParse(T data, Field field, Integer currentRowNum) {
        LongParseField longParseField = field.getAnnotation(LongParseField.class);
        if (Objects.nonNull(longParseField)) {
            try {
                Object obj = field.get(data);
                Long.valueOf(String.valueOf(obj));
            } catch (IllegalAccessException e) {
                log.error("extractedByLongParse.IllegalAccessException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9904, e.getMessage());
            } catch (NumberFormatException e) {
                log.error("extractedByLongParse.NumberFormatException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9905, "第" + currentRowNum + "行" + longParseField.index() + "列：" + longParseField.msg());
            }
        }
    }

    private void extractedByNonNull(T data, Field field, Integer currentRowNum) {
        NotNullField annotation = field.getAnnotation(NotNullField.class);
        if (Objects.nonNull(annotation)) {
            try {
                Object o = field.get(data);
                if (Objects.isNull(o)) {
                    throw new CcsServiceException(9906, "第" + currentRowNum + "行" + annotation.index() + "列：" + annotation.msg());
                }
            } catch (IllegalAccessException e) {
                log.error("extractedByNonNull.IllegalAccessException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9907, e.getMessage());
            }
        }
    }

    private void extractedByStopAnalyzeParse(T data, Field field, Integer currentRowNum) throws ExcelStopAnalyzeException {
        StopAnalyzeParseAnnotation stopAnalyzeParseAnnotation = field.getAnnotation(StopAnalyzeParseAnnotation.class);
        if (Objects.nonNull(stopAnalyzeParseAnnotation)) {
            try {
                Object o = field.get(data);
                if (Objects.isNull(o)) {
                    throw new ExcelStopAnalyzeException(9908, "第" + currentRowNum + "行" + stopAnalyzeParseAnnotation.index() + "列：" + stopAnalyzeParseAnnotation.msg());
                }
            } catch (IllegalAccessException e) {
                log.error("extractedByStopAnalyzeParse.IllegalAccessException,{},{}", e.getMessage(), e);
                throw new CcsServiceException(9909, e.getMessage());
            }
        }
    }

    /**
     * if have something to do after all analysis
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 分析完以后，其他操作，例如发送邮件
    }

}
