package com.fuiou.ccs.workorder.util;

import com.google.gson.JsonArray;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 比较类的工具类
 *
 * <AUTHOR>
 * @since 1.0
 */
public class ClassCompareUtil {

    /**
     * 获取当前对象的所有成员变量集合
     *
     * @param object 当前对象
     */
    private static Field[] getAllFields(Object object) {
        Class clazz = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    /**
     * 比较两个对象的成员变量值是否一直相同，如果不相同则返回不同的值
     *
     * @param beforeObj 比较前对象
     * @param afterObj  比较后对象
     * @return
     */
    public static String compareContract(Object beforeObj, Object afterObj) {
        JsonArray jsonElements = new JsonArray();
        try {
            Field[] fields = getAllFields(beforeObj);
            for (int j = 0; j < fields.length; j++) {
                fields[j].setAccessible(true);
                // 字段值
                if (fields[j].get(afterObj) != null) {
                    if (fields[j].getAnnotation(ApiModelProperty.class) != null) {
                        if (fields[j].get(beforeObj) == null) {
                            jsonElements.add(fields[j].getAnnotation(ApiModelProperty.class).value() + "填充为" + fields[j].get(afterObj));
                        } else if (!fields[j].get(beforeObj).equals(fields[j].get(afterObj))) {
                            jsonElements.add(fields[j].getAnnotation(ApiModelProperty.class).value() + "由" + fields[j].get(beforeObj) + "调整为" + fields[j].get(afterObj));
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return jsonElements.toString();
    }


}
