package com.fuiou.ccs.workorder.external.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.ccs.workorder.external.ToExternalService;
import com.fuiou.common.utils.LocalDateUtil;
import com.fuiou.file.dto.FileLink;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 客服工单，对外（电商公司）通知接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service("etcToexternalservice")
public class ExternalToElectricityServiceImpl implements ToExternalService {

    /**
     * 路由审批转电商处理接口
     */
    @Value("${thirdParty.etc.transferElectricity}")
    String transferElectricity;

    /**
     * 客服工单意见交流传给电商记录接口
     */
    @Value("${thirdParty.etc.eventExChangeHandle}")
    String eventExChangeHandle;

    /**
     * 加急通知电商接口
     */
    @Value("${thirdParty.etc.eventHurryNotice}")
    String eventHurryNotice;

    /**
     * 审批驳回给电商接口
     */
    @Value("${thirdParty.etc.eventRejectNotice}")
    String eventRejectNotice;

    /**
     * 客服工单完结，通知电商接口
     */
    @Value("${thirdParty.etc.eventOverNotice}")
    String eventOverNotice;

    /**
     * 客服工单业务类型，新增，通知电商同步接口
     */
    @Value("${thirdParty.etc.businessAddSync}")
    String businessAddSync;

    /**
     * 客服工单业务类型，修改，通知电商同步接口
     */
    @Value("${thirdParty.etc.businessUpdSync}")
    String businessUpdSync;

    /**
     * 客服工单平台终止后通知电商
     */
    @Value("${thirdParty.etc.eventStopSync}")
    String eventStopSync;

    @Override
    public boolean transferElectricity(String formName, List<SysFormDetailDTO> formDetails,
            Map<String, Object> detailMap,
            Long eventId,
            String userName, Integer hurryStatus, SysFormBusinessVO sysFormBusinessVO, List<FileLink> linkList,
            String eventRemark) throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(32);
        // 固定字段
        paramMap.put("callerMobile", Optional.ofNullable(detailMap.get("CALL_NUMBER")).orElse(""));
        paramMap.put("callerName", Optional.ofNullable(detailMap.get("CALLER_NAME")).orElse(""));
        paramMap.put("callerType", buildCallerType(detailMap.get("CALLER_TYPE")));
        paramMap.put("eventTs", LocalDateUtil.format(detailMap.get("EVENT_DATE")));
        paramMap.put("content", Optional.ofNullable(detailMap.get("EVENT_DESC")).orElse(""));
        paramMap.put("subElement", Optional.ofNullable(detailMap.get("SUB_ELEMENT")).orElse(""));
        // 非固定字段
        paramMap.put("courierMobile", Optional.ofNullable(detailMap.get("COURIERMOBILE")).orElse(""));
        paramMap.put("mobile", Optional.ofNullable(detailMap.get("MOBILE")).orElse(""));
        paramMap.put("boxNo", Optional.ofNullable(detailMap.get("BOXNO")).orElse(""));
        paramMap.put("waybill", Optional.ofNullable(detailMap.get("WAYBILL")).orElse(""));
        paramMap.put("pushTs", Optional.ofNullable(detailMap.get("PUSHTS")).orElse(""));
        paramMap.put("hostId", Optional.ofNullable(detailMap.get("HOSTID")).orElse(""));
        paramMap.put("pickTs", Optional.ofNullable(detailMap.get("PICKTS")).orElse(""));
        // 2022-11-08 客服新增需求，增加字段传电商
        paramMap.put("eventRemark", StringUtils.isNotBlank(eventRemark) ? eventRemark : "");
        paramMap.put("orderNo", Optional.ofNullable(detailMap.get("ORDERNO")).orElse(""));
        paramMap.put("orderAmt", Optional.ofNullable(detailMap.get("AMT")).orElse(""));
        // 相关字段
        paramMap.put("eventNo", eventId);
        paramMap.put("loginId", userName);
        paramMap.put("urgentType", hurryStatus);
        paramMap.put("businessType", sysFormBusinessVO.getBusId());
        paramMap.put("workType", sysFormBusinessVO.getWorkId());
        paramMap.put("workSubType", sysFormBusinessVO.getWorkSubId());
        paramMap.put("linkList", linkList);
        return queryExternalInterface(paramMap, transferElectricity);
    }

    /**
     * 转换为电商字典值
     *
     * @param obj
     * @return
     */
    private Object buildCallerType(Object obj) {
        if (Objects.nonNull(obj)) {
            try {
                Integer integer = Integer.parseInt((String) obj);
                switch (integer) {
                    case 1:
                        return 1;
                    case 2:
                        return 0;
                    case 3:
                        return 2;
                    case 4:
                        return 3;
                    case 5:
                        return 4;
                    default:
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    @Override
    public boolean eventExChangeHandle(Long eventId, String remark, String userName, List<FileLink> linkList)
            throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("eventNo", eventId);
        paramMap.put("remark", remark);
        paramMap.put("loginId", userName);
        paramMap.put("linkList", linkList);
        return queryExternalInterface(paramMap, eventExChangeHandle);
    }

    @Override
    public boolean eventHurryNotice(Long eventId, String remark, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("eventNo", eventId);
        paramMap.put("remark", remark);
        paramMap.put("loginId", userName);
        return queryExternalInterface(paramMap, eventHurryNotice);
    }

    @Override
    public boolean eventRejectNotice(Long eventId, String remark, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("eventNo", eventId);
        paramMap.put("remark", remark);
        paramMap.put("loginId", userName);
        return queryExternalInterface(paramMap, eventRejectNotice);
    }

    @Override
    public boolean eventOverNotice(Long eventId, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("eventNo", eventId);
        paramMap.put("loginId", userName);
        return queryExternalInterface(paramMap, eventOverNotice);
    }

    @Override
    public boolean businessAddSync(Long busId, Long parentId, String name, Integer busLevel) throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("busId", busId);
        paramMap.put("name", name);
        paramMap.put("parentId", parentId);
        paramMap.put("busLevel", busLevel);
        return queryExternalInterface(paramMap, businessAddSync);
    }

    @Override
    public boolean businessUpdSync(Long busId, String name, Long parentId, Integer busLevel, Integer status)
            throws CcsServiceException {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("busId", busId);
        paramMap.put("parentId", parentId);
        paramMap.put("name", name);
        paramMap.put("busLevel", busLevel);
        paramMap.put("status", status);
        return queryExternalInterface(paramMap, businessUpdSync);
    }

    @Override
    public boolean eventStopSync(Long eventId, String userName) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("eventNo", String.valueOf(eventId));
        paramMap.put("loginId", userName);
        return queryExternalInterface(paramMap, eventStopSync);
    }

}
