package com.fuiou.ccs.workorder.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.workorder.api.vo.CcsExchangeVO;
import com.fuiou.ccs.workorder.repository.entity.Exchange;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 工单业务数据备注信息记录 服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExchangeService extends IService<Exchange> {

    /**
     * 根据工单数据表名， 和数据id，查询交流记录
     *
     * @param formName
     * @param formInfoId
     * @return
     */
    List<CcsExchangeVO> queryExchangeList(String formName, Long formInfoId);

    /**
     * 查询意见交流日志列表，分公司
     *
     * @param formInfoIdSets
     * @return formInfoId，意见交流列表
     */
    Map<Long, List<CcsExchangeVO>> queryExchangeListByBranchOffice(Set<Long> formInfoIdSets);
}
