package com.fuiou.ccs.workorder.thirdparty.impl;

import com.fuiou.ccs.workorder.thirdparty.WorkOrderStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/29 14:01
 */
@RefreshScope
@Slf4j
@Service("fuPayGetCancelRefund")
public class FUPayGetCancelRefundStrategy implements WorkOrderStrategy {


    @Value("${thirdParty.fuPay.getCancelRefundInfoUrl}")
    private String getCancelRefundInfoUrl;

    @Autowired
    private FUPayBasicsService fuPayBasicsService;

    @Override
    public Map<String, Object> excute(Map<String, Object> param) {
        return fuPayBasicsService.sendPayInterface(param, getCancelRefundInfoUrl);
    }

}
