package com.fuiou.ccs.workorder.client;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.flow.api.error.FlowErrorCode;
import com.fuiou.flow.api.provider.FlowProcessProvider;
import com.fuiou.flow.api.provider.FlowTaskProvider;
import com.fuiou.flow.api.vo.FlowProcessInstance;
import com.fuiou.flow.api.vo.FlowTask;
import com.fuiou.flow.api.vo.FlowTaskDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年11月08日 16:29
 * @Description 路由服务操作类
 */
@Slf4j
@Component
public class FlowOperatorClient {

    @DubboReference
    FlowProcessProvider flowProcessProvider;
    @DubboReference
    FlowTaskProvider flowTaskProvider;

    /**
     * @return boolean
     * 启动路由
     * @params [processKey, businessId, tenantId, variableMap]
     * @date 2021/11/8 16:31
     */
    public FlowProcessInstance startFlow(String processKey, String businessId, String tenantId, Map<String, Object> variableMap) {
        if (StringUtils.isBlank(processKey) || StringUtils.isBlank(businessId) || StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(variableMap)) {
            log.warn("FlowOperatorClient.startFlow.启动路由时缺失方法入参, processKey：{}, businessId：{},tenantId：{}, variableMap：{}", processKey, businessId, tenantId, variableMap);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<FlowProcessInstance> apiResult;
        try {
            apiResult = flowProcessProvider.startFlow(processKey, businessId, tenantId, variableMap);
            log.info("FlowOperatorClient.startFlow.启动会签路由，入参：processKey={},businessId={},tenantId={},variableMap={}; 接口返回： {} ", processKey, businessId, tenantId, JacksonUtil.toJson(variableMap), JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.startFlow.启动路由发送异常，入参：processKey={},businessId={},tenantId={},variableMap={}; 异常原因ERROR：{} , {} ", processKey, businessId, tenantId, JacksonUtil.toJson(variableMap), e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.START_FLOW_IS_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.startFlow.启动路由返回失败：{} ", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * @return java.lang.Boolean
     * 启动路由，并通过第一个任务节点（发起人节点）
     * @params [processInstanceId, deleteReason]
     * @date 2021/11/11 14:45
     */
    public String startFlowToCompleteTask(String processInstanceId, String businessId, String tenantId, String userId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(userId)) {
            log.warn("FlowOperatorClient.startFlowToCompleteTask.启动路由，并通过第一个任务节点（发起人节点）缺失方法入参, processInstanceId：{}, businessId：{} , tenantId: {} , userId",
                    processInstanceId, businessId, tenantId, userId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<String> apiResult;
        try {
            apiResult = flowTaskProvider.startFlowToCompleteTask(processInstanceId, businessId, tenantId, userId);
            log.info("FlowOperatorClient.startFlowToCompleteTask.启动路由，并通过第一个任务节点（发起人节点），入参：processInstanceId：{}, businessId：{} tenantId：{} ，userId：{} ; 接口返回： {} ",
                    processInstanceId, businessId, tenantId, userId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.startFlowToCompleteTask.启动路由，并通过第一个任务节点（发起人节点），入参：processInstanceId：{}, businessId：{} tenantId：{} ，userId：{}  ; 异常原因ERROR：{}, {}", processInstanceId, businessId, tenantId, userId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.START_FLOW_TO_COMPLETE_TASK);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.startFlowToCompleteTask.启动路由，并通过第一个任务节点（发起人节点）返回失败：{}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 根据入参，审批完成通过当前任务
     *
     * @param processInstanceId
     * @param businessId
     * @param taskId
     * @param userId
     * @param comments
     * @param yes
     */
    public FlowTask completeTask(String processInstanceId, String businessId, String tenantId, String taskId, String userId, String comments, int yes) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(taskId) || StringUtils.isBlank(userId)) {
            log.warn("FlowOperatorClient.completeTask.根据入参，审批完成通过当前任务接口调用，入参存在空值。");
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<FlowTask> apiResult;
        try {
            apiResult = flowTaskProvider.completeTask(processInstanceId, businessId, tenantId, taskId, userId,
                    StringUtils.isNotBlank(comments) ? comments : "审批通过", yes);
            log.info("FlowOperatorClient.completeTask.根据入参，审批完成通过当前任务接口调用，入参：  processInstanceId={}, businessId={}, tenantId={}, taskId={}, userId={}, "
                    + "comments={}, yes={} ; 接口返回： {} ", processInstanceId, businessId, tenantId, taskId, userId, comments, yes, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.completeTask.根据入参，审批完成通过当前任务接口调用，入参：processInstanceId={}, businessId={}, tenantId={}, taskId={}, userId={}, "
                    + "comments={}, yes={}  ; 异常原因ERROR：{}, {}", processInstanceId, businessId, tenantId, taskId, userId, comments, yes, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.START_FLOW_TO_COMPLETE_TASK);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.completeTask.根据入参，审批完成通过当前任务接口调用，返回失败：{}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }


    /**
     * @return java.lang.Boolean
     * 检查实例流程是否结束
     * @params [processInstanceId, businessId]
     * @date 2021/11/11 14:44
     */
    public boolean verifyProcessIsEnd(String processInstanceId, String businessId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId)) {
            log.warn("FlowOperatorClient.verifyProcessIsEnd.检查实例流程是否结束时缺失方法入参, processInstanceId：{}, businessId：{}", processInstanceId, businessId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<Map<String, Object>> apiResult;
        try {
            apiResult = flowProcessProvider.verifyProcessIsEnd(processInstanceId, businessId);
            log.info("FlowOperatorClient.verifyProcessIsEnd.检查实例流程是否结束，入参：processInstanceId：{}, businessId：{} ; 接口返回： {} ", processInstanceId, businessId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.verifyProcessIsEnd.检查实例流程是否结束，入参：processInstanceId：{}, businessId：{} ; 异常原因ERROR：{}, {}", processInstanceId, businessId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.VERIFY_PROCESS_IS_END_IS_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            boolean flag = apiResult.getData().get("flag") != null;
            if (flag) {
                return (Boolean) apiResult.getData().get("flag");
            }
        }
        log.error("FlowOperatorClient.verifyProcessIsEnd.检查实例流程是否结束时返回失败: {}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }


    /**
     * @return com.fuiou.flow.api.vo.FlowTaskAssignee
     * 获取当前任务设置的候选人candidateUser+groups+办理人assignee
     * @params [processInstanceId, tenantId, businessId]
     * @date 2021/11/25 14:04
     */
    public List<FlowTaskDefinition> getIdentityUserIds(String processInstanceId, String businessId, String tenantId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId) || StringUtils.isBlank(tenantId)) {
            log.warn("FlowOperatorClient.getIdentityUserIds. 获取当前任务设置的候选人candidateUser+groups+办理人assignee缺失方法入参, processInstanceId：{} , "
                    + "businessId={} , " + "tenantId={}   ", processInstanceId, businessId, tenantId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<List<FlowTaskDefinition>> apiResult;
        try {
            apiResult = flowTaskProvider.getIdentityUserIds(processInstanceId, businessId, tenantId);
            log.info("FlowOperatorClient.getIdentityUserIds. 获取当前任务设置的候选人candidateUser+groups+办理人assignee，入参： processInstanceId：{} , businessId={} , "
                    + "tenantId={}    ; 接口返回： {} ", processInstanceId, businessId, tenantId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.getIdentityUserIds. 获取当前任务设置的候选人candidateUser+groups+办理人assignee，入参： processInstanceId：{} , businessId={} , "
                    + "tenantId={}    ; 异常原因ERROR：{} ， {} ", processInstanceId, businessId, tenantId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.GET_IDENTITY_BY_TASKID_IS_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.getIdentityUserIds. 获取当前任务设置的候选人candidateUser+groups+办理人assignee返回失败：{} ", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 驳回任务给到发起人
     *
     * @params [taskId, userId, comments, variablesMap]
     * @date 2021/11/15 16:33
     */
    public FlowTask rejectCcsTaskToAtWill(String taskId, String userId, String tenantId, String activityId, String comments,
                                          Map<String, Object> variablesMap, String previousGroupIds) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(activityId) || StringUtils.isBlank(userId)) {
            log.warn("FlowOperatorClient.rejectCcsTaskToAtWill.驳回任务给到上一个审批节点缺失方法入参");
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<FlowTask> apiResult;
        try {
            apiResult = flowTaskProvider.rejectCcsTaskToAtWill(taskId, userId, tenantId,
                    StringUtils.isNotBlank(comments) ? comments : "驳回上一个节点", activityId,
                    variablesMap, previousGroupIds);
            log.info("FlowOperatorClient.rejectCcsTaskToAtWill.驳回任务给到上一个审批节点缺失方法入参，入参：taskId={},userId={} ,previousGroupIds={}; 接口返回： {} ", taskId,
                    userId, previousGroupIds,
                    JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.rejectCcsTaskToAtWill.驳回任务给到上一个审批节点缺失方法入参，入参：taskId={},userId={},previousGroupIds={} ; 异常原因ERROR：{}, {}",
                    taskId, userId, previousGroupIds,
                    e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.REJECT_TASK_TO_PREVIOUS);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.rejectCcsTaskToAtWill.驳回任务给到上一个审批节点缺失方法入参返回失败：{} ", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }


    /**
     * @return void
     * 认领/取消认领任务到我名下
     * 任务id
     * 需要认领任务的用户
     * 认领归属组
     * 认领任务/取消认领
     * @params [taskId, userId，groupIds，type]
     * type，true-认领任务，false-取消认领任务
     * @date 2021/11/24 13:51
     */
    public FlowTask claimTaskByCcs(String taskId, String userId, List<String> groupIds, boolean type) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(userId)) {
            log.warn("FlowOperatorClient.claimTaskByCcs.认领任务到我名下时缺失方法入参");
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        if (CollectionUtils.isEmpty(groupIds)) {
            groupIds = new ArrayList<>(8);
        }
        ApiResult<FlowTask> apiResult;
        try {
            apiResult = flowTaskProvider.claimTaskByCcs(taskId, userId, groupIds, type);
            log.info("FlowOperatorClient.claimTaskByCcs.认领任务到我名下，入参：taskId={},userId={},groupIds={},type={}; 接口返回： {} ", taskId, userId,
                    JacksonUtil.toJson(groupIds), type, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.claimTaskByCcs.认领任务到我名下，入参：taskId={},userId={},groupIds={} ,type={}; 异常原因ERROR：{} ， {} ", taskId, userId,
                    JacksonUtil.toJson(groupIds), type, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.TASK_CLAIM_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.claimTaskByCcs.认领任务到我名下，返回失败：{} ", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 指派认领任务到其他人名下报错
     *
     * @param taskId
     * @param assignee
     * @param groupIds
     * @param assigneeOtherUserId
     * @return
     */
    public FlowTask assignOtherUserclaimTaskByCcs(String taskId, String assignee, List<String> groupIds, String assigneeOtherUserId,
                                                  String assigneeOtherGroupId) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(assignee) || CollectionUtils.isEmpty(groupIds) || (StringUtils.isBlank(assigneeOtherUserId) && StringUtils.isBlank(assigneeOtherGroupId))) {
            log.warn("FlowOperatorClient.assignOtherUserclaimTaskByCcs.指派认领任务到其他人名下报错缺失方法入参，taskId={} , assignee={} , groupIds={} , "
                    + "assigneeOtherUserId={} , assigneeOtherGroupId={} ", taskId, assignee, JacksonUtil.toJson(groupIds), assigneeOtherUserId, assigneeOtherGroupId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<FlowTask> apiResult;
        try {
            apiResult = flowTaskProvider.assignOtherUserclaimTaskByCcs(taskId, assignee, groupIds, assigneeOtherUserId, assigneeOtherGroupId);
            log.info("FlowOperatorClient.assignOtherUserclaimTaskByCcs.指派认领任务到其他人名下报错，入参：taskId={},assignee={},groupIds={},assigneeOtherUserId={}, "
                            + "assigneeOtherGroupId={}" + "; " + "接口返回： {} ", taskId,
                    assignee, JacksonUtil.toJson(groupIds), JacksonUtil.toJson(apiResult), assigneeOtherUserId, assigneeOtherGroupId);
        } catch (Exception e) {
            log.error("FlowOperatorClient.assignOtherUserclaimTaskByCcs.指派认领任务到其他人名下报错，入参：taskId={},assignee={},groupIds={},assigneeOtherUserId={} "
                            + ",assigneeOtherGroupId={}; 异常原因ERROR：{} ， {} ", taskId, assignee, JacksonUtil.toJson(groupIds), assigneeOtherUserId,
                    assigneeOtherGroupId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.TASK_CLAIM_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.assignOtherUserclaimTaskByCcs.指派认领任务到其他人名下报错，返回失败：{} ", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 挂起任务
     *
     * @params [processInstanceId, businessId]
     * @date 2021/11/11 14:44
     */
    public void suspendTask(String processInstanceId, String businessId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId)) {
            log.warn("FlowOperatorClient.suspendTask.挂起任务缺失方法入参, processInstanceId：{}, businessId：{}", processInstanceId, businessId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<Void> apiResult;
        try {
            apiResult = flowProcessProvider.suspendTask(processInstanceId, businessId);
            log.info("FlowOperatorClient.suspendTask.挂起任务，入参：processInstanceId：{}, businessId：{} ; 接口返回： {} ", processInstanceId, businessId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.suspendTask.挂起任务，入参：processInstanceId：{}, businessId：{} ; 异常原因ERROR：{}, {}", processInstanceId, businessId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.SUSPEND_TASK_IS_FAIL);
        }
        if (!Boolean.TRUE.equals(apiResult.getSuccess())) {
            log.error("FlowOperatorClient.suspendTask.挂起任务返回失败：{}", JacksonUtil.toJson(apiResult));
            throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
        }
    }

    /**
     * 重启挂起任务
     *
     * @params [processInstanceId, businessId]
     * @date 2021/11/11 14:45
     */
    public void continueTask(String processInstanceId, String businessId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId)) {
            log.warn("FlowOperatorClient.continueTask.重启挂起任务缺失方法入参, processInstanceId：{}, businessId：{}", processInstanceId, businessId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<Void> apiResult;
        try {
            apiResult = flowProcessProvider.continueTask(processInstanceId, businessId);
            log.info("FlowOperatorClient.continueTask.重启挂起任务，入参：processInstanceId：{}, businessId：{} ; 接口返回： {} ", processInstanceId, businessId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.continueTask.重启挂起任务，入参：processInstanceId：{}, businessId：{} ; 异常原因ERROR：{}, {}", processInstanceId, businessId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.CONTINUE_TASK_IS_FAIL);
        }
        if (!Boolean.TRUE.equals(apiResult.getSuccess())) {
            log.error("FlowOperatorClient.continueTask.重启挂起任务返回失败：{}", JacksonUtil.toJson(apiResult));
            throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
        }
    }

    /**
     * 根据入参，查询关于我的任务信息
     *
     * @param userId
     * @param tenantId
     * @param processInstanceId
     * @param businessId
     * @param groupIds
     */
    public List<FlowTask> getMeTaskListByCcs(String userId, String tenantId, String processInstanceId, String businessId, List<String> groupIds) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId) || groupIds == null) {
            log.warn("FlowOperatorClient.getMeTaskListByCcs.根据入参，查询关于我的任务信息，缺失方法入参,  userId={},   tenantId={},   processInstanceId={},  "
                    + "businessId={}, groupIds", userId, tenantId, processInstanceId, businessId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<List<FlowTask>> apiResult;
        try {
            apiResult = flowTaskProvider.getMeTaskListByCcs(userId, tenantId, processInstanceId, businessId, groupIds);
            log.info("FlowOperatorClient.getMeTaskListByCcs.根据入参，查询关于我的任务信息，入参：userId={}, tenantId={}, processInstanceId={}, businessId={}, "
                    + "groupIds={}; 接口返回： {} ", userId, tenantId, processInstanceId, businessId, JacksonUtil.toJson(groupIds), JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.getMeTaskListByCcs.根据入参，查询关于我的任务信息，入参：userId={}, tenantId={}, processInstanceId={}, businessId={}, groupIds={}; 异常原因ERROR：{}, {}", userId, tenantId, processInstanceId, businessId, JacksonUtil.toJson(groupIds), e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.FILTER_ME_TASK_FAIL);
        }

        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.getMeTaskListByCcs.根据入参，查询关于我的任务信息，返回失败：{}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 根据taskId，查询下一个节点信息
     *
     * @param taskId
     * @param approve
     * @return
     */
    public FlowTaskDefinition getNextNodeByTaskId(String taskId, Integer approve) {
        if (StringUtils.isBlank(taskId)) {
            log.warn("FlowOperatorClient.getNextNodeByTaskId.根据taskId，查询下一个节点信息，缺失入参taskId ");
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<FlowTaskDefinition> apiResult;
        try {
            apiResult = flowTaskProvider.getNextNodeByTaskId(taskId, approve);
            log.info("FlowOperatorClient.getNextNodeByTaskId.根据taskId，查询下一个节点信息，入参：taskId={} ,approve ={} ; 接口返回： {} ", taskId, approve,
                    JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.getNextNodeByTaskId.根据taskId，查询下一个节点信息，入参：taskId={} ,approve ={} ; 异常原因ERROR：{}, {}", taskId, approve, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.GET_NEXT_NODE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.getNextNodeByTaskId.根据taskId，查询下一个节点信息，返回失败：{}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 检查这些流程实例是否同一个版本
     *
     * @param processKey
     * @param processInstanceIdList
     * @return
     */
    public Map<String, List<String>> checkFlowVersionIsSame(String processKey, List<String> processInstanceIdList) {
        if (StringUtils.isBlank(processKey) || CollectionUtils.isEmpty(processInstanceIdList)) {
            log.warn("FlowOperatorClient.checkFlowVersionIsSame.检查这些流程实例是否同一个版本，缺失入参processKey,processInstanceIdList");
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<Map<String, List<String>>> apiResult;
        try {
            apiResult = flowProcessProvider.checkFlowVersionIsSame(processKey, processInstanceIdList);
            log.info("FlowOperatorClient.checkFlowVersionIsSame.检查这些流程实例是否同一个版本，入参：processKey={} ," + "processInstanceIdList ={} ; 接口返回： {} ", processKey, JacksonUtil.toJson(processInstanceIdList), JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.checkFlowVersionIsSame.检查这些流程实例是否同一个版本，入参：processKey={} ,"
                    + "processInstanceIdList ={} ; 异常原因ERROR：{}, {}", processKey, JacksonUtil.toJson(processInstanceIdList), e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.GET_NEXT_NODE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(apiResult.getSuccess())) {
            return apiResult.getData();
        }
        log.error("FlowOperatorClient.checkFlowVersionIsSame.检查这些流程实例是否同一个版本，返回失败：{}", JacksonUtil.toJson(apiResult));
        throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
    }

    /**
     * 终止任务
     *
     * @params [processInstanceId, businessId]
     * @date 2021/11/11 14:44
     */
    public void terminationTask(String processInstanceId, String businessId) {
        if (StringUtils.isBlank(processInstanceId) || StringUtils.isBlank(businessId)) {
            log.warn("FlowOperatorClient.terminationProcess.终止任务缺失方法入参, processInstanceId：{}, businessId：{}", processInstanceId, businessId);
            throw new CcsServiceException(FlowErrorCode.FLOW_PARAMS_IS_NULL);
        }
        ApiResult<Void> apiResult;
        try {
            apiResult = flowProcessProvider.terminationProcess(processInstanceId, businessId);
            log.info("FlowOperatorClient.terminationTask.终止任务，入参：processInstanceId：{}, businessId：{} ; 接口返回： {} ", processInstanceId, businessId, JacksonUtil.toJson(apiResult));
        } catch (Exception e) {
            log.error("FlowOperatorClient.terminationTask.终止任务，入参：processInstanceId：{}, businessId：{} ; 异常原因ERROR：{}, {}", processInstanceId, businessId, e.getMessage(), e);
            throw new CcsServiceException(FlowErrorCode.TERMINATION_PROCESS_IS_FAIL);
        }
        if (!Boolean.TRUE.equals(apiResult.getSuccess())) {
            log.error("FlowOperatorClient.terminationTask.终止任务返回失败：{}", JacksonUtil.toJson(apiResult));
            throw new CcsServiceException(apiResult.getCode(), apiResult.getMsg());
        }
    }
}
