package com.fuiou.ccs.workorder.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.workorder.repository.entity.OutsideSource;
import com.fuiou.ccs.workorder.repository.mapper.OutsideSourceMapper;
import com.fuiou.ccs.workorder.service.OutsideSourceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外部来源配置 服务实现类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Service
public class OutsideSourceServiceImpl extends ServiceImpl<OutsideSourceMapper, OutsideSource> implements OutsideSourceService {

    /**
     * 查询所有部来源数据的URL的ID集合
     * @return List<Long>
     */
    @Override
    public List<Long> queryAllUrlId() {
        return baseMapper.queryAllUrlId();
    }
}
