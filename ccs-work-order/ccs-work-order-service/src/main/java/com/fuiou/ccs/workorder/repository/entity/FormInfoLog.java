package com.fuiou.ccs.workorder.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单数据表，修改日志记录
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_FORM_INFO_LOG")
public class FormInfoLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @TableId(value = "LOG_ID", type = IdType.AUTO)
    private Long logId;

    /**
     * 工单数据表表名
     */
    @TableField("FORM_NAME")
    private String formName;

    /**
     * 工单数据表ID
     */
    @TableField("FORM_INFO_ID")
    private Long formInfoId;

    /**
     * 修改内容，（文字格式描述）
     */
    @TableField("CONTENT")
    private String content;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
