package com.fuiou.ccs.workorder.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单业务数据备注信息记录
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EXCHANGE")
public class Exchange implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "EXCHANGE_ID", type = IdType.AUTO)
    private Long exchangeId;

    /**
     * 工单数据表表名
     */
    @TableField("FORM_NAME")
    private String formName;

    /**
     * 工单数据表id
     */
    @TableField("FORM_INFO_ID")
    private Long formInfoId;

    /**
     * 备注信息，正文内容（html标签格式）
     */
    @TableField("EXCHANGE_CONTENT")
    private String exchangeContent;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 其他系统传过来的交流意见保存，记录用户名
     */
    @TableField(value = "OUT_EMP_NAME")
    private String outEmpName;

    /**
     * 当前处理人
     */
    @TableField(value = "ASSIGNEE")
    private String assignee;

    /**
     * 多选用户
     */
    @TableField(value = "CANDIDATEUSERS")
    private String candidateusers;

    /**
     * 候选组
     */
    @TableField(value = "CANDIDATEGROUPS")
    private String candidategroups;

    /**
     * 当前任务节点名称
     */
    @TableField(value = "NODE_NAME")
    private String nodeName;

    /**
     * 事件ID
     */
    @TableField(value = "EVENT_ID")
    private Long eventId;

    /**
     * 当前事件状态
     */
    @TableField(value = "EVENT_STATUS")
    private Integer eventStatus;

}
