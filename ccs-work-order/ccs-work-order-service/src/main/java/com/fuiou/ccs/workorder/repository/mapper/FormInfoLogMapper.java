package com.fuiou.ccs.workorder.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fuiou.ccs.workorder.api.vo.FormInfoLogVO;
import com.fuiou.ccs.workorder.repository.entity.FormInfoLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单数据表，修改日志记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface FormInfoLogMapper extends BaseMapper<FormInfoLog> {

    /**
     * 根据数据工单表名，表id, 查询当前数据对应的全部修改日志
     *
     * @param formName
     * @param formInfoId
     * @return
     */
    List<FormInfoLogVO> queryFormInfoLogList(@Param("formName") String formName, @Param("formInfoId") Long formInfoId);

}
