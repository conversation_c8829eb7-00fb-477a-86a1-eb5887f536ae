package com.fuiou.ccs.workorder.thirdparty.impl;

import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.ccs.workorder.thirdparty.WorkOrderStrategy;
import com.fuiou.common.utils.HttpClientUtils;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13 14:04
 */
@RefreshScope
@Slf4j
@Service("fuEComQryWaybill")
public class FUEComQryWaybillStrategy implements WorkOrderStrategy {

    @Value("${thirdParty.fuECom.qryWaybillUrl}")
    private String qryWaybillUrl;

    @Override
    public Map<String, Object> excute(Map<String, Object> param) {
        if (param == null) {
            throw new CcsServiceException(CcsGlobalError.THIRD_PARTY_INTERFACE_FAIL.getCode(), "电商公司接口查询运单信息参数为空");
        }
        Map<String, Object> resultMap;
        try {
            log.info("fuEComQryWaybill.excute.调用电商公司接口查询运单信息入参：{}", param);
            String result = HttpClientUtils.doPostJson(JacksonUtil.toJson(param), qryWaybillUrl);
            log.info("fuEComQryWaybill.excute.调用电商公司接口查询运单信息，接口返回，入参： param={} ，返回：result={} ", param, result);
            resultMap = JacksonUtil.parseMap(result);
        } catch (Exception e) {
            log.error("fuEComQryWaybill.excute.调用电商公司接口查询运单信息异常！e:", e);
            throw new CcsServiceException(CcsGlobalError.THIRD_PARTY_INTERFACE_FAIL.getCode(), "调用电商公司接口查询运单信息失败！");
        }

        //{"code":500,"desc":"暂无该终端信息","success":false}

        if (resultMap != null && resultMap.get("code") != null) {
            if (resultMap.get("code").equals(200)) {
                return (Map<String, Object>) resultMap.get("data");
            } else {
                throw new CcsServiceException(CcsGlobalError.THIRD_PARTY_INTERFACE_RETURN_NULL.getCode(), "电商公司接口查询运单信息失败，" + resultMap.get("desc").toString());
            }
        }
        //返回{}/[]/" "
        throw new CcsServiceException(CcsGlobalError.THIRD_PARTY_INTERFACE_FAIL.getCode(), "调用电商公司接口查询运单信息失败！");

    }
}
