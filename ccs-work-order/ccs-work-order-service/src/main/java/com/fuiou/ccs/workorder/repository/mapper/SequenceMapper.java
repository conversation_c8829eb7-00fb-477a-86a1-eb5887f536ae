package com.fuiou.ccs.workorder.repository.mapper;

import com.fuiou.ccs.workorder.repository.entity.CcsSeq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SequenceMapper {


    /**
     * 获取工单序列
     *
     * @param key
     * @param seqDate
     * @return
     */
    CcsSeq getCcsSeq(@Param("key") String key, @Param("seqDate") String seqDate);

    /**
     * 创建新的工单序列
     *
     * @param key
     * @param seqDate
     * @param desc
     * @param version
     * @param moudleType
     */
    void insertCcsSeq(@Param("key") String key, @Param("seqDate") String seqDate, @Param("desc") String desc, @Param("version") String version, @Param("moudleType") String moudleType);

    int updateCcsSeq(@Param("key") String key, @Param("seqDate") String seqDate, @Param("newVersion") String newVersion, @Param("oldVersion") String oldVersion);

}
