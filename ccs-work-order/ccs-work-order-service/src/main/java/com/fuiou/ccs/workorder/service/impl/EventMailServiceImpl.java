package com.fuiou.ccs.workorder.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.system.dto.CcsSysUserInfo;
import com.fuiou.ccs.workorder.api.dto.mail.CcsRepeatMailDTO;
import com.fuiou.ccs.workorder.api.dto.mail.CcsUserMailDTO;
import com.fuiou.ccs.workorder.api.dto.mail.EventWorkOrderMailDTO;
import com.fuiou.ccs.workorder.api.enums.MailTemplateEnums;
import com.fuiou.ccs.workorder.api.vo.CcsEventVO;
import com.fuiou.ccs.workorder.api.vo.SysFormEventVO;
import com.fuiou.ccs.workorder.client.CcsUserClient;
import com.fuiou.ccs.workorder.convert.EventConvert;
import com.fuiou.ccs.workorder.service.CcsSendMailService;
import com.fuiou.ccs.workorder.service.EventMailService;
import com.fuiou.ccs.workorder.service.EventService;
import com.fuiou.common.utils.FreemarkerUtils;
import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.mail.service.MailService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 邮件发送接口
 *
 * <AUTHOR>
 * @date 2022-9-16
 */
@Slf4j
@Service
public class EventMailServiceImpl implements EventMailService {

    @Autowired
    CcsUserClient ccsUserClient;
    @Autowired
    CcsSendMailService ccsSendMailService;
    @Value("${fuiou.ccs.url}")
    String gdpbHttpUrl;
    @Autowired
    @Lazy
    EventService eventService;
    @Resource(name = "mailService")
    MailService mailService;

    @Override
    public CcsRepeatMailDTO buildCompleteTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.COMPLETE, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildRejectTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.REJECT, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildComeOnTimeoutTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.COME_ON_TIMEOUT, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildAlreadyTimeoutTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.ALREADY_TIMEOUT, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildCancelClaimTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.CANCEL_CLAIM, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildInvalidNoticeTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.INVALID_NOTICE, gdpbHttpUrl);
    }

    @Override
    public CcsRepeatMailDTO buildImportExcelNoticeTemplate() {
        return new CcsRepeatMailDTO(MailTemplateEnums.IMPORT_EXCEL_RESULT_NOTICE, gdpbHttpUrl);
    }

    @Override
    public void sendMailToFlowProcess(String userId, Long eventId, String toUser, CcsRepeatMailDTO ccsRepeatMailDto, String logId, List<String> toUsers) {
        if (StringUtils.isBlank(userId) || eventId == null || ccsRepeatMailDto == null || logId == null) {
            log.error("EventMailServiceImpl.sendMailToFlowProcess.logId={},存在空", logId);
            return;
        }
        CcsSendMailServiceImpl.EXECUTOR.execute(() -> {
            try {
                Thread.sleep(1000);
                sendMail(userId, eventId, toUser, ccsRepeatMailDto, logId, toUsers);
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("EventMailServiceImpl.sendMailToFlowProcess.execute，logId={}, 方法调用报错，{} ，{}", logId, e.getMessage(), e);
            }
        });
    }

    /**
     * 发送邮件
     * 注意：客服工单的用户都是发送到登录链接，所以可以抄送，后续如果需要免登录，则不能设置抄送
     */
    private void sendMail(String userId, Long eventId, String toUser, CcsRepeatMailDTO ccsRepeatMailDto, String logId, List<String> toUsers) {
        if (StringUtils.isBlank(toUser) && CollectionUtils.isEmpty(toUsers)) {
            return;
        }
        List<String> userIds = new ArrayList<>(16);
        if (StringUtils.isNotBlank(toUser)) {
            userIds.add(toUser);
        }
        if (CollectionUtils.isNotEmpty(toUsers)) {
            userIds.addAll(toUsers);
        }
        List<CcsSysUserInfo> userListByUserIds = ccsUserClient.getUserListByUserIds(userIds);
        List<CcsUserMailDTO> ccsUserMailDTOS = EventConvert.INSTANCE.toUserMailDtoList(userListByUserIds);
        ccsUserMailDTOS.forEach(x -> x.setMailUrls(Lists.newArrayList(ccsRepeatMailDto.getLongLink())));
        if (CollectionUtils.isEmpty(ccsUserMailDTOS)) {
            return;
        }
        EventWorkOrderMailDTO eventWorkOrderMailDTO = new EventWorkOrderMailDTO();
        eventWorkOrderMailDTO.setEventId(String.valueOf(eventId));
        SysFormEventVO formEventVO = eventService.querySysFormInfo(eventId);
        eventWorkOrderMailDTO.setFormCode(formEventVO.getFORM_EVENT_CODE());
        if (ccsRepeatMailDto.getTemplateType().getValue().equals(MailTemplateEnums.INVALID_NOTICE.getValue())) {
            CcsEventVO ccsEventVO = EventConvert.INSTANCE.toVO(eventService.getById(eventId));
            eventWorkOrderMailDTO.setStatusName(ccsEventVO.getStatusName());
            CcsUserMailDTO ccsUserMailDTO = ccsUserMailDTOS.stream().filter(u -> u.getUserId().equals(toUser)).findFirst().get();
            eventWorkOrderMailDTO.setDealUserName(ccsUserMailDTO.getUsername());
            eventWorkOrderMailDTO.setDealEmpName(ccsUserMailDTO.getEmpName());
        }
        List<CcsUserMailDTO> toUserMail = new ArrayList<>();
        List<CcsUserMailDTO> ccUserMail = new ArrayList<>();
        if (StringUtils.isBlank(toUser)) {
            for (int i = 0; i < ccsUserMailDTOS.size(); i++) {
                if (i == 0) {
                    toUserMail.add(ccsUserMailDTOS.get(i));
                } else {
                    ccUserMail.add(ccsUserMailDTOS.get(i));
                }
            }
        } else {
            toUserMail = ccsUserMailDTOS.stream().filter(x -> x.getUserId().equals(toUser)).collect(Collectors.toList());
            ccUserMail = ccsUserMailDTOS.stream().filter(x -> !x.getUserId().equals(toUser)).collect(Collectors.toList());
        }
        ccsSendMailService.sendMail(toUserMail, userId, eventWorkOrderMailDTO, eventId, ccsRepeatMailDto.getTemplateType(), logId, ccUserMail);
        log.info("EventMailServiceImpl.sendMailToFlowProcess.sendMail.邮件发送集合。logId={}，{} , {}, {}, {}, {}", logId, JacksonUtil.toJson(ccsUserMailDTOS), userId, new EventWorkOrderMailDTO(), eventId, JacksonUtil.toJson(ccsRepeatMailDto.getTemplateType()));
        Map<String, String> params = new ConcurrentHashMap<>(8);
        params.put("formCode", formEventVO.getFORM_EVENT_CODE());
        sendSmsToFlowProcess(ccsUserMailDTOS, ccsRepeatMailDto, params, logId);
    }

    @Override
    public void sendSmsToFlowProcess(List<CcsUserMailDTO> ccsUserMailDTOS, CcsRepeatMailDTO ccsRepeatMailDto, Map<String, String> params, String logId) {
        if (CollectionUtils.isEmpty(ccsUserMailDTOS) || Objects.isNull(ccsRepeatMailDto)) {
            log.error("EventMailServiceImpl.sendSmsToFlowProcess.logId={},存在空", logId);
            return;
        }
        CcsSendMailServiceImpl.EXECUTOR.execute(() -> {
            try {
                Thread.sleep(1000);
                sendSms(ccsUserMailDTOS, ccsRepeatMailDto.getTemplateType(), params, logId);
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("EventMailServiceImpl.sendSmsToFlowProcess.execute，logId={}, 方法调用报错，{} ，{}", logId, e.getMessage(), e);
            }
        });
    }

    /**
     * 发送短信
     *
     * @param ccsUserMailDTOS
     * @param templateType
     * @param params          短信模板入参，除了收件人之外的参数
     * @param logId
     */
    private void sendSms(List<CcsUserMailDTO> ccsUserMailDTOS, MailTemplateEnums templateType, Map<String, String> params, String logId) {
        ccsSendMailService.sendSms(ccsUserMailDTOS, templateType, params, logId);
    }


    @Override
    public void sendMailAndSmsToParams(Map<String, Object> params, CcsRepeatMailDTO ccsRepeatMailDto, String logId, String toUser, List<String> toUsers) {
        if (CollectionUtils.isEmpty(params) || ccsRepeatMailDto == null || StringUtils.isBlank(logId)) {
            log.error("EventMailServiceImpl.sendMailAndSmsToParams.logId={},存在空", logId);
            return;
        }
        CcsSendMailServiceImpl.EXECUTOR.execute(() -> {
            try {
                Thread.sleep(1000);
                sendMailAndSms(params, ccsRepeatMailDto, logId, toUser, toUsers);
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("EventMailServiceImpl.sendMailAndSmsToParams.execute，logId={}, 方法调用报错，{} ，{}", logId, e.getMessage(), e);
            }
        });
    }

    private void sendMailAndSms(Map<String, Object> params, CcsRepeatMailDTO ccsRepeatMailDto, String logId, String toUser, List<String> toUsers) throws MessagingException {
        if (StringUtils.isBlank(toUser) && CollectionUtils.isEmpty(toUsers)) {
            return;
        }
        if (toUsers == null) {
            toUsers = Lists.newArrayList();
        }
        toUsers.add(toUser);
        List<CcsSysUserInfo> userListByUserIds = ccsUserClient.getUserListByUserIds(toUsers);
        List<CcsUserMailDTO> ccsUserMailDTOS = EventConvert.INSTANCE.toUserMailDtoList(userListByUserIds);
        ccsUserMailDTOS.forEach(x -> x.setMailUrls(Lists.newArrayList(ccsRepeatMailDto.getLongLink())));
        if (CollectionUtils.isEmpty(ccsUserMailDTOS)) {
            return;
        }
        CcsUserMailDTO toUserMail;
        List<CcsUserMailDTO> ccUserMail = new ArrayList<>();
        if (StringUtils.isBlank(toUser)) {
            toUserMail = ccsUserMailDTOS.get(0);
            ccsUserMailDTOS.remove(0);
            ccUserMail.addAll(ccsUserMailDTOS);
        } else {
            toUserMail = ccsUserMailDTOS.stream().filter(x -> x.getUserId().equals(toUser)).findFirst().orElse(new CcsUserMailDTO());
            ccUserMail.addAll(ccsUserMailDTOS.stream().filter(x -> !x.getUserId().equals(toUser)).collect(Collectors.toList()));
        }
        params.put("mailUrls", toUserMail.getMailUrls());
        String subject = getSubject(ccsRepeatMailDto.getTemplateType().getSubject(), params);
        String content = FreemarkerUtils.getEmailHtml(params, ccsRepeatMailDto.getTemplateType().getTemplateName());
        mailService.sendHtmlMail(toUserMail.getEmail(), subject, content, ccUserMail.stream().map(c -> c.getEmail()).collect(Collectors.toList()).toArray(new String[0]));
        log.info("EventMailServiceImpl.sendMailAndSmsToParams.sendMail.邮件发送集合。logId={}，{} , {}, {}", logId, JacksonUtil.toJson(ccsUserMailDTOS), toUser, JacksonUtil.toJson(ccsRepeatMailDto.getTemplateType()));
        Map<String, String> smsParams = new ConcurrentHashMap<>(8);
        smsParams.put("dealResult", String.valueOf(params.get("dealResult")));
        sendSmsToFlowProcess(ccsUserMailDTOS, ccsRepeatMailDto, smsParams, logId);
    }

    private String getSubject(String subject, Map<String, Object> params) {
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            subject = subject.replace("${" + entry.getKey() + "}", Objects.nonNull(entry.getValue()) ? String.valueOf(entry.getValue()) : "");
        }
        return subject;
    }
}
