package com.fuiou.ccs.workorder.client;

import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.file.provider.SysFileProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 表单配置详情接口调用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CcsFileClient {

    @DubboReference
    SysFileProvider sysFileProvider;

    /**
     * 删除文件
     *
     * @param fileIds
     */
    public void removeFileData(String... fileIds) {
        if (fileIds == null || fileIds.length == 0) {
            log.error("CcsFileClient.removeFileData.删除文件，接口入参为空！fileIds=null.error=NullPointException");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<Void> result;
        try {
            result = sysFileProvider.removeFileData(fileIds);
            log.info("CcsFileClient.removeFileData.删除文件，接口返回，入参： fileIds={} ，返回：result={} ", JacksonUtil.toJson(fileIds), JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("CcsFileClient.removeFileData.删除文件，异常，入参： fileIds={} ，报错：error={}", JacksonUtil.toJson(fileIds), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (!Boolean.TRUE.equals(result.getSuccess())) {
            log.error("CcsFileClient.removeFileData.删除文件，入参：fileIds={} ， 返回：{}", JacksonUtil.toJson(fileIds), JacksonUtil.toJson(result));
            throw new CcsServiceException(result.getCode(), result.getMsg());
        }
    }


}
