package com.fuiou.ccs.workorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuiou.ccs.workorder.repository.entity.OutsideSource;

import java.util.List;

/**
 * 外部来源配置 服务类
 *
 * <AUTHOR>
 * @since  1.0.0
 */
public interface OutsideSourceService extends IService<OutsideSource> {

    /**
     * 查询所有部来源数据的URL的ID集合
     * @return List<Long>
     */
    List<Long> queryAllUrlId();
}
