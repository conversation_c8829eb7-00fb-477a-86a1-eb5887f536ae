package com.fuiou.ccs.workorder.client;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fuiou.ccs.system.dto.SysGroupDTO;
import com.fuiou.ccs.system.dto.SysGroupMemberDTO;
import com.fuiou.ccs.system.provider.SysGroupProvider;
import com.fuiou.ccs.system.vo.SysGroupVO;
import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.common.api.ApiResult;
import com.fuiou.common.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工单组接口调用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CcsGroupClient {

    @DubboReference
    SysGroupProvider sysGroupProvider;

    /**
     * 根据用户userId,查询该用户归属组
     *
     * @param userId
     * @return
     */
    public List<SysGroupDTO> queryBelongGroupList(String userId) {
        if (StringUtils.isBlank(userId)) {
            log.error("SysGroupClient.queryBelongGroupList.根据用户userId,查询该用户归属组，接口入参为空！userId=null.error=NullPointException");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<List<SysGroupDTO>> result;
        try {
            result = sysGroupProvider.queryBelongGroupList(userId);
            log.info("SysGroupClient.queryBelongGroupList.根据用户userId,查询该用户归属组，接口返回，入参： userId={} ，返回：result={} ", userId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.queryBelongGroupList.根据用户userId,查询该用户归属组，异常，入参： userId={} ，报错：error={}", userId, e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.queryBelongGroupList.根据用户userId,查询该用户归属组，失败，入参：userId={} ， 返回：{}", userId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据入参，检查当前任务是否可以指派
     *
     * @param userId                         登录用户，操作指定他人认领的用户
     * @param groupList                      归属组，需要认领的组，集合
     * @param claimTaskFromAssignOtherUserId 被指派去认领的userId
     * @return true-可以指派，false-不可以指派
     */
    public Boolean checkIsAssignClaim(String userId, List<Long> groupList, String claimTaskFromAssignOtherUserId) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(groupList) || StringUtils.isBlank(claimTaskFromAssignOtherUserId)) {
            log.error("SysGroupClient.checkIsAssignClaim.根据入参，检查当前任务是否可以指派，接口入参为空！userId , groupList, claimTaskFromAssignOtherUserId");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<Boolean> result;
        try {
            result = sysGroupProvider.checkIsAssignClaim(userId, groupList, claimTaskFromAssignOtherUserId);
            log.info("SysGroupClient.checkIsAssignClaim.根据入参，检查当前任务是否可以指派，接口返回，入参：userId={}，groupList={}，claimTaskFromAssignOtherUserId={} "
                    + "，返回：result={} ", userId, JacksonUtil.toJson(groupList), claimTaskFromAssignOtherUserId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.checkIsAssignClaim.根据入参，检查当前任务是否可以指派，异常，入参：userId={}，groupList={}，claimTaskFromAssignOtherUserId={} "
                    + "，报错：{}，{}", userId, JacksonUtil.toJson(groupList), claimTaskFromAssignOtherUserId, e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.checkIsAssignClaim.根据入参，检查当前任务是否可以指派，失败，入参：userId={}，groupList={}，claimTaskFromAssignOtherUserId={} ，返回：{}",
                userId, JacksonUtil.toJson(groupList), claimTaskFromAssignOtherUserId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }


    /**
     * 根据组长角色，或者经理角色，获取用户id,查询该用户配置的所有员工集合
     *
     * @param userId 登录用户
     * @return
     */
    public List<SysGroupMemberDTO> queryGroupUserIds(String userId) {
        if (StringUtils.isBlank(userId)) {
            log.error("SysGroupClient.queryGroupUserIds.根据入参，检查当前任务是否可以指派，接口入参为空！userId");
            throw new CcsServiceException(CcsGlobalError.DUBBO_INPUT_PARAM_IS_NULL);
        }
        ApiResult<List<SysGroupMemberDTO>> result;
        try {
            result = sysGroupProvider.queryGroupUserIds(userId);
            log.info("SysGroupClient.queryGroupUserIds.根据组长角色，或者经理角色，获取用户id,查询该用户配置的所有员工集合，接口返回，入参：userId={}，返回：result={} ", userId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.queryGroupUserIds.根据组长角色，或者经理角色，获取用户id,查询该用户配置的所有员工集合，异常，入参：userId={}，，报错：{}，{}", userId, e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.queryGroupUserIds.根据组长角色，或者经理角色，获取用户id,查询该用户配置的所有员工集合，失败，入参：userId={}，返回：{}", userId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据组ids，查询对应的所有组长userIds
     *
     * @param groupIds
     * @return
     */
    public List<String> getGroupLeader(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            log.error("SysGroupClient.getGroupLeader.根据组ids，查询对应的所有组长userIds, 入参为空 groupIds = null");
            return null;
        }
        ApiResult<List<String>> result;
        try {
            result = sysGroupProvider.getGroupLeader(groupIds);
            log.info("SysGroupClient.getGroupLeader.根据组ids，查询对应的所有组长userIds，接口返回，入参：groupIds={}，返回：result={} ", JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.getGroupLeader.根据组ids，查询对应的所有组长userIds，异常，入参：groupIds={}，，报错：{}，{}", JacksonUtil.toJson(groupIds), e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.getGroupLeader.根据组ids，查询对应的所有组长userIds，失败，入参：groupIds={}，返回：{}", JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据组ids，查询对应的组长信息
     *
     * @param groupIds
     * @return
     */
    public List<SysGroupDTO> getGroupLeaderAndManager(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            log.error("SysGroupClient.getGroupLeaderAndManager.根据组ids，查询对应的组长信息, 入参为空 groupIds = null");
            return null;
        }
        ApiResult<List<SysGroupDTO>> result;
        try {
            result = sysGroupProvider.getGroupLeaderAndManager(groupIds);
            log.info("SysGroupClient.getGroupLeaderAndManager.根据组ids，查询对应的组长信息，接口返回，入参：groupIds={}，返回：result={} ", JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.getGroupLeaderAndManager.根据组ids，查询对应的组长信息，异常，入参：groupIds={}，，报错：{}，{}", JacksonUtil.toJson(groupIds), e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.getGroupLeaderAndManager.根据组ids，查询对应的组长信息，失败，入参：groupIds={}，返回：{}", JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据组ids，查询对应的所有成员
     *
     * @param groupIds
     * @return
     */
    public List<String> getGroupAllUserId(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            log.error("SysGroupClient.getGroupAllUserId.根据组ids，查询对应的所有成员, 入参为空 groupIds = null");
            return null;
        }
        ApiResult<List<String>> result;
        try {
            result = sysGroupProvider.getGroupAllUserId(groupIds);
            log.info("SysGroupClient.getGroupAllUserId.根据组ids，查询对应的所有成员，接口返回，入参：groupIds={}，返回：result={} ",
                    JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.getGroupAllUserId.根据组ids，查询对应的所有成员，异常，入参：groupIds={}，，报错：{}，{}",
                    JacksonUtil.toJson(groupIds), e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.getGroupAllUserId.根据组ids，查询对应的所有成员，失败，入参：groupIds={}，返回：{}",
                JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据组id，查询组信息
     *
     * @param groupId
     * @return
     */
    public SysGroupVO detailById(Long groupId) {
        if (groupId == null) {
            log.error("SysGroupClient.detailById.根据组id，查询对应的所有成员, 入参为空 groupId = null");
            return new SysGroupVO();
        }
        ApiResult<SysGroupVO> result;
        try {
            result = sysGroupProvider.detailById(groupId);
            log.info("SysGroupClient.detailById.根据组id，查询对应的所有成员，接口返回，入参：groupId={}，返回：result={} ", groupId, JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.detailById.根据组id，查询对应的所有成员，异常，入参：groupId={}，，报错：{}，{}", groupId, e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.detailById.根据组id，查询对应的所有成员，失败，入参：groupId={}，返回：{}", groupId, JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

    /**
     * 根据组ids，查询对应的所有成员
     *
     * @param groupIds
     * @return
     */
    public List<String> getGroupMemberUserList(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            log.error("SysGroupClient.getGroupMemberUserList.根据组ids，查询对应的所有组员, 入参为空 groupIds = null");
            return null;
        }
        ApiResult<List<String>> result;
        try {
            result = sysGroupProvider.getGroupMemberUserList(groupIds);
            log.info("SysGroupClient.getGroupMemberUserList.根据组ids，查询对应的所有组员，接口返回，入参：groupIds={}，返回：result={} ",
                    JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        } catch (Exception e) {
            log.error("SysGroupClient.getGroupMemberUserList.根据组ids，查询对应的所有组员，异常，入参：groupIds={}，，报错：{}，{}",
                    JacksonUtil.toJson(groupIds), e.getMessage(), e);
            throw new CcsServiceException(CcsGlobalError.DUBBO_INTERFACE_IS_FAIL);
        }
        if (Boolean.TRUE.equals(result.getSuccess())) {
            return result.getData();
        }
        log.error("SysGroupClient.getGroupMemberUserList.根据组ids，查询对应的所有组员，失败，入参：groupIds={}，返回：{}",
                JacksonUtil.toJson(groupIds), JacksonUtil.toJson(result));
        throw new CcsServiceException(result.getCode(), result.getMsg());
    }

}
