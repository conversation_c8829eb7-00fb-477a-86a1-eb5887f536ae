package com.fuiou.ccs.workorder.convert;

import com.fuiou.ccs.workorder.api.dto.CcsExchangeDTO;
import com.fuiou.ccs.workorder.api.param.CcsExchangeParam;
import com.fuiou.ccs.workorder.api.vo.CcsExchangeVO;
import com.fuiou.ccs.workorder.repository.entity.Exchange;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单业务数据备注信息记录 对象转化器
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Mapper
public interface ExchangeConvert {

    ExchangeConvert INSTANCE = Mappers.getMapper(ExchangeConvert.class);

    @Mappings({})
    CcsExchangeVO toVO(Exchange exchange);

    @Mappings({})
    List<CcsExchangeVO> toVO(List<Exchange> exchangeList);

    @Mappings({})
    CcsExchangeDTO toDTO(Exchange exchange);

    @Mappings({})
    List<CcsExchangeDTO> toDTO(List<Exchange> exchangeList);

    @Mappings({})
    Exchange toEntity(CcsExchangeParam ccsExchangeParam);

    @Mappings({})
    Exchange toEntity(CcsExchangeDTO ccsExchangeDTO);

    @Mappings({})
    Exchange toEntity(CcsExchangeVO ccsExchangeVO);

}
