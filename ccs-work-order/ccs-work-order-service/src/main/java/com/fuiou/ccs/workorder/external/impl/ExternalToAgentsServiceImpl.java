package com.fuiou.ccs.workorder.external.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.system.dto.CcsDictItemDTO;
import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.vo.SysBusinessVO;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import com.fuiou.ccs.system.vo.SysFormVO;
import com.fuiou.ccs.workorder.api.dto.SysFormDetailToBranchCompanyDTO;
import com.fuiou.ccs.workorder.client.CcsBusinessClient;
import com.fuiou.ccs.workorder.client.CcsDictClient;
import com.fuiou.ccs.workorder.client.CcsFormClient;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.ccs.workorder.external.ToExternalService;
import com.fuiou.common.constants.CommonConstants;
import com.fuiou.common.utils.LocalDateUtil;
import com.fuiou.file.dto.FileLink;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客服工单，对外（分公司代理商）通知接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service("agentsToexternalservice")
public class ExternalToAgentsServiceImpl implements ToExternalService {

    private final CcsDictClient ccsDictClient;
    private final CcsFormClient ccsFormClient;
    private final CcsBusinessClient ccsBusinessClient;
    /**
     * 路由审批转电商处理接口
     */
    @Value("${thirdParty.agents.transferElectricity}")
    String transferElectricity;

    /**
     * 客服工单意见交流传给电商记录接口
     */
    @Value("${thirdParty.agents.eventExChangeHandle}")
    String eventExChangeHandle;

    /**
     * 加急通知电商接口
     */
    @Value("${thirdParty.agents.eventHurryNotice}")
    String eventHurryNotice;

    /**
     * 审批驳回给电商接口
     */
    @Value("${thirdParty.agents.eventRejectNotice}")
    String eventRejectNotice;

    /**
     * 客服工单完结，通知电商接口
     */
    @Value("${thirdParty.agents.eventOverNotice}")
    String eventOverNotice;

    /**
     * 客服工单平台终止后通知电商
     */
    @Value("${thirdParty.agents.eventStopSync}")
    String eventStopSync;

    public ExternalToAgentsServiceImpl(CcsDictClient ccsDictClient, CcsFormClient ccsFormClient,
            CcsBusinessClient ccsBusinessClient) {
        this.ccsDictClient = ccsDictClient;
        this.ccsFormClient = ccsFormClient;
        this.ccsBusinessClient = ccsBusinessClient;
    }

    @Override
    public boolean transferElectricity(String formName, List<SysFormDetailDTO> formDetails, Map<String, Object> detailMap, Long eventId, String userName, Integer hurryStatus, SysFormBusinessVO sysFormBusinessVO, List<FileLink> linkList, String eventRemark) {
        Object inscdObj = detailMap.get("INSCD");
        if (Objects.isNull(inscdObj) || "".equals(inscdObj)) {
            throw new CcsServiceException(9999, "合作方为空或录入错误，无法转分公司代理商处理！");
        }
        String inscdStr = String.valueOf(inscdObj);
        if (inscdStr.length() != 10 || !inscdStr.matches("^[A-Z0-9]{10}$")) {
            throw new CcsServiceException(9999, "合作方格式错误，必须为10位大写字母和数字组合！");
        }
        detailMap.put("relateInscd", Optional.ofNullable(detailMap.get("RELATEINSCD")).orElse(""));
        detailMap.put("inscd", inscdObj);
        List<SysFormDetailToBranchCompanyDTO> dynamicFields = formDetails.stream().filter(f -> f.getPresetStatus().equals(CommonConstants.NO)).map(f -> {
            SysFormDetailToBranchCompanyDTO dto = new SysFormDetailToBranchCompanyDTO();
            dto.setColumnsName(f.getColumnsName());
            dto.setCnColumnsName(f.getCnColumnsName());
            dto.setColumnsType(f.getColumnsType());
            dto.setColumnsOrder(f.getColumnsOrder());
            dto.setValue(detailMap.get(f.getColumnsName()));
            return dto;
        }).sorted(Comparator.comparing(SysFormDetailToBranchCompanyDTO::getColumnsOrder)).collect(Collectors.toList());
        detailMap.put("dynamicFields", dynamicFields);
        detailMap.put("filePaths", Optional.ofNullable(linkList).orElse(new ArrayList<>()));
        detailMap.putAll(buildGenericsParams(eventId, userName));
        detailMap.put("comments", StringUtils.isNotBlank(eventRemark) ? eventRemark : "");
        detailMap.put("hurryStatus", Optional.ofNullable(hurryStatus).orElse(0));
        detailMap.put("eventDate", LocalDateUtil.format(detailMap.get("EVENT_DATE")));
        detailMap.put("callNumber", Optional.ofNullable(detailMap.get("CALL_NUMBER")).orElse(""));
        detailMap.put("callerName", Optional.ofNullable(detailMap.get("CALLER_NAME")).orElse(""));
        detailMap.put("eventDesc", Optional.ofNullable(detailMap.get("EVENT_DESC")).orElse(""));
        // 工单编号
        detailMap.put("formEventCode", Optional.ofNullable(detailMap.get("FORM_EVENT_CODE")).orElse(""));
        // 是否风控可查看封装
        detailMap.put("riskToView", Optional.ofNullable(detailMap.get("RISK_TO_VIEW")).orElse(0));
        // 封装业务类型4要素中文
        detailMap.put("businessType", Optional.ofNullable(ccsBusinessClient.queryBusinessNameById(sysFormBusinessVO.getBusId())).orElse(new SysBusinessVO()).getName());
        detailMap.put("workType", Optional.ofNullable(ccsBusinessClient.queryBusinessNameById(sysFormBusinessVO.getWorkId())).orElse(new SysBusinessVO()).getName());
        detailMap.put("workSubtype", Optional.ofNullable(ccsBusinessClient.queryBusinessNameById(sysFormBusinessVO.getWorkSubId())).orElse(new SysBusinessVO()).getName());
        Object object = detailMap.get("SUB_ELEMENT");
        if (Objects.nonNull(object)) {
            detailMap.put("subElement", Optional.ofNullable(ccsBusinessClient.queryBusinessNameById((Long) object)).orElse(new SysBusinessVO()).getName());
        }
        // 来电人类型封装
        SysFormVO sysFormVO = ccsFormClient.querySysForm(formName);
        List<CcsDictItemDTO> callers = ccsDictClient.getDictItems(sysFormVO.getCallerDictCode());
        callers.stream().filter(c -> c.getValue().equals(detailMap.get("CALLER_TYPE"))).findAny().ifPresent(c -> detailMap.put("callerType", c.getLabel()));
        // 事件等级封装
        List<CcsDictItemDTO> eventLevel = ccsDictClient.getDictItems("EVENT_LEVEL");
        eventLevel.stream().filter(e -> e.getValue().equals(detailMap.get("EVENT_LEVEL"))).findAny().ifPresent(e -> detailMap.put("eventLevel", e.getLabel()));
        return queryExternalInterface(detailMap, transferElectricity);
    }

    private Map<String, Object> buildGenericsParams(Long eventId, String userName) {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("eventId", String.valueOf(eventId));
        paramMap.put("userName", userName);
        return paramMap;
    }

    @Override
    public boolean eventExChangeHandle(Long eventId, String remark, String userName, List<FileLink> linkList)
            throws CcsServiceException {
        Map<String, Object> paramMap = buildGenericsParams(eventId, userName);
        paramMap.put("remark", remark);
        paramMap.put("linkList", linkList);
        return queryExternalInterface(paramMap, eventExChangeHandle);
    }

    @Override
    public boolean eventHurryNotice(Long eventId, String remark, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = buildGenericsParams(eventId, userName);
        paramMap.put("remark", remark);
        return queryExternalInterface(paramMap, eventHurryNotice);
    }

    @Override
    public boolean eventRejectNotice(Long eventId, String remark, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = buildGenericsParams(eventId, userName);
        paramMap.put("remark", remark);
        return queryExternalInterface(paramMap, eventRejectNotice);
    }

    @Override
    public boolean eventOverNotice(Long eventId, String userName) throws CcsServiceException {
        Map<String, Object> paramMap = buildGenericsParams(eventId, userName);
        return queryExternalInterface(paramMap, eventOverNotice);
    }

    @Override
    public boolean eventStopSync(Long eventId, String userName) {
        Map<String, Object> paramMap = buildGenericsParams(eventId, userName);
        return queryExternalInterface(paramMap, eventStopSync);
    }

    @Override
    public boolean businessAddSync(Long busId, Long parentId, String name, Integer busLevel) throws CcsServiceException {
        throw new CcsServiceException(9999, "未知接口");
    }

    @Override
    public boolean businessUpdSync(Long busId, String name, Long parentId, Integer busLevel, Integer status)
            throws CcsServiceException {
        throw new CcsServiceException(9999, "未知接口");
    }


}
