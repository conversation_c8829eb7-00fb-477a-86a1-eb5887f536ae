package com.fuiou.ccs.workorder.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 外部来源字段信息
 *
 * <AUTHOR>
 * @since  1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_OUTSIDE_SOURCE_COLUMN")
public class OutsideSourceColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键字段ID
     */
    @TableId(value = "COLUMN_ID", type = IdType.AUTO)
    private Long columnId;

    /**
     * 字段英文名
     */
    @TableField("COLUMN_NAME")
    private String columnName;

    /**
     * 字段中文名
     */
    @TableField("CN_COLUMN_NAME")
    private String cnColumnName;

    /**
     * 字段是否非空;1-非空，0-不非空（默认）
     */
    @TableField("COLUMNS_NOT_NULL")
    private Integer columnsNotNull;

    /**
     * 表单中对应字段类型
     */
    @TableField("COLUMNS_TYPE")
    private String columnsType;

    /**
     * 外部来源数据的URL的ID
     */
    @TableField("SOURCE_URL_ID")
    private Long sourceUrlId;


}
