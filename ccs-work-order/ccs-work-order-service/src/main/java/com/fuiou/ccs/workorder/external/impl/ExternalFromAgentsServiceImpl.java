package com.fuiou.ccs.workorder.external.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fuiou.ccs.workorder.api.dto.CcsExchangeDTO;
import com.fuiou.ccs.workorder.api.enums.EventEnums;
import com.fuiou.ccs.workorder.api.param.CaseOverParam;
import com.fuiou.ccs.workorder.api.param.EventProcessParam;
import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.ccs.workorder.external.FromExternalService;
import com.fuiou.ccs.workorder.repository.entity.Event;
import com.fuiou.ccs.workorder.repository.entity.Exchange;
import com.fuiou.ccs.workorder.service.EventService;
import com.fuiou.ccs.workorder.service.ExchangeService;
import com.fuiou.common.api.ApiAssert;
import com.fuiou.flow.api.enums.FlowDynamicParamsManagerEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 分公司对外接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service("agentsFromexternalservice")
public class ExternalFromAgentsServiceImpl implements FromExternalService {

    @Autowired
    @Lazy
    EventService eventService;
    @Autowired
    @Lazy
    ExchangeService exchangeService;

    @Transactional(rollbackForClassName = {"CcsServiceException", "Exception", "RuntimeException"},
                   propagation = Propagation.REQUIRED)
    @Override
    public boolean caseOver(CaseOverParam caseOverParam) {
        /**
         * 1，申请结案，根据eventId，查询对应事件，并查询对应任务
         * 2，调用接口，通过当前节点
         * 3，查询当前任务是否结束并更新下一个审批人
         * 4，记录审批日志
         */
        if (caseOverParam.getEventId() == null || caseOverParam.getStatus() == null || StringUtils.isBlank(
                caseOverParam.getEmpName()) || StringUtils.isBlank(caseOverParam.getRemark()))
        {
            ApiAssert.failure(CcsGlobalError.REQUEST_PARAM_IS_NULL);
        }
        Event event = eventService.getById(caseOverParam.getEventId());
        ApiAssert.isFalse(CcsGlobalError.QUERY_EVENT_FAIL, Objects.isNull(event));
        // 判断是否分公司代理
        boolean agentFlag = Objects.equals(event.getClaimUserId(),
                FlowDynamicParamsManagerEnums.BRANCH_COMPANY_USER_TO_EXTERNAL.getExample());
        ApiAssert.isTrue(CcsGlobalError.NOW_TASK_NODE_NOT_EXTERNAL_FAIL, agentFlag);
        try {
            EventProcessParam eventProcessParam = new EventProcessParam();
            eventProcessParam.setUserId(event.getClaimUserId());
            eventProcessParam.setUserEmpName(caseOverParam.getEmpName());
            eventProcessParam.setComments(caseOverParam.getRemark());
            eventProcessParam.setEventId(caseOverParam.getEventId());
            eventProcessParam.setExternalFlag(true);
            if (caseOverParam.getStatus() != null && caseOverParam.getStatus() == EventEnums.NO) {
                eventService.rejectToUp(eventProcessParam);
            } else {
                eventService.completeToNext(eventProcessParam);
            }
            return true;
        } catch (CcsServiceException e) {
            throw new CcsServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            throw new CcsServiceException(CcsGlobalError.DEAL_FAIL);
        }
    }

    @Override
    public boolean backInfo(CcsExchangeDTO ccsExchangeDTO) {
        /**
         * 1，根据eventId,封装表单数据
         * 2，保存进意见交流记录
         * 3，根据是否加急操作， 更新对应加急状态
         */
        if (ccsExchangeDTO.getEventId() == null || StringUtils.isBlank(ccsExchangeDTO.getEmpName()) ||
            StringUtils.isBlank(ccsExchangeDTO.getExchangeContent()))
        {
            ApiAssert.failure(CcsGlobalError.REQUEST_PARAM_IS_NULL);
        }
        Event event = eventService.getById(ccsExchangeDTO.getEventId());
        ApiAssert.isFalse(CcsGlobalError.QUERY_EVENT_FAIL, Objects.isNull(event));
        Exchange exchange = new Exchange();
        try {
            exchange.setFormInfoId(event.getFormInfoId());
            exchange.setFormName(event.getFormName());
            exchange.setExchangeContent(ccsExchangeDTO.getExchangeContent());
            exchange.setCreateTime(LocalDateTime.now());
            exchange.setOutEmpName(ccsExchangeDTO.getEmpName());
            exchange.setExchangeId(IdWorker.getId());
            exchangeService.save(exchange);
            // if (StringUtils.isNotBlank(ccsExchangeDTO.getFileCodes())) {
            //     // 文件不为空的情况下，需要调用接口获取文件，然后上传，并追加到event表
            //     // work表有字段保存文件ID
            //     // 我需要调接口先去下载文件，然后上传到我们自己的平台
            //     // 然后在获取id，更新我们的表
            // }
            return true;
        } catch (CcsServiceException e) {
            throw new CcsServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            throw new CcsServiceException(CcsGlobalError.DEAL_FAIL);
        }
    }

}
