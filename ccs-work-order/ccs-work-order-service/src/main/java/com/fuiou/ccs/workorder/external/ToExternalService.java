package com.fuiou.ccs.workorder.external;

import com.fuiou.ccs.system.dto.SysFormDetailDTO;
import com.fuiou.ccs.system.vo.SysFormBusinessVO;
import com.fuiou.ccs.workorder.api.vo.ExternalDataVO;
import com.fuiou.ccs.workorder.exception.CcsGlobalError;
import com.fuiou.ccs.workorder.exception.CcsServiceException;
import com.fuiou.common.utils.HttpClientUtils;
import com.fuiou.common.utils.JacksonUtil;
import com.fuiou.file.dto.FileLink;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 客服工单，对外通知接口
 */
public interface ToExternalService {

    Log LOG = LogFactory.getLog(ToExternalService.class);

    /**
     * 转外部接口处理
     *
     * @param formDetails       表单字段明细
     * @param detailMap         表单数据
     * @param eventId           事件id
     * @param sysFormBusinessVO 业务类型
     * @param userName          操作人username
     * @param eventRemark       审批人备注
     * @return
     */
    boolean transferElectricity(String formName, List<SysFormDetailDTO> formDetails, Map<String, Object> detailMap,
            Long eventId, String userName, Integer hurryStatus,
            SysFormBusinessVO sysFormBusinessVO, List<FileLink> linkList, String eventRemark)
            throws CcsServiceException;

    /**
     * 发送交流意见给到外部接口
     *
     * @param eventId
     * @param remark
     * @param userName
     * @return
     */
    boolean eventExChangeHandle(Long eventId, String remark, String userName, List<FileLink> linkList)
            throws CcsServiceException;

    /**
     * 发送加急备注给到外部接口
     *
     * @param eventId
     * @param remark
     * @param userName
     * @return
     */
    boolean eventHurryNotice(Long eventId, String remark, String userName) throws CcsServiceException;

    /**
     * 事件驳回通知外部接口处理
     *
     * @param eventId
     * @param remark
     * @param userName
     * @return
     */
    boolean eventRejectNotice(Long eventId, String remark, String userName) throws CcsServiceException;

    /**
     * 工单完结，通知外部接口处理
     *
     * @param eventId
     * @param userName
     * @return
     */
    boolean eventOverNotice(Long eventId, String userName) throws CcsServiceException;

    /**
     * 客服工单业务类型，增加 同步给到外部接口
     *
     * @param busId    业务类型ID
     * @param parentId 上级业务ID
     * @param name     业务名称（修改仅修改名称）
     * @param busLevel 业务树的级别(1-业务类型，2-工单类型，3-工单子类型，4-子类要素)
     * @return
     */
    boolean businessAddSync(Long busId, Long parentId, String name, Integer busLevel) throws CcsServiceException;

    /**
     * 客服工单业务类型，修改 同步给到外部接口
     *
     * @param busId    业务类型ID
     * @param parentId 上级业务ID
     * @param name     业务名称（修改仅修改名称）
     * @param busLevel 业务树的级别(1-业务类型，2-工单类型，3-工单子类型 4-子类要素)
     * @param status   业务状态（1-正常，0-停用）
     * @return
     */
    boolean businessUpdSync(Long busId, String name, Long parentId, Integer busLevel, Integer status)
            throws CcsServiceException;

    /**
     * 客服工单平台终止后通知电商
     *
     * @param eventId  事件id
     * @param userName 操作人账号（英文缩写）不需要姓名
     * @return
     */
    boolean eventStopSync(Long eventId, String userName);

    /**
     * 调用外部接口
     *
     * @return
     */
    default boolean queryExternalInterface(Map<String, Object> param, String url) throws CcsServiceException {
        ExternalDataVO externalDataVO;
        String json = "";
        try {
            LOG.info(
                    String.format("ToExternalService.queryExternalInterface.info.接口返回，入参： param = %s , url = %s ",
                            JacksonUtil.toJson(param), url));
            json = HttpClientUtils.doPostJson(JacksonUtil.toJson(param), url);
            LOG.info(String.format(
                    "ToExternalService.queryExternalInterface.info.接口返回，入参： param = %s , url = %s ；返回原始数据：json = %s",
                    JacksonUtil.toJson(param), url, json));
            externalDataVO = JacksonUtil.parse(json, ExternalDataVO.class);
            LOG.info(String.format(
                    "ToExternalService.queryExternalInterface.info.接口返回，入参： param = %s , url = %s "
                    + "；返回解析后数据：externalDataVO = %s",
                    JacksonUtil.toJson(param), url, JacksonUtil.toJson(externalDataVO)));
        } catch (Exception e) {
            LOG.error(String.format(
                    "ToExternalService.queryExternalInterface.error，入参： param = %s , url = %s ；返回：json = %s  ; 报错日志： "
                    + "{}", JacksonUtil.toJson(param), url, json), e);
            throw new CcsServiceException(CcsGlobalError.EXTERNAL_INTERFACE_IS_FAIL.getCode(),
                    String.format(CcsGlobalError.EXTERNAL_INTERFACE_IS_FAIL.getMsg(), json));
        }
        if (Objects.nonNull(externalDataVO)) {
            if (Boolean.TRUE.equals(externalDataVO.getSuccess())) {
                return true;
            }
            throw new CcsServiceException(externalDataVO.getCode(), externalDataVO.getDesc());
        }
        throw new CcsServiceException(CcsGlobalError.THIRD_PARTY_INTERFACE_RETURN_FAIL);
    }
}
