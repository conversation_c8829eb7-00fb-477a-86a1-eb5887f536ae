package com.fuiou.ccs.workorder.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuiou.ccs.workorder.api.vo.CcsExchangeVO;
import com.fuiou.ccs.workorder.repository.entity.Exchange;
import com.fuiou.ccs.workorder.repository.mapper.ExchangeMapper;
import com.fuiou.ccs.workorder.service.ExchangeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工单业务数据备注信息记录 服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ExchangeServiceImpl extends ServiceImpl<ExchangeMapper, Exchange> implements ExchangeService {

    @Override
    public List<CcsExchangeVO> queryExchangeList(String formName, Long formInfoId) {
        return baseMapper.queryExchangeList(formName, formInfoId);
    }

    /**
     * 查询意见交流日志列表，分公司
     *
     * @param formInfoIdSets
     * @return formInfoId，意见交流列表
     */
    @Override
    public Map<Long, List<CcsExchangeVO>> queryExchangeListByBranchOffice(Set<Long> formInfoIdSets) {
        if (CollectionUtils.isEmpty(formInfoIdSets)) {
            return new ConcurrentHashMap<>(0);
        }
        List<Long> collect = formInfoIdSets.stream().collect(Collectors.toList());
        int index = collect.size();
        int tempIndex = 800;
        int toIndex = tempIndex;
        List<CcsExchangeVO> exchangeList = new ArrayList<>();
        for (int i = 0; i < collect.size(); i += tempIndex) {
            if (i + tempIndex > index) {
                toIndex = index - i;
            }
            exchangeList.addAll(baseMapper.queryExchangeListByInfoId(collect.subList(i, i + toIndex)));
        }
        return exchangeList.stream().collect(Collectors.groupingBy(CcsExchangeVO::getFormInfoId));
    }
}
