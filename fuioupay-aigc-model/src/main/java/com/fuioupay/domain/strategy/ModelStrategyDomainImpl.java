package com.fuioupay.domain.strategy;

import com.fuioupay.common.utils.WebClientUtils;
import com.fuioupay.domain.model.ModelStrategyDomain;
import com.fuioupay.domain.model.req.ModelRequest;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模型基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ModelStrategyDomainImpl implements ModelStrategyDomain {

    // 端点
    protected abstract String getEndpoint();

    // 模型参数
    protected abstract Map<String, Object> getVendorSpecificParams();

    // API密钥
    protected abstract String getApiKey();

    // base-url
    protected abstract String getBaseUrl();

    @Override
    public Mono<Object> execute(ModelRequest request) {
        Map<String, Object> requestBody = buildRequestBody(request, false);
        return WebClientUtils.postForMonoWithHeaders(getBaseUrl(), getEndpoint(), requestBody, builderRequsetHeader())
                .doOnSuccess(response -> log.info("Mono响应成功: {}", response))
                .doOnError(error -> log.error("Mono调用失败: {}", error.getMessage(), error));
    }

    @Override
    public Flux<Object> executeStream(ModelRequest request) {
        Map<String, Object> requestBody = buildRequestBody(request, true);
        return WebClientUtils.postForFluxWithHeaders(getBaseUrl(), getEndpoint(), requestBody, builderRequsetHeader())
                .doOnError(error -> log.error("Flux调用失败: {}", error.getMessage(), error));
    }

    private Map<String, String> builderRequsetHeader() {
        Map<String, String> requestHeader = new ConcurrentHashMap<>();
        requestHeader.put("Authorization", "Bearer " + getApiKey());
        requestHeader.put("Content-Type", "application/json");
        return requestHeader;
    }

    // 构建请求体
    private Map<String, Object> buildRequestBody(ModelRequest request, boolean stream) {
        Map<String, Object> requestBody = new HashMap<>(getVendorSpecificParams());
        // 构建消息数组
        List<Map<String, String>> messages = new ArrayList<>();
        // 添加系统提示
        if (request.getSystemPrompt() != null && !request.getSystemPrompt().isEmpty()) {
            messages.add(Map.of(
                    "role", "system",
                    "content", request.getSystemPrompt()
            ));
        }
        // 添加用户提示
        messages.add(Map.of(
                "role", "user",
                "content", request.getPrompt()
        ));
        requestBody.put("messages", messages);
        requestBody.put("stream", stream);
        // 设置后台调用默认温度，前台调用需覆盖
        requestBody.put("temperature", 0.6);
        // 前台传入，可选参数
        if (request.getProviderParams() != null) {
            requestBody.putAll(request.getProviderParams());
        }
        return requestBody;
    }
}