package com.fuioupay.domain.deepseek;

import com.fuioupay.domain.model.enums.ModelType;
import com.fuioupay.domain.strategy.ModelStrategyDomainImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * deepseek-r1，火山大模型
 *
 * <AUTHOR>
 */
@Component
public class VolcanoToR1DomainImpl extends ModelStrategyDomainImpl {

    @Value("${aigcModel.volcano.key:}")
    private String apiKey;

    @Value("${aigcModel.volcano.baseUrl:https://ark.cn-beijing.volces.com/api/v3}")
    private String baseUrl;

    @Override
    protected String getEndpoint() {
        return "/chat/completions";
    }

    @Override
    protected Map<String, Object> getVendorSpecificParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("max_tokens", 16384);
        params.put("model", "deepseek-r1-250528");
        return params;
    }
    
    @Override
    protected String getApiKey() {
        return apiKey;
    }

    @Override
    protected String getBaseUrl() {
        return baseUrl;
    }

    @Override
    public ModelType getSupportedModelType() {
        return ModelType.DEEPSEEK_R1_VOLCANO;
    }

}
