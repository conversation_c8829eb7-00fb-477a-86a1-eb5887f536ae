package com.fuioupay.domain.doubao;

import com.fuioupay.domain.model.enums.ModelType;
import com.fuioupay.domain.strategy.ModelStrategyDomainImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * deepseek-r1，火山大模型
 *
 * <AUTHOR>
 */
@Component
public class VolcanoDoubaoThinkingProDomainImpl extends ModelStrategyDomainImpl {

    @Value("${aigcModel.volcano.key:}")
    private String apiKey;

    @Value("${aigcModel.volcano.baseUrl:https://ark.cn-beijing.volces.com/api/v3}")
    private String baseUrl;

    @Override
    protected String getEndpoint() {
        return "/chat/completions";
    }

    @Override
    protected Map<String, Object> getVendorSpecificParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("max_tokens", 16384);
        params.put("model", "doubao-1-5-thinking-pro-250415");
        return params;
    }

    @Override
    protected String getApiKey() {
        return apiKey;
    }

    @Override
    protected String getBaseUrl() {
        return baseUrl;
    }

    @Override
    public ModelType getSupportedModelType() {
        return ModelType.DOUBAO_1_5_THINKING_PRO_250415;
    }

}
